from dataclasses import dataclass

from marshmallow import fields

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


@dataclass
class History(BasicAttributes):
    id: str = None
    data: dict = None
    company_id: str = None
    action: str = None
    table: str = None
    version: str = None
    old_data: dict = None
    note: str = None


class HistorySchema(BasicAttributesSchema):
    id = fields.UUID(required=True)
    company_id = fields.UUID(required=True)
    data = fields.Dict(required=True)
    version = fields.Str(required=True)
    action = fields.Str(required=True)
    table = fields.Str(required=True)
    note = fields.Str(allow_none=True)
    old_data = fields.Dict(allow_none=True)


class HistoryModel(BasicModel):
    key__id = 'id'
    key__ranges = ['version']
    table_name = 'history'
    attributes: History
    attributes_schema = HistorySchema
    attributes_class = History

    query_mapping = {
        'query': ['user'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'note': 'query',
            'table': 'query',
            'action': 'query',
            'version': 'range',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'user': 'user',
            'data': 'data',
            'old_data': 'old_data'
        }
    }
