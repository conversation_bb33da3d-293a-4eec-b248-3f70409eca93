role:
  statements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
      Resource: "*"
    - Effect: Allow
      Action:
        - dynamodb:Get*
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Scan
        - dynamodb:BatchWriteItem
        - dynamodb:Query
      Resource: "*"
    - Effect: Allow
      Action:
        - ec2:CreateNetworkInterface
        - ec2:DescribeNetworkInterfaces
        - ec2:DeleteNetworkInterface
        - ec2:AssignPrivateIpAddresses
        - ec2:UnassignPrivateIpAddresses
      Resource: "*"
    - Effect: Allow
      Action:
        - sqs:SendMessage
        - sqs:ReceiveMessage
        - sqs:DeleteMessage
        - sqs:GetQueueAttributes
        - sqs:GetQueueUrl
      Resource:
        - Fn::GetAtt: [RetrieverQueue, Arn]