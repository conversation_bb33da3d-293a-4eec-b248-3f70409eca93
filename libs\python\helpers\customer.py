import os
import uuid
from decimal import Decimal
from typing import Union

from nolicore.utils.exceptions import BadRequest

from helpers.common import is_valid_uuid
from helpers.customer_group import get_default_customer_group
from helpers.errors import CustomerExisted
from helpers.lambda_client import lambda_client_invoke_async
from helpers.user import create_user_info_by_api
from helpers.utils import RESOURCE_SERVICE
from models.actor.customer import CustomerModel
from models.actor.customer_group import CustomerGroupModel
from models.basic import BasicAttributes
from models.order.order import OrderModel
from models.order.order_return import ReturnModel

ENV = os.getenv('ENV')


def validate_customer(data, company_id, old_data: CustomerModel = None):
    phone = data.get("phone")
    if phone:
        try:
            search_params = {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"phone.keyword": phone}},
                            {"term": {"company_id.keyword": company_id}},
                        ]
                    }
                }
            }
            items = CustomerModel.report(search_params, service=RESOURCE_SERVICE).body['hits']['hits']
            res = [item['_source'] for item in items]
        except IndexError:
            res = []

        if len(res) > 0:
            if old_data is not None:
                if res[0].get("phone") != old_data.attributes.phone:
                    raise CustomerExisted()
            else:
                raise CustomerExisted()


def update_customer(company_id, customer: CustomerModel, obj: Union[OrderModel, ReturnModel], order_action,
                    loyal_point=None, redeem_point=None):
    sale_order_attrs = customer.attributes.sale_order
    loyal_customer_attrs = customer.attributes.loyal_customer
    if order_action == "create":
        sale_order = {
            "last_order_on": str(obj.attributes.created_at),
            "net_quantity": sale_order_attrs.net_quantity if sale_order_attrs is not None else 0,
            "purchased_order": sale_order_attrs.purchased_order if sale_order_attrs is not None else 0,
            "returned_item_quantity": sale_order_attrs.returned_item_quantity if sale_order_attrs is not None else 0,
            "total_spent": sale_order_attrs.total_spent if sale_order_attrs is not None else 0,
        }
        used_point = (loyal_customer_attrs.used_point if loyal_customer_attrs else 0) + (redeem_point or 0)
        point = (loyal_customer_attrs.point if loyal_customer_attrs else 0) - (redeem_point or 0)
        loyal_customer = {
            "used_point": used_point,
            "point": point,
        }
        customer.update({"sale_order": sale_order, "loyal_customer": loyal_customer})
    elif order_action == "pay":
        product_quantity = sum(item.quantity for item in (obj.attributes.order_line_items or []))
        net_quantity = (sale_order_attrs.net_quantity if sale_order_attrs is not None else 0) + product_quantity
        redeem_discount = (
                              obj.attributes.reward_program.money if obj.attributes.reward_program is not None else 0) * (
                                  obj.attributes.redeem_point or 0)
        total_spent = (sale_order_attrs.total_spent if sale_order_attrs is not None else 0) + (
                obj.attributes.total or 0) - redeem_discount
        sale_order = {
            "last_order_on": str(sale_order_attrs.last_order_on) if sale_order_attrs is not None else None,
            "net_quantity": net_quantity,
            "purchased_order": (sale_order_attrs.purchased_order if sale_order_attrs is not None else 0) + 1,
            "returned_item_quantity": sale_order_attrs.returned_item_quantity if sale_order_attrs is not None else 0,
            "total_spent": total_spent,
        }
        point = (loyal_customer_attrs.point if loyal_customer_attrs is not None else 0) + (loyal_point or 0)
        loyal_customer = {
            "point": point,
            "used_point": loyal_customer_attrs.used_point if loyal_customer_attrs is not None else 0
        }
        customer.update({"sale_order": sale_order, "loyal_customer": loyal_customer})
        next_group = customer.attributes.customer_group.next_group if customer.attributes.customer_group is not None else None
        if next_group is None:
            return
        customer_group_id = next_group.id
        upgrade_customer_group(customer_group_id, company_id, customer)
    elif order_action == "cancel":
        product_quantity = sum(item.quantity for item in (obj.attributes.order_line_items or []))
        net_quantity = (sale_order_attrs.net_quantity if sale_order_attrs is not None else 0) - product_quantity
        returned_item_quantity = (
                                     sale_order_attrs.returned_item_quantity if sale_order_attrs is not None else 0) + product_quantity
        redeem_discount = (
                              obj.attributes.reward_program.money if obj.attributes.reward_program is not None else 0) * (
                                  obj.attributes.redeem_point or 0)
        order_total = (obj.attributes.total or 0) - redeem_discount
        total_spent = (sale_order_attrs.total_spent if sale_order_attrs is not None else 0) - order_total
        sale_order = {
            "last_order_on": str(sale_order_attrs.last_order_on) if sale_order_attrs is not None else None,
            "net_quantity": net_quantity,
            "purchased_order": sale_order_attrs.purchased_order if sale_order_attrs is not None else 0,
            "returned_item_quantity": returned_item_quantity,
            "total_spent": total_spent,
        }

        redeem_point = obj.attributes.redeem_point or 0
        loyal_point = obj.attributes.loyal_point or 0
        used_point = (loyal_customer_attrs.used_point if loyal_customer_attrs else 0) - redeem_point
        point = (loyal_customer_attrs.point if loyal_customer_attrs else 0) + redeem_point - loyal_point
        loyal_customer = {
            "used_point": used_point,
            "point": point,
        }
        customer.update({"sale_order": sale_order, "loyal_customer": loyal_customer})


def upgrade_customer_group(customer_group_id, company_id, customer: CustomerModel):
    customer_group_obj = CustomerGroupModel.by_key(
        {CustomerGroupModel.key__id: customer_group_id, "company_id": company_id})
    if customer_group_obj is None:
        return
    sale_order_attrs = customer.attributes.sale_order
    current_total_spent = sale_order_attrs.total_spent if sale_order_attrs is not None else 0
    if customer_group_obj.attributes.min_purchased_amount <= current_total_spent:
        new_customer_group = {k: v for k, v in customer_group_obj.attributes_dict.items() if k not in {
            'created_at', 'company_id', 'updated_at', 'user'
        }}
        customer.update({"customer_group": new_customer_group})
        next_group = customer_group_obj.attributes.next_group if customer_group_obj.attributes.next_group is not None else None
        if next_group is None:
            return
        customer_group_id = next_group.id
        upgrade_customer_group(customer_group_id, company_id, customer)


def check_point(customer, redeem_point):
    current_point = customer.loyal_customer.point if customer.loyal_customer is not None else 0
    if current_point < Decimal(redeem_point):
        raise BadRequest("Insufficient points: You don't have enough points to complete this redemption.")


def invoke_import_customer(request_id, company_id, start=0):
    payload = {
        'request_id': request_id,
        'company_id': company_id,
        'start': start
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-actors-service-{ENV}-importCustomerHandle')


def invoke_format_customer(request_id, company_id):
    payload = {
        'request_id': request_id,
        'company_id': company_id,
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-actors-service-{ENV}-formatCustomerHandle')


def add_customer(customer, company_id):
    has_account = customer.get('has_account')
    customer_id = customer.get('id')

    validate_customer(customer, company_id)

    if customer_id:
        is_valid_uuid4 = is_valid_uuid(customer_id, 4)
        if not is_valid_uuid4:
            raise BadRequest('Id is not invalid. Required uuid4!')
        key = {
            CustomerModel.key__id: customer_id,
            'company_id': company_id
        }

        customer_obj = CustomerModel.by_key(key)
        if customer_obj is not None:
            raise BadRequest('Id is available!')
    else:
        customer_id = str(uuid.uuid4())

    if has_account:
        create_user_info_by_api({**customer, "sub": customer_id})
        customer["sub"] = customer_id
    if "is_customer" in customer:
        customer.pop('is_customer')
    customer[CustomerModel.key__id] = customer_id
    customer_group = customer.get("customer_group")
    if customer_group is None:
        customer_group = get_default_customer_group(company_id)
        customer['customer_group'] = {k: v for k, v in customer_group.items() if k not in {
            'created_at', 'company_id', 'updated_at', 'user'
        }}
    customer_obj = CustomerModel(BasicAttributes.add_basic_attributes(customer, company_id))
    customer_obj.save()
    return customer_obj.attributes_dict
