import json
import unittest
from datetime import datetime
from unittest.mock import patch, MagicMock

from tests.base import BaseTestCase

from connections.src.apis.connection import (
    get_connection_types,
    get_connection_setup_fields,
    setup_connection,
    oauth2_callback, get_connection_type, setup_default_connection
)
from nolicore.utils.exceptions import NotFoundRequest
from integrations.channels.business_central.business_central_connection import BusinessCentralConnection
from integrations.channels.connection_base import OAuth2Connection
from integrations.channels.shopify.shopify_connection import ShopifyConnection
from connections.src.apis.connection import get_default_connections


class ConnectionApiTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

    def create_lambda_event(self, method='GET', path='/', body=None, query_params=None, path_params=None):
        event = {
            'httpMethod': method,
            'body': json.dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': {},
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
            },
            'isBase64Encoded': False
        }
        return event

    def create_lambda_context(self):
        context = MagicMock()
        context.function_name = 'test_function'
        context.function_version = '$LATEST'
        context.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test_function'
        context.memory_limit_in_mb = 128
        context.aws_request_id = 'test_request_id'
        context.log_group_name = '/aws/lambda/test_function'
        context.log_stream_name = '2023/05/01/[$LATEST]abcdefghijklmnopqrstuvwxyz'
        context.identity = None
        context.client_context = None
        return context

    def create_mock_oauth2_connection(self, connection_id='test_id', status='pending', connected=False):
        mock_connection = MagicMock(spec=OAuth2Connection)
        mock_connection.id = connection_id
        mock_connection.get_authorization_url.return_value = 'https://auth.example.com'
        mock_connection.logo = 'https://example.com/logo.png'
        mock_connection.status = status
        mock_connection.scope = 'read_orders,write_products'
        mock_connection.client_secret = 'test_secret'
        mock_connection.name = 'Test Connection'
        mock_connection.auth_url = 'https://auth.example.com'
        mock_connection.company_id = 'company_123'
        mock_connection.client_id = 'test_client_id'
        mock_connection.type = 'oauth2'
        mock_connection.created_at = datetime.now().isoformat()
        mock_connection.token_url = 'https://token.example.com'
        mock_connection.description = 'Test Description'
        mock_connection.auth_type = 'oauth2'
        mock_connection.redirect_uri = 'https://redirect.example.com'
        mock_connection.path = '/test/path'
        mock_connection.connected = connected
        mock_connection.updated_at = datetime.now().isoformat()
        mock_connection.channel_type = 'business_central'
        return mock_connection

    def assert_api_response(self, response, expected_body):
        self.assertIsInstance(response, dict)
        self.assertIn('headers', response)
        self.assertIn('statusCode', response)
        self.assertIn('body', response)
        self.assertIn('isBase64Encoded', response)

        self.assertEqual(response['statusCode'], 200)
        self.assertEqual(response['isBase64Encoded'], False)

        self.assertDictEqual(response['headers'], {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
            'content-type': 'application/json'
        })

        actual_body = json.loads(response['body'])
        self.assertEqual(actual_body, expected_body)

    def test_get_connection_types(self):
        event = self.create_lambda_event()
        context = self.create_lambda_context()
        response = get_connection_types(event, context)

        expected_body = {
            'connection_types': [
                {'key': 'test_connection', 'name': 'Test Connection'}
            ]
        }
        self.assert_api_response(response, expected_body)

    def test_get_connection_type(self):
        event = self.create_lambda_event()
        context = self.create_lambda_context()
        response = get_connection_type(event, context)

        expected_body = {
            'connection_types': [
                {'key': 'test_connection', 'name': 'Test Connection'}
            ]
        }
        self.assert_api_response(response, expected_body)

    @patch('integration.connections.src.apis.connection.connection_registry')
    def test_get_connection_setup_fields(self, mock_registry):
        mock_connection_class = MagicMock()
        mock_connection_class.get_setup_fields.return_value = ['field1', 'field2']
        mock_registry.get.return_value = mock_connection_class

        event = self.create_lambda_event(path_params={'connection_type': 'test_connection'})
        context = self.create_lambda_context()
        response = get_connection_setup_fields(event, context)

        expected_body = {'setup_fields': ['field1', 'field2']}
        self.assert_api_response(response, expected_body)

    @patch('integration.connections.src.apis.connection.connection_registry')
    def test_setup_normal_connection(self, mock_registry):
        mock_connection = MagicMock(spec=ShopifyConnection)
        mock_connection.attributes.id = 'shopify_connection_id'
        ShopifyConnection.create_connection = MagicMock(return_value=mock_connection)
        mock_registry.get.return_value = ShopifyConnection

        event = self.create_lambda_event(
            method='POST',
            body={
                "connection_type": "shopify",
                "setup_data": {
                    "shop_url": "test-shop.myshopify.com",
                    "api_key": "test_api_key",
                    "api_secret": "test_api_secret"
                }
            }
        )
        context = self.create_lambda_context()
        response = setup_connection(event, context)

        expected_body = {
            'message': 'shopify connection setup successful',
            'connection_id': 'shopify_connection_id'
        }
        self.assert_api_response(response, expected_body)

        ShopifyConnection.create_connection.assert_called_once_with({
            "shop_url": "test-shop.myshopify.com",
            "api_key": "test_api_key",
            "api_secret": "test_api_secret"
        })

    @patch('integration.connections.src.apis.connection.connection_registry')
    def test_setup_oauth2_connection(self, mock_registry):
        mock_connection = MagicMock(spec=BusinessCentralConnection)
        mock_connection.attributes.id = 'business_central_connection_id'
        mock_connection.get_authorization_url.return_value = 'https://auth.example.com'
        BusinessCentralConnection.create_connection = MagicMock(return_value=mock_connection)
        mock_registry.get.return_value = BusinessCentralConnection

        event = self.create_lambda_event(
            method='POST',
            body={
                "connection_type": "business_central",
                "setup_data": {
                    "tenant_id": "test_tenant_id",
                    "client_id": "test_client_id",
                    "client_secret": "test_client_secret",
                    "company_id": "test_company_id"
                }
            }
        )
        context = self.create_lambda_context()
        response = setup_connection(event, context)

        expected_body = {
            'message': 'business_central OAuth2 flow initiated',
            'authorization_url': 'https://auth.example.com',
            'connection_id': 'business_central_connection_id'
        }
        self.assert_api_response(response, expected_body)

        BusinessCentralConnection.create_connection.assert_called_once_with({
            "tenant_id": "test_tenant_id",
            "client_id": "test_client_id",
            "client_secret": "test_client_secret",
            "company_id": "test_company_id"
        })

    def test_oauth2_callback(self):
        event = {'version': '2.0', 'routeKey': 'GET /oauth2_callback', 'rawPath': '/oauth2_callback',
                 'rawQueryString': 'oa_id=2421215564594712167&code=J1WT1n1D2MfCAovLB3KCHWaI3qnX4J9eJ1T_T4fgPH0k2s9AGcX5IZfFTN4uLqvvNrjuN4mk07nwHoyg4YmkSdXC2Kq0RXXN2s0HOMjb9rz4Q6TQVGHV2nHb14LVBreWVMicLXbg6K1uVZG73oKbGtaB4svnU1Om1Hy-6dqYCJPCDnjsApStHdHoCW4GU4401q81GMWc9MK3FHLNJLjBU54ZOaWZVtm60cKF1ZLP4aWiPdCj6IvC1ZmXPdnDG6O_CXDoPbhkLWK7dH1hROh7QGQGxaKtYT58LFt5M7xefN9_iPCIO-wdT1c-jsw_rCzKlFh5NokCp4yrkRb5PQJu1NpJdavosjHYVzsDK3NQrpaakV5i7jwX4rvP9bm_gYb9Imja&state=eJyrVkrOz8tLTS7JzM-Lz0xRslJQMjdMSzVKSTbTTUw0NtU1MTFJ1k0ySzPQNUw2STI0SDOzMDRLUaoFAAu2EM4%3D',
                 'headers': {
                     'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                     'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-US,en;q=0.7',
                     'content-length': '0', 'host': 'api-dev.onexbots.com', 'priority': 'u=0, i',
                     'referer': 'https://oauth.zaloapp.com/',
                     'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"', 'sec-ch-ua-mobile': '?0',
                     'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'document', 'sec-fetch-mode': 'navigate',
                     'sec-fetch-site': 'cross-site', 'sec-fetch-user': '?1', 'sec-gpc': '1',
                     'upgrade-insecure-requests': '1',
                     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                     'x-amzn-trace-id': 'Root=1-6847fe11-408161fd031ca6ae677ca2c3', 'x-forwarded-for': '**************',
                     'x-forwarded-port': '443', 'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'code': 'J1WT1n1D2MfCAovLB3KCHWaI3qnX4J9eJ1T_T4fgPH0k2s9AGcX5IZfFTN4uLqvvNrjuN4mk07nwHoyg4YmkSdXC2Kq0RXXN2s0HOMjb9rz4Q6TQVGHV2nHb14LVBreWVMicLXbg6K1uVZG73oKbGtaB4svnU1Om1Hy-6dqYCJPCDnjsApStHdHoCW4GU4401q81GMWc9MK3FHLNJLjBU54ZOaWZVtm60cKF1ZLP4aWiPdCj6IvC1ZmXPdnDG6O_CXDoPbhkLWK7dH1hROh7QGQGxaKtYT58LFt5M7xefN9_iPCIO-wdT1c-jsw_rCzKlFh5NokCp4yrkRb5PQJu1NpJdavosjHYVzsDK3NQrpaakV5i7jwX4rvP9bm_gYb9Imja',
                'oa_id': '2421215564594712167',
                'state': 'eJyrVkrOz8tLTS7JzM-Lz0xRslJQMjdMSzVKSTbTTUw0NtU1MTFJ1k0ySzPQNUw2STI0SDOzMDRLUaoFAAu2EM4='},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk',
                                    'domainName': 'api-dev.onexbots.com', 'domainPrefix': 'api-dev',
                                    'http': {'method': 'GET', 'path': '/oauth2_callback', 'protocol': 'HTTP/1.1',
                                             'sourceIp': '**************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                    'requestId': 'L8Sivh5HyQ0EJFA=', 'routeKey': 'GET /oauth2_callback',
                                    'stage': '$default', 'time': '10/Jun/2025:09:42:41 +0000',
                                    'timeEpoch': *************}, 'isBase64Encoded': False}

        context = self.create_lambda_context()
        response = oauth2_callback(event, context)

        expected_body = {
            'message': 'Authorization successful',
            'connection_id': 'test_id'
        }
        self.assert_api_response(response, expected_body)

    def test_get_connection_setup_fields_not_found(self):
        event = self.create_lambda_event(path_params={'connection_type': 'non_existent'})
        context = self.create_lambda_context()

        response = get_connection_setup_fields(event, context)

        # Assert the structure of the response
        self.assertIsInstance(response, dict)
        self.assertIn('headers', response)
        self.assertIn('statusCode', response)
        self.assertIn('body', response)
        self.assertIn('isBase64Encoded', response)

        # Check the headers
        expected_headers = {
            'Access-Control-Allow-Credentials': True,
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
            'Access-Control-Allow-Origin': '*',
            'content-type': 'application/json'
        }
        self.assertDictEqual(response['headers'], expected_headers)

        # Check the status code
        self.assertEqual(response['statusCode'], 404)  # Assuming 404 for not found

        # Check the body
        body = json.loads(response['body'])
        self.assertEqual(body['error'], 'Not found')
        self.assertEqual(body['message'], 'Connection type not found')

        # Check isBase64Encoded
        self.assertFalse(response['isBase64Encoded'])

    @patch('integration.connections.src.apis.connection.connection_registry')
    def test_setup_connection_not_found(self, mock_registry):
        mock_registry.get.side_effect = NotFoundRequest('Connection type not found')

        event = self.create_lambda_event(
            method='POST',
            body={"connection_type": "non_existent", "setup_data": {}}
        )
        context = self.create_lambda_context()

        response = setup_connection(event, context)

        # Assert the structure of the response
        self.assertIsInstance(response, dict)
        self.assertIn('headers', response)
        self.assertIn('statusCode', response)
        self.assertIn('body', response)
        self.assertIn('isBase64Encoded', response)

        # Check the headers
        expected_headers = {
            'Access-Control-Allow-Credentials': True,
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
            'Access-Control-Allow-Origin': '*',
            'content-type': 'application/json'
        }
        self.assertDictEqual(response['headers'], expected_headers)

        # Check the status code
        self.assertEqual(response['statusCode'], 404)

        # Check the body
        body = json.loads(response['body'])
        self.assertEqual(body['error'], 'Not found')
        self.assertEqual(body['message'], 'Connection type not found')

        # Check isBase64Encoded
        self.assertFalse(response['isBase64Encoded'])

    def test_oauth2_callback_missing_params(self):
        event = self.create_lambda_event(method='GET')
        context = self.create_lambda_context()

        response = oauth2_callback(event, context)

        # Assert the structure of the response
        self.assertIsInstance(response, dict)
        self.assertIn('headers', response)
        self.assertIn('statusCode', response)
        self.assertIn('body', response)
        self.assertIn('isBase64Encoded', response)

        # Check the headers
        expected_headers = {
            'Access-Control-Allow-Credentials': True,
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
            'Access-Control-Allow-Origin': '*',
            'content-type': 'application/json'
        }
        self.assertDictEqual(response['headers'], expected_headers)

        # Check the status code
        self.assertEqual(response['statusCode'], 400)

        # Check the body
        body = json.loads(response['body'])
        self.assertEqual(body['error'], 'Bad request')
        self.assertEqual(body['message'], 'Missing required parameters')

        # Check isBase64Encoded
        self.assertFalse(response['isBase64Encoded'])

    def test_get_default_connections(self):
        event = {'version': '2.0', 'routeKey': 'POST /setup_default/{connection_type}',
                 'rawPath': '/setup_default/zalo_oa',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AIsCI9b8cwssGTBJaKRt_6WkPmoqOmbIFXd5vyoOipHiwCMMKheoPFyTPz_H0_A7hTn2DxoxsZp8AHbfa6WyQstr3EN6iMyXwUK-buq6zBNQSE-_UwDSIgXcQFaLmjMx4QX6EaSPZQB9BWYQ_YiFypWK4ebFs4GKCC04Ou0ATJxRjG_uc8x9DUdbn04zHPBuL_zE75aJHJ8_zGpoSP6QqnbCCfjdzxiW2YmR208u3BpvKf-DUyP3mjP_353alHeCGelPSsipO13Q_FsAYIan-aNfPn0KIX7oUSLWaxuPdg6iE0hNF35_f8vmWEJa59zLQ_idbV4AVMhaVBXH71P-tQ',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EDvPSMiDc61XxkllA5ltjnZ9XF0yiL85wz6Gvj8UxV-FMcOhIdd_JIUortD8Tmbl18_Mo1Xzfm7kU05SEM1LahSDoMtfRyadgKQ_Xpnhqd8rLkaj-jmxONwQCTCeRSDx5JV3ueOXxfAifVjw-hBTjQfHCwfJH6PhO3uPYRpPdiZvjEEoWxHHFRLjWAYP80C3ldjm-tQz8G-B3JZAIGULknB2eMtuG5plhFNsk353gClN1ehCLhNNCl-Xy9plRkROlUT3Vc6wSADssLYeyG2X4-cgPFMXwyOZ2xPcRFYNToy-_EHvUa6_AYx70lr_lWj_N2MWCrTzx7Jd0sdhXE0Fvg',
                             'content-length': '0', 'host': 'api-dev.onexbots.com', 'origin': 'http://localhost:3000',
                             'priority': 'u=1, i', 'referer': 'http://localhost:3000/',
                             'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                             'sec-fetch-mode': 'cors', 'sec-fetch-site': 'cross-site', 'sec-gpc': '1',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                             'x-amzn-trace-id': 'Root=1-6847def9-6b4ab2f5436830a30a72ebf4',
                             'x-forwarded-for': '**************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AIsCI9b8cwssGTBJaKRt_6WkPmoqOmbIFXd5vyoOipHiwCMMKheoPFyTPz_H0_A7hTn2DxoxsZp8AHbfa6WyQstr3EN6iMyXwUK-buq6zBNQSE-_UwDSIgXcQFaLmjMx4QX6EaSPZQB9BWYQ_YiFypWK4ebFs4GKCC04Ou0ATJxRjG_uc8x9DUdbn04zHPBuL_zE75aJHJ8_zGpoSP6QqnbCCfjdzxiW2YmR208u3BpvKf-DUyP3mjP_353alHeCGelPSsipO13Q_FsAYIan-aNfPn0KIX7oUSLWaxuPdg6iE0hNF35_f8vmWEJa59zLQ_idbV4AVMhaVBXH71P-tQ'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': 'b394446b-d92e-4f20-a162-b9c50491d4c4', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': 'b11e1760-8c44-40e5-b0a4-cc55c2aa4392',
                                'origin_jti': '8eadab2d-ba64-4e3e-9f57-aba5f18c998a',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-dev.onexbots.com', 'domainPrefix': 'api-dev',
                                    'http': {'method': 'POST', 'path': '/setup_default/zalo_oa', 'protocol': 'HTTP/1.1',
                                             'sourceIp': '**************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                    'requestId': 'L7_HAgpJSQ0EM2w=',
                                    'routeKey': 'POST /setup_default/{connection_type}', 'stage': '$default',
                                    'time': '10/Jun/2025:07:30:01 +0000', 'timeEpoch': 1749540601269},
                 'pathParameters': {'connection_type': 'zalo_oa'}, 'isBase64Encoded': False}

        context = self.create_lambda_context()

        response = get_default_connections(event, context)

    def test_setup_default_connection(self):
        event = {'version': '2.0', 'routeKey': 'POST /setup_default/{connection_type}',
                 'rawPath': '/setup_default/zalo_oa',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AIsCI9b8cwssGTBJaKRt_6WkPmoqOmbIFXd5vyoOipHiwCMMKheoPFyTPz_H0_A7hTn2DxoxsZp8AHbfa6WyQstr3EN6iMyXwUK-buq6zBNQSE-_UwDSIgXcQFaLmjMx4QX6EaSPZQB9BWYQ_YiFypWK4ebFs4GKCC04Ou0ATJxRjG_uc8x9DUdbn04zHPBuL_zE75aJHJ8_zGpoSP6QqnbCCfjdzxiW2YmR208u3BpvKf-DUyP3mjP_353alHeCGelPSsipO13Q_FsAYIan-aNfPn0KIX7oUSLWaxuPdg6iE0hNF35_f8vmWEJa59zLQ_idbV4AVMhaVBXH71P-tQ',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EDvPSMiDc61XxkllA5ltjnZ9XF0yiL85wz6Gvj8UxV-FMcOhIdd_JIUortD8Tmbl18_Mo1Xzfm7kU05SEM1LahSDoMtfRyadgKQ_Xpnhqd8rLkaj-jmxONwQCTCeRSDx5JV3ueOXxfAifVjw-hBTjQfHCwfJH6PhO3uPYRpPdiZvjEEoWxHHFRLjWAYP80C3ldjm-tQz8G-B3JZAIGULknB2eMtuG5plhFNsk353gClN1ehCLhNNCl-Xy9plRkROlUT3Vc6wSADssLYeyG2X4-cgPFMXwyOZ2xPcRFYNToy-_EHvUa6_AYx70lr_lWj_N2MWCrTzx7Jd0sdhXE0Fvg',
                             'content-length': '0', 'host': 'api-dev.onexbots.com', 'origin': 'http://localhost:3000',
                             'priority': 'u=1, i', 'referer': 'http://localhost:3000/',
                             'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                             'sec-fetch-mode': 'cors', 'sec-fetch-site': 'cross-site', 'sec-gpc': '1',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                             'x-amzn-trace-id': 'Root=1-6847def9-6b4ab2f5436830a30a72ebf4',
                             'x-forwarded-for': '**************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AIsCI9b8cwssGTBJaKRt_6WkPmoqOmbIFXd5vyoOipHiwCMMKheoPFyTPz_H0_A7hTn2DxoxsZp8AHbfa6WyQstr3EN6iMyXwUK-buq6zBNQSE-_UwDSIgXcQFaLmjMx4QX6EaSPZQB9BWYQ_YiFypWK4ebFs4GKCC04Ou0ATJxRjG_uc8x9DUdbn04zHPBuL_zE75aJHJ8_zGpoSP6QqnbCCfjdzxiW2YmR208u3BpvKf-DUyP3mjP_353alHeCGelPSsipO13Q_FsAYIan-aNfPn0KIX7oUSLWaxuPdg6iE0hNF35_f8vmWEJa59zLQ_idbV4AVMhaVBXH71P-tQ'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': 'b394446b-d92e-4f20-a162-b9c50491d4c4', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': 'b11e1760-8c44-40e5-b0a4-cc55c2aa4392',
                                'origin_jti': '8eadab2d-ba64-4e3e-9f57-aba5f18c998a',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-dev.onexbots.com', 'domainPrefix': 'api-dev',
                                    'http': {'method': 'POST', 'path': '/setup_default/zalo_oa', 'protocol': 'HTTP/1.1',
                                             'sourceIp': '**************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                    'requestId': 'L7_HAgpJSQ0EM2w=',
                                    'routeKey': 'POST /setup_default/{connection_type}', 'stage': '$default',
                                    'time': '10/Jun/2025:07:30:01 +0000', 'timeEpoch': 1749540601269},
                 'pathParameters': {'connection_type': 'zalo_oa'}, 'isBase64Encoded': False}

        context = self.create_lambda_context()
        response = setup_default_connection(event, context)


if __name__ == '__main__':
    unittest.main()
