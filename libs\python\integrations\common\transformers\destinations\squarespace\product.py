from typing import Dict, Any, List
import logging

from ...base import DestinationTransformer
from integrations.channels.action_types import ActionGroup
from models.product.product import ProductAttributesSchema

logger = logging.getLogger(__name__)


class SquarespaceProductTransformer(DestinationTransformer):
    channel_name = 'squarespace'
    object_type = 'product'

    def _map_object_type_to_action_type(self) -> str:
        return 'sync_product'

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)

            # Transform variants first
            variants = self._transform_variants(data.get('variants', []))

            # Transform to Squarespace product format
            transformed_data = {
                "type": "PHYSICAL",
                "storePageId": self.dynamic_settings.get('store_page_id'),
                "name": data['name'],
                "description": data.get('description', ''),
                "isVisible": data.get('publish', True),
                "variantAttributes": self._get_variant_attributes(data.get('options', [])),
                "variants": variants,
                "tags": self._transform_tags(data.get('tags', '')),
                "images": self._transform_images(data.get('images', [])),
            }

            # Output validation
            self.validate_output(transformed_data)
            return transformed_data
        except Exception as e:
            logger.error(f"Error transforming to Squarespace product: {str(e)}")
            raise

    def _transform_variants(self, variants: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        def round_price_by_currency(value, currency):
            currency_decimals = {
                'USD': 2, 'EUR': 2, 'AUD': 2, 'CAD': 2, 'GBP': 2, 'SGD': 2, 'NZD': 2,
                'JPY': 0, 'KRW': 0, 'VND': 0, 'IDR': 0, 'THB': 2, 'MYR': 2
            }
            decimals = currency_decimals.get(currency, 2)
            return f"{round(float(value), decimals):.{decimals}f}"

        squarespace_variants = []

        for variant in variants:
            # Get base price from pricing data
            base_price = next(
                (price['price'] for price in variant.get('prices', [])
                 if price.get('price_group', {}).get('id') == 'RETAIL'),
                0.0
            )
            inventories = variant.get('inventories', {}).get('inventories', [])
            quantity = inventories[0].get('available', 0) if inventories else 0
            quantity = quantity if quantity > 0 else 0
            currency = 'AUD'  # default
            # Nếu có currency trong price_group hoặc price, lấy ra
            for price in variant.get('prices', []):
                if price.get('price_group', {}).get('id') == 'RETAIL' and price.get('currency'):
                    currency = price['currency']
                    break
            # Làm tròn giá trị basePrice.value
            base_price_str = round_price_by_currency(base_price, currency)
            variant_data = {
                "sku": variant.get('sku', ''),
                "pricing": {
                    "basePrice": {
                        "currency": currency,
                        "value": base_price_str
                    },
                    "onSale": False,
                    "salePrice": {
                        "currency": currency,
                        "value": round_price_by_currency(0, currency)
                    }
                },
                "stock": {
                    "quantity": quantity,
                    "unlimited": False
                },
                "attributes": self._build_variant_attributes(variant),
                "shippingMeasurements": {
                    "weight": {
                        "unit": "KILOGRAM",
                        "value": variant['measurements']['weight_value']
                    }
                }
            }
            squarespace_variants.append(variant_data)

        return squarespace_variants

    def _build_variant_attributes(self, variant: Dict[str, Any]) -> Dict[str, str]:
        # Changed to return Dict instead of List
        attributes = {}
        for i in range(1, 4):
            option_value = variant.get(f'option{i}')
            option_title = variant.get(f'optionTitle{i}')

            if option_value and option_title:
                attributes[option_title] = option_value

        return attributes

    def _get_variant_attributes(self, options: List[Dict[str, Any]]) -> List[str]:
        # Changed to return List[str] instead of List[Dict]
        return [option['name'] for option in options if option.get('name')]

    def _transform_tags(self, tags: str) -> List[str]:
        if not tags:
            return []
        return [tag.strip() for tag in tags.split(',') if tag.strip()]

    def _transform_categories(self, category: Dict[str, Any]) -> List[Dict[str, str]]:
        if not category or not category.get('name'):
            return []

        # Get category mapping from connection settings
        category_mapping = self.connection_settings.get('dynamic_settings', {}).get('category_mapping', {})

        # Try to get mapped category ID, or use default
        category_id = category_mapping.get(category['name'],
                                           self.connection_settings.get('default_category_id'))

        if category_id:
            return [{"id": category_id}]
        return []

    def _transform_images(self, images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        sorted_images = [image for image in images if image.get('thumbnail')] + [image for image in images if
                                                                                 not image.get('thumbnail', False)]
        return [{
            "url": image['src'],
            "filename": image['name'],
            "caption": "",  # Squarespace allows captions but we don't have them in master format
            "altText": image.get('name', '').split('.')[0]  # Use filename without extension as alt text
        } for image in sorted_images]

    def _generate_url(self, name: str) -> str:
        """Generate URL-friendly slug from product name."""
        return name.lower().replace(' ', '-').replace('/', '-')

    def validate_input(self, data: Dict[str, Any]):
        required_fields = ['name', 'variants']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f"Missing required fields in master format: {', '.join(missing_fields)}")

    def validate_output(self, data: Dict[str, Any]):
        required_fields = ['name', 'variants', 'type']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f"Missing required fields in Squarespace format: {', '.join(missing_fields)}")

    @property
    def input_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())

    @property
    def output_schema(self):
        return {
            "type": "object",
            "properties": {
                "type": {"type": "string", "enum": ["PHYSICAL", "DIGITAL", "SERVICE"]},
                "storePageId": {"type": "string"},
                "name": {"type": "string"},
                "url": {"type": "string"},
                "description": {
                    "type": "object",
                    "properties": {
                        "html": {"type": "string"}
                    }
                },
                "excerpt": {"type": "string"},
                "isVisible": {"type": "boolean"},
                "variantAttributes": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "variants": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "string"},
                            "sku": {"type": "string"},
                            "pricing": {
                                "type": "object",
                                "properties": {
                                    "basePrice": {
                                        "type": "object",
                                        "properties": {
                                            "currency": {"type": "string"},
                                            "value": {"type": "string"}
                                        }
                                    }
                                }
                            },
                            "stock": {
                                "type": "object",
                                "properties": {
                                    "quantity": {"type": "integer"},
                                    "unlimited": {"type": "boolean"}
                                }
                            },
                            "attributes": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "key": {"type": "string"},
                                        "value": {"type": "string"}
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "required": ["type", "name", "variants"]
        }

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.product
