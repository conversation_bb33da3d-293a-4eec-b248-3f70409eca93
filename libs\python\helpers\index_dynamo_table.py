import os

from nolicore.adapters.db.aws.elastic_search import ElasticSearch

from models.accounts.permission import PermissionModel
from models.accounts.roles import RoleModel
from models.actor.customer import CustomerModel
from models.actor.customer_group import CustomerGroupModel
from models.actor.supplier import SupplierModel
from models.blog.blog import BlogModel
from models.blog.blog_category import BlogCategoryModel
from models.blog.comment import CommentModel
from models.filter.filter import FilterModel
from models.finance.account import AccountModel
from models.finance.payment_method import PaymentMethodModel
from models.finance.transaction import TransactionModel
from models.finance.voucher import TransactionVoucherModel
from models.history.history import HistoryModel
from models.imports.import_record import ImportRecordModel
from models.imports.imports import ImportModel
from models.integration.connection import ConnectionModel
from models.integration.destination_data import DestinationDataModel
from models.integration.fetch_event import FetchEventModel
from models.inventory.inventory_item import InventoryItemModel
from models.inventory.inventory_transaction import InventoryTransactionModel
from models.inventory.location import LocationModel
from models.inventory.stock_adjustment import StockAdjustmentModel
from models.inventory.stock_relocate import StockRelocateModel
from models.logistic.shipping_provider import ShippingProviderModel
from models.loyalty.loyalty_program import LoyaltyProgramModel
from models.loyalty.reward_program import RewardProgramModel
from models.notification.notification import NotificationModel
from models.notification.pos_socket import PosSocketModel
from models.notification.screen_socket import ScreenSocketModel
from models.order.order import OrderModel
from models.order.order_return import ReturnModel
from models.order.package import PackageModel
from models.pos.draft_order import DraftOrderModel
from models.pos.shift_pay_line import ShiftPayLineItemModel
from models.price.price_group import PriceGroupModel
from models.product.brand import BrandModel
from models.product.category import CategoryModel
from models.product.product import ProductModel
from models.product.unit import UnitModel
from models.product.variant import VariantModel
from models.promotion.discount import DiscountModel
from models.promotion.used_voucher import UsedVoucherModel
from models.promotion.voucher import VoucherModel
from models.promotion.voucher_code import VoucherCodeModel
from models.purchase_order.purchase_order import PurchaseOrderModel
from models.sale_channel.sale_channel import SaleChannelModel
from models.sale_channel.store import StoreModel
from models.pos.shift import ShiftModel
from models.pos.terminal import TerminalModel
from models.integration.sync_record import SyncRecordModel
from models.vn_public.district import DistrictModel
from models.vn_public.province import ProvinceModel
from models.vn_public.ward import WardModel
from models.zalo_app.wishlist import WishlistModel
from models.bots.virtual_staff import VirtualStaffModel
from models.bots.department import DepartmentModel
from models.bots.task import TaskModel
from models.bots.task_execution import TaskExecutionModel
from models.bots.conversation import ConversationModel
from models.bots.message import MessageModel
from models.bots.knowledge import KnowledgeModel
from models.bots.feedback import FeedbackModel

table_mappings = {
    'location': LocationModel,
    'account': AccountModel,
    'order': OrderModel,
    'returnOrder': ReturnModel,
    'product': ProductModel,
    'imports': ImportModel,
    'importRecord': ImportRecordModel,
    'customer': CustomerModel,
    'package': PackageModel,
    'purchaseOrder': PurchaseOrderModel,
    'variant': VariantModel,
    'priceGroup': PriceGroupModel,
    'brand': BrandModel,
    'category': CategoryModel,
    'filter': FilterModel,
    'voucher': VoucherModel,
    'transaction': TransactionModel,
    'transactionVoucher': TransactionVoucherModel,
    'paymentMethod': PaymentMethodModel,
    'saleChannel': SaleChannelModel,
    'store': StoreModel,
    'shippingProvider': ShippingProviderModel,
    'supplier': SupplierModel,
    'discount': DiscountModel,
    'wishlist': WishlistModel,
    'history': HistoryModel,
    'voucherCode': VoucherCodeModel,
    'usedVoucher': UsedVoucherModel,
    'customerGroup': CustomerGroupModel,
    "role": RoleModel,
    'permission': PermissionModel,
    'blog': BlogModel,
    'blogCategory': BlogCategoryModel,
    'comment': CommentModel,
    'province': ProvinceModel,
    'district': DistrictModel,
    'ward': WardModel,
    'posSocket': PosSocketModel,
    'screenSocket': ScreenSocketModel,
    'shift': ShiftModel,
    'shiftPayLineItem': ShiftPayLineItemModel,
    'terminal': TerminalModel,
    'draftOrder': DraftOrderModel,
    'stockAdjustment': StockAdjustmentModel,
    'stockRelocate': StockRelocateModel,
    'inventoryItem': InventoryItemModel,
    'inventoryTransaction': InventoryTransactionModel,
    'unit': UnitModel,
    'loyaltyProgram': LoyaltyProgramModel,
    'rewardProgram': RewardProgramModel,
    'connection': ConnectionModel,
    'notification': NotificationModel,
    'syncRecord': SyncRecordModel,
    'fetchEvent': FetchEventModel,
    'destinationData': DestinationDataModel,
    'virtualStaff': VirtualStaffModel,
    'department': DepartmentModel,
    'task': TaskModel,
    'taskExecution': TaskExecutionModel,
    'conversation': ConversationModel,
    'message': MessageModel,
    'knowledge': KnowledgeModel,
    'feedback': FeedbackModel,
}

INDEXES = {}
SERVICE = os.getenv('SERVICE')
ENV = os.getenv('ENV')


def create_index_not_existed(table_name) -> ElasticSearch:
    index_name = f'{table_name}{SERVICE}prod' if ENV == 'prod' else f'{table_name}{SERVICE}'
    if table_name not in INDEXES:
        INDEXES[index_name] = ElasticSearch(index_name)
    return INDEXES[index_name]
