import os
from typing import List

import pendulum

from helpers.finance import sync_payment
from helpers.lambda_client import lambda_client_invoke_async
from models.common.status import PaymentStatus, update_payment_status
from models.finance.transaction import TransactionSchema, Transaction
from models.finance.voucher import ReservedVoucher
from models.purchase_order.purchase_order import PurchaseOrderModel, PurchaseOrderExternalIdIndex

ENV = os.getenv("ENV")


def sync_purchase_order_payment(purchase_order: PurchaseOrderModel, payments: List[dict], user):
    staff_id = purchase_order.attributes.staff_id
    company_id = purchase_order.attributes.company_id
    total = purchase_order.attributes.total
    current_payments = purchase_order.attributes.payments
    reference = purchase_order.attributes.id
    effective_date = pendulum.now().to_iso8601_string()
    note = purchase_order.attributes.note
    voucher_id = ReservedVoucher.PURCHASE_ORDER_VOUCHER.value
    transactions = sync_payment(str(company_id), user, total, current_payments, payments, voucher_id,
                                reference=reference, note=note, effective_date=effective_date)
    payments_data = {
        'payments': [
                        TransactionSchema(Transaction).dump(t) for t in current_payments or []
                    ] + [
                        {k: v for k, v in t.items() if k not in {
                            'created_at', 'company_id', 'updated_at', 'user'
                        }} for t in transactions
                    ]
    }
    update_payment_status(staff_id, purchase_order, PaymentStatus.PAID, extras=payments_data)
    return transactions


def invoke_import_purchase_order(request_id, company_id, start=0):
    payload = {
        'request_id': request_id,
        'company_id': company_id,
        'start': start
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-po-service-{ENV}-importPurchaseOrderHandle')


def invoke_format_purchase_order(request_id, user_id, user_name, company_id, staff_id, location, supplier):
    payload = {
        'request_id': request_id,
        'user_id': user_id,
        'user_name': user_name,
        'company_id': company_id,
        'staff_id': staff_id,
        'location': location,
        'supplier': supplier,
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-po-service-{ENV}-formatPurchaseOrderHandle')


def get_purchase_order_obj(company_id, purchase_order, purchase_order_id=None):
    purchase_order_id = purchase_order_id or purchase_order.get('id')
    external_id = purchase_order.get('external_id')
    order_obj = None
    if purchase_order_id:
        order_obj = PurchaseOrderModel.by_key({
            PurchaseOrderModel.key__id: purchase_order_id,
            'company_id': company_id
        })

    if order_obj is None and external_id is not None:
        try:
            order_obj = PurchaseOrderModel(PurchaseOrderExternalIdIndex.list({
                PurchaseOrderExternalIdIndex.key__id: external_id,
                'company_id': company_id
            }, limit=10)['Items'][0])
        except IndexError:
            pass

    return order_obj


def format_data(rows):
    new_data = []
    for row in rows.values():
        if isinstance(row.get('code'), str):
            measurements = {
                'length_value': row.get('length', 0),
                'height_unit': 'cm',
                'weight_unit': 'g',
                'weight_value': row.get('shippingWeight', 0),
                'width_unit': 'cm',
                'height_value': row.get('height', 0),
                'width_value': row.get('width', 0),
                'length_unit': 'cm',
            }
            variants = []
            for item in row.get('variants', []):
                v_measurements = {
                    'length_value': item.get('length', 0),
                    'height_unit': 'cm',
                    'weight_unit': 'g',
                    'weight_value': item.get('shippingWeight', 0),
                    'width_unit': 'cm',
                    'height_value': item.get('height', 0),
                    'width_value': item.get('width', 0),
                    'length_unit': 'cm',
                }
                option1 = (list(item['attributes'][0].values())[0]['name'] if item.get('attributes') and len(
                    item['attributes']) > 0 and item['attributes'][0] else None)
                option2 = (list(item['attributes'][1].values())[0]['name'] if item.get('attributes') and len(
                    item['attributes']) > 1 and item['attributes'][1] else None)
                option3 = (list(item['attributes'][2].values())[0]['name'] if item.get('attributes') and len(
                    item['attributes']) > 2 and item['attributes'][2] else None)

                template_variant = {
                    'name': item['name'],
                    'brand': None,
                    'category': None,
                    'images': [{'url': item['image']}],
                    'option1': option1,
                    'option2': option2,
                    'option3': option3,
                    'measurements': v_measurements,
                    'inventories': None,
                    'prices': [
                        {
                            'price_group': {
                                'name': 'Giá nhập',
                                'id': 'c83f8392-9414-42d6-b303-d30620ef566a',
                            },
                            'price': item.get('importPrice', 0),
                        },
                        {
                            'price_group': {
                                'name': 'Giá bán lẻ',
                                'id': '974b6457-bff6-4479-9350-da4fdd863b20',
                            },
                            'price': item.get('price', 0),
                        },
                    ],
                    'sku': item['code'],
                    'barcode': item['barcode'],
                }
                variants.append(template_variant)
            options = []
            for item in row.get('variants', []):
                option1 = (
                    list(item.get('attributes', [{}])[0].values())[0] if item.get('attributes') and item['attributes'][
                        0] else {})

                check = next((subItem for subItem in options if subItem['name'] == option1.get('attributeName')), None)
                if check:
                    if option1['name'] not in check['values']:
                        check['values'].append(option1['name'])
                else:
                    options.append({'name': option1.get('attributeName', ''), 'values': [option1.get('name', '')]})
            template_product = {
                'name': row['name'],
                'brand': None,
                'category': None,
                'description': '',
                'images': [{'url': row['image']}],
                'options': options,
                'measurements': measurements,
                'inventories': None,
                'prices': [
                    {
                        'price_group': {
                            'name': 'Giá nhập',
                            'id': 'c83f8392-9414-42d6-b303-d30620ef566a',
                        },
                        'price': row.get('importPrice', 0),
                    },
                    {
                        'price_group': {
                            'name': 'Giá bán lẻ',
                            'id': '974b6457-bff6-4479-9350-da4fdd863b20',
                        },
                        'price': row.get('price', 0),
                    },
                ],
                'publish': row['status'] == 'Active',
                'shortDescription': '',
                'sku': row['code'],
                'tags': '',
                'barcode': row['barcode'],
                'variants': variants,
            }
            if variants:
                del template_product['prices']
                del template_product['barcode']
                del template_product['inventories']
            new_data.append(template_product)
    return new_data
