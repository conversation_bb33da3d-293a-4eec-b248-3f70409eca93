import pendulum
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import ApiExp, NotFoundRequest, BadRequest, UnauthorizedExp, ServerExp
from nolicore.utils.utils import logger

from integrations.channels.action_types import ACTION_GROUP_MAPPING
from integrations.channels.channel_factory import ChannelFactory
from integrations.channels.channel_library import AbstractChannel
from integrations.channels.connection_types import ConnectionStatus
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.event import FetchEvent, EventSource
from integrations.common.queue_helper import enqueue_fetch_event
from models.integration.connection import ConnectionModel
from models.integration.webhook import WebhookModel


def verify_and_process_webhook(connection: ConnectionModel, payload: dict, raw_payload: str, headers: dict):
    channel_instance = connection.get_channel()
    if not channel_instance.verify_webhook_signature(raw_payload, headers, connection.attributes.settings):
        raise UnauthorizedExp("Invalid webhook signature")

    event_type = channel_instance.extract_event_type(payload, headers)
    process_webhook(channel_instance, event_type, payload, connection)


def process_webhook(channel: AbstractChannel, event_type: str, payload: dict, connection: ConnectionModel):
    logger.info(f"Processing webhook for event type: {event_type}")
    process_webhook_event = channel.process_webhook_event(event_type, payload)
    if process_webhook_event:
        return
    # Use the channel's extract_object_details method to get the FetchEvent
    fetch_event: FetchEvent = channel.extract_object_details(payload, event_type=event_type)
    if fetch_event is None:
        return

    if not should_fetch_webhook(fetch_event, connection):
        return

    # Update the connection_id and event_time
    fetch_event.connection_id = channel.connection.id
    fetch_event.event_time = pendulum.now().to_iso8601_string()
    fetch_event.event_source = EventSource.webhook

    # Add any additional information if needed
    fetch_event.object_data['event_type'] = event_type

    try:
        enqueue_fetch_event(fetch_event, connection.attributes.company_id)
        logger.info(f"FetchEvent for {fetch_event.action_group} "
                    f"{'batch' if fetch_event.is_batch else fetch_event.object_id} enqueued successfully")
    except Exception as e:
        logger.error(f"Failed to enqueue FetchEvent: {str(e)}")
        raise

    # You can add any additional webhook processing logic here if needed


def should_fetch_webhook(fetch_event: FetchEvent, connection: ConnectionModel) -> bool:
    if connection.attributes.status == ConnectionStatus.INACTIVE:
        return False
    action_type = fetch_event.action_type
    action_group = ACTION_GROUP_MAPPING.get(action_type)
    if not action_group:
        return False
    action_settings = connection.get_fetch_action_settings(action_group, action_type)
    if not action_settings or not action_settings.enabled:
        return False
    return True


@as_api()
def webhook_handler_by_channel_name(api_gateway_request: ApiGatewayRequest):
    try:
        channel_name = api_gateway_request.path_parameters.get('channel_name')
        if not channel_name:
            raise BadRequest("Missing channel_name parameter")

        channel_class = ChannelFactory.get_channel(channel_name)
        if not channel_class:
            raise NotFoundRequest(f"Unsupported channel: {channel_name}")

        payload = api_gateway_request.body
        headers = api_gateway_request.headers
        raw_payload = api_gateway_request.raw_body
        query_string_params = api_gateway_request.query_string_parameters

        # Handle test event before extracting merchant ID
        try:
            test_event_result = channel_class.handle_test_event(payload, headers, query_string_params)
            if test_event_result:
                logger.info(f"Test event processed: {test_event_result}")
                return test_event_result
        except ValueError:
            # Not a test event, proceed with normal processing
            pass

        merchant_id = channel_class.extract_merchant_id(payload)
        if not merchant_id:
            raise BadRequest("Unable to extract merchant ID from payload")

        webhook = WebhookModel.get_by_id(f"{channel_name}-{merchant_id}")
        if not webhook:
            raise NotFoundRequest("Webhook not found")

        connection = get_connection_by_id(webhook.attributes.connection_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        if connection.attributes.status != ConnectionStatus.ACTIVE:
            return channel_class.webhook_response_message(False, "Connection inactive")

        verify_and_process_webhook(connection, payload, raw_payload, api_gateway_request.headers)
        return channel_class.webhook_response_message(True, "Webhook processed successfully")

    except (BadRequest, NotFoundRequest, ApiExp) as e:
        logger.error(f"Error in webhook_handler_by_channel_name: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in webhook_handler_by_channel_name: {str(e)}")
        raise ServerExp("An unexpected error occurred while processing the webhook")


@as_api()
def webhook_handler_by_connection_id(api_gateway_request: ApiGatewayRequest):
    try:
        connection_id = api_gateway_request.path_parameters.get('connection_id')
        logger.info(f"connection_id {connection_id}")
        if not connection_id:
            raise BadRequest("Missing connection_id parameter")

        connection = get_connection_by_id(connection_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        channel_instance = connection.get_channel()
        headers = api_gateway_request.headers
        payload = api_gateway_request.body
        raw_payload = api_gateway_request.raw_body
        query_string_params = api_gateway_request.query_string_parameters

        # Handle test event
        try:
            test_event_result = channel_instance.handle_test_event(payload, headers, query_string_params)
            if test_event_result:
                return test_event_result
        except ValueError:
            # Not a test event, proceed with normal processing
            pass

        if connection.attributes.status != ConnectionStatus.ACTIVE:
            return {"status": "false", "message": "Connection inactive"}

        verify_and_process_webhook(connection, payload, raw_payload, api_gateway_request.headers)

        return {"status": "success", "message": "Webhook processed successfully"}

    except (BadRequest, NotFoundRequest, ApiExp) as e:
        logger.error(f"Error in webhook_handler_by_connection_id: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in webhook_handler_by_connection_id: {str(e)}")
        raise ServerExp("An unexpected error occurred while processing the webhook")
