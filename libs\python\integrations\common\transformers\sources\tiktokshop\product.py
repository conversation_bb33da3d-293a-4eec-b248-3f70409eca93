import logging
from typing import Dict, Any, List, Optional

import pendulum

from integrations.channels.action_types import ActionGroup
from integrations.common.transformers.base import SourceTransformer
from models.product.product import ProductAttributesSchema
from .inventory import TikTokShopInventoryTransformer

logger = logging.getLogger(__name__)


class TikTokShopProductTransformer(SourceTransformer):
    def __init__(self, connection_settings: Dict[str, Any]):
        super().__init__(connection_settings)
        self.inventory_transformer = TikTokShopInventoryTransformer(connection_settings)

    channel_name = 'tiktok_shop'
    object_type = 'product'

    def _map_object_type_to_action_type(self) -> str:
        return 'get_product'

    def transform_measurements(self, data: Dict[str, Any]) -> Dict[str, Any]:
        unit_mapping = {
            'KILOGRAM': 'kg',
            'GRAM': 'g',
            'CENTIMETER': 'cm',
            'MILLIMETER': 'mm',
        }
        return {
            'weight_value': float(data.get('package_weight', {}).get('value', 0)),
            'weight_unit': unit_mapping.get(data.get('package_weight', {}).get('unit', 'KILOGRAM').lower(), 'g'),
            'width_value': float(data.get('package_dimensions', {}).get('width', 0)),
            'width_unit': unit_mapping.get(data.get('package_dimensions', {}).get('unit', 'CENTIMETER').lower(), 'cm'),
            'length_value': float(data.get('package_dimensions', {}).get('length', 0)),
            'length_unit': unit_mapping.get(data.get('package_dimensions', {}).get('unit', 'CENTIMETER').lower(), 'cm'),
            'height_value': float(data.get('package_dimensions', {}).get('height', 0)),
            'height_unit': unit_mapping.get(data.get('package_dimensions', {}).get('unit', 'CENTIMETER').lower(), 'cm'),
        }

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Chuyển đổi dữ liệu sản phẩm từ TikTok Shop API sang định dạng master
        """
        try:
            # Validate input
            self.validate_input(data)
            brand_mapping = self.connection_settings.get('dynamic_settings', {}).get('brand')
            category_mapping = self.connection_settings.get('dynamic_settings', {}).get('category')

            # Get basic product information
            product_id = data.get('id', '')
            product_title = data.get('title', '')
            product_desc = data.get('description', '')
            product_status = data.get('status', '') == 'ACTIVATE'
            product_create_time = data.get('create_time', 0)
            product_update_time = data.get('update_time', 0)

            # Get brand information
            brand_name = None
            if data.get('brand') and data['brand'].get('name'):
                brand_name = data['brand']['name']
            brand = {'name': brand_name or brand_mapping} if brand_name or brand_mapping else None

            # Get category information
            category_name = None
            if data.get('category_chains') and len(data['category_chains']) > 0:
                # Get leaf category (last in chain)
                for cat in data['category_chains']:
                    if cat.get('is_leaf', False):
                        category_name = cat.get('local_name', '')
                        break
                # If no leaf category found, get last category
                if not category_name and data['category_chains']:
                    category_name = data['category_chains'][-1].get('local_name', '')

            category = {'name': category_name or category_mapping} if category_name or category_mapping else None

            # Process images
            images = []
            if data.get('main_images'):
                for idx, img in enumerate(data['main_images']):
                    if img.get('urls') and len(img['urls']) > 0:
                        images.append(
                            {
                                'src': img['urls'][0],
                                'name': f"main_image_{idx}"
                            }
                        )

            # Build options from sales_attributes of all SKUs
            options_data = {}
            for sku in data.get('skus', []):
                if sku.get('sales_attributes'):
                    for attr in sku['sales_attributes']:
                        attr_name = attr.get('name')
                        attr_value = attr.get('value_name')
                        if attr_name and attr_value:
                            if attr_name not in options_data:
                                options_data[attr_name] = set()
                            options_data[attr_name].add(attr_value)

            # Convert options_data to list of options
            options = []
            for name, values in options_data.items():
                options.append({
                    'name': name,
                    'values': list(values)
                })

            # Process variants (SKUs)
            variants = []
            for sku in data.get('skus', []):
                # Get price information
                price = 0
                if sku.get('price') and sku['price'].get('sale_price'):
                    price = float(sku['price']['sale_price'])

                # Get barcode information
                barcode = ''
                if sku.get('identifier_code') and sku['identifier_code'].get('code'):
                    barcode = sku['identifier_code']['code']

                # Get options information from sales_attributes
                option1 = None
                option2 = None
                option3 = None
                option_title1 = None
                option_title2 = None
                option_title3 = None

                if sku.get('sales_attributes'):
                    for idx, attr in enumerate(sku['sales_attributes']):
                        if idx == 0:
                            option1 = attr.get('value_name')
                            option_title1 = attr.get('name')
                        elif idx == 1:
                            option2 = attr.get('value_name')
                            option_title2 = attr.get('name')
                        elif idx == 2:
                            option3 = attr.get('value_name')
                            option_title3 = attr.get('name')

                # Create variant name
                variant_name = sku.get('seller_sku', '')
                if option1 and option2 and option3:
                    variant_name = f"{option1} / {option2} / {option3}"
                elif option1 and option2:
                    variant_name = f"{option1} / {option2}"
                elif option1:
                    variant_name = option1

                # Get size and weight information
                measurements = self.transform_measurements(data)

                # Create variant
                variant = {
                    'name': variant_name,
                    'sku': sku.get('seller_sku', ''),
                    'barcode': barcode,
                    'prices': [
                        {
                            'price': price,
                            'price_group': {'id': 'RETAILS'}
                        }
                    ],
                    'measurements': self.transform_measurements(data),
                    # 'inventories': [self.inventory_transformer.transform(sku) for sku in data['skus']],
                    'option1': option1,
                    'option2': option2,
                    'option3': option3,
                    'optionTitle1': option_title1,
                    'optionTitle2': option_title2,
                    'optionTitle3': option_title3,
                    'images': images,  # Using product images for all variants
                }
                variants.append(variant)

            # If no variants, create a default variant
            if not variants:
                variants.append({
                    'name': product_title,
                    'sku': str(product_id),
                    'barcode': '',
                    'prices': [],
                    'measurements': self.transform_measurements(data),
                    # 'inventories': [self.inventory_transformer.transform(sku) for sku in data['skus']],
                    'images': images,
                })

            # Build final data
            transformed_data = {
                'name': product_title,
                'sku': variants[0].get('sku') if variants else str(product_id),
                'barcode': variants[0].get('barcode') if variants else '',
                'publish': product_status,
                'description': product_desc,
                'shortDescription': product_desc[:100] if product_desc else None,
                'tags': '',
                'brand': brand,
                'category': category,
                'images': images,
                'variants': variants,
                'options': options,
                'measurements': self.transform_measurements(data) if len(variants) == 0 else None,
                'createdAt': pendulum.from_timestamp(product_create_time).isoformat() if product_create_time else None,
                'updatedAt': pendulum.from_timestamp(product_update_time).isoformat() if product_update_time else None,
            }

            # Validate output
            self.validate_output(transformed_data)

            return transformed_data
        except KeyError as e:
            logger.error(f"Missing required field in TikTok Shop product data: {str(e)}")
            raise ValueError(f"Missing required field in TikTok Shop product data: {str(e)}")
        except Exception as e:
            logger.error(f"Error transforming TikTok Shop product data: {str(e)}")
            raise

    def validate_input(self, data: Dict[str, Any]):
        # Validate input data is TikTok Shop API
        if not data.get('id') or not data.get('title'):
            raise ValueError("Missing required fields 'id' or 'title' in TikTok Shop product data")

    def validate_output(self, data: Dict[str, Any]):
        # Validate required fields in output data
        required_fields = ['name', 'variants']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f"Missing required fields in output data: {', '.join(missing_fields)}")

    @property
    def input_schema(self):
        # Schema for TikTok Shop API data
        return {
            'type': 'object',
            'properties': {
                'id': {'type': 'string'},
                'title': {'type': 'string'},
                'status': {'type': 'string'},
                'create_time': {'type': 'integer'},
                'update_time': {'type': 'integer'},
                'is_not_for_sale': {'type': 'boolean'},
                'sales_regions': {
                    'type': 'array',
                    'items': {'type': 'string'}
                },
                'recommended_categories': {
                    'type': 'array',
                    'items': {'type': 'object'}
                },
                'audit': {
                    'type': 'object',
                    'properties': {
                        'status': {'type': 'string'}
                    }
                },
                'brand': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'string'},
                        'name': {'type': 'string'}
                    }
                },
                'category_chains': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'id': {'type': 'string'},
                            'parent_id': {'type': 'string'},
                            'local_name': {'type': 'string'},
                            'is_leaf': {'type': 'boolean'}
                        }
                    }
                },
                'description': {'type': ['string', 'null']},
                'main_images': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'height': {'type': 'integer'},
                            'width': {'type': 'integer'},
                            'uri': {'type': 'string'},
                            'urls': {
                                'type': 'array',
                                'items': {'type': 'string'}
                            },
                            'thumb_urls': {
                                'type': 'array',
                                'items': {'type': 'string'}
                            }
                        }
                    }
                },
                'package_dimensions': {
                    'type': 'object',
                    'properties': {
                        'height': {'type': 'string'},
                        'width': {'type': 'string'},
                        'length': {'type': 'string'},
                        'unit': {'type': 'string'}
                    }
                },
                'package_weight': {
                    'type': 'object',
                    'properties': {
                        'value': {'type': 'string'},
                        'unit': {'type': 'string'}
                    }
                },
                'skus': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'id': {'type': 'string'},
                            'seller_sku': {'type': 'string'},
                            'price': {
                                'type': 'object',
                                'properties': {
                                    'currency': {'type': 'string'},
                                    'sale_price': {'type': 'string'},
                                    'tax_exclusive_price': {'type': 'string'}
                                }
                            },
                            'inventory': {
                                'type': 'array',
                                'items': {
                                    'type': 'object',
                                    'properties': {
                                        'warehouse_id': {'type': 'string'},
                                        'quantity': {'type': 'integer'}
                                    }
                                }
                            },
                            'sales_attributes': {
                                'type': 'array',
                                'items': {
                                    'type': 'object',
                                    'properties': {
                                        'id': {'type': 'string'},
                                        'name': {'type': 'string'},
                                        'value_id': {'type': 'string'},
                                        'value_name': {'type': 'string'},
                                        'sku_img': {
                                            'type': 'object',
                                            'properties': {
                                                'uri': {'type': 'string'},
                                                'urls': {'type': 'array', 'items': {'type': 'string'}}
                                            }
                                        }
                                    }
                                }
                            },
                            'identifier_code': {
                                'type': 'object',
                                'properties': {
                                    'code': {'type': 'string'},
                                    'type': {'type': 'string'}
                                }
                            },
                            'external_sku_id': {'type': 'string'},
                            'list_price': {
                                'type': 'object',
                                'properties': {
                                    'amount': {'type': 'string'},
                                    'currency': {'type': 'string'}
                                }
                            }
                        }
                    }
                }
            },
            'required': ['id', 'title', 'skus', 'status']
        }

    @property
    def output_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.product
