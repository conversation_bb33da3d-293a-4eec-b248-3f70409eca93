import json
import unittest

from tests.base import BaseTestCase
from integrations.common.connection_helper import get_connection_by_id
from nolicore.utils.exceptions import UnauthorizedExp, BadRequest

from connections.src.apis.setting import set_fetch_action_settings
from integrations.channels.action_types import ActionGroup, ActionType
from models.integration.connection import ConnectionModel

from connections.src.apis.connection import get_connection_types, oauth2_callback, test_connection
from integrations.channels.zalo_oa.zalo_oa_library import ZaloOALibrary


class TestZaloOAConnection(BaseTestCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.app_id = '1447241861612872028'
        cls.secret_key = 'j8Q876s512TQ3HWAnKMc'
        cls.redirect_uri = "https://api-dev.onexbots.com/connections/oauth2_callback"
        cls.zalo_oa_library = ZaloOALibrary(
            app_id=cls.app_id,
            secret_key=cls.secret_key,
        )
        if not all([cls.app_id, cls.secret_key]):
            raise ValueError("Zalo OA credentials not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"

    def create_lambda_event(self, method='GET', path='/', body=None, query_params=None, path_params=None, headers=None):
        event = {
            'httpMethod': method,
            'body': json.dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': headers,
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            },
            'isBase64Encoded': False
        }
        return event

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test_function'

        return LambdaContext()

    def test_get_connection_types(self):
        response = get_connection_types(self.create_lambda_event(), {})
        self.assertTrue(True)

    def test_get_authorization_url(self):
        connection_id = '7888a03a-3526-450d-94c6-67e00afce5f5'
        auth_url = self.zalo_oa_library.get_authorization_url(connection_id=connection_id)
        self.assertIsNotNone(auth_url)
        self.assertIn(self.app_id, auth_url)
        self.assertIn(connection_id, auth_url)

    def test_oauth2_callback(self):
        # Mock OAuth callback parameters
        callback_params = {
            'code': 'test_auth_code',
            'state': 'test_connection_id'
        }

        event = self.create_lambda_event(query_params=callback_params)
        context = self.create_lambda_context()
        response = oauth2_callback(event, context)
        self.assertTrue(True)

    def test_test_connection(self):
        # Create a mock connection record to test
        connection_data = {
            "connection_id": "7888a03a-3526-450d-94c6-67e00afce5f5",
            "connection_type": "zalo_oa",
            "settings": {
                "app_id": self.app_id,
                "secret_key": self.secret_key,
                "access_token": "test_access_token"
            }
        }

        event = self.create_lambda_event(body=connection_data)
        response = test_connection(event, {})

        # Assert the response
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertTrue(response_body['success'])
        self.assertTrue(response_body['is_connected'])

    def test_refresh_access_token(self):
        connection_id = "7314ccaa-9230-4590-99db-7590608417df"
        connection = get_connection_by_id(connection_id, self.company_id, False)
        channel = connection.get_channel()

        result = channel.refresh_oauth_token()

    def test_set_fetch_action_settings_success(self):
        connection_id = 'test_connection_id'
        event_body = {
            "actions": {
                "get_message": {
                    "enabled": True,
                    "rate_limit": 60,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "schedule": {
                        "type": "interval",
                        "value": "300"
                    },
                    "status": {
                        "last_run": None,
                        "next_run": None
                    }
                }
            }
        }
        event = self.create_lambda_event(
            body=event_body,
            path_params={'connection_id': connection_id}
        )
        context = self.create_lambda_context()

        response = set_fetch_action_settings(event, context)

        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Fetch action settings updated successfully')


if __name__ == '__main__':
    unittest.main()
