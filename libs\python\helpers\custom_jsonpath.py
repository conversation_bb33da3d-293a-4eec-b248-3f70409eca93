from jsonpath_ng import jsonpath, parse
from nolicore.utils.utils import logger


def get_value_from_jsonpath(data, jsonpath_expression):
    """
    Extract value from data using JSONPath expression.

    Args:
        data: The source data to search in
        jsonpath_expression: JSONPath expression to find the value

    Returns:
        The found value or None if not found
    """
    try:
        jsonpath_expr = parse(jsonpath_expression)
        matches = [match.value for match in jsonpath_expr.find(data)]
        return matches[0] if matches else None
    except Exception as e:
        logger.error(f"Error parsing JSONPath '{jsonpath_expression}': {str(e)}")
        return None


def set_value_using_jsonpath(data, jsonpath_expression, value):
    """
    Set value in data using JSONPath expression.

    Args:
        data: The target data to update
        jsonpath_expression: JSONPath expression where to set the value
        value: The value to set
    """
    try:
        jsonpath_expr = parse(jsonpath_expression)
        jsonpath_expr.update(data, value)
    except Exception as e:
        logger.error(f"Error updating JSONPath '{jsonpath_expression}': {str(e)}")
