from dataclasses import dataclass
from decimal import Decimal
from typing import Union

from elasticsearch import NotFoundError
from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.order_line import OrderLineItem
from models.common.stock_line import StockLineItem


@dataclass
class InventoryItem(Attributes):
    id: str = None
    location_id: str = None
    sku: str = None
    product_id: str = None
    on_hand: Decimal = 0
    available: Decimal = 0
    cost: Decimal = 0
    shipping: Decimal = 0
    packing: Decimal = 0
    returning: Decimal = 0
    incoming: Decimal = 0
    version: int = 0
    location_name: str = None
    variant_id: str = None
    min_value: Decimal = None
    max_value: Decimal = None
    last_transaction: str = None


@dataclass
class InventoryItemAttributes(InventoryItem, BasicAttributes):
    pass


class InventoryItemSchema(AttributesSchema):
    id = fields.Str(required=True)
    sku = fields.Str(required=True)
    product_id = fields.Str(required=True)
    location_id = fields.UUID(required=True)
    location_name = fields.Str(allow_none=True)
    variant_id = fields.UUID(allow_none=True)
    on_hand = fields.Decimal(allow_none=True)
    available = fields.Decimal(allow_none=True)
    cost = fields.Decimal(allow_none=True)
    shipping = fields.Decimal(allow_none=True)
    packing = fields.Decimal(allow_none=True)
    retuning = fields.Decimal(allow_none=True)
    version = fields.Int(allow_none=True)
    incoming = fields.Decimal(allow_none=True)
    min_value = fields.Decimal(allow_none=True)
    max_value = fields.Decimal(allow_none=True)
    last_transaction = fields.Str(allow_none=True)


class InventoryItemAttributesSchema(InventoryItemSchema, BasicAttributesSchema):
    pass


class InventoryItemModel(BasicModel):
    key__id = 'id'  # sku
    key_range_id = 'company_location'
    key__ranges = ['company_id', 'location_id']
    table_name = 'inventoryItem'
    version = True
    attributes: InventoryItemAttributes
    attributes_schema = InventoryItemAttributesSchema
    attributes_class = InventoryItemAttributes

    query_mapping = {
        'query': ['id', 'sku', 'location_name', 'product_id', 'location_id', 'variant_id'],
        'filter': {
            'id': 'query',
            'location_id': 'query',
            'sku': 'query',
            'product_id': 'query',
            'on_hand': 'number',
            'available': 'number',
            'cost': 'number',
            'shipping': 'number',
            'packing': 'number',
            'returning': 'number',
            'incoming': 'number',
            'variant_id': 'query',
            'min_value': 'number',
            'max_value': 'number',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }

    @classmethod
    def get_by_order_line_item(cls, company_id, location_id, line_item: Union[OrderLineItem, StockLineItem],
                               location_name=None):
        obj = InventoryItemModel.by_key({
            'id': line_item.sku,
            'company_id': company_id,
            'location_id': location_id
        })
        if obj is None:
            inventory_item_data = BasicAttributes.add_basic_attributes({
                'id': line_item.sku,
                'sku': line_item.sku,
                'product_id': str(line_item.product_id),
                'location_id': location_id,
                'location_name': location_name,
                'variant_id': line_item.variant_id
            }, company_id)
            obj = InventoryItemModel(inventory_item_data)

        return obj

    @classmethod
    def get_calculated_inventory_obj(cls, company_id, location_id,
                                     order_line: OrderLineItem):
        inventory_item_obj = cls.get_by_order_line_item(company_id, location_id, order_line)
        inventory_item_atts = inventory_item_obj.attributes
        inventory_item_atts.available = inventory_item_atts.on_hand - inventory_item_atts.shipping - inventory_item_atts.packing
        return inventory_item_obj

    @classmethod
    def list_inventory_by_sku(cls, company_id, sku):
        try:
            # fix after Inventory using elastic search
            total = InventoryItemModel.list({"id": sku, "company_id": company_id}, limit=1000).get('Count', 0)
            if total == 0:
                return []
            response = InventoryItemModel.list({"id": sku, "company_id": company_id}, limit=total).get('Items', [])
            return response
        except NotFoundError:
            return []


class InventoryItemProductIdIndex(InventoryItemModel):
    index_name = 'inventoryItemProductIdIndex'
    key__id = 'product_id'
    key__ranges = ['id']
