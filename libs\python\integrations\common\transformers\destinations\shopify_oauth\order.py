from enum import Enum
from typing import Dict, Any, List
import logging

from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.transformers.base import DestinationTransformer
from models.order.order import OrderAttributesSchema
from models.common.status import PaymentStatus, Status

logger = logging.getLogger(__name__)


class ShopifyOauthOrderTransformer(DestinationTransformer):
    channel_name = 'shopify_oauth'
    object_type = ActionGroup.order.value

    def __init__(self, connection_settings: Dict[str, Any]):
        super().__init__(connection_settings)

    def _map_object_type_to_action_type(self) -> str:
        return ActionType.get_order.value

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.order

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Validate input data
            if not data:
                raise ValueError("Input data cannot be empty")

            # Get dynamic_settings
            destination_location_id = self.dynamic_settings.get('locationId')

            # Get opti data
            payment_status = PaymentStatus(data.get('payment_status', 'UNPAID')).value
            order_status = Status(data.get('status', 'DRAFT')).value
            order_line_items = data.get('order_line_items', [])
            customer = data.get('customer', {})

            # Get source info
            source = data.get('source', {})

            # Transform to Shopify line_items
            line_items = self._transform_line_items(order_line_items)

            # Transform to Shopify data
            fulfillment_status = self._transform_to_fulfillment_status(order_status)
            transaction_status = self._transform_to_order_transaction_status(payment_status)
            financial_status = self._transform_to_financial_status(payment_status)
            shipment_status = self._transform_to_shipmentStatus(order_status)
            transformed_customer = self._transform_to_customer(customer)

            # Ensure total is present and valid
            total = data.get('total')
            if total is None:
                raise ValueError("Order total is required")

            # Get customer information
            customer = data.get('customer', {})
            billing_address = data.get('billing_address', {})
            shipping_address = data.get('shipping_address', {})

            # Get and validate phone numbers
            billing_phone = self._validate_phone_format(billing_address.get('phone') or customer.get('phone', ''))
            shipping_phone = self._validate_phone_format(shipping_address.get('phone') or customer.get('phone', ''))

            shopify_order = {
                "customer": {
                    "toUpsert": transformed_customer
                },
                "billingAddress": {
                    "firstName": customer.get('first_name', ''),
                    "lastName": customer.get('last_name', ''),
                    "company": customer.get('company_name', ''),
                    "address1": billing_address.get('address1', ''),
                    "address2": billing_address.get('address2', ''),
                    "phone": billing_phone,
                    "city": billing_address.get('city', ''),
                    "province": billing_address.get('province', ''),
                    "countryCode": billing_address.get('countryCode', 'VN'),
                    "zip": billing_address.get('zip', '')
                },
                "closedAt": data.get('updated_at') if data.get('status') == 'CANCELLED' else None,
                "companyLocationId": None,
                "currency": data.get('currency', 'USD'),
                "email": customer.get('email', ''),
                "financialStatus": financial_status,
                # "fulfillment": {
                #     "locationId": destination_location_id,
                #     "notifyCustomer": False,  ## not exist in order -> False by default
                #     "originAddress": None,
                #     "shipmentStatus": shipment_status,
                #     "trackingCompany": '',
                #     "trackingNumber": '',
                # },  ## temporary disable fulfillment
                "fulfillmentStatus": fulfillment_status,
                # Note: Can be set fulfillmentStatus: 'UNFULFILLED' if no fulfillment attached only
                "lineItems": line_items,
                "metafields": None,
                "name": data.get('external_id'),
                "note": data.get('note', ''),
                "phone": self._validate_phone_format(customer.get('phone', '')),
                "poNumber": None,
                "presentmentCurrency": "USD",
                "processedAt": data.get('created_at'),
                "referringSite": None,
                "shippingAddress": {
                    "firstName": customer.get('first_name', ''),
                    "lastName": customer.get('last_name', ''),
                    "company": customer.get('company_name', ''),
                    "address1": shipping_address.get('address1', ''),
                    "address2": shipping_address.get('address2', ''),
                    "phone": shipping_phone,
                    "city": shipping_address.get('city', ''),
                    "province": shipping_address.get('province', ''),
                    "countryCode": shipping_address.get('countryCode', 'VN'),
                    "zip": shipping_address.get('zip', '')
                },
                "shippingLines": None,
                "sourceIdentifier": source.get('id'),
                "sourceName": source.get('name'),
                "sourceUrl": None,
                "tags": data.get('tags', []),
                "taxesIncluded": False,
                "taxLines": None,
                "test": False,
                "transactions": [
                    {
                        "amountSet": {
                            "shopMoney": {
                                "amount": str(total),
                                "currencyCode": "USD"
                            }
                        },
                        "authorizationCode": None,
                        "deviceId": None,
                        "gateway": None,
                        "giftCardId": None,
                        "kind": "SALE",
                        "locationId": None,
                        "processedAt": None,
                        "receiptJson": None,
                        "status": transaction_status,
                        "test": False,
                        "userId": None,
                    }
                ],
                "userId": None,
            }

            # Validate the output before returning
            self.validate_output(shopify_order)
            return shopify_order

        except Exception as e:
            logger.error(f"Error transforming order data: {str(e)}")
            raise

    def _transform_line_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform order line items to Shopify format"""
        if not items:
            raise ValueError("Order must contain at least one line item")

        return [{
            "fulfillmentService": None,
            "giftCard": False,
            "priceSet": {
                "presentmentMoney": None,
                "shopMoney": {  ## required
                    "amount": str(item.get('sale_price', 0)),
                    "currencyCode": "USD"
                }
            },
            "productId": None,  ## item.get('product_id', ''),
            "properties": None,
            "quantity": int(item.get('quantity', 1)),
            "requiresShipping": False,
            "sku": item.get('sku', ''),
            "taxable": False,
            "taxLines": None,
            "title": item.get('name', ''),
            "variantId": None,  ## item.get('variant_id', ''),
            "variantTitle": item.get('variant_name', ''),
            "vendor": '',  ## (item.get('brand') or {}).get('name', ''),
            # "weight": None ## temporary disable weight
        } for item in items]

    def _transform_to_fulfillment_status(seft, status: Status) -> str:
        status_mapping = {
            Status.DRAFT.value: None,
            Status.PENDING.value: None,
            Status.AWAIT_PACKING.value: None,
            Status.PACKING.value: None,
            Status.PARTIAL_PACKING.value: None,
            Status.READY.value: FulfillmentStatus.FULFILLED.value,
            Status.PARTIAL_READY.value: None,
            Status.IN_CANCEL.value: FulfillmentStatus.RESTOCKED.value,
            Status.CANCELLED.value: FulfillmentStatus.RESTOCKED.value,
            Status.SHIPPING.value: FulfillmentStatus.FULFILLED.value,
            Status.PARTIAL_SHIPPING.value: FulfillmentStatus.PARTIAL.value,
            Status.DELIVERED.value: FulfillmentStatus.FULFILLED.value,
            Status.PARTIAL_DELIVERED.value: FulfillmentStatus.FULFILLED.value,
            Status.RETURNING.value: FulfillmentStatus.RESTOCKED.value,
            Status.PARTIAL_RETURNING.value: FulfillmentStatus.RESTOCKED.value,
            Status.PARTIAL_COMPLETED.value: FulfillmentStatus.FULFILLED.value,
            Status.COMPLETED.value: FulfillmentStatus.FULFILLED.value,
        }
        return status_mapping.get(status, None)

    def _transform_to_shipmentStatus(self, status: Status) -> str:
        status_mapping = {
            Status.DRAFT.value: FulfillmentEventStatus.CONFIRMED.value,
            Status.PENDING.value: FulfillmentEventStatus.CONFIRMED.value,
            Status.AWAIT_PACKING.value: FulfillmentEventStatus.CONFIRMED.value,
            Status.PACKING.value: FulfillmentEventStatus.CONFIRMED.value,
            Status.PARTIAL_PACKING.value: FulfillmentEventStatus.CONFIRMED.value,
            Status.READY.value: FulfillmentEventStatus.READY_FOR_PICKUP.value,
            Status.PARTIAL_READY.value: FulfillmentEventStatus.CONFIRMED.value,
            Status.IN_CANCEL.value: FulfillmentEventStatus.FAILURE.value,
            Status.CANCELLED.value: FulfillmentEventStatus.FAILURE.value,
            Status.SHIPPING.value: FulfillmentEventStatus.ATTEMPTED_DELIVERY.value,
            Status.PARTIAL_SHIPPING.value: FulfillmentEventStatus.ATTEMPTED_DELIVERY.value,
            Status.DELIVERED.value: FulfillmentEventStatus.DELIVERED.value,
            Status.PARTIAL_DELIVERED.value: FulfillmentEventStatus.ATTEMPTED_DELIVERY.value,
            Status.RETURNING.value: FulfillmentEventStatus.FAILURE.value,
            Status.PARTIAL_RETURNING.value: FulfillmentEventStatus.FAILURE.value,
            Status.PARTIAL_COMPLETED.value: FulfillmentEventStatus.ATTEMPTED_DELIVERY.value,
            Status.COMPLETED.value: FulfillmentEventStatus.DELIVERED.value,
        }
        return status_mapping.get(status, FulfillmentEventStatus.CONFIRMED.value)

    def _transform_to_financial_status(self, payment_status: PaymentStatus) -> str:
        """Map internal payment status to Shopify financial status"""
        status_mapping = {
            PaymentStatus.UNPAID.value: FinancialStatus.AUTHORIZED.value,
            PaymentStatus.PARTIAL_PAID.value: FinancialStatus.PARTIALLY_PAID.value,
            PaymentStatus.COD.value: FinancialStatus.AUTHORIZED.value,
            PaymentStatus.PENDING_ESCROW.value: FinancialStatus.PENDING.value,
            PaymentStatus.PAID.value: FinancialStatus.PAID.value,
            PaymentStatus.PARTIAL_REFUNDED.value: FinancialStatus.PARTIALLY_REFUNDED.value,
            PaymentStatus.REFUNDED.value: FinancialStatus.REFUNDED.value,
            PaymentStatus.VOIDED.value: FinancialStatus.VOIDED.value,
        }
        return status_mapping.get(payment_status, FinancialStatus.AUTHORIZED.value)

    def _transform_to_order_transaction_status(self, payment_status: PaymentStatus) -> str:
        """Map internal payment status to Shopify transaction status"""
        status_mapping = {
            PaymentStatus.UNPAID.value: TransactionStatus.UNKNOWN.value,
            PaymentStatus.PARTIAL_PAID.value: TransactionStatus.UNKNOWN.value,
            PaymentStatus.COD.value: TransactionStatus.PENDING.value,
            PaymentStatus.PENDING_ESCROW.value: TransactionStatus.PENDING.value,
            PaymentStatus.PAID.value: TransactionStatus.SUCCESS.value,
            PaymentStatus.PARTIAL_REFUNDED.value: TransactionStatus.FAILURE.value,
            PaymentStatus.REFUNDED.value: TransactionStatus.SUCCESS.value,
            PaymentStatus.VOIDED.value: TransactionStatus.ERROR.value,
        }
        return status_mapping.get(payment_status, TransactionStatus.UNKNOWN.value)

    def _transform_to_customer(self, customer):
        def _transform_to_customer_address(address):
            customer_address = {
                'address1': address.get('address1'),
                'address2': address.get('address1'),
                'city': address.get('city'),
                'country': address.get('country'),
                'firstName': address.get('first_name'),
                'lastName': address.get('last_name'),
                'zip': address.get('zip')
            }
            return customer_address

        # Transform each address in the addresses list
        addresses = customer.get("addresses", [])
        transformed_addresses = [_transform_to_customer_address(addr) for addr in addresses]

        transformed_customer = {
            "addresses": transformed_addresses,
            "firstName": customer.get('first_name'),
            "lastName": customer.get('last_name'),
            # "phone": customer.get('phone'),
            "email": customer.get('email'),
            "tags": customer.get('tags')
        }
        return transformed_customer

    def _validate_phone_format(self, phone: str) -> str:
        """
        Validates if phone number matches E.164 format (+[country code][number])
        E.164 requirements:
        - Must start with '+'
        - Country code (1-3 digits)
        - Subscriber number (up to 15 digits total)
        - Total length must be between 8 and 15 digits (excluding '+')
        Returns the phone number if valid, None if invalid
        """
        if not phone:
            return None

        # Remove any spaces, dashes, or parentheses
        cleaned_phone = ''.join(filter(lambda x: x.isdigit() or x == '+', phone))

        # Check if matches E.164 format
        if not cleaned_phone.startswith('+'):
            return None

        # Remove '+' for length check
        digits_only = cleaned_phone[1:]

        # Check total length (8-15 digits)
        if len(digits_only) < 8 or len(digits_only) > 15:
            return None

        # Check if all remaining characters are digits
        if not digits_only.isdigit():
            return None

        return cleaned_phone

    def validate_input(self, data: Dict[str, Any]):
        pass

    def validate_output(self, data: Dict[str, Any]):
        """Validate the transformed output data"""
        if not data:
            raise ValueError("Transformed data cannot be empty")

        order = data
        # if not order.get('currency'):
        #     raise ValueError("Currency is required")
        #
        if not order.get('lineItems'):
            raise ValueError("At least one line item is required")

        # if not order.get('transactions'):
        #     raise ValueError("At least one transaction is required")
        #
        # Validate billing address
        if not order.get('billingAddress'):
            raise ValueError("Billing address is required")

        # billing_address = order['billingAddress']
        # if not billing_address.get('address1'):
        #     raise ValueError("Billing address line 1 is required")
        # if not billing_address.get('city'):
        #     raise ValueError("Billing address city is required")
        # if not billing_address.get('countryCode'):
        #     raise ValueError("Billing address country code is required")
        #
        # Validate shipping address
        if not order.get('shippingAddress'):
            raise ValueError("Shipping address is required")
        #
        # shipping_address = order['shippingAddress']
        # if not shipping_address.get('address1'):
        #     raise ValueError("Shipping address line 1 is required")
        # if not shipping_address.get('city'):
        #     raise ValueError("Shipping address city is required")
        # if not shipping_address.get('countryCode'):
        #     raise ValueError("Shipping address country code is required")
        #
        for line_item in order['lineItems']:
            if not line_item.get('title'):
                raise ValueError("Line item title is required")
            if not line_item.get('quantity'):
                raise ValueError("Line item quantity is required")
            if not line_item.get('priceSet'):
                raise ValueError("Line item priceSet is required")
            if not line_item['priceSet'].get('shopMoney'):
                raise ValueError("Line item priceSet.shopMoney is required")
        #
        # for transaction in order['transactions']:
        #     if not transaction.get('kind'):
        #         raise ValueError("Transaction kind is required")
        #     if not transaction.get('status'):
        #         raise ValueError("Transaction status is required")
        #     if not transaction.get('amountSet'):
        #         raise ValueError("Transaction amount is required")

    @property
    def input_schema(self):
        return OrderAttributesSchema().dump(OrderAttributesSchema())

    @property
    def output_schema(self):
        return {
            'type': 'object',
            'properties': {
                'order': {
                    'type': 'object',
                    'properties': {
                        'currency': {'type': 'string'},
                        'processedAt': {'type': 'string'},
                        'lineItems': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'properties': {
                                    'title': {'type': 'string'},
                                    'quantity': {'type': 'integer'},
                                    'priceSet': {
                                        'type': 'object',
                                        'properties': {
                                            'shopMoney': {
                                                'type': 'object',
                                                'properties': {
                                                    'amount': {'type': 'string'},
                                                    'currencyCode': {'type': 'string'}
                                                },
                                                'required': ['amount', 'currencyCode']
                                            }
                                        },
                                        'required': ['shopMoney']
                                    }
                                },
                                'required': ['title', 'quantity', 'priceSet']
                            }
                        },
                        'transactions': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'properties': {
                                    'kind': {'type': 'string'},
                                    'status': {'type': 'string'},
                                    'amount': {'type': 'string'}
                                },
                                'required': ['kind', 'status', 'amount']
                            }
                        }
                    },
                    'required': ['currency', 'lineItems']
                }
            },
            'required': ['order']
        }

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.order


class FinancialStatus(str, Enum):
    AUTHORIZED = 'AUTHORIZED'
    EXPIRED = 'EXPIRED'
    PAID = 'PAID'
    PARTIALLY_PAID = 'PARTIALLY_PAID'
    PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED'
    PENDING = 'PENDING'
    REFUNDED = 'REFUNDED'
    VOIDED = 'VOIDED'

    # Document:
    # OrderCreateFinancialStatus: https://shopify.dev/docs/api/admin-graphql/unstable/enums/OrderCreateFinancialStatus


class TransactionStatus(str, Enum):
    AWAITING_RESPONSE = ''
    ERROR = 'ERROR'
    FAILURE = 'FAILURE'
    PENDING = 'PENDING'
    SUCCESS = 'SUCCESS'
    UNKNOWN = 'UNKNOWN'

    # Document:
    # OrderTransactionStatus: https://shopify.dev/docs/api/admin-graphql/unstable/enums/OrderTransactionStatus


class FulfillmentStatus(str, Enum):
    FULFILLED = 'FULFILLED'
    PARTIAL = 'PARTIAL'
    RESTOCKED = 'RESTOCKED'

    # Document:
    # OrderCreateFulfillmentStatus: https://shopify.dev/docs/api/admin-graphql/unstable/enums/OrderCreateFulfillmentStatus

    # Note:
    # The fulfillment status of the order. Will default to 'unfulfilled' if not included.


class FulfillmentEventStatus(str, Enum):
    ATTEMPTED_DELIVERY = 'ATTEMPTED_DELIVERY'
    CARRIER_PICKED_UP = 'CARRIER_PICKED_UP'
    CONFIRMED = 'CONFIRMED'
    DELAYED = 'DELAYED'
    DELIVERED = 'DELIVERED'
    FAILURE = 'FAILURE'
    IN_TRANSIT = 'IN_TRANSIT'
    LABEL_PRINTED = 'LABEL_PRINTED'
    LABEL_PURCHASED = 'LABEL_PURCHASED'
    OUT_FOR_DELIVERY = 'OUT_FOR_DELIVERY'
    PICKED_UP = 'PICKED_UP'
    READY_FOR_PICKUP = 'READY_FOR_PICKUP'

    # Describe:
    # The status that describes a fulfillment or delivery event.

    # Document:
    # FulfillmentEventStatus: https://shopify.dev/docs/api/admin-graphql/unstable/enums/FulfillmentEventStatus
