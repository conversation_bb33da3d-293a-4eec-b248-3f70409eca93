import unittest

from tests.base import BaseTestCase
from flows.src.apis.webhook import webhook_handler_by_connection_id
from flows.tests.apis.webhook.data_test import tiktokshop_webhook_event, shopify_oauth_webhook_event, subscription_event


class TestChannelWebhookApiTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

    def test_channel_webhook(self):
        webhook_handler_by_connection_id(shopify_oauth_webhook_event, {})

    def test_channel_webhook_subscription_event(self):
        webhook_handler_by_connection_id(subscription_event, {})


if __name__ == '__main__':
    unittest.main()
