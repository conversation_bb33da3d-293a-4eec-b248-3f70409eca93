import unittest
from tests.base import BaseTestCase
from nolicore.utils.exceptions import NotFoundRequest, BadRequest

from models.bots.virtual_staff import VirtualStaffModel, Role
from onexbots.src.apis.virtual_staff import (
    create_virtual_staff,
    get_virtual_staff,
    list_virtual_staffs,
    update_virtual_staff,
    delete_virtual_staff
)


class TestVirtualStaff(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    def test_create_virtual_staff(self):
        """Test creating a new virtual staff"""
        # Prepare test data
        test_data = {
            "name": "Test Virtual Staff",
            "department_id": "test-department-id",
            "role": Role.VIRTUAL_STAFF.value,
            "skills": ["customer service", "sales"],
            "interaction_style": {
                "tone": "professional",
                "language": "en",
                "response_length": "MEDIUM",
                "trait": "helpful",
                "ethical_constraints": True
            },
            "llm_configuration": {
                "model": "gpt-4",
                "url": "https://api.openai.com/v1/chat/completions",
                "instruction": "You are a helpful assistant"
            }
        }

        event = self.create_lambda_event(body=test_data)
        context = self.create_lambda_context()

        # Test successful creation
        response = create_virtual_staff(event, context)
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Virtual staff created successfully')
        self.assertIsNotNone(response['data'])

        # Test missing required fields
        invalid_data = test_data.copy()
        del invalid_data['name']
        event = self.create_lambda_event(body=invalid_data)
        
        with self.assertRaises(BadRequest) as context:
            create_virtual_staff(event, self.create_lambda_context())
        self.assertEqual(str(context.exception), "Missing required field: name")

    def test_get_virtual_staff(self):
        """Test getting a virtual staff by ID"""
        # First create a virtual staff
        test_data = {
            "name": "Test Virtual Staff",
            "department_id": "test-department-id",
            "role": Role.VIRTUAL_STAFF.value,
            "skills": ["customer service"],
            "interaction_style": {
                "tone": "professional",
                "language": "en",
                "response_length": "MEDIUM",
                "trait": "helpful",
                "ethical_constraints": True
            },
            "llm_configuration": {
                "model": "gpt-4",
                "url": "https://api.openai.com/v1/chat/completions",
                "instruction": "You are a helpful assistant"
            }
        }
        create_event = self.create_lambda_event(body=test_data)
        created_staff = create_virtual_staff(create_event, self.create_lambda_context())
        staff_id = created_staff['data']['id']

        # Test getting the created virtual staff
        event = self.create_lambda_event(
            path_params={'id': staff_id}
        )
        response = get_virtual_staff(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Virtual staff retrieved successfully')
        self.assertEqual(response['data']['id'], staff_id)

        # Test getting non-existent virtual staff
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'}
        )
        with self.assertRaises(NotFoundRequest):
            get_virtual_staff(event, self.create_lambda_context())

    def test_list_virtual_staffs(self):
        """Test listing virtual staffs"""
        # Create test virtual staffs
        test_data = {
            "name": "Test Virtual Staff",
            "department_id": "test-department-id",
            "role": Role.VIRTUAL_STAFF.value,
            "skills": ["customer service"],
            "interaction_style": {
                "tone": "professional",
                "language": "en",
                "response_length": "MEDIUM",
                "trait": "helpful",
                "ethical_constraints": True
            },
            "llm_configuration": {
                "model": "gpt-4",
                "url": "https://api.openai.com/v1/chat/completions",
                "instruction": "You are a helpful assistant"
            }
        }
        create_event = self.create_lambda_event(body=test_data)
        create_virtual_staff(create_event, self.create_lambda_context())

        # Test listing all virtual staffs
        event = self.create_lambda_event()
        response = list_virtual_staffs(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Virtual staffs retrieved successfully')
        self.assertIsInstance(response['data'], dict)
        self.assertIn('items', response['data'])


    def test_update_virtual_staff(self):
        """Test updating a virtual staff"""
        # First create a virtual staff
        test_data = {
            "name": "Test Virtual Staff",
            "department_id": "test-department-id",
            "role": Role.VIRTUAL_STAFF.value,
            "skills": ["customer service"],
            "interaction_style": {
                "tone": "professional",
                "language": "en",
                "response_length": "MEDIUM",
                "trait": "helpful",
                "ethical_constraints": True
            },
            "llm_configuration": {
                "model": "gpt-4",
                "url": "https://api.openai.com/v1/chat/completions",
                "instruction": "You are a helpful assistant"
            }
        }
        create_event = self.create_lambda_event(body=test_data)
        created_staff = create_virtual_staff(create_event, self.create_lambda_context())
        staff_id = created_staff['data']['id']

        # Test updating the virtual staff
        update_data = {
            "name": "Updated Virtual Staff",
            "skills": ["customer service", "sales", "support"]
        }
        event = self.create_lambda_event(
            path_params={'id': staff_id},
            body=update_data
        )
        response = update_virtual_staff(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Virtual staff updated successfully')
        self.assertEqual(response['data']['name'], "Updated Virtual Staff")
        self.assertEqual(len(response['data']['skills']), 3)

        # Test updating non-existent virtual staff
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'},
            body=update_data
        )
        with self.assertRaises(NotFoundRequest):
            update_virtual_staff(event, self.create_lambda_context())

    def test_delete_virtual_staff(self):
        """Test deleting a virtual staff"""
        # First create a virtual staff
        test_data = {
            "name": "Test Virtual Staff",
            "department_id": "test-department-id",
            "role": Role.VIRTUAL_STAFF.value,
            "skills": ["customer service"],
            "interaction_style": {
                "tone": "professional",
                "language": "en",
                "response_length": "MEDIUM",
                "trait": "helpful",
                "ethical_constraints": True
            },
            "llm_configuration": {
                "model": "gpt-4",
                "url": "https://api.openai.com/v1/chat/completions",
                "instruction": "You are a helpful assistant"
            }
        }
        create_event = self.create_lambda_event(body=test_data)
        created_staff = create_virtual_staff(create_event, self.create_lambda_context())
        staff_id = created_staff['data']['id']

        # Test deleting the virtual staff
        event = self.create_lambda_event(
            path_params={'id': staff_id}
        )
        response = delete_virtual_staff(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Virtual staff deleted successfully')

        # Verify the virtual staff is deleted
        with self.assertRaises(NotFoundRequest):
            get_virtual_staff(event, self.create_lambda_context())

        # Test deleting non-existent virtual staff
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'}
        )
        with self.assertRaises(NotFoundRequest):
            delete_virtual_staff(event, self.create_lambda_context())


if __name__ == '__main__':
    unittest.main()
