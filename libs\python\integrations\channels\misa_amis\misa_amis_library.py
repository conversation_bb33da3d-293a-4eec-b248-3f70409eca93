import json
import uuid
from datetime import datetime
from functools import lru_cache
from typing import Dict, Any, Union

import requests

from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, SyncAPIResponse,
    PublishMetaData, StandardOrder, StandardProduct, StandardReturnOrder, StandardPurchaseOrder
)


class MisaAmisEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.SYNC = EndpointType(type="rest", method="POST", path=f"apir/sync/actopen/save")
        self.SYNC_DICTIONARY = EndpointType(type="rest", method="POST", path=f"apir/sync/actopen/save_dictionary")


class MisaAmisLibrary(BaseAPILibrary):

    def __init__(self,
                 app_id: str,
                 access_code: str,
                 org_company_code: str,
                 base_url: str = "https://actapp.misa.vn"):
        super().__init__(base_url)
        self.app_id = app_id
        self.access_code = access_code
        self.org_company_code = org_company_code
        self.base_url = base_url
        self.endpoints = MisaAmisEndpoints(self.endpoints.base_url)

    @lru_cache(maxsize=128)
    def get_token(self):
        url = f'{self.base_url}/api/oauth/actopen/connect'
        data = {
            "app_id": self.app_id,
            "access_code": self.access_code,
            "org_company_code": self.org_company_code
        }
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                data = json.loads(result.get("Data"))
                return data.get('access_token')
            else:
                # If the request failed, raise an exception
                raise Exception(response.text)
        except Exception as e:
            raise Exception(f'Failed to get misa amis token: {e}')

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None) -> Dict[
        str, Any]:

        headers = {
            'X-MISA-AccessToken': self.get_token(),
            'Content-Type': 'application/json'
        }

        payload = {
            'app_id': self.app_id,
            'org_company_code': self.org_company_code,
            **data
        }

        response = self._make_request_sync(endpoint, headers=headers, params=params, json=payload)
        return response

    def test_connection(self) -> bool:
        try:
            self.get_token()
            return True
        except Exception:
            return False

    @staticmethod
    def extract_merchant_id(payload: Dict[str, Any]) -> str:
        return payload.get('org_company_code')

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return payload.get('data_type')

    def sync_order(self, order_data: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        try:
            if 'dictionary' in order_data:
                dictionary = order_data.pop('dictionary')
            else:
                dictionary = []
            data = {"voucher": [order_data],
                    "dictionary": []
                    }
            self._make_request(self.endpoints.SYNC_DICTIONARY, data={"dictionary": dictionary})
            synced_order = self._make_request(self.endpoints.SYNC, data=data)
            if synced_order.get("Success"):
                return SyncAPIResponse(
                    success=True,
                    data=None,
                    meta=PublishMetaData(updated_at=datetime.now()),
                    message=synced_order.get("Data", "Đồng bộ thành công")
                )
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=synced_order.get("ErrorMessage", ""))
        except Exception as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync order: {str(e)}")

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def standardize_order(self, raw_order: Dict[str, Any]) -> StandardOrder:
        pass

    def get_products(self, params: Dict[str, Any] = None) -> FetchAPIResponse[StandardProduct]:
        pass

    def sync_product(self, product_data: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        pass

    def standardize_product(self, raw_product: Dict[str, Any]) -> StandardProduct:
        pass

    def get_orders(self, params: Dict[str, Any] = None) -> FetchAPIResponse[StandardOrder]:
        pass