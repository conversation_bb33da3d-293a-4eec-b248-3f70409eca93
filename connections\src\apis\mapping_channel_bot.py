from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import NotFoundRequest
from models.integration.bot_channel_connection import BotChannelConnectionModel, \
    BotChannelConnectionByBotIdAndConnectionIdIndex
from helpers.custom_jsonpath import get_value_from_jsonpath
from integrations.common.connection_helper import get_connection_by_id
from nolicore.utils.exceptions import BadRequest
from models.integration.connection import ConnectionStatus
from models.integration.bot_channel_connection import BotChannelConnectionModel


@as_api()
def create_mapping_channel_bot(api_gateway_request: ApiGatewayRequest):
    bot_id = api_gateway_request.path_parameters.get('bot_id')
    company_id = api_gateway_request.company_id
    connection_id = api_gateway_request.path_parameters.get('connection_id')
    channel_id = api_gateway_request.path_parameters.get('channel_id')

    # validate required fields
    required_fields = {
        'bot_id': 'Bot ID',
        'connection_id': 'Connection ID',
        'channel_id': 'Channel ID'
    }
    for field, name in required_fields.items():
        if not locals()[field]:
            raise BadRequest(f"{name} is required")

    connection = get_connection_by_id(connection_id)
    if not connection:
        raise NotFoundRequest(f"Connection not found for connection_id: {connection_id}")

    if connection.attributes.status != ConnectionStatus.ACTIVE:
        raise BadRequest(f"Connection is not active for connection_id: {connection_id}")

    channel_name = connection.attributes.channel_name

    mapping = BotChannelConnectionModel.get_bot_channel_connection(connection_id, channel_id)
    if mapping:
        if mapping.attributes.bot_id == bot_id:
            raise BadRequest(
                f"Mapping already exists for bot_id: {mapping.attributes.bot_id} connection_id: {mapping.attributes.connection_id} and channel_id: {mapping.attributes.channel_id}")

    bot_channel_connection = BotChannelConnectionModel.create_bot_channel_connection(bot_id, connection_id, channel_id,
                                                                                     channel_name)
    if not bot_channel_connection:
        return {"is_success": False, "message": "Failed to create mapping"}
    return {"is_success": True, "message": "Mapping created successfully"}


@as_api()
def delete_mapping_channel_bot(api_gateway_request: ApiGatewayRequest):
    channel_id = api_gateway_request.path_parameters['channel_id']
    connection_id = api_gateway_request.path_parameters['connection_id']

    mapping = BotChannelConnectionModel.get_bot_channel_connection(connection_id, channel_id)
    if not mapping:
        return {"is_success": False, "message": "Mapping not found"}

    response = BotChannelConnectionModel.delete_bot_channel_connection(connection_id, channel_id)
    if not response:
        return {"is_success": False, "message": "Failed to delete mapping"}
    return {"is_success": True, "message": "Mapping deleted successfully"}


@as_api()
def get_pages(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    bot_id = api_gateway_request.path_parameters['bot_id']
    connection = get_connection_by_id(connection_id)
    if not connection:
        raise NotFoundRequest("Connection not found")
    if connection.attributes.status != ConnectionStatus.ACTIVE:
        return []
    channel_name = connection.attributes.channel_name
    mapping_by_bot_id_connection_id = BotChannelConnectionByBotIdAndConnectionIdIndex.get_bot_channel_connection_by_bot_id_and_connection_id(
        bot_id, connection_id)

    jsonpath = ''
    if channel_name == "zalo_oa":
        jsonpath = '$.token_data.user_info'
    elif channel_name == "facebook_oauth":
        jsonpath = '$.token_data.pages'
    # channel_datas = pages or user_info
    account_datas = get_value_from_jsonpath(connection.attributes_dict, jsonpath) or []
    result = []
    # conbine pages and mapping
    for account_data in account_datas:
        result_account = {'name': account_data['name'], 'id': account_data['id'], 'avatar': account_data.get('avatar'),
                          'is_active': False, 'is_enabled': False}

        if mapping_by_bot_id_connection_id:
            for mapping in mapping_by_bot_id_connection_id:
                if mapping['channel_id'] == account_data['id']:
                    result_account['is_active'] = True
                    result_account['is_enabled'] = True
        else:
            # check if channel_id is mapped by other bot
            mapping_by_connection_id_channel_id = BotChannelConnectionModel.get_bot_channel_connection(
                connection_id,
                account_data['id'])
            if mapping_by_connection_id_channel_id:
                result_account['is_active'] = False
                result_account['is_enabled'] = False
            else:
                result_account['is_active'] = False
                result_account['is_enabled'] = True
        result.append(result_account)
    return result
