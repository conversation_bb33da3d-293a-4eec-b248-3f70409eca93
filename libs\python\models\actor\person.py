from dataclasses import dataclass
from typing import List

from marshmallow import fields, EXCLUDE
from nolicore.adapters.db.model import AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema
from models.common.address import Address, AddressSchema
from models.finance.account import Account, AccountSchema


@dataclass
class Person:
    id: str
    email: str = None
    phone: str = None
    company_name: str = None
    first_name: str = None
    last_name: str = None
    gender: str = None
    birthday: str = None
    nationality: str = None
    image: str = None
    tags: str = None
    billing_address: Address = None
    addresses: List[Address] = None
    account: Account = None


@dataclass
class PersonAttributes(Person, BasicAttributes):
    pass


class PersonSchema(AttributesSchema):
    class Meta:
        unknown = EXCLUDE

    id = fields.Str(required=True)
    email = fields.Email(allow_none=True)
    phone = fields.Str(allow_none=True)
    company_name = fields.Str(allow_none=True)
    first_name = fields.Str(allow_none=True)
    last_name = fields.Str(allow_none=True)
    gender = fields.Str(allow_none=True)
    birthday = fields.Str(allow_none=True)
    nationality = fields.Str(allow_none=True)
    image = fields.Str(allow_none=True)
    tags = fields.Str(allow_none=True)
    billing_address = fields.Nested(AddressSchema(Address), allow_none=True)
    addresses = fields.List(fields.Nested(AddressSchema(Address)), allow_none=True)
    account = fields.Nested(AccountSchema(Account), allow_none=True)


class PersonAttributesSchema(PersonSchema, BasicAttributesSchema):
    pass
