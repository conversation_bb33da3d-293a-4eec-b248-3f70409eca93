Resources:
  FetchQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-fetch-queue
      VisibilityTimeout: 120
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt: [FetchDeadLetterQueue, Arn]
        maxReceiveCount: 3 # Maximum number of attempts before moving to DLQ

  FetchDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-fetch-dead-letter-queue
      VisibilityTimeout: 120

  RawDataBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:service}-${self:provider.stage}-raw-data
      VersioningConfiguration:
        Status: Enabled

  TransformedDataBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:service}-${self:provider.stage}-transformed-data
      VersioningConfiguration:
        Status: Enabled

  TransformQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-transform-queue
      VisibilityTimeout: 120

  PublishQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-publish-queue
      VisibilityTimeout: 120
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt: [PublishDeadLetterQueue, Arn]
        maxReceiveCount: 3 # Maximum number of attempts before moving to DLQ

  PublishDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-publish-dead-letter-queue
      VisibilityTimeout: 120

  SyncMappingQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-sync-mapping-queue
      VisibilityTimeout: 120

Outputs:
  FetchQueueUrl:
    Value: !Ref FetchQueue
    Export:
      Name: ${self:service}-${self:provider.stage}-FetchQueueUrl

  RawDataBucketName:
    Value: !Ref RawDataBucket
    Export:
      Name: ${self:service}-${self:provider.stage}-RawDataBucketName

  TransformedDataBucketName:
    Value: !Ref TransformedDataBucket
    Export:
      Name: ${self:service}-${self:provider.stage}-TransformedDataBucketName

  TransformQueueUrl:
    Value: !Ref TransformQueue
    Export:
      Name: ${self:service}-${self:provider.stage}-TransformQueueUrl

  PublishQueueUrl:
    Value: !Ref PublishQueue
    Export:
      Name: ${self:service}-${self:provider.stage}-PublishQueueUrl

  SyncMappingQueueUrl:
    Value: !Ref SyncMappingQueue
    Export:
      Name: ${self:service}-${self:provider.stage}-SyncMappingQueueUrl
