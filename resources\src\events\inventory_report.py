import os

from boto3.dynamodb.types import TypeDeserializer
from nolicore.utils.utils import logger

from helpers.inventory import update_indexer_inventory
from models.order.order import OrderModel

deserializer = TypeDeserializer()

SERVICE = os.getenv('SERVICE')
ENV = os.getenv('ENV')


def handle(event, context):
    logger.info(event)

    sku_list_by_company = {}
    sku_dict_by_company = {}

    for record in event['Records']:
        if record['eventName'] in ['MODIFY', 'INSERT']:
            record_data = {key: deserializer.deserialize(value) for key, value in
                           record['dynamodb']['NewImage'].items()}
            company_id = record_data['company_id']
            sku_list_by_company.setdefault(company_id, set())
            sku_list_by_company[company_id] = sku_list_by_company[company_id].union(
                {order_line['sku'] for order_line in record_data['order_line_items']})
            order_obj = OrderModel(record_data)
            sku_dict_by_company.update(
                {f'{company_id}-{item.sku}': item for item in order_obj.attributes.order_line_items})

    update_indexer_inventory(sku_list_by_company, sku_dict_by_company)
