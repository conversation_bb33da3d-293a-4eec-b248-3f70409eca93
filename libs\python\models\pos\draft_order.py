from dataclasses import dataclass

from marshmallow import fields

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


@dataclass
class DraftOrderAttributes(BasicAttributes):
    user_id: str = None
    draft_orders: list[dict] = None


class DraftOrderAttributesSchema(BasicAttributesSchema):
    user_id = fields.Str(required=True)
    draft_orders = fields.List(fields.Dict(required=True), required=True)


class DraftOrderModel(BasicModel):
    table_name = 'draftOrder'
    attributes: DraftOrderAttributes
    attributes_schema = DraftOrderAttributesSchema
    attributes_class = DraftOrderAttributes

    query_mapping = {
        'query': ['id'],
        'filter': {
            'id': 'query',
            'user_id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'draft_orders': 'draft_orders'
        }
    }


class DraftOrderUserIdIndex(DraftOrderModel):
    key__id = 'user_id'
    index_name = 'draftOrderUserIdIndex'

    @classmethod
    def get_draft_order_by_user_id(cls, user_id, company_id):
        try:
            return cls(
                DraftOrderUserIdIndex.list({'user_id': user_id, 'company_id': company_id}, limit=10).get('Items', [])[
                    0])
        except IndexError:
            return None
