import base64
import os
import uuid
from copy import copy
from decimal import Decimal
from email.header import decode_header
from typing import Union

from nolicore.adapters.db.model import Model
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import BadRequest
from nolicore.utils.utils import logger

from helpers.errors import InventoryUpdateMultiProcessing
from models.basic import BasicAttributes
from models.common.status import update_status
from models.inventory.inventory_item import InventoryItemModel
from models.inventory.inventory_transaction import TransactionType, InventoryTransactionModel
from models.inventory.stock_adjustment import StockAdjustmentModel
from models.inventory.stock_relocate import StockRelocateModel
from models.order.order import OrderModel
from models.order.order_return import ReturnModel
from models.order.package import PackageModel
from models.product.product import ProductModel

from models.product.variant import VariantModel, VariantOriginalSkuIndex, VariantSkuIndex
from elasticsearch.helpers import bulk

from models.purchase_order.purchase_order import PurchaseOrderModel

RESOURCE_SERVICE = os.getenv('RESOURCE_SERVICE')


def update_indexer_inventory(sku_list_by_company: dict, sku_dict_by_company: dict,
                             inventory_items: dict = None) -> None:
    """

    @param sku_list_by_company: {'company_id':{'sku1','sku2'}}
    @param sku_dict_by_company: {'company_id':{'sku1':{orderLineItems}}}
    @param inventory_items:
    """
    variant_indexer = VariantModel.get_search_index(RESOURCE_SERVICE)
    variant_index_name = VariantModel.get_search_index_name(RESOURCE_SERVICE)
    product_indexer = ProductModel.get_search_index(RESOURCE_SERVICE)
    product_index_name = ProductModel.get_search_index_name(RESOURCE_SERVICE)
    elastic_search = OrderModel.get_search_index(RESOURCE_SERVICE)
    variant_documents = []
    product_documents = []
    sku_dict = {}
    product_ids = []
    inventory_items = inventory_items or {}
    for company_id, sku_list in sku_list_by_company.items():
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "company_id.keyword": company_id
                            }
                        },
                        {
                            "terms": {
                                "order_line_items.sku.keyword": list(sku_list)
                            }
                        }
                    ]
                }
            },
            "aggs": {
                "status_counts": {
                    "terms": {
                        "field": "status.keyword",
                        "size": 10
                    },
                    "aggs": {
                        "location_counts": {
                            "terms": {
                                "field": "order_line_items.location_id.keyword",
                                "size": 10
                            },
                            "aggs": {
                                "sku_filter": {
                                    "filters": {
                                        "filters": {
                                            sku: {
                                                "term": {
                                                    "order_line_items.sku.keyword": sku
                                                }
                                            } for sku in sku_list
                                        }
                                    },
                                    "aggs": {
                                        "total_quantity": {
                                            "sum": {
                                                "field": "order_line_items.quantity"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        result = elastic_search.search(query)

        for status_data in result.body['aggregations']['status_counts']['buckets']:
            status = status_data['key']
            if status in ['COMPLETED', 'CANCELLED', 'DRAFT']:
                continue

            # init location quantity
            packing_quantity = 0
            shipping_quantity = 0

            for location_data in status_data['location_counts']['buckets']:
                location_id = location_data['key']
                for sku, sku_data in location_data['sku_filter']['buckets'].items():
                    sku_location = f'{sku}_{location_id}'
                    company_sku = f'{company_id}-{sku}'
                    if sku_location not in inventory_items:
                        inventory_item = InventoryItemModel.get_by_order_line_item(company_id, location_id,
                                                                                   sku_dict_by_company[company_sku])
                        inventory_items[sku_location] = inventory_item.attributes_dict
                    if status in ['PENDING', 'AWAIT_PACKING', 'PACKING', 'PARTIAL_PACKING', 'READY',
                                  'PARTIAL_READY']:
                        packing_quantity += sku_data['total_quantity']['value']
                    elif status in ['SHIPPING', 'PARTIAL_SHIPPING', 'DELIVERED', 'PARTIAL_DELIVERED']:
                        shipping_quantity += sku_data['total_quantity']['value']

                    on_hand_quantity = inventory_items[sku_location]['on_hand']
                    inventory_items[sku_location]['packing'] = packing_quantity
                    inventory_items[sku_location]['shipping'] = shipping_quantity
                    inventory_items[sku_location]['available'] = on_hand_quantity - packing_quantity

        inventory_item_by_sku = {}
        for sku_location, inventory in inventory_items.items():
            sku = inventory['sku']
            inventory_item_by_sku.setdefault(sku, [])
            inventory_item_by_sku[sku].append(inventory)

        for sku, inventories in inventory_item_by_sku.items():
            variant_id = inventories[0]['variant_id']
            key = {
                'id': variant_id,
                'company_id': company_id
            }
            old_inventories = InventoryItemModel.list_inventory_by_sku(company_id, sku)

            # Create a dictionary for quick lookup by location_id from old_inventories
            old_inventories_dict = {item['location_id']: item for item in old_inventories}

            # Iterate through inventories and update or add to old_inventories_dict
            for item in inventories:
                old_inventories_dict[item['location_id']] = item

            # Convert the dictionary back to a list
            new_inventories = list(old_inventories_dict.values())

            variant_documents.append({
                "_op_type": "update",
                "_index": variant_index_name,
                "_id": variant_indexer.get_search_key(key),
                "doc": {
                    'inventories': new_inventories
                },
                "doc_as_upsert": True
            })

            # Get product ids which needed to update
            product_id = inventories[0]['product_id']
            if product_id not in product_ids:
                product_ids.append(product_id)
            # Iterate through sku and update inventories of variants of product
            sku_dict[sku] = new_inventories

        for item in product_ids:
            product_key = {
                'id': item,
                'company_id': company_id
            }
            product_obj = ProductModel.by_key(product_key)
            if product_obj:
                product_documents.append({
                    "_op_type": "update",
                    "_index": product_index_name,
                    "_id": product_indexer.get_search_key(product_key),
                    "doc": {
                        'variants': [
                            {
                                **v,
                                'inventories': sku_dict.get(v['sku'])
                            } if sku_dict.get(v['sku']) else v
                            for v in (product_obj.attributes_dict.get("variants", []) or [])
                        ]
                    },
                    "doc_as_upsert": True
                })

    if len(variant_documents) > 0:
        success, failed = bulk(variant_indexer.elastic_search, variant_documents, raise_on_error=True, refresh="false")
        logger.info(variant_documents)
        logger.info(f"Successful upserts: {success}")
        logger.info(f"Failed upserts: {failed}")

    if len(product_documents) > 0:
        success, failed = bulk(product_indexer.elastic_search, product_documents, raise_on_error=True, refresh="false")
        logger.info(product_documents)
        logger.info(f"Successful upserts: {success}")
        logger.info(f"Failed upserts: {failed}")


def decode_mime_encoded_word(encoded_word):
    decoded_parts = decode_header(encoded_word)
    decoded_text = ""
    for part, charset in decoded_parts:
        if isinstance(part, bytes):
            decoded_text += part.decode(charset or 'utf-8', errors='replace')
        else:
            decoded_text += part
    return decoded_text


def extract_data(_api_request: ApiGatewayRequest):
    form_data = base64.b64decode(_api_request.body)
    boundary = _api_request.headers['content-type'].split('=')[1].replace('"', '')
    form_items = form_data.split(boundary.encode())[1:-1]
    form_data_dict = {}
    for item in form_items:
        meta = item.split(b'\r\n\r\n')
        names = meta[0].decode().split(';')
        item_dict = {}
        for name in names[1:]:
            name_key, value = name.split('=', maxsplit=1)
            name_key = name_key.strip()
            value = value.replace('"', '').strip()
            if name_key == 'filename':
                file_name, content_type = value.split('\r\n')
                if 'utf-8' in file_name:
                    file_name = decode_mime_encoded_word(file_name)
                item_dict[name_key] = file_name
                item_dict['contentType'] = content_type.split(': ')[-1]
            else:
                item_dict[name_key] = value
        item_value = meta[1][:-4]
        item_dict['value'] = item_value if 'filename' in item_dict else item_value.decode()
        form_data_dict[item_dict['name']] = item_dict
    return form_data_dict


def update_inventory(model: Model.__class__, obj: Union[
    ReturnModel, OrderModel, PackageModel, PurchaseOrderModel, StockAdjustmentModel, StockRelocateModel],
                     company_id, user, update_type, status=None, updated_data=None, location=None, source=None,
                     destination=None):
    inventory_items = []
    transactions_status = []
    transactions_inventory_item = []
    transactions_inventory_transaction = []
    # validate stock quantity

    if isinstance(obj, StockAdjustmentModel):
        for line_item in obj.attributes.stock_line_items:
            location_id = location["id"]
            location_name = location["name"]
            update_transactions(model, obj, user, line_item, company_id, location_id, location_name,
                                update_type=update_type,
                                inventory_items=inventory_items,
                                transactions_inventory_item=transactions_inventory_item,
                                transactions_inventory_transaction=transactions_inventory_transaction)
    elif isinstance(obj, StockRelocateModel):
        for line_item in obj.attributes.stock_line_items:
            location_id = source["id"]
            location_name = source["name"]
            new_update_type = "relocate_source_delivery" if update_type == "delivery" else "relocate_source_receive"
            update_transactions(model, obj, user, line_item, company_id, location_id, location_name,
                                update_type=new_update_type,
                                inventory_items=inventory_items,
                                transactions_inventory_item=transactions_inventory_item,
                                transactions_inventory_transaction=transactions_inventory_transaction)

            location_id = destination["id"]
            location_name = destination["name"]
            new_update_type = "relocate_destination_delivery" if update_type == "delivery" else "relocate_destination_receive"
            update_transactions(model, obj, user, line_item, company_id, location_id, location_name,
                                update_type=new_update_type,
                                inventory_items=inventory_items,
                                transactions_inventory_item=transactions_inventory_item,
                                transactions_inventory_transaction=transactions_inventory_transaction)
    elif isinstance(obj, ReturnModel):
        for line_item in obj.attributes.return_order_line_items:
            location_id = line_item.location.id
            location_name = line_item.location.name
            update_transactions(model, obj, user, line_item, company_id, location_id, location_name,
                                update_type=update_type,
                                inventory_items=inventory_items,
                                transactions_inventory_item=transactions_inventory_item,
                                transactions_inventory_transaction=transactions_inventory_transaction)
    else:
        for line_item in obj.attributes.order_line_items:
            location_id = line_item.location.id
            location_name = line_item.location.name
            update_transactions(model, obj, user, line_item, company_id, location_id, location_name,
                                update_type=update_type,
                                inventory_items=inventory_items,
                                transactions_inventory_item=transactions_inventory_item,
                                transactions_inventory_transaction=transactions_inventory_transaction)

    if status is not None:
        transactions_status.append(update_status(user, obj, status, prepared_request=True, extras=updated_data))

    transaction = transactions_inventory_item + transactions_inventory_transaction + transactions_status
    try:
        model.transact_write_items(transaction)
    except Exception:
        raise InventoryUpdateMultiProcessing()
    sku_list_by_company = {}
    sku_dict_by_company = {}
    sku_list_by_company.setdefault(company_id, set())
    if isinstance(obj, (StockAdjustmentModel, StockRelocateModel)):
        line_items = obj.attributes_dict.get('stock_line_items')
    elif isinstance(obj, ReturnModel):
        line_items = obj.attributes_dict.get('return_order_line_items')
    else:
        line_items = obj.attributes_dict.get('order_line_items')
    sku_list_by_company[company_id] = sku_list_by_company[company_id].union(
        {line_item['sku'] for line_item in line_items})
    sku_dict_by_company.update(
        {(company_id + '-' + item['sku']): item for item in line_items})
    inventory_items = {f'{item["sku"]}_{item["location_id"]}': item for item in inventory_items}
    update_indexer_inventory(sku_list_by_company, sku_dict_by_company, inventory_items=inventory_items)
    return obj


def calculate_cost(obj, company_id, order_line):
    fees = getattr(obj, 'attributes', {}).fees or []
    total_fee = sum(fee.amount for fee in fees)
    item_weight = {}
    total_weight = 0
    for item in obj.attributes.order_line_items:
        variant_obj = VariantModel.by_key({
            'id': str(item.variant_id),
            'company_id': company_id
        })
        weight = variant_obj.attributes.measurements.weight_value
        item_weight[item.sku] = weight
        total_weight += weight * item.quantity
    current_cost = order_line.sale_price + item_weight[
        order_line.sku] / total_weight * total_fee if total_weight * total_fee > 0 else order_line.sale_price + \
                                                                                        item_weight[order_line.sku]
    return current_cost


def handle_inventory_type(inventory_item, update_type, quantity, current_cost=0):
    inventory_update = {}
    quantity = Decimal(str(quantity))
    if update_type == "confirm_purchase_order":
        incoming = inventory_item.incoming + quantity
        inventory_update['incoming'] = incoming
    elif update_type == "cancel_purchase_order":
        incoming = inventory_item.incoming - quantity
        inventory_update['incoming'] = incoming
    elif update_type == "complete_purchase_order":
        incoming = inventory_item.incoming - quantity
        on_hand = inventory_item.on_hand + quantity
        available = inventory_item.available + quantity
        cost = ((inventory_item.on_hand * inventory_item.cost
                 + quantity * current_cost) / on_hand) if on_hand > 0 else current_cost
        inventory_update['incoming'] = incoming
        inventory_update['on_hand'] = on_hand
        inventory_update['cost'] = cost
        inventory_update['available'] = available
    elif update_type == "return_purchase_order":
        on_hand = inventory_item.on_hand - quantity
        available = inventory_item.available - quantity
        cost = ((inventory_item.on_hand * inventory_item.cost
                 - quantity * current_cost) / on_hand) if on_hand > 0 else 0
        inventory_update['on_hand'] = on_hand
        inventory_update['cost'] = cost
        inventory_update['available'] = available
    elif update_type == "import_purchase_orders":
        on_hand = inventory_item.on_hand + quantity
        available = inventory_item.available + quantity
        cost = (inventory_item.on_hand * inventory_item.cost
                + quantity * current_cost) / (
                       inventory_item.on_hand + quantity) if on_hand > 0 else current_cost
        inventory_update['on_hand'] = on_hand
        inventory_update['cost'] = cost
        inventory_update['available'] = available
    elif update_type == "confirm":
        available = inventory_item.available - quantity
        packing = inventory_item.packing + quantity
        inventory_update['available'] = available
        inventory_update['packing'] = packing
    elif update_type == "pay_pos":
        on_hand = inventory_item.on_hand - quantity
        available = inventory_item.available - quantity
        inventory_update['available'] = available
        inventory_update['on_hand'] = on_hand
    elif update_type == "ship":
        on_hand = inventory_item.on_hand - quantity
        packing = inventory_item.packing - quantity
        shipping = inventory_item.shipping + quantity
        inventory_update['on_hand'] = on_hand
        inventory_update['packing'] = packing
        inventory_update['shipping'] = shipping
    elif update_type == "delivered":
        shipping = inventory_item.shipping - quantity
        inventory_update['shipping'] = shipping
    elif update_type == "returning":
        returning = inventory_item.returning + quantity
        inventory_update['returning'] = returning
    elif update_type == "returned":
        returning = inventory_item.returning - quantity
        on_hand = inventory_item.on_hand + quantity
        available = inventory_item.available + quantity
        inventory_update['returning'] = returning
        inventory_update['on_hand'] = on_hand
        inventory_update['available'] = available
    elif update_type == "cancelled":
        packing = inventory_item.packing - quantity
        available = inventory_item.available + quantity
        inventory_update['packing'] = packing
        inventory_update['available'] = available
    elif update_type == "received":
        on_hand = inventory_item.on_hand + quantity
        inventory_update['on_hand'] = on_hand
    elif update_type == "relocate_source_delivery":
        on_hand = inventory_item.on_hand - quantity
        available = inventory_item.available - quantity
        shipping = inventory_item.shipping + quantity
        inventory_update['on_hand'] = on_hand
        inventory_update['available'] = available
        inventory_update['shipping'] = shipping
    elif update_type == "relocate_destination_delivery":
        incoming = inventory_item.incoming + quantity
        inventory_update['incoming'] = incoming
    elif update_type == "relocate_source_receive":
        shipping = inventory_item.shipping - quantity
        inventory_update['shipping'] = shipping
    elif update_type == "relocate_destination_receive":
        on_hand = inventory_item.on_hand + quantity
        available = inventory_item.available + quantity
        incoming = inventory_item.incoming - quantity
        inventory_update['on_hand'] = on_hand
        inventory_update['available'] = available
        inventory_update['incoming'] = incoming
    elif update_type == "adjust":
        on_hand = quantity
        available = quantity
        inventory_update['on_hand'] = on_hand
        inventory_update['available'] = available
    return inventory_update


def handle_transaction_type(table_name):
    transaction_map = {
        "purchaseOrder": TransactionType.PURCHASE.value,
        "order": TransactionType.ORDER.value,
        "returnOrder": TransactionType.RETURN.value,
        "stockAdjustment": TransactionType.ADJUSTMENT.value,
        "stockRelocate": TransactionType.RELOCATE.value,
        "package": TransactionType.PACKAGE.value
    }
    transaction_type = transaction_map.get(table_name)
    if transaction_type is None:
        raise BadRequest("Transaction type not found!")
    return transaction_type


def update_transactions(model: Model.__class__,
                        obj: Union[
                            ReturnModel, OrderModel, PackageModel, PurchaseOrderModel, StockAdjustmentModel, StockRelocateModel],
                        user, line_item, company_id, location_id, location_name, update_type, inventory_items,
                        transactions_inventory_item,
                        transactions_inventory_transaction):
    inventory_item: InventoryItemModel = InventoryItemModel.get_by_order_line_item(company_id,
                                                                                   location_id,
                                                                                   line_item,
                                                                                   location_name)
    current_cost = calculate_cost(obj, company_id, line_item) if isinstance(obj, PurchaseOrderModel) else 0
    # update inventory
    inventory_update = handle_inventory_type(inventory_item.attributes, update_type, line_item.quantity,
                                             current_cost=current_cost)
    inventory_items.append({**inventory_item.attributes_dict, **inventory_update})
    transaction_type = handle_transaction_type(model.table_name)
    inventory_transaction_payload = {
        'id': str(uuid.uuid4()),
        'inventory_item_id': inventory_item.attributes.id,
        'location_id': location_id,
        'note': line_item.note,
        'reference': str(obj.attributes.id),
        'staff_id': user['id'],
        'transaction_type': transaction_type,
        'quantity': inventory_item.attributes.on_hand,
        'cost': inventory_item.attributes.cost,
        'change': line_item.quantity,
    }
    inventory_transaction_payload = BasicAttributes.add_basic_attributes(inventory_transaction_payload,
                                                                         company_id)
    inventory_transaction = InventoryTransactionModel(inventory_transaction_payload)
    inventory_update['last_transaction'] = str(uuid.uuid4())
    transactions_inventory_item.append(
        inventory_item.update(inventory_update, prepared_request=True))
    transactions_inventory_transaction.append(inventory_transaction.save(prepared_request=True))

    calculate_same_original_sku_inventory(model, obj, user, line_item, company_id, location_id, location_name,
                                          update_type,
                                          inventory_items,
                                          transactions_inventory_item,
                                          transactions_inventory_transaction)


def calculate_same_original_sku_inventory(model: Model.__class__, obj, user, stock_line, company_id, location_id,
                                          location_name, update_type,
                                          inventory_items,
                                          transactions_inventory_item,
                                          transactions_inventory_transaction):
    # calculate other variant inventory with same original_sku
    variant_obj = VariantSkuIndex.get_variant_by_sku(stock_line.sku, company_id)
    list_variants = VariantOriginalSkuIndex.list_variants_by_original_sku(variant_obj.attributes.original_sku,
                                                                          company_id)
    new_stock_lines = [item for item in list_variants if item["sku"] != stock_line.sku]
    if len(new_stock_lines) != 0:
        current_variant = next((item for item in list_variants if item.get("sku") == stock_line.sku))
        current_unit = current_variant.get("unit", None)
        if current_unit is None:
            current_ratio = 1
        else:
            current_ratio = current_unit.get("ratio", 1)
        for item in new_stock_lines:
            line_item_unit = item.get("unit")
            if line_item_unit is None:
                line_item_ratio = 1
            else:
                line_item_ratio = line_item_unit.get("ratio", 1)
            new_stock_line = copy(stock_line)
            new_stock_line.sku = item.get("sku")
            new_stock_line.variant_id = item.get("id")
            new_stock_line.product_id = item.get("product_id")
            new_stock_line.quantity = Decimal(str(new_stock_line.quantity)) / Decimal(str(line_item_ratio)) * Decimal(
                current_ratio)
            inventory_item_same_original_sku: InventoryItemModel = InventoryItemModel.get_by_order_line_item(
                company_id,
                location_id,
                new_stock_line,
                location_name)
            inventory_update = handle_inventory_type(inventory_item_same_original_sku.attributes, update_type,
                                                     new_stock_line.quantity)
            inventory_items.append({**inventory_item_same_original_sku.attributes_dict, **inventory_update})
            transaction_type = handle_transaction_type(model.table_name)
            inventory_transaction_payload = {
                'id': str(uuid.uuid4()),
                'inventory_item_id': inventory_item_same_original_sku.attributes.id,
                'location_id': location_id,
                'note': stock_line.note,
                'reference': str(obj.attributes.id),
                'staff_id': user['id'],
                'transaction_type': transaction_type,
                'quantity': inventory_item_same_original_sku.attributes.on_hand,
                'cost': inventory_item_same_original_sku.attributes.cost,
                'change': new_stock_line.quantity,
            }
            inventory_transaction_payload = BasicAttributes.add_basic_attributes(inventory_transaction_payload,
                                                                                 company_id)
            inventory_transaction = InventoryTransactionModel(inventory_transaction_payload)
            inventory_update['last_transaction'] = str(uuid.uuid4())
            transactions_inventory_item.append(
                inventory_item_same_original_sku.update(inventory_update, prepared_request=True))

            transactions_inventory_transaction.append(inventory_transaction.save(prepared_request=True))
