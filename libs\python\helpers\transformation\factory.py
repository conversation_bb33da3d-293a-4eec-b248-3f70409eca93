from typing import Dict, Type, Optional
from .transformation import BaseTransformationHandler, Transformation, TransformationType, TransformationConfig
from .transformation_types.direct import DirectTransformationHandler
from .transformation_types.lowercase import LowercaseTransformationHandler
from .transformation_types.replace import ReplaceTransformationHandler
from .transformation_types.titlecase import Titlecase<PERSON>rans<PERSON>Hand<PERSON>
from .transformation_types.uppercase import UppercaseTransformationHandler
from .transformation_types.trim import TrimTransformationHandler
from .transformation_types.concat import ConcatTransformationHandler
from .transformation_types.substring import SubstringTransformationHandler
from .transformation_types.pad import PadTransformationHandler
from .transformation_types.split import SplitTransformationHandler
from .transformation_types.prefix import PrefixTransformationHandler
from .transformation_types.postfix import PostfixTransformationHandler


class TransformationHandlerFactory:
    """Factory for creating transformation handlers"""
    
    # Static registry for handler classes
    _handlers: Dict[TransformationType, Type[BaseTransformationHandler]] = {
        TransformationType.DIRECT: DirectTransformationHandler,
        TransformationType.UPPERCASE: UppercaseTransformationHandler,
        TransformationType.LOWERCASE: LowercaseTransformationHandler,
        TransformationType.REPLACE: ReplaceTransformationHandler,
        TransformationType.TITLECASE: TitlecaseTransformationHandler,
        TransformationType.TRIM: TrimTransformationHandler,
        TransformationType.CONCAT: ConcatTransformationHandler,
        TransformationType.SUBSTRING: SubstringTransformationHandler,
        TransformationType.PAD: PadTransformationHandler,
        TransformationType.SPLIT: SplitTransformationHandler,
        TransformationType.PREFIX: PrefixTransformationHandler,
        TransformationType.POSTFIX: PostfixTransformationHandler,
    }
    
    # Cache for transformation configurations
    _configs: Dict[TransformationType, TransformationConfig] = {}
    
    @classmethod
    def register_handler(cls, transformation_type: TransformationType, handler_class: Type[BaseTransformationHandler]) -> None:
        """
        Register a new transformation handler
        
        Args:
            transformation_type: The type of transformation
            handler_class: The handler class for the transformation
        """
        cls._handlers[transformation_type] = handler_class
        # Cache the configuration
        cls._configs[transformation_type] = handler_class.config_definition
    
    @classmethod
    def get_handler_class(cls, transformation_type: TransformationType) -> Optional[Type[BaseTransformationHandler]]:
        """Get the handler class for a transformation type"""
        return cls._handlers.get(transformation_type)
    
    @classmethod
    def get_config(cls, transformation_type: TransformationType) -> Optional[TransformationConfig]:
        """
        Get the configuration for a transformation type
        
        Args:
            transformation_type: The type of transformation
            
        Returns:
            The transformation configuration if registered, None otherwise
        """
        # If config not cached, try to get it from handler
        if transformation_type not in cls._configs and transformation_type in cls._handlers:
            cls._configs[transformation_type] = cls._handlers[transformation_type].config_definition
        return cls._configs.get(transformation_type)
    
    @classmethod
    def get_all_configs(cls) -> Dict[TransformationType, TransformationConfig]:
        """Get configurations for all registered transformations"""
        # Ensure all configs are cached
        for transformation_type, handler_class in cls._handlers.items():
            if transformation_type not in cls._configs:
                cls._configs[transformation_type] = handler_class.config_definition
        return cls._configs
    
    @classmethod
    def create(cls, transformation: Transformation) -> BaseTransformationHandler:
        """
        Creates a transformation handler based on the transformation type
        
        Args:
            transformation: The transformation configuration
            
        Returns:
            An instance of the appropriate transformation handler
            
        Raises:
            ValueError: If the transformation type is not supported
        """
        handler_class = cls.get_handler_class(transformation.type)
        if not handler_class:
            raise ValueError(f"Unsupported transformation type: {transformation.type}")
        
        return handler_class(transformation) 