from dataclasses import dataclass, field

from marshmallow import fields
from ..connection_base import OAuth2ConnectionAttributes, OAuth2ConnectionSchema, OAuth2Connection, \
    OAuth2SettingsSchema, OAuth2Settings


class ShopifyOauthSettingsSchema(OAuth2SettingsSchema):
    api_key = fields.Str(required=True)
    secret_key = fields.Str(required=True)
    shop_url = fields.Url(required=True)
    api_version = fields.Str(default="2025-04")


@dataclass
class ShopifyOAuthSettings(OAuth2Settings):
    api_key: str = None
    secret_key: str = None
    shop_url: str = None
    api_version: str = "2025-04"


@dataclass
class ShopifyOAuthConnectionAttributes(OAuth2ConnectionAttributes):
    channel_name: str = field(default='shopify_oauth')
    settings: ShopifyOAuthSettings = field(default_factory=ShopifyOAuthSettings)


class ShopifyOAuthConnectionSchema(OAuth2ConnectionSchema):
    channel_name = fields.Str(required=True)
    settings = fields.Nested(ShopifyOauthSettingsSchema(ShopifyOAuthSettings))


class ShopifyOAuthConnection(OAuth2Connection):
    attributes: ShopifyOAuthConnectionAttributes = None
    attributes_schema = ShopifyOAuthConnectionSchema
    attributes_class = ShopifyOAuthConnectionAttributes
