set_fetch_action_settings:
  handler: src.apis.setting.set_fetch_action_settings
  events:
    - httpApi:
        path: /connection/{connection_id}/fetch_action_settings
        method: post
        authorizer:
          name: optiAuthorizer

set_publish_act_settings:
  handler: src.apis.setting.set_publish_action_settings
  events:
    - httpApi:
        path: /connection/{connection_id}/publish_action_settings
        method: post
        authorizer:
          name: optiAuthorizer

update_webhook_settings:
  handler: src.apis.setting.update_webhook_settings
  events:
    - httpApi:
        path: /connection/{connection_id}/webhook_settings
        method: put
        authorizer:
          name: optiAuthorizer

update_dynamic_settings:
  handler: src.apis.setting.update_dynamic_settings
  events:
    - httpApi:
        path: /connection/{connection_id}/dynamic_settings
        method: put
        authorizer:
          name: optiAuthorizer

remove_dynamic_settings:
  handler: src.apis.setting.remove_dynamic_settings
  events:
    - httpApi:
        path: /connection/{connection_id}/remove_dynamic_settings
        method: put
        authorizer:
          name: optiAuthorizer

synchronization:
  handler: src.apis.setting.synchronization
  events:
    - httpApi:
        path: /connection/synchronization
        method: post
        authorizer:
          name: opti<PERSON><PERSON><PERSON>zer
          