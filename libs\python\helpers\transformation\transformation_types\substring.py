from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class SubstringTransformationHandler(BaseTransformationHandler):
    """Handler for substring transformation"""
    
    config_definition = TransformationConfig(
        label="Substring",
        description="Extract part of text",
        direction=TransformationDirection(
            title="Extract Substring",
            content="Extract a portion of text based on start and end positions",
            example='"Hello World" → "Hello" (start: 0, end: 5)',
            value_type="Single Value"
        ),
        config_fields=[
            {
                "key": "start",
                "label": "Start Position",
                "type": "number",
                "placeholder": "0",
                "description": "The starting position to extract from (0-based)"
            },
            {
                "key": "end",
                "label": "End Position",
                "type": "number",
                "placeholder": "5",
                "description": "The ending position to extract to (exclusive)"
            }
        ]
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Extracts a substring from the value if it's not None"""
        if value is None:
            return None
            
        if not self.transformation.config or 'start' not in self.transformation.config or 'end' not in self.transformation.config:
            return value
            
        try:
            start = int(self.transformation.config['start'])
            end = int(self.transformation.config['end'])
            return str(value)[start:end]
        except (ValueError, IndexError):
            return value 