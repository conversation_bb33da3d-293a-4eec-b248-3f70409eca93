from copy import copy
from dataclasses import dataclass

from marshmallow import fields

from helpers.permission import permissions, PLANS, activate_permissions
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


@dataclass
class RoleAttributes(BasicAttributes):
    id: str = None
    role: str = None
    permission: dict = None
    full_permission: bool = False
    note: str = None


class RoleSchema(BasicAttributesSchema):
    id = fields.UUID(required=True)
    role = fields.Str(required=True)
    note = fields.Str(allow_none=True)
    permission = fields.Dict(allow_none=True)
    full_permission = fields.Bool(allow_none=True, default=False)


class RoleModel(BasicModel):
    key__id = 'id'
    key__ranges = ['company_id']
    table_name = 'role'
    attributes: RoleAttributes
    attributes_schema = RoleSchema
    attributes_class = RoleAttributes

    query_mapping = {
        'query': ['id', 'role'],
        'filter': {
            'id': 'query',
            'full_permission': 'boolean',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'note': 'note',
        }
    }

    @classmethod
    def initialize_data(cls, user_attributes_obj, initialized_data):
        company_id = user_attributes_obj.get('custom:company_id')
        plan = user_attributes_obj.get('custom:plan')
        try:
            plan = PLANS[plan]
        except ValueError:
            plan = PLANS['POS']
        admin_features = activate_permissions(plan)
        pos_features = activate_permissions(['pos'])
        cms_features = activate_permissions(['blog', 'blog_category'])

        manager_features_key = ['product', 'variant', 'brand', 'category', 'order', 'return_order', 'package',
                                'shipping_provider',
                                'stock_adjustment', 'stock_relocate', 'location', 'inventory_item', 'discount',
                                'voucher', 'voucher_code',
                                'customer', 'purchase_orders', 'supplier']
        manager_features = activate_permissions([item for item in manager_features_key if item in plan])

        customer_features_key = ['product', 'variant', 'brand', 'category', 'order', 'return_order', 'package']
        customer_features = activate_permissions([item for item in customer_features_key if item in plan])

        accountant_features_key = ['product', 'variant', 'brand', 'category', 'order', 'return_order', 'package',
                                   'shipping_provider', 'stock_adjustment', 'stock_relocate', 'location',
                                   'inventory_item', 'report']
        accountant_features = activate_permissions([item for item in accountant_features_key if item in plan])

        sale_features_key = ['product', 'variant', 'brand', 'category', 'order', 'return_order', 'package',
                             'shipping_provider']
        sale_features = activate_permissions([item for item in sale_features_key if item in plan])

        data = [
            {"role": "Admin", "permission": admin_features},
            {"role": "POS", "permission": pos_features},
            {"role": "CMS", "permission": cms_features},
            {"role": "Manager", "permission": manager_features},
            {"role": "Customer", "permission": customer_features},
            {"role": "Accountant", "permission": accountant_features},
            {"role": "Sale", "permission": sale_features},
        ]
        init_data = [BasicAttributes.add_basic_attributes(item, company_id) for item in data]
        initialized_data[cls.table_name] = init_data
        cls.batch_add(init_data)
