import math
from typing import Dict, Any, List
from decimal import Decimal

from helpers.custom_jsonpath import get_value_from_jsonpath
from ...base import DestinationTransformer
from integrations.channels.action_types import ActionGroup
from models.product.product import ProductAttributesSchema


class TiktokShopProductTransformer(DestinationTransformer):
    channel_name = 'tiktok_shop'
    object_type = 'product'

    def _map_object_type_to_action_type(self) -> str:
        return 'create_product'

    @staticmethod
    def _transform_images(images: List[Dict]) -> List[Dict]:
        """Transform image list to TikTok Shop format"""
        # if len(images) == 0:
        #     raise ValueError("No images found. Required at least 1 image")
        return [{'src': img.get('src', ''), 'name': img.get('name', '')} for img in images[:9] if img.get('src')]

    def _transform_inventories(self, inventories: []):
        dynamic_settings = self.connection_settings.get('dynamic_settings', {})
        mapping_warehouses = dynamic_settings.get('mapping_warehouses', {})

        transformed_inventories = []
        for inventory in inventories:
            data = {
                "warehouse_id": mapping_warehouses.get(
                    get_value_from_jsonpath(inventory, '$.location_id'),
                    None),
                "quantity": get_value_from_jsonpath(inventory, '$.available' or 1)
            }
            transformed_inventories.append(data)
        return transformed_inventories

    def _transform_variants(self, variants: List[Dict], location_mapping: Dict[str, str], currency: str,
                            mapping_warehouses) -> \
            List[Dict]:
        """Transform variants to TikTok Shop format"""
        tiktok_variants = []

        for variant in variants:
            variant_sku = variant.get('sku', '')
            if variant is None:
                raise ValueError(f"Variant can not be none")
            if not variant_sku:
                raise ValueError("SKU of variant is required")
            if len(variant_sku) >= 50:
                raise ValueError(f"{variant_sku} sku requires less than 50 characters")
            # Xử lý giá
            original_price = next(
                (v['price'] for v in variant.get('prices', [])
                 if v.get('price_group', {}).get('id') == "RETAILS"),
                1
            )
            if original_price == 0:
                raise ValueError(f"{variant_sku} price is 0. Tiktok requires more than 0")

            sale_attributes = []
            supplementary_sku_images = self._transform_images(variant.get('images', []))
            if variant.get('option1'):
                sale_attributes.append({
                    "value_name": variant.get('option1', ''),
                    "name": variant.get('optionTitle1', ''),
                    "supplementary_sku_images": supplementary_sku_images,
                })
            if variant.get('option2'):
                sale_attributes.append({
                    "value_name": variant.get('option2', ''),
                    "name": variant.get('optionTitle2', ''),
                    "supplementary_sku_images": supplementary_sku_images,
                })
            if variant.get('option3'):
                sale_attributes.append({
                    "value_name": variant.get('option3', ''),
                    "name": variant.get('optionTitle3', ''),
                    "supplementary_sku_images": supplementary_sku_images,
                })
            if next((item for item in sale_attributes if item.get('name') and len(item.get('name')) > 20), None):
                raise ValueError(f"The attribute name of sku in a product cannot exceed 20 characters")

            tiktok_variant = {
                "price": {"amount": str(original_price), "currency": currency},
                "seller_sku": variant.get('sku', ''),
                "sales_attributes": sale_attributes,
                "external_sku_id": variant.get('sku', ''),
                "inventory": self._transform_inventories(get_value_from_jsonpath(variant, '$.inventories') or [])
                # "list_price": [{"amount": str(v['price']), "currency": currency} for v in variant.get('prices', [])],
            }
            inventories = []
            if isinstance(variant.get('inventories'), dict):
                for inventory in variant.get('inventories', {}).get('inventories', []):
                    location_id = inventory.get('location_id')
                    quantity = inventory.get('quantity') or 0
                    if location_id and location_mapping.get(location_id):
                        inventory.append({
                            "available_stock": int(quantity) if int(quantity) > 0 else 0,
                            "warehouse_id": location_mapping.get(location_id)
                        })
            if inventories:
                tiktok_variant["inventory"] = inventories

            tiktok_variants.append(tiktok_variant)

        if len(tiktok_variants) >= 100:
            raise ValueError("The number of sku in a product cannot exceed 100")
        return tiktok_variants

    def _transform_measurements(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform measurements to TikTok Shop format"""
        measurements = data.get('measurements') or data.get('variants', [])[0].get('measurements')
        if measurements is None:
            raise ValueError(f"Measurements is required")
        return {
            "package_dimensions": {
                "length": str(measurements.get('length', 1) or 1),
                "width": str(measurements.get('width', 1) or 1),
                "height": str(measurements.get('height', 1) or 1),
                "unit": "CENTIMETER",
            },
            "package_weight": {"value": str(float(measurements.get('weight_value', 0)) or 1),
                               "unit": "KILOGRAM"},
        }

    def _transform_product_attributes(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform product attributes to TikTok Shop format"""
        return [
            {
                "name": attr.get('name', ''),
                "values": [
                    {
                        "name": value,
                    }
                    for value in attr.get('values', [])
                ],
            }
            for attr in data
        ]

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform product data to TikTok Shop format"""
        dynamic_settings = self.connection_settings.get('dynamic_settings', {})
        location_mapping = dynamic_settings.get('location_mapping', {})
        currency = dynamic_settings.get('currency', 'USD')
        is_cod_allowed = dynamic_settings.get('is_cod_allowed', True)
        mapping_warehouses = dynamic_settings.get('mapping_warehouses', {})

        category_name = data.get('category', {}).get('name', '') if data.get('category', {}) is not None else None
        if category_name is None:
            raise ValueError(f"Category is required")
        brand_name = data.get('brand', {}).get('name', '') if data.get('brand', {}) is not None else None
        measurements = self._transform_measurements(data)

        # Basic product info
        product = {
            "save_mode": "LISTING",
            "description": data.get('description', '') or 'unknown',
            "brand": {"name": brand_name},
            "category": {"name": category_name},
            "main_images": self._transform_images(data.get('images', [])),
            "skus": self._transform_variants(
                data.get('variants', []),
                location_mapping,
                currency,
                mapping_warehouses
            ),
            "title": data.get('name', ''),
            "is_cod_allowed": is_cod_allowed,
            "product_attributes": self._transform_product_attributes(data.get('options', [])),
            **measurements,
        }
        return product

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.product

    @property
    def input_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())

    @property
    def output_schema(self):
        return {
            "type": "object",
            "properties": {
                "title": {"type": "string"},
                "description": {"type": "string"},
                "category": {"type": "object",
                             "properties": {
                                 "name": {"type": "string"}
                             }
                             },
                "brand": {"type": "object",
                          "properties": {
                              "name": {"type": "string"}
                          }
                          },
                "main_images": {
                    "type": "array",
                    "items": {"type": "object",
                              "properties": {
                                  "url": {"type": "string"},
                                  "name": {"type": "string"}
                              }
                              }
                },
                "is_cod_allowed": {"type": "boolean"},
                "product_attributes": {
                    "type": "array",
                    "items": {"type": "object",
                              "properties": {
                                  "name": {"type": "string"},
                                  "values": {"type": "array", "items": {"type": "object",
                                                                        "properties": {
                                                                            "name": {"type": "string"}
                                                                        }
                                                                        }}
                              }
                              }
                },
                "package_dimensions": {
                    "type": "object",
                    "properties": {
                        "length": {"type": "integer"},
                        "width": {"type": "integer"},
                        "height": {"type": "integer"},
                        "unit": {"type": "string"},
                    }
                },
                "package_weight": {
                    "type": "object",
                    "properties": {
                        "value": {"type": "string"},
                        "unit": {"type": "string"}
                    }
                },
                "skus": {
                    "type": "array",
                    "items": {"type": "object",
                              "properties": {
                                  "price": {
                                      "type": "object",
                                      "properties": {
                                          "amount": {"type": "string"},
                                          "currency": {"type": "string"}
                                      }
                                  },
                                  "seller_sku": {"type": "string"},
                                  "sales_attributes": {
                                      "type": "array",
                                      "items": {"type": "object",
                                                "properties": {
                                                    "name": {"type": "string"},
                                                    "value_name": {"type": "string"},
                                                    "supplementary_sku_images": {
                                                        "type": "array",
                                                        "items": {"type": "object",
                                                                  "properties": {
                                                                      "url": {"type": "string"},
                                                                      "name": {"type": "string"}
                                                                  }
                                                                  }
                                                    }
                                                }
                                                }
                                  },
                                  "inventory": {
                                      "type": "array",
                                      "items": {
                                          "type": "object",
                                          "properties": {
                                              "warehouse_id": {"type": "string"},
                                              "available_stock": {"type": "integer"}
                                          }
                                      }
                                  },
                                  "list_price": {
                                      "type": "array",
                                      "items": {
                                          "type": "object",
                                          "properties": {
                                              "amount": {"type": "string"},
                                              "currency": {"type": "string"}
                                          }
                                      }
                                  }
                              }
                              }
                }
            }
        }
