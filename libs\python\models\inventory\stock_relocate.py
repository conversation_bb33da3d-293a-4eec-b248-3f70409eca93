from dataclasses import dataclass
from decimal import Decimal
from typing import List

from marshmallow import fields
from marshmallow_enum import <PERSON>umField
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.default_obj import DefaultObject, DefaultObjectSchema
from models.common.fee import Fee, FeeSchema
from models.common.status import StockRelocateStatus
from models.common.stock_line import StockLineItem, StockLineItemSchema


@dataclass
class StockRelocate(Attributes):
    id: str = None
    code: str = None
    source: DefaultObject = None
    destination: DefaultObject = None
    stock_line_items: List[StockLineItem] = None
    creating_staff: DefaultObject = None
    delivering_staff: DefaultObject = None
    receiving_staff: DefaultObject = None
    cancelling_staff: DefaultObject = None
    status: StockRelocateStatus = StockRelocateStatus.AWAIT_DELIVERY
    other_fees: List[Fee] = None
    quantity: Decimal = 0
    delivered_total: Decimal = 0
    received_quantity: Decimal = 0
    received_total: Decimal = 0
    return_quantity: Decimal = 0
    return_total: Decimal = 0
    delivered_at: str = None
    received_at: str = None
    cancelled_at: str = None
    note: str = None
    tags: str = None
    reference: str = None


@dataclass
class StockRelocateAttributes(StockRelocate, BasicAttributes):
    pass


class StockRelocateSchema(AttributesSchema):
    id = fields.Str(required=True)
    code = fields.Str(required=True)
    source = fields.Nested(DefaultObjectSchema(DefaultObject), required=True)
    destination = fields.Nested(DefaultObjectSchema(DefaultObject), required=True)
    stock_line_items = fields.List(fields.Nested(StockLineItemSchema(StockLineItem)), required=True)
    creating_staff = fields.Nested(DefaultObjectSchema(DefaultObject), required=True)
    delivering_staff = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    receiving_staff = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    cancelling_staff = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    status = EnumField(StockRelocateStatus, allow_none=True, default=StockRelocateStatus.AWAIT_DELIVERY)
    other_fees = fields.List(fields.Nested(FeeSchema(Fee)), allow_none=True)
    quantity = fields.Decimal(required=True)
    delivered_total = fields.Decimal(required=True)
    received_quantity = fields.Decimal(allow_none=True)
    received_total = fields.Decimal(allow_none=True)
    return_quantity = fields.Decimal(allow_none=True)
    return_total = fields.Decimal(allow_none=True)
    delivered_at = fields.Str(allow_none=True)
    received_at = fields.Str(allow_none=True)
    cancelled_at = fields.Str(allow_none=True)
    note = fields.Str(allow_none=True)
    tags = fields.Str(allow_none=True)
    reference = fields.Str(allow_none=True)


class StockRelocateAttributesSchema(StockRelocateSchema, BasicAttributesSchema):
    pass


class StockRelocateModel(BasicModel):
    table_name = 'stockRelocate'
    attributes: StockRelocateAttributes
    attributes_schema = StockRelocateAttributesSchema
    attributes_class = StockRelocateAttributes

    query_mapping = {
        'query': ['code', 'id', 'reference'],
        'filter': {
            'id': 'query',
            'status': 'query',
            'source.id': 'query',
            'source.channel_name': 'query',
            'destination.id': 'query',
            'creating_staff.id': 'query',
            'delivering_staff.id': 'query',
            'receiving_staff.id': 'query',
            'tags': 'tags',
            'quantity': 'number',
            'delivered_total': 'number',
            'company_id': 'query',
            'delivered_at': 'range',
            'received_at': 'range',
            'cancelled_at': 'range',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'source': 'source',
            'destination': 'destination',
            'creating_staff': 'creating_staff',
            'delivering_staff': 'delivering_staff',
            'receiving_staff': 'receiving_staff',
            'cancelling_staff': 'cancelling_staff',
        }
    }
