import pendulum
from elasticsearch import NotFoundError
from nolicore.utils.exceptions import BadRequest

from helpers.errors import LoyaltyProgramDateInvalid
from helpers.utils import RESOURCE_SERVICE
from models.loyalty.reward_program import RewardProgramModel, RewardType


def validate_reward_program(company_id, reward_type: RewardType, start_date, expiry_date=None, reward_program_id=None):
    if not reward_type:
        raise BadRequest("Required reward_type")
    if not start_date:
        raise BadRequest("Required start_date")
    datetime_obj1 = pendulum.parse(start_date)
    if expiry_date is not None:
        datetime_obj2 = pendulum.parse(expiry_date)
        if datetime_obj1 < datetime_obj2:
            raise LoyaltyProgramDateInvalid()
    try:
        search_params = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "company_id.keyword": company_id
                            }
                        },
                        {
                            "term": {
                                "type": reward_type
                            }
                        },
                    ],
                    "should": [
                        {
                            "range": {
                                "expiry_date": {
                                    "gt": start_date,
                                }
                            }
                        },
                        {
                            "bool": {
                                "must_not": {
                                    "exists": {
                                        "field": "expiry_date"
                                    }
                                }
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
        }
        # If reward_program_id is provided, add a must_not clause to exclude matching ids
        if reward_program_id is not None:
            search_params["query"]["bool"]["must_not"] = [
                {
                    "term": {
                        "id.keyword": reward_program_id
                    }
                }
            ]
        response = RewardProgramModel.report(search_params, service=RESOURCE_SERVICE).body['hits']['hits']
    except NotFoundError as ex:
        response = []
    result = [item['_source'] for item in response]

    if len(result) > 0:
        raise BadRequest("Can not create a reward program in effective loyalty program date time!")
