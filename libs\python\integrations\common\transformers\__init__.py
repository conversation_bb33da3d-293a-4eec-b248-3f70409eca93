import os
import importlib
from typing import Dict, Type, Optional, Any
from .base import SourceTransformer, DestinationTransformer, BaseTransformer


class TransformerRegistry:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TransformerRegistry, cls).__new__(cls)
            cls._instance.source_transformers = {}
            cls._instance.destination_transformers = {}
        return cls._instance

    def register_transformer(self, transformer_class: Type[BaseTransformer]):
        channel_name = transformer_class.channel_name
        object_type = transformer_class.object_type

        if issubclass(transformer_class, SourceTransformer):
            if channel_name not in self.source_transformers:
                self.source_transformers[channel_name] = {}
            self.source_transformers[channel_name][object_type] = transformer_class
        elif issubclass(transformer_class, DestinationTransformer):
            if channel_name not in self.destination_transformers:
                self.destination_transformers[channel_name] = {}
            self.destination_transformers[channel_name][object_type] = transformer_class

    def get_source_transformer(self, channel_name: str, object_type: str, connection_settings: Dict[str, Any]) -> \
            Optional[SourceTransformer]:
        transformer_class = self.source_transformers.get(channel_name, {}).get(object_type)
        return transformer_class(connection_settings) if transformer_class else None

    def get_destination_transformer(self, channel_name: str, object_type: str, connection_settings: Dict[str, Any]) -> \
            Optional[DestinationTransformer]:
        transformer_class = self.destination_transformers.get(channel_name, {}).get(object_type)
        return transformer_class(connection_settings) if transformer_class else None


registry = TransformerRegistry()


def load_transformers():
    base_dir = os.path.dirname(__file__)
    for transformer_type in ['sources', 'destinations']:
        type_dir = os.path.join(base_dir, transformer_type)
        for root, dirs, files in os.walk(type_dir):
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    module_path = os.path.relpath(os.path.join(root, file), base_dir)
                    module_name = os.path.splitext(module_path.replace(os.path.sep, '.'))[0]
                    module = importlib.import_module(f'.{module_name}', package='integrations.common.transformers')
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)
                        if isinstance(attr, type) and issubclass(attr, BaseTransformer) and attr not in (
                                SourceTransformer, DestinationTransformer):
                            registry.register_transformer(attr)


# Call load_transformers() when the module is imported
load_transformers()

# Export the registry
__all__ = ['registry']
