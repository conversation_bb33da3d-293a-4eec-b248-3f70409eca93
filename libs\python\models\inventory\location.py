from dataclasses import dataclass
from typing import List

from marshmallow import fields

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.address import Address, AddressSchema
from models.common.meta_data import MetaData, MetaDataSchema
from models.media.image import Image, ImageSchema


@dataclass
class LocationAttributes(Address, BasicAttributes):
    images: List[Image] = None
    description: str = None
    open_time: str = None
    meta_data: MetaData = None


class LocationAttributesSchema(BasicAttributesSchema, AddressSchema):
    images = fields.List(fields.Nested(ImageSchema(Image), allow_none=True))
    description = fields.Str(allow_none=True)
    open_time = fields.Str(allow_none=True)
    meta_data = fields.Nested(MetaDataSchema(MetaData), allow_none=True)


class LocationModel(BasicModel):
    table_name = 'location'
    attributes: LocationAttributes
    attributes_schema = LocationAttributesSchema
    attributes_class = LocationAttributes

    query_mapping = {
        'query': ['name', 'id', 'phone'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'description': 'description',
            'open_time': 'open_time',
            'images': 'images'
        }
    }

    @classmethod
    def initialize_data(cls, user_attributes_obj, initialized_data):
        company_id = user_attributes_obj.get('custom:company_id')
        data = [
            {"name": "Default"}
        ]
        init_data = [BasicAttributes.add_basic_attributes(item, company_id) for item in data]
        initialized_data[cls.table_name] = init_data
        cls.batch_add(init_data)


class LocationCompanyIdIndex(LocationModel):
    key__id = 'company_id'
    key__ranges = ['id']
    index_name = 'locationCompanyIdIndex'

    @classmethod
    def get_all_locations(cls, company_id, limit=1000):
        try:
            return cls.list({'company_id': company_id}, limit=limit).get('Items', [])
        except IndexError:
            return []
