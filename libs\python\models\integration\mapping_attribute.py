from dataclasses import dataclass
from enum import Enum
from typing import Optional, Dict, Any, List

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import AttributesSchema, Attributes

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema
from integrations.channels.action_types import ActionGroup
from nolicore.utils.utils import logger
from helpers.transformation.transformation import TransformationType


@dataclass
class Transformation(Attributes):
    type: TransformationType = None
    config: Dict[str, Any] = None


class TransformationSchema(AttributesSchema):
    type = EnumField(TransformationType, required=True)
    config = fields.Dict(required=False, allow_none=True)


@dataclass
class Mapping(Attributes):
    source_field: str = None
    destination_field: str = None
    error_message: Optional[str] = None
    transformations: Optional[List[Transformation]] = None
    enabled: bool = True


class MappingSchema(AttributesSchema):
    source_field = fields.Str(required=True)
    destination_field = fields.Str(required=True)
    error_message = fields.Str(allow_none=True)
    transformations = fields.List(fields.Nested(TransformationSchema(Transformation)), allow_none=True)
    enabled = fields.Bool(required=True, default=True)


@dataclass
class MappingAttributeAttributes(BasicAttributes):
    connection_id: str = None
    type: ActionGroup = None
    channel_name: str = None
    mappings: List[Mapping] = None


class MappingAttributeAttributesSchema(BasicAttributesSchema):
    connection_id = fields.Str(required=True)
    type = EnumField(ActionGroup, required=True)
    channel_name = fields.Str(required=True)
    mappings = fields.List(fields.Nested(MappingSchema(Mapping)), required=True)


class MappingAttributeModel(BasicModel):
    table_name = 'mappingAttribute'
    attributes: MappingAttributeAttributes
    attributes_schema = MappingAttributeAttributesSchema
    attributes_class = MappingAttributeAttributes

    query_mapping = {
        'query': ['id', 'type', 'channel_name'],
        'filter': {
            'id': 'query',
            'type': 'query',
            'channel_name': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
    }


class MappingAttributeTypeIndex(MappingAttributeModel):
    key__id = 'connection_id'
    key__ranges = ['type']
    index_name = 'mappingAttributeTypeIndex'

    @classmethod
    def get_mapping_attribute(cls, connection_id, object_type):
        try:
            return cls(cls.list({
                cls.key__id: connection_id,
                cls.key__ranges[0]: object_type
            }, limit=10).get('Items', [])[0])
        except Exception as e:
            logger.error(f"No data found in get_mapping_attribute: {str(e)}")
            return None

    @classmethod
    def put(cls, connection_id, object_type, mappings, company_id=None, channel_info=None):
        try:
            record = cls(cls.list({
                cls.key__id: connection_id,
                cls.key__ranges[0]: object_type
            }, limit=10).get('Items', [])[0])
        except Exception as e:
            record = None

        if record:
            record.update({"mappings": mappings})
        else:
            record = cls.create(company_id, {'connection_id': connection_id, 'type': object_type,
                                             'channel_name': channel_info['channel_name'],
                                             'mappings': mappings})
        return record
