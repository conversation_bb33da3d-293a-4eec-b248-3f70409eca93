import json
import unittest
import uuid

from tests.base import BaseTestCase
from connections.src.apis.setting import set_fetch_action_settings, set_publish_action_settings, \
    update_webhook_settings
from integrations.channels.action_types import ActionGroup, ActionType
from models.integration.connection import ConnectionModel


class SettingApisIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add any global setup here, like initializing a test database

    def setUp(self):
        self.maxDiff = None
        self.company_id = '9e61d187-426a-45ec-914d-7aea8ca7d42d'
        self.user_id = str(uuid.uuid4())
        self.username = f"user_{self.user_id}"
        self.connection_id = '8c5a6527-b38b-4605-b386-96e4adc72b1e'

    def tearDown(self):
        # Clean up the test connection
        connection = ConnectionModel.get(self.company_id, self.connection_id)
        if connection:
            connection.delete()

    def create_lambda_event(self, body, path_parameters=None):
        event = {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }
        if path_parameters:
            event['pathParameters'] = path_parameters
        return event

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_set_fetch_action_settings_success(self):
        # Arrange
        event_body = {
            "actions": {
                "get_product": {
                    "enabled": True,
                    "rate_limit": 60,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "custom_settings": {
                        "limit": 50,
                        "status": "active"
                    },
                    "schedule": {
                        "type": "interval",
                        "value": "3600"
                    },
                    "status": {
                        "last_run": None,
                        "next_run": None
                    }
                }
            }
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = set_fetch_action_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Fetch action settings updated successfully')

        # Verify the settings were actually updated in the database
        updated_connection = ConnectionModel.get(self.company_id, self.connection_id)
        fetch_settings = updated_connection.get_fetch_action_settings(ActionGroup.product, ActionType.get_product)
        self.assertTrue(fetch_settings.enabled)
        self.assertEqual(fetch_settings.rate_limit, 60)
        self.assertEqual(fetch_settings.custom_settings['limit'], 50)

    def test_set_publish_action_settings_success(self):
        # Arrange
        event_body = {
            "actions": {
                "sync_product": {
                    "enabled": True,
                    "payload_template": {
                        "title": "{{product.title}}",
                        "description": "{{product.description}}",
                        "price": "{{product.price}}"
                    },
                    "rate_limit": 30,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "custom_settings": {
                        "update_existing": True,
                        "create_new": False
                    }
                }
            }
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = set_publish_action_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Publish action settings updated successfully')

        # Verify the settings were actually updated in the database
        updated_connection = ConnectionModel.get(self.company_id, self.connection_id)
        publish_settings = updated_connection.get_publish_action_settings(ActionGroup.product, ActionType.sync_product)
        self.assertTrue(publish_settings.enabled)
        self.assertEqual(publish_settings.rate_limit, 30)
        self.assertEqual(publish_settings.custom_settings['update_existing'], True)

    def test_update_webhook_settings_success(self):
        # Arrange
        event_body = {
            "webhook_settings": {
                "orders/create": True,
                "products/create": False,
                "inventory_levels/update": False,
                "orders/updated": False,
                "products/update": False
            }
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = update_webhook_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Webhook settings updated successfully')

        # Verify the settings were actually updated in the database
        updated_connection = ConnectionModel.get(self.company_id, self.connection_id)
        webhook_settings = updated_connection.get_webhook_settings()
        self.assertEqual(webhook_settings, event_body['webhook_settings'])

    def test_set_fetch_action_settings_invalid_action(self):
        # Arrange
        event_body = {
            "actions": {
                "invalid_action": {
                    "enabled": True
                }
            }
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = set_fetch_action_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertIn('Invalid action type: invalid_action', response_body['message'])

    def test_update_webhook_settings_invalid_format(self):
        # Arrange
        event_body = {
            "webhook_settings": "invalid_format"
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = update_webhook_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertIn('Invalid webhook settings format', response_body['message'])


if __name__ == '__main__':
    unittest.main()
