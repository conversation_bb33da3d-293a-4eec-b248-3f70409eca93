from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import List

from marshmallow import fields
from marshmallow_enum import EnumField

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes
from models.inventory.location import LocationAttributes, LocationAttributesSchema
from models.media.image import ImageSchema, Image
from models.promotion.discount_info.discount_product_by_order_total import DiscountExtraProductAttributes, \
    GiftOrderTotalAttributes, DiscountExtraProductAttributesSchema, GiftOrderTotalAttributesSchema
from models.promotion.discount_info.discount_order_total import DiscountOrderTotalAttributes, \
    DiscountOrderTotalAttributesSchema
from models.promotion.discount_info.discount_product import DiscountProductAttributes, DiscountProductAttributesSchema
from models.promotion.discount_info.discount_purchase_product import DiscountPurchaseProductAttributes, \
    DiscountPurchaseProductAttributesSchema, GiftPurchaseProductAttributes, \
    GiftPurchaseProductAttributesSchema
from models.promotion.discount_info.discount_quantity import DiscountQuantityAttributes, \
    DiscountQuantityAttributesSchema
from models.promotion.discount_info.effective_customer import EffectiveCustomerAttributes, \
    EffectiveCustomerAttributesSchema


class DiscountStatus(Enum):
    RUNNING = 'RUNNING'
    PAUSED = 'PAUSED'
    CANCELED = 'CANCELED'
    DRAFT = 'DRAFT'


class DiscountType(Enum):
    BY_ORDER_TOTAL = 'BY_ORDER_TOTAL'
    BY_PRODUCT = 'BY_PRODUCT'
    PRODUCT_BY_ORDER_TOTAL = 'PRODUCT_BY_ORDER_TOTAL'
    BY_QUANTITY = 'BY_QUANTITY'
    BY_PURCHASE_PRODUCT = 'BY_PURCHASE_PRODUCT'
    GIFT_BY_ORDER_TOTAL = 'GIFT_BY_ORDER_TOTAL'
    GIFT_BY_PURCHASE_PRODUCT = 'GIFT_BY_PURCHASE_PRODUCT'


@dataclass
class DiscountAttributes(BasicAttributes):
    name: str = None
    discount_code: str = None
    max_using_times: Decimal = None
    start_at: str = None
    status: DiscountStatus = DiscountStatus.DRAFT
    type: DiscountType = None
    publish: bool = None
    used_times: Decimal = 0
    is_stackable: bool = False
    locations: List[LocationAttributes] = None
    sources: List[LocationAttributes] = None
    customers: EffectiveCustomerAttributes = None
    description: str = None
    image: Image = None
    expire_at: str = None
    effective_time_range: str = None
    effective_days_of_week: List[Decimal] = None
    effective_days_of_month: List[Decimal] = None
    effective_months: List[Decimal] = None
    discount_by_order_total: List[DiscountOrderTotalAttributes] = None
    discount_by_product: List[DiscountProductAttributes] = None
    discount_product_by_order_total: List[DiscountExtraProductAttributes] = None
    discount_by_quantity: List[DiscountQuantityAttributes] = None
    discount_by_purchase_product: List[DiscountPurchaseProductAttributes] = None
    gift_by_purchase_product: List[GiftPurchaseProductAttributes] = None
    gift_by_order_total: List[GiftOrderTotalAttributes] = None


class DiscountAttributesSchema(BasicAttributesSchema):
    name = fields.Str(required=True)
    discount_code = fields.Str(required=True)
    max_using_times = fields.Decimal(allow_none=True)
    start_at = fields.DateTime(required=True)
    locations = fields.List(fields.Nested(LocationAttributesSchema(LocationAttributes)), allow_none=True)
    sources = fields.List(fields.Nested(LocationAttributesSchema(LocationAttributes)), allow_none=True)
    customers = fields.Nested(EffectiveCustomerAttributesSchema(EffectiveCustomerAttributes), allow_none=True)
    status = EnumField(DiscountStatus, required=True, default=DiscountStatus.DRAFT)
    type = EnumField(DiscountType, required=True)
    description = fields.Str(allow_none=True)
    publish = fields.Bool(required=True)
    used_times = fields.Decimal(allow_none=True, default=0)
    is_stackable = fields.Bool(allow_none=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    expire_at = fields.DateTime(allow_none=True)
    effective_time_range = fields.Str(allow_none=True)
    effective_days_of_week = fields.List(fields.Decimal(required=True), allow_none=True)
    effective_days_of_month = fields.List(fields.Decimal(required=True), allow_none=True)
    effective_months = fields.List(fields.Decimal(required=True), allow_none=True)
    discount_by_order_total = fields.List(
        fields.Nested(DiscountOrderTotalAttributesSchema(DiscountOrderTotalAttributes)), allow_none=True)
    discount_by_product = fields.List(
        fields.Nested(DiscountProductAttributesSchema(DiscountProductAttributes)), allow_none=True)
    discount_product_by_order_total = fields.List(
        fields.Nested(DiscountExtraProductAttributesSchema(DiscountExtraProductAttributes)), allow_none=True)
    discount_by_quantity = fields.List(
        fields.Nested(DiscountQuantityAttributesSchema(DiscountQuantityAttributes)), allow_none=True)
    discount_by_purchase_product = fields.List(
        fields.Nested(DiscountPurchaseProductAttributesSchema(DiscountPurchaseProductAttributes)), allow_none=True)
    gift_by_purchase_product = fields.List(
        fields.Nested(GiftPurchaseProductAttributesSchema(GiftPurchaseProductAttributes)), allow_none=True)
    gift_by_order_total = fields.List(
        fields.Nested(GiftOrderTotalAttributesSchema(GiftOrderTotalAttributes)), allow_none=True)


class DiscountModel(BasicModel):
    key__id = 'id'
    table_name = 'discount'
    attributes: DiscountAttributes
    attributes_schema = DiscountAttributesSchema
    attributes_class = DiscountAttributes

    query_mapping = {
        'query': ['id', 'name', 'discount_code'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'name': 'query',
            'discount_code': 'query',
            'type': 'query',
            'publish': 'boolean',
            'status': 'query',
            'max_using_times': 'query',
            'start_at': 'range',
            'expire_at': 'range',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'image': 'image',
            'effective_time_range': 'effective_time_range',
            'effective_days_of_week': 'effective_days_of_week',
            'effective_days_of_month': 'effective_days_of_month',
            'effective_months': 'effective_months',
            'discount_by_order_total': 'discount_by_order_total',
            'discount_by_product': 'discount_by_product',
            'discount_product_by_order_total': 'discount_product_by_order_total',
            'discount_by_quantity': 'discount_by_quantity',
            'discount_by_purchase_product': 'discount_by_purchase_product',
            'gift_by_purchase_product': 'gift_by_purchase_product',
            'gift_by_order_total': 'gift_by_order_total',
            'used_times': 'used_times',
            'is_stackable': 'is_stackable',
            'locations': 'locations',
            'sources': 'sources',
            'customers': 'customers',
            'description': 'description',
        }
    }

    @classmethod
    def using_discount(cls, discount_id, company_id):
        discount_obj = DiscountModel.by_key({
            'id': discount_id,
            'company_id': company_id
        })
        update_payload = {
            'used_times': int(discount_obj.attributes.used_times) + 1,
        }
        if discount_obj:
            discount_obj.update(update_payload)
        return discount_obj.attributes_dict


class DiscountCodeIndex(DiscountModel):
    key__id = 'discount_code'
    index_name = 'discountCodeIndex'

    @classmethod
    def get_discount_by_code(cls, code, company_id):
        try:
            return cls(
                DiscountCodeIndex.list({'discount_code': code, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return
