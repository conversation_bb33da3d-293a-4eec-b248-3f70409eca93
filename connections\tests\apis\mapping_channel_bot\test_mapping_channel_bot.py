import os
import json
import unittest
from unittest.mock import MagicMock

from tests.base import BaseTestCase
from connections.src.apis.mapping_channel_bot import get_pages, create_mapping_channel_bot, delete_mapping_channel_bot


class MappingChannelBotApiTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

    def create_lambda_event(self, method='GET', path='/', body=None, query_params=None, path_params=None):
        event = {
            'httpMethod': method,
            'body': json.dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': {},
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
            },
            'isBase64Encoded': False
        }
        return event

    def create_lambda_context(self):
        context = MagicMock()
        context.function_name = 'test_function'
        context.function_version = '$LATEST'
        context.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test_function'
        context.memory_limit_in_mb = 128
        context.aws_request_id = 'test_request_id'
        context.log_group_name = '/aws/lambda/test_function'
        context.log_stream_name = '2023/05/01/[$LATEST]abcdefghijklmnopqrstuvwxyz'
        context.identity = None
        context.client_context = None
        return context

    def test_get_pages(self):
        event = {'version': '2.0', 'routeKey': 'GET /mapping/channel/bot/{bot_id}/{connection_id}/pages',
                 'rawPath': '/mapping/channel/bot/9b4c4505-dbee-47fc-9ece-d6ea32aabe10/ea8e035a-98d9-4329-a326-ee38bc531fe5/pages',
                 'rawQueryString': '', 'headers': {'accept': '*/*', 'accept-encoding': 'gzip, deflate, br',
                                                   'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Db3sUJo3kjSWnU4vSiGOT6PrxmZi2yga8BKCaUw5-uH15oUoInTm4B6mgJZSVLXLM8q8jbhC6zfIu_0AZEM-kmt7R65JySf7SFPIWHqQJOrbQvv2UOKtHbKl1D_oX9Ev7FwbUOVbR29HR_YIm9xKqbjUjUrQ7KKOi0PZE7ScRlf67JyVWLTQoqhlO_Li8G9z9VWAcd-3XAzrhkk08DHXAdah96eVIlwawkZGUvqEb6B-S5lILzdNdmz_uhoiw_1YRN8MMH6b1iUaQV_eArWvkSGgf4E8ltJ_ct0wPXajz4bYAsK549S96BDDRRKmZYO-hgPA9WChHNGl2RUtX4Qcvg',
                                                   'cache-control': 'no-cache', 'content-length': '0',
                                                   'host': 'api-staging.optiwarehouse.com',
                                                   'postman-token': '68dc8624-1c72-461d-bdc7-dbc8ef7589f2',
                                                   'user-agent': 'PostmanRuntime/7.44.0',
                                                   'x-amzn-trace-id': 'Root=1-684686c5-227447774a10051446442f71',
                                                   'x-forwarded-for': '*************', 'x-forwarded-port': '443',
                                                   'x-forwarded-proto': 'https'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': 'f2a23ac2-8561-47bf-919f-5d27a4e09652', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': 'deedafae-5b0b-4347-a475-ad42ab25bfee',
                                'origin_jti': '7818acf0-2e30-41d4-99e5-88a9311637d0',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging',
                                    'http': {'method': 'GET',
                                             'path': '/mapping/channel/bot/9b4c4505-dbee-47fc-9ece-d6ea32aabe10/ea8e035a-98d9-4329-a326-ee38bc531fe5/pages',
                                             'protocol': 'HTTP/1.1', 'sourceIp': '*************',
                                             'userAgent': 'PostmanRuntime/7.44.0'}, 'requestId': 'L4n-2hAuSQ0EJaw=',
                                    'routeKey': 'GET /mapping/channel/bot/{bot_id}/{connection_id}/pages',
                                    'stage': '$default', 'time': '09/Jun/2025:07:01:25 +0000',
                                    'timeEpoch': 1749452485045},
                 'pathParameters': {'bot_id': '9b4c4505-dbee-47fc-9ece-d6ea32aabe10',
                                    'connection_id': 'ea8e035a-98d9-4329-a326-ee38bc531fe5'}, 'isBase64Encoded': False}

        context = self.create_lambda_context()
        result = get_pages(event, context)

    def test_create_mapping(self):
        event = {'version': '2.0', 'routeKey': 'POST /mapping/channel/bot/{bot_id}/{connection_id}/{channel_id}',
                 'rawPath': '/mapping/channel/bot/9b4c4505-dbee-47fc-9ece-d6ea32aabe10/ea8e035a-98d9-4329-a326-ee38bc531fe5/613369051869024',
                 'rawQueryString': '', 'headers': {'accept': '*/*', 'accept-encoding': 'gzip, deflate, br',
                                                   'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.U7JISp883N8NHPoEF2Afz3j6hojxzPoq5MUNnf406cAI_sR0Dfwex6CIjulKOzTRMjuY0FWyTyRrO7RwJOHNE-67j-US8dLlMmwb5fNmFvkIF4hjGWaSnW0HfevTXowtYBAF7Ca1ThFyUtdPQaV5E-XUiW65b6TzJjNe-4WQ5sZzfEg7T9a7KOkzZynXd-UG8KtcqeehKtgYZsAyLX6NwuIQcXv7M6bGT4UEje9Wmxq6-Cr09G56cjqmFLd1yfMK3ae7cx1HhVNmag5m2Ki4jCxupSU9yZQX1sZiC566uBPM-UNu2sdUZFK8aSShmau0PJNqur4jW0zuIOofTUwieg',
                                                   'cache-control': 'no-cache', 'content-length': '2',
                                                   'content-type': 'application/json',
                                                   'host': 'api-staging.optiwarehouse.com',
                                                   'postman-token': '8d5b020f-0e64-4298-87f7-d452438fbb98',
                                                   'user-agent': 'PostmanRuntime/7.44.0',
                                                   'x-amzn-trace-id': 'Root=1-68455c9f-408d6cd4091267101cc83dc3',
                                                   'x-forwarded-for': '**************', 'x-forwarded-port': '443',
                                                   'x-forwarded-proto': 'https'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': '5181ac61-26c8-4fd3-9dae-ee3cdaa12bdd', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': 'cf0a63ca-c961-4df8-a08c-588689e3d3eb',
                                'origin_jti': 'bed6ffb5-6ff3-4786-8df2-9de87cdf9556',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging',
                                    'http': {'method': 'POST',
                                             'path': '/mapping/channel/bot/9b4c4505-dbee-47fc-9ece-d6ea32aabe10/ea8e035a-98d9-4329-a326-ee38bc531fe5/613369051869024',
                                             'protocol': 'HTTP/1.1', 'sourceIp': '**************',
                                             'userAgent': 'PostmanRuntime/7.44.0'}, 'requestId': 'L1to6jrvSQ0EMqA=',
                                    'routeKey': 'POST /mapping/channel/bot/{bot_id}/{connection_id}/{channel_id}',
                                    'stage': '$default', 'time': '08/Jun/2025:09:49:19 +0000',
                                    'timeEpoch': 1749376159090},
                 'pathParameters': {'bot_id': '9b4c4505-dbee-47fc-9ece-d6ea32aabe10', 'channel_id': '613369051869024',
                                    'connection_id': 'ea8e035a-98d9-4329-a326-ee38bc531fe5'}, 'body': '{}',
                 'isBase64Encoded': False}

        context = self.create_lambda_context()
        result = create_mapping_channel_bot(event, context)

    def test_delete_mapping(self):
        event = {'version': '2.0', 'routeKey': 'DELETE /mapping/channel/bot/{connection_id}/{channel_id}',
                 'rawPath': '/mapping/channel/bot/52a2150a-95f7-4217-a1f1-358888978906/613369051869024',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hqDgGSwRmSIltoDoKMsZyO8YiX7OAEFEyMv_SH56BjudKc1bxfxX8cqsvCLYp5xKpKzgHCpIB_uoDfSvUigNVvTW8sDq26ljBZqA-8vnuFEjZLQ4RWtg0hXJArj6zi7AjDdEOZqDMFkLfL-JATQu7JwNGF8uzGYOgnhQcjZIpjtiG-EAdh_OmXZOsRNWQwfFhd_m3954YvsQDu54vDhtDxen6bj4clGEK_0l3e7X89mXToYhohXpXm3WtlYTF5bEhZe4sn9O4ggBZM4MLjV57PvHCwjnZlpoMkp2_UV3tVxBceCqrxbIPTEphlGPk6M6-cCDmXfnfBlP7QOrjgcpnQ',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.E3pSCD8cpLPBWNMGNBRGF0hIZ9OcwlB0G4V_tx84r0LH1C95C5yVP2mvAF7DBNjCGMjh2dZ3aAwGoDZ4xAuyEoDkDl58_lhN2iIZhScC62pR-GAtIU2I0sf9shk2gI75nfTIhyhH1IifWjXQ5WFCeaq-5UneusOau3ZingTOO6O3ZSYbCxNHURHy46PaACC6hVYpAcW6JG1YTxw4nO56RgSP8H7zWdlOI7UEXnqMfkHxcHqVMPDQjM6ojGscZ3KBLXje_TC6vGXBJI36jawmwOYyjdiInbo-hU-mu_kXbuu-1XNckNWldeKxarkW-7Iwfxj6ys2xuBiJ0Kit4B30Qg',
                             'content-length': '0', 'host': 'api-dev.onexbots.com', 'origin': 'http://localhost:3000',
                             'priority': 'u=1, i', 'referer': 'http://localhost:3000/',
                             'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                             'sec-fetch-mode': 'cors', 'sec-fetch-site': 'cross-site', 'sec-gpc': '1',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                             'x-amzn-trace-id': 'Root=1-68469817-35cc51400b0f0f3f27592da7',
                             'x-forwarded-for': '*************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hqDgGSwRmSIltoDoKMsZyO8YiX7OAEFEyMv_SH56BjudKc1bxfxX8cqsvCLYp5xKpKzgHCpIB_uoDfSvUigNVvTW8sDq26ljBZqA-8vnuFEjZLQ4RWtg0hXJArj6zi7AjDdEOZqDMFkLfL-JATQu7JwNGF8uzGYOgnhQcjZIpjtiG-EAdh_OmXZOsRNWQwfFhd_m3954YvsQDu54vDhtDxen6bj4clGEK_0l3e7X89mXToYhohXpXm3WtlYTF5bEhZe4sn9O4ggBZM4MLjV57PvHCwjnZlpoMkp2_UV3tVxBceCqrxbIPTEphlGPk6M6-cCDmXfnfBlP7QOrjgcpnQ'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': 'b394446b-d92e-4f20-a162-b9c50491d4c4', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': '5108b299-61c3-4890-8712-675f6246924a',
                                'origin_jti': '8eadab2d-ba64-4e3e-9f57-aba5f18c998a',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-dev.onexbots.com', 'domainPrefix': 'api-dev',
                                    'http': {'method': 'DELETE',
                                             'path': '/mapping/channel/bot/52a2150a-95f7-4217-a1f1-358888978906/613369051869024',
                                             'protocol': 'HTTP/1.1', 'sourceIp': '*************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                    'requestId': 'L4yzuh6MyQ0EMqw=',
                                    'routeKey': 'DELETE /mapping/channel/bot/{connection_id}/{channel_id}',
                                    'stage': '$default', 'time': '09/Jun/2025:08:15:19 +0000',
                                    'timeEpoch': 1749456919497}, 'pathParameters': {'channel_id': '613369051869024',
                                                                                    'connection_id': '52a2150a-95f7-4217-a1f1-358888978906'},
                 'isBase64Encoded': False}

        context = self.create_lambda_context()
        result = delete_mapping_channel_bot(event, context)


if __name__ == '__main__':
    unittest.main()
