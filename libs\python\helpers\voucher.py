import os

import pendulum

from helpers.utils import RESOURCE_SERVICE
from models.promotion.used_voucher import UsedVoucherModel
from models.promotion.voucher import CustomValueError, VoucherModel, ValueCouponType

SERVICE = os.getenv('SERVICE')


def handle_check_valid_voucher(voucher_id, company_id, customer_id, voucher_code, total_order, order_line_items):
    voucher_attributes = VoucherModel.by_key({
        VoucherModel.key__id: voucher_id,
        'company_id': company_id
    }).attributes
    if pendulum.parse(str(voucher_attributes.start_at)) > pendulum.now():
        raise CustomValueError('Code not active!')
    if voucher_attributes.expire_at is not None:
        if pendulum.now() > pendulum.parse(str(voucher_attributes.expire_at)):
            raise CustomValueError('Code is expired!')
    if voucher_code is not None:
        list_used_voucher_obj = UsedVoucherModel.search({
            UsedVoucherModel.key__id: customer_id,
            'voucher_code': voucher_code,
        }, service=RESOURCE_SERVICE, company_id=company_id)
        if voucher_attributes.use_voucher_customer is not None:
            if voucher_attributes.use_voucher_customer < list_used_voucher_obj[
                'total']:
                raise CustomValueError('Code has been used up!')
    if voucher_attributes.condition_order > total_order:
        raise CustomValueError('Order does not qualify!')
    list_condition_contains_products = voucher_attributes.condition_contains_order.get('products')
    list_condition_contains_category = voucher_attributes.condition_contains_order.get('category')
    list_condition_contains_brand = voucher_attributes.condition_contains_order.get('brand')

    if list_condition_contains_products is not None:
        handle_check_condition_contains_products('id', order_line_items, list_condition_contains_products)
    if list_condition_contains_category is not None:
        handle_check_condition_contains_products('category', order_line_items, list_condition_contains_category)
    if list_condition_contains_brand is not None:
        handle_check_condition_contains_products('brand', order_line_items, list_condition_contains_brand)

    total_apply_voucher = 0
    if voucher_attributes.value_coupon.type.value == ValueCouponType.PERCENT.value:
        if voucher_attributes.value_coupon.amount is not None:
            total_voucher = total_order * voucher_attributes.value_coupon.amount / 100
            total_apply_voucher = min(total_voucher, voucher_attributes.max_value_coupon)
    else:
        total_apply_voucher = voucher_attributes.value_coupon.amount
    return {
        'voucher_code': voucher_code,
        'voucher_id': voucher_id,
        'max_value_coupon': voucher_attributes.max_value_coupon,
        'total_apply_voucher': int(total_apply_voucher),
        'total_order': total_order,
        'total_voucher': total_order - int(total_apply_voucher),
        'apply_with_other_promotion': voucher_attributes.apply_with_other_promotion
    }


def handle_check_condition_contains_products(key_check, list_product, list_condition_contains):
    if list_product is None:
        raise CustomValueError('Code cannot be applied to orders!')
    for product in list_product:
        for condition_product in list_condition_contains:
            if (
                    condition_product['id'] == product[key_check] or
                    (
                            condition_product["id"] == "all" and
                            product["quantity"] >= int(condition_product["quantity"])
                    )
            ):
                break
        else:
            continue
        break
    else:
        raise CustomValueError('Code cannot be applied to orders!')
