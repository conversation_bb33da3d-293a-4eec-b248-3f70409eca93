import json
import os

import pendulum
from nolicore.utils.api import api_message
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.decorator import invoke
from nolicore.utils.aws.request import ApiGatewayRequest, LambdaRequest
from nolicore.utils.exceptions import BadRequest
from nolicore.utils.utils import logger, json_dumps

from helpers.lambda_client import lambda_client_invoke_async
from integrations.common.bucket_helper import get_transformed_data, store_transformed_data
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.queue_helper import enqueue_mapping, enqueue_publish_message
from models.integration.mapping_attribute import MappingAttributeTypeIndex
from models.integration.sync_record import SyncRecordCompanyIdIndex, MappingStatus, SyncRecordStatus, SyncRecordModel, \
    SyncRecordConnectionIdIndex
from ..events.step_3_transform import transform_to_destination_format

ENV = os.getenv('ENV')
TRANSFORMED_DATA_BUCKET = os.environ['TRANSFORMED_DATA_BUCKET']


@as_api()
def sync_mapping(api_gateway_request: ApiGatewayRequest):
    destination_id = api_gateway_request.path_parameters['destination_id']
    object_type = api_gateway_request.path_parameters['type']
    sync_record_ids = api_gateway_request.body.get('sync_record_ids', None) if api_gateway_request.body else None
    company_id = api_gateway_request.company_id

    for field_name, value in {"destination_id": destination_id, "type": object_type,
                              "company_id": company_id}.items():
        if not value:
            raise BadRequest(f"Missing required field: {field_name}")

    trigger_invoke_sync_mapping_handle(destination_id, object_type, sync_record_ids, company_id)
    return api_message('Receive process mapping request successful')


def trigger_invoke_sync_mapping_handle(destination_id, object_type, sync_record_ids, company_id, next_token=None):
    payload = {
        'destination_id': destination_id,
        'type': object_type,
        'sync_record_ids': sync_record_ids,
        'company_id': company_id,
        'next_token': next_token
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-flow-service-{ENV}-invoke_sync_records_handle')


@invoke()
def invoke_sync_records_handle(lambda_request: LambdaRequest):
    event = lambda_request.event
    destination_id = event['destination_id']
    object_type = event['type']
    sync_record_ids = event['sync_record_ids']
    company_id = event['company_id']
    next_token = event.get('next_token', None)
    params = {
        "is_source": False,
        "record_type": object_type,
        "connection_id": destination_id,
        'mapping_status': [MappingStatus.ERROR.value, MappingStatus.MAPPED.value]
    }
    last_evaluated_key = next_token
    logger.info(f"Processing invoke_sync_records_handle event: {event}")

    if not sync_record_ids:
        ## without [id]
        try:
            response = SyncRecordConnectionIdIndex.list(params, limit=20, last_evaluated_key=last_evaluated_key) or {}
            last_evaluated_key = response.get('LastEvaluatedKey')
            sync_records = response.get('Items', [])
        except Exception as e:
            print(f"Error fetching sync records without IDs: {e}")
            return
    else:
        ## with [id]
        sync_records = []
        for sync_record_id in sync_record_ids:
            try:
                params_with_id = {**params, "id": sync_record_id}
                result = SyncRecordConnectionIdIndex.list(params_with_id, limit=1000) or {}
                items = result.get('Items', [])  # Use 'Items' to match the casing
                filtered_items = [item for item in items if item.get('record_type') == object_type]
                sync_records.extend(filtered_items)
            except Exception as e:
                print(f"Error fetching sync record with ID {sync_record_id}: {e}")
    logger.info(f"Enqueueing enqueue_mapping with sync_records: {sync_records}")
    for sync_record in sync_records:
        try:
            enqueue_mapping(sync_record)
        except Exception as e:
            print(f"Error enqueuing mapping for sync_record {sync_record.get('id')}: {e}")
    if last_evaluated_key:
        try:
            trigger_invoke_sync_mapping_handle(
                destination_id, object_type, sync_record_ids, company_id, next_token=last_evaluated_key
            )
        except Exception as e:
            print(f"Error triggering next batch with last_evaluated_key {last_evaluated_key}: {e}")


@invoke()
def mapping_handler(lambda_request: LambdaRequest):
    logger.info(f"Processing mapping_handler event: {lambda_request.event['Records']}")
    for record in lambda_request.event['Records']:
        message = json.loads(record['body'])
        sync_record = message['sync_record']
        destination_id = sync_record['connection_id']
        destination_key = sync_record['transformed_record_id']
        destination_version_id = sync_record['transformed_record_version']
        record_type = sync_record['record_type']

        logger.info(f"Processing sync record: {sync_record['id']}")
        logger.info(f"Full sync record data: {json_dumps(sync_record)}")
        logger.info(f"Destination key: {destination_key}")
        logger.info(f"Destination version ID: {destination_version_id}")
        logger.info(f"Record type: {record_type}")

        source_connection_id, action_type, object_type, filename = sync_record['id'].split('/')

        # Get mapping info
        mapping_data = MappingAttributeTypeIndex.get_mapping_attribute(destination_id, record_type)
        mappings = mapping_data.attributes_dict.get('mappings', []) if mapping_data else []

        destination_instance = get_connection_by_id(destination_id)
        destination_sync_record_obj = SyncRecordModel.put(sync_record['id'], destination_id, {
            'response_message': None,
            'response': None,
            'remote_record_id': None,
        })

        # Get master_data
        try:
            logger.info(f"Fetching source sync record for ID: {sync_record['id']}")
            source_sync_records = SyncRecordCompanyIdIndex.list({'id': sync_record['id'], 'is_source': True},
                                                                limit=1000)
            if not source_sync_records.get('Items'):
                logger.error(f"No source sync record found for ID: {sync_record['id']}")
                raise Exception(f"No source sync record found for ID: {sync_record['id']}")

            source_sync_record = source_sync_records['Items'][0]
            logger.info(f"Found source sync record: {json_dumps(source_sync_record)}")

            source_key = source_sync_record['transformed_record_id']
            source_version_id = source_sync_record['transformed_record_version']
            logger.info(f"Source key: {source_key}")
            logger.info(f"Source version ID: {source_version_id}")

            master_data = get_transformed_data(TRANSFORMED_DATA_BUCKET, source_key, source_version_id)
        except Exception as e:
            logger.error(f"Error processing source sync record: {str(e)}")
            logger.error(f"Sync record data: {json_dumps(sync_record)}")
            raise

        try:
            destination_data = transform_to_destination_format(master_data,
                                                               destination_instance.attributes_dict['channel_name'],
                                                               object_type,
                                                               destination_instance.attributes_dict, mappings)
        except Exception as e:
            destination_sync_record_obj.update({
                'response_message': f"Transform failed: {str(e)}",
                'transformed_at': pendulum.now().to_iso8601_string(),
                'finished_at': pendulum.now().to_iso8601_string(),
                'status': SyncRecordStatus.ERROR.value,
                'mapping_status': MappingStatus.ERROR.value
            })
            raise

        if destination_data:
            destination_version_id = store_transformed_data(TRANSFORMED_DATA_BUCKET, destination_key,
                                                            destination_data)
            updated_destination_sync_record = {
                'transformed_record_id': destination_key,
                'transformed_record_version': destination_version_id,
                'transformed_at': pendulum.now().to_iso8601_string(),
                'status': SyncRecordStatus.TRANSFORMED.value,
            }

            # Enqueue to step_4
            destination_sync_record_obj.update(updated_destination_sync_record)
            enqueue_publish_message(source_connection_id, destination_id, sync_record['id'],
                                    destination_key,
                                    destination_version_id,
                                    source_sync_record['raw_record_version'], object_type,
                                    sync_record['fetch_event_id'])
        else:
            now = pendulum.now().to_iso8601_string()
            destination_sync_record_obj.update({
                'transformed_record_id': None,
                'transformed_record_version': None,
                'transformed_at': now,
                'finished_at': now,
                'response': {
                    'status': 'skipped',
                    'error_message': 'Empty data'
                },
                'status': SyncRecordStatus.SKIPPED.value,
                'response_message': f"Publish skipped: Empty data",
            })
