import json
import os

from botocore.exceptions import ClientError
from nolicore.utils.aws.boto_helper import get_client
from nolicore.utils.utils import logger, json_dumps

from integrations.common.event import FetchEvent, FetchEventStatus
from models.integration.fetch_event import FetchEventModel

sqs = get_client('sqs')
FETCH_QUEUE_URL = os.environ['FETCH_QUEUE_URL']
PUBLISH_QUEUE_URL = os.environ['PUBLISH_QUEUE_URL']
SYNC_MAPPING_QUEUE_URL = os.environ['SYNC_MAPPING_QUEUE_URL']


def enqueue_fetch_event(event: FetchEvent, company_id: str):
    try:
        if event.status != FetchEventStatus.PENDING:
            event.status = FetchEventStatus.PENDING
        event.company_id = str(company_id)
        message_body = json_dumps(event.to_dict())
        FetchEventModel.create(company_id, event.to_dict())
        response = sqs.send_message(
            QueueUrl=FETCH_QUEUE_URL,
            MessageBody=message_body
        )
        logger.info(f"FetchEvent enqueued successfully. MessageId: {response['MessageId']}")
    except ClientError as e:
        logger.error(f"Failed to enqueue FetchEvent: {str(e)}")
        raise


def enqueue_publish_message(connection_id, destination_id, source_key, destination_key, destination_version_id,
                            source_version_id, object_type, fetch_event_id, retry_count=0):
    try:
        sqs.send_message(
            QueueUrl=PUBLISH_QUEUE_URL,
            MessageBody=json.dumps({
                'connection_id': connection_id,
                'destination_id': destination_id,
                'source_key': source_key,
                'destination_key': destination_key,
                'destination_version_id': destination_version_id,
                'source_version_id': source_version_id,
                'retry_count': retry_count,
                'object_type': object_type,
                'fetch_event_id': fetch_event_id,
            }),
        )
    except ClientError as e:
        logger.error(f"Failed to enqueue publish message: {str(e)}")


def enqueue_mapping(sync_record):
    try:
        sqs.send_message(
            QueueUrl=SYNC_MAPPING_QUEUE_URL,
            MessageBody=json_dumps({
                'sync_record': sync_record,
            })
        )
    except ClientError as e:
        logger.error(f"Failed to enqueue mapping message: {str(e)}")
