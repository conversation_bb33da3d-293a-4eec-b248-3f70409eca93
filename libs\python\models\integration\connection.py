import os
from datetime import datetime
from typing import Optional, Dict, Any, List, Type

import pendulum
from croniter import croniter

from integrations.channels.action_types import ACTION_TYPE_MAPPING, ActionGroup, ActionType, ACTION_GROUP_MAPPING, \
    ScheduleType, \
    FetchActionSettings, ActionGroupSettings, PublishActionSettings, ActionKind
from integrations.channels.channel_library import AbstractChannel
from integrations.channels.connection_types import ConnectionAttributes, ConnectionSchema, ConnectionStatus
from models.basic import BasicModel
from helpers.utils import (
    FACEBOOK_APP_ID, FACEBOOK_APP_SECRET,
    ZALO_OA_APP_ID, ZALO_OA_SECRET, ZALO_OA_APP_SECRET,
    SHOPIFY_CLIENT_ID, SHOPIFY_CLIENT_SECRET,
    TIKTOK_APP_KEY, TIKTOK_APP_SECRET, TIKTOK_SERVICE_ID
)


def get_channel_factory():
    from integrations.channels.channel_factory import ChannelFactory
    return ChannelFactory


def get_connection_registry():
    from integrations.channels.connection_registry import connection_registry
    return connection_registry


class ConnectionModel(BasicModel):
    key__id = 'id'
    table_name = 'connection'
    attributes: ConnectionAttributes = None
    attributes_schema = ConnectionSchema
    attributes_class = ConnectionAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'status': 'query',
            'name': 'query',
            'connected': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range'
        },
        'mapping': {
            'url': 'url',
            'image': 'logo'
        }
    }

    @classmethod
    def get_all_active_connections(cls):
        last_evaluated_key = None
        all_connections = []
        limit = 300
        filter_params = {'status': ConnectionStatus.ACTIVE.value}
        while True:
            connections = cls.list(filter_params, limit=limit, last_evaluated_key=last_evaluated_key)
            last_evaluated_key = connections.get('LastEvaluatedKey')
            all_connections.extend(connections['Items'])
            if not last_evaluated_key:
                break
        return all_connections

    def activate_connection(self):
        """
        Activate the connection after a successful test.
        """
        self.attributes.status = ConnectionStatus.ACTIVE
        self.save()

    def deactivate_connection(self):
        """
        Activate the connection after a successful test.
        """
        self.attributes.status = ConnectionStatus.INACTIVE
        self.save()

    @property
    def id(self):
        return str(self.attributes.id)

    @classmethod
    def get(cls, company_id, _id, _except=False):
        return cls.by_key({
            'id': str(_id),
            'company_id': str(company_id)
        })

    def update_fetch_action_status(self, action: ActionType, run_time: datetime):
        action_group = ACTION_GROUP_MAPPING.get(action)
        if not action_group:
            return

        action_settings = self.get_fetch_action_settings(action_group, action)
        if not action_settings:
            return

        action_settings.status.last_run = run_time

        if action_settings.schedule:
            if action_settings.schedule.type == ScheduleType.INTERVAL:
                interval = int(action_settings.schedule.value)
                action_settings.status.next_run = run_time + pendulum.duration(seconds=interval)
            elif action_settings.schedule.type == ScheduleType.CRON:
                cron = croniter(action_settings.schedule.value, run_time)
                action_settings.status.next_run = cron.get_next(datetime)

    def get_fetch_action_settings(self, group: ActionGroup, action: ActionType) -> Optional[FetchActionSettings]:
        return self.attributes.action_groups.get(group, ActionGroupSettings(group_name=group).__dict__)[
            'fetch_actions'].get(action)

    def get_publish_action_settings(self, group: ActionGroup, action: ActionType = None) -> Optional[
        PublishActionSettings]:
        publish_actions = self.attributes.action_groups.get(group, ActionGroupSettings(group_name=group).__dict__)[
            'publish_actions']
        if action:
            return publish_actions.get(action)
        return list(publish_actions.values())[0] if publish_actions.values() else {}

    def get_publish_action_type(self, group: ActionGroup) -> Optional[ActionType]:
        publish_actions = self.attributes.action_groups.get(group, ActionGroupSettings(group_name=group).__dict__)[
            'publish_actions']
        for action_name in publish_actions.keys():
            try:
                action_type = ActionType(action_name)
                if ACTION_GROUP_MAPPING.get(action_type) == group:
                    return action_type
            except ValueError:
                # If action_name is not a valid ActionType, skip it
                continue
        return None

    def set_fetch_action_settings(self, group: ActionGroup, action: ActionType, settings: FetchActionSettings):
        if group not in self.attributes.action_groups:
            self.attributes.action_groups[group] = ActionGroupSettings(group_name=group)
        self.attributes.action_groups[group]['fetch_actions'][action] = settings

    def set_publish_action_settings(self, group: ActionGroup, action: ActionType, settings: PublishActionSettings):
        if group not in self.attributes.action_groups:
            self.attributes.action_groups[group] = ActionGroupSettings(group_name=group)
        self.attributes.action_groups[group]['publish_actions'][action] = settings

    def is_action_enabled(self, group: ActionGroup, action: ActionType, kind: ActionKind) -> bool:
        if kind == ActionKind.FETCH:
            settings = self.get_fetch_action_settings(group, action)
        elif kind == ActionKind.PUBLISH:
            settings = self.get_publish_action_settings(group, action)
        else:
            raise ValueError(f"Unsupported action kind: {kind}")

        return settings.enabled if settings else False

    def get_enabled_actions(self, kind: ActionKind) -> List[Dict[str, Any]]:
        enabled_actions = []
        for group, group_settings in self.attributes.action_groups.items():
            if kind == ActionKind.FETCH:
                actions = group_settings.fetch_actions
            elif kind == ActionKind.PUBLISH:
                actions = group_settings.publish_actions
            else:
                raise ValueError(f"Unsupported action kind: {kind}")

            for action, action_settings in actions.items():
                if action_settings.enabled:
                    enabled_actions.append({
                        'group': group,
                        'action': action,
                        'settings': action_settings.__dict__
                    })
        return enabled_actions

    def should_fetch(self, action: ActionType, current_time: datetime) -> bool:
        if self.attributes.status == ConnectionStatus.INACTIVE:
            return False
        action_group = ACTION_GROUP_MAPPING.get(action)
        if not action_group:
            return False

        action_settings = self.get_fetch_action_settings(action_group, action)
        if not action_settings or not action_settings.enabled:
            return False

        schedule = action_settings.schedule
        if not schedule:
            return False

        last_run = action_settings.status.last_run
        if last_run is None:
            return True

        if schedule.type == ScheduleType.INTERVAL:
            interval = int(schedule.value)
            return (pendulum.parse(current_time.isoformat()) - pendulum.parse(
                last_run.isoformat())).total_seconds() >= interval
        elif schedule.type == ScheduleType.CRON:
            cron = croniter(schedule.value, last_run)
            next_run = cron.get_next(datetime)
            return current_time >= next_run
        else:
            raise ValueError(f"Unsupported schedule type: {schedule.type}")

    def should_execute_action(self, action: ActionType) -> bool:
        if self.attributes.status == ConnectionStatus.INACTIVE:
            return False

        action_group = ACTION_GROUP_MAPPING.get(action)
        if not action_group:
            return False
        action_settings = self.get_fetch_action_settings(action_group, action)
        if not action_settings:
            return False
        return True

    def should_save_to_storage(self, action: ActionType) -> bool:
        if self.attributes.status == ConnectionStatus.INACTIVE:
            return False
        action_group = ACTION_GROUP_MAPPING.get(action)
        new_action_type = ACTION_TYPE_MAPPING.get(action)
        if not action_group or not new_action_type:
            return False
        fetch_action_settings = self.get_fetch_action_settings(action_group, action)
        if not fetch_action_settings or not fetch_action_settings.enabled:
            return False
        if hasattr(fetch_action_settings, 'storage_enabled') and fetch_action_settings.storage_enabled:
            return True
        publish_action_settings = self.get_publish_action_settings(action_group, new_action_type)
        if not publish_action_settings or not publish_action_settings.enabled:
            return False
        return True

    def should_import_data(self, action: ActionType) -> bool:
        if self.attributes.status == ConnectionStatus.INACTIVE:
            return False
        action_group = ACTION_GROUP_MAPPING.get(action)
        if not action_group:
            return False
        fetch_action_settings = self.get_fetch_action_settings(action_group, action)
        if not fetch_action_settings or not fetch_action_settings.enabled:
            return False
        if (hasattr(fetch_action_settings, 'import_enabled') and fetch_action_settings.import_enabled) or not hasattr(
                fetch_action_settings, 'import_enabled'):
            return True
        return False

    def get_available_locations(self) -> Dict[str, Any]:
        channel = self.get_channel()
        return channel.get_dynamic_settings().get('location_mapping', {})

    @classmethod
    def get_setup_fields(cls) -> Dict[str, str]:
        return cls.attributes_schema(cls.attributes_class).json_schema()

    @classmethod
    def create_connection(cls: Type['ConnectionModel'], connection_data: Dict[str, Any]) -> 'ConnectionModel':
        ChannelFactory = get_channel_factory()
        channel_class = ChannelFactory.get_channel(cls.attributes_class.channel_name)

        # Get channel info
        channel_info = channel_class.get_info()

        # Create default settings with channel info
        default_settings = ChannelFactory.create_default_connection_settings(cls.attributes_class.channel_name)
        default_settings.update(channel_info)

        # Create connection instance
        connection = cls(
            cls.attributes_class.add_basic_attributes({
                **default_settings,
                'settings': connection_data.get('settings', {})
            }, connection_data['company_id'])
        )
        return connection

    @classmethod
    def get_channel_class(cls, channel_name=None) -> Type[AbstractChannel]:
        ChannelFactory = get_channel_factory()
        channel_name = channel_name or cls.attributes_class.channel_name
        return ChannelFactory.get_channel(channel_name)

    def get_channel(self) -> AbstractChannel:
        channel_class = self.get_channel_class(channel_name=self.attributes.channel_name)
        # Get the connection registry
        registry = get_connection_registry()

        # Get the specific connection class for this channel
        specific_connection_class = registry.get(self.attributes.channel_name)

        if specific_connection_class is None:
            raise ValueError(f"No specific connection class found for channel: {self.attributes.channel_name}")

        specific_connection = specific_connection_class(self.attributes_dict)

        return channel_class(specific_connection)

    def get_webhook_url(self, event_type: Optional[str] = None) -> str:
        base_url = os.environ.get('WEBHOOK_BASE_URL', 'https://api.yourcompany.com/webhooks')
        url = f"{base_url}/flows/webhook/direct/{self.attributes.id}"

        if event_type:
            url += f"/{event_type}"

        return url

    def update_webhook_settings(self, new_settings: Dict[str, bool]):
        """
        Update the webhook settings for this connection.

        :param new_settings: A dictionary of webhook topics and their new enabled/disabled status
        """
        # Store the original settings in case we need to rollback
        original_settings = self.attributes.webhook_settings.copy()

        # Update the settings in memory
        self.attributes.webhook_settings.update(new_settings)

        try:
            # Attempt to set up the webhooks with the new settings
            self._setup_webhooks_sync()

            # If webhook setup is successful, save the new settings
            self.save()
        except Exception as e:
            # If webhook setup fails, rollback to the original settings
            self.attributes.webhook_settings = original_settings
            # Log the error or handle it appropriately
            print(f"Error setting up webhooks: {str(e)}")
            # Re-raise the exception or handle it as needed
            raise

    def _setup_webhooks_sync(self):
        """
        Synchronous method to set up webhooks.
        """
        channel = self.get_channel()
        if hasattr(channel, 'setup_webhooks'):
            channel.setup_webhooks()

    def get_webhook_settings(self) -> Dict[str, bool]:
        """
        Get the current webhook settings for this connection.

        :return: A dictionary of webhook topics and their enabled/disabled status
        """
        return self.attributes.webhook_settings

    def update_dynamic_settings(self, settings: Dict[str, Any]):
        self.attributes.dynamic_settings.update(settings)
        self.save()

    def remove_dynamic_settings(self, remove_keys: List[str]):
        if remove_keys:
            for key in remove_keys:
                dynamic_settings = self.attributes.dynamic_settings
                if key in dynamic_settings:
                    dynamic_settings.pop(key, None)
        self.save()

    def get_dynamic_settings(self) -> Dict[str, Any]:
        return self.attributes.dynamic_settings


class ConnectionByCompany(ConnectionModel):
    key__id = 'company_id'
    key__ranges = ['id']
    index_name = 'connectionByCompanyIndex'

    @classmethod
    def get_active_publish_actions_by_company(cls, company_id: str) -> List[Dict[str, Any]]:
        last_evaluated_key = None
        all_connections = []
        limit = 300
        filter_params = {'company_id': company_id,
                         'status': ConnectionStatus.ACTIVE.value}
        while True:
            connections = cls.list(filter_params, limit=limit, last_evaluated_key=last_evaluated_key)
            last_evaluated_key = connections.get('LastEvaluatedKey')
            all_connections.extend(connections['Items'])
            if not last_evaluated_key:
                break
        active_connections = []

        for connection in all_connections:
            active_publish_actions = []
            for action_group in connection.get('action_groups', {}).values():
                for action_name, action_data in action_group.get('publish_actions', {}).items():
                    if action_data.get('enabled', False):
                        active_publish_actions.append({
                            'name': action_name,
                            'data': action_data
                        })

            if active_publish_actions:
                connection_copy = connection.copy()
                connection_copy['active_publish_actions'] = active_publish_actions
                active_connections.append(connection_copy)

        return active_connections

    @classmethod
    def get_connections_by_credentials(cls, connection_type: str, company_id: str) -> List[Dict[str, Any]]:
        """
        Get connections that match the specified credentials and have token data.
        Returns both active and inactive connections.

        Args:
            channel_name (str): The name of the channel
            app_id (str): The application ID
            secret_key (str): The secret key
            company_id (str): The company ID

        Returns:
            List[Dict[str, Any]]: List of matching connections that have token data
        """
        last_evaluated_key = None
        matching_connections = []
        limit = 300
        filter_params = {
            'company_id': company_id
        }

        while True:
            connections = cls.list(filter_params, limit=limit, last_evaluated_key=last_evaluated_key)
            last_evaluated_key = connections.get('LastEvaluatedKey')

            for connection in connections['Items']:
                # Define credential mappings for each connection type
                credential_mappings = {
                    "facebook_oauth": {
                        "app_id": FACEBOOK_APP_ID,
                        "app_secret": FACEBOOK_APP_SECRET
                    },
                    "zalo_oa": {
                        "app_id": ZALO_OA_APP_ID,
                        "secret_key": ZALO_OA_APP_SECRET,
                        "oa_secret_key": ZALO_OA_SECRET
                    },
                    "tiktok_shop": {
                        "app_key": TIKTOK_APP_KEY,
                        "app_secret": TIKTOK_APP_SECRET,
                        "service_id": TIKTOK_SERVICE_ID
                    },
                    "shopify_oauth": {
                        "client_id": SHOPIFY_CLIENT_ID,
                        "client_secret": SHOPIFY_CLIENT_SECRET
                    }
                }

                if connection_type in credential_mappings:
                    mapping = credential_mappings[connection_type]
                    settings = connection.get('settings', {})

                    # Check if all required credentials match
                    if (connection.get('channel_name') == connection_type and
                            connection.get('token_data') and
                            all(settings.get(k) == v for k, v in mapping.items())):
                        # Get expiration information
                        expires_at = connection.get('expires_at')
                        is_expired = False

                        if expires_at:
                            try:
                                expire_datetime = pendulum.parse(expires_at)
                                is_expired = expire_datetime < pendulum.now()
                            except:
                                is_expired = False

                        formated_connection = {
                            'id': connection.get('id'),
                            'status': connection.get('status'),
                            'is_expired': is_expired,
                            'channel_name': connection.get('channel_name'),
                        }
                        matching_connections.append(formated_connection)

            if not last_evaluated_key:
                break

        return matching_connections
