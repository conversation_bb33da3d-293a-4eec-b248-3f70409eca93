from dataclasses import dataclass

from nolicore.utils.utils import compress

from models.actor.person import Person, PersonSchema, PersonAttributes, PersonAttributesSchema
from models.basic import BasicModel, BasicAttributes


@dataclass
class Supplier(Person):
    pass


class SupplierSchema(PersonSchema):
    pass


@dataclass
class SupplierAttributes(PersonAttributes):
    pass


class SupplierAttributesSchema(PersonAttributesSchema):
    pass


class SupplierModel(BasicModel):
    table_name = 'supplier'
    attributes: SupplierAttributes
    attributes_schema = SupplierAttributesSchema
    attributes_class = SupplierAttributes

    query_mapping = {
        'query': ['id', 'email', 'phone', 'first_name', 'last_name', 'company_name'],
        'filter': {
            'id': 'query',
            'first_name': 'query',
            'last_name': 'query',
            'company_name': 'query',
            'email': 'query',
            'tags': 'tags',
            'source': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'billing_address': 'billing_address',
            'addresses': 'addresses',
            'account': 'account'
        }
    }

    @classmethod
    def get_hash(cls, supplier):
        phone = cls.parse_phone(supplier)
        if phone:
            return compress({
                "phone": supplier.get('phone'),
            })
        return compress({
            "email": supplier.get('email'),
            "phone": phone,
            "first_name": supplier.get('first_name'),
            "last_name": supplier.get('last_name'),
            "billing_address": supplier.get('billing_address')
        })

    @classmethod
    def parse_phone(cls, supplier):
        phone = supplier.get('phone', '')
        if phone and '*' not in phone:
            return phone

    @classmethod
    def get_supplier(cls, company_id, supplier):
        supplier_id = supplier.get('id') or cls.get_hash(supplier)
        supplier_obj = SupplierModel.by_key({
            'id': str(supplier_id),
            'company_id': company_id
        })
        return supplier_obj

    @classmethod
    def sync(cls, company_id, data):
        supplier_obj = SupplierModel.get_supplier(company_id, data)
        if supplier_obj:
            supplier_obj.update(data)
            return supplier_obj.attributes_dict

        supplier_id = SupplierModel.get_hash(data)
        data['id'] = supplier_id
        supplier_payload = BasicAttributes.add_basic_attributes(data, company_id)

        supplier_obj = SupplierModel(supplier_payload)
        supplier_obj.save()

        return supplier_obj.attributes_dict
