schedule_handler:
  handler: src.events.step_1_schedule.schedule_handler
  events:
    - schedule: rate(1 minute)
  timeout: 120
  memorySize: 256
fetch_handler:
  handler: src.events.step_2_fetch.fetch_lambda_handler
  events:
    - sqs:
        arn: ${self:custom.fetchQueueArn}
        batchSize: 10
        maximumBatchingWindow: 120
  timeout: 120
  memorySize: 256

transform_handler:
  handler: src.events.step_3_transform.transform_lambda_handler
  events:
    - s3:
        bucket: ${self:service}-${self:provider.stage}-raw-data
        event: s3:ObjectCreated:*
        existing: true
  timeout: 120
  memorySize: 256


publish_handler:
  handler: src.events.step_4_publish.publish_lambda_handler
  events:
    - sqs:
        arn: ${self:custom.publishQueueArn}
        batchSize: 200
        maximumBatchingWindow: 120
        maximumConcurrency: 3
  timeout: 120
  memorySize: 256

mapping_handler:
  handler: src.apis.sync_mapping.mapping_handler
  events:
    - sqs:
        arn: ${self:custom.syncMappingQueueArn}
        batchSize: 10
        maximumBatchingWindow: 120
  timeout: 120
  memorySize: 256