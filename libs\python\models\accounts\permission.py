from dataclasses import dataclass

from marshmallow import fields
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


@dataclass
class PermissionAttributes(BasicAttributes):
    user_id: str = None
    user_dict: dict = None
    role: dict = None


class PermissionSchema(BasicAttributesSchema):
    user_id = fields.Str(required=True)
    user_dict = fields.Dict(required=True)
    role = fields.Dict(required=True)


class PermissionModel(BasicModel):
    key__id = 'user_id'
    key__ranges = ['company_id']
    table_name = 'permission'
    attributes: PermissionAttributes
    attributes_schema = PermissionSchema
    attributes_class = PermissionAttributes

    query_mapping = {
        'query': ['user_id'],
        'filter': {
            'id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'role': 'role',
            'user_dict': 'user_dict'
        }
    }

    @classmethod
    def initialize_data(cls, user_attributes_obj, initialized_data):
        company_id = user_attributes_obj.get('custom:company_id')
        user_id = user_attributes_obj.get('sub')
        email = user_attributes_obj.get('email')
        username = user_attributes_obj.get('Username')
        phone = user_attributes_obj.get('phone', "")
        birthdate = user_attributes_obj.get('birthdate', "")
        address = user_attributes_obj.get('address', "")

        roles = initialized_data.get("role", [])
        locations = initialized_data.get("location", [])

        role = roles[0]['role']
        role_id = roles[0]['id']
        location_id = locations[0]['id']
        data = [
            {'role': {'ALL': {'data': roles[0], 'label': role, 'value': role_id},
                      location_id: {'data': roles[0], 'label': role, 'value': role_id}},
             'user_dict': {
                 "username": username,
                 "phone": phone,
                 "birthdate": birthdate,
                 "email": email,
                 "address": address,
                 "name": username
             }}
        ]
        init_data = [BasicAttributes.add_basic_attributes({**item, "user_id": user_id}, company_id) for item in
                     data]
        initialized_data[cls.table_name] = init_data
        cls.batch_add(init_data)
