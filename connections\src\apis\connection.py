import base64
import os
import urllib

from nolicore.utils.api import api_message
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import ApiExp, NotFoundRequest, BadRequest
from nolicore.utils.utils import logger

from helpers.custom_jsonpath import get_value_from_jsonpath
from helpers.utils import RESOURCE_SERVICE
from integrations.channels.channel_library import OAuth2Channel
from integrations.channels.connection_base import OAuth2Connection
from integrations.channels.connection_registry import connection_registry
from integrations.channels.connection_types import ConnectionStatus
from integrations.common.base_api_library import BaseAPILibrary
from integrations.common.connection_helper import _create_connection, get_connection_by_id, process_oauth2_callback, \
    process_connection, generate_response
from integrations.integration_exception import ConnectionTestFailedException
from models.integration.connection import ConnectionModel, ConnectionByCompany

FACEBOOK_APP_ID = os.environ['FACEBOOK_APP_ID']
FACEBOOK_APP_SECRET = os.environ['FACEBOOK_APP_SECRET']
ZALO_OA_APP_ID = os.environ['ZALO_OA_APP_ID']
ZALO_OA_APP_SECRET = os.environ['ZALO_OA_APP_SECRET']
ZALO_OA_SECRET = os.environ['ZALO_OA_SECRET']
APP_URL = os.environ['APP_URL']


@as_api()
def get_connection_types(api_gateway_request: ApiGatewayRequest):
    connection_types = []
    for key, connection_class in connection_registry.get_all().items():
        info = connection_class.get_channel_class().get_info()
        if info:
            connection_types.append({
                "key": key,
                **info
            })
        else:
            logger.info(f"Connection type {key} does not have info")
    return {"connection_types": connection_types}


@as_api()
def get_connection_type(api_gateway_request: ApiGatewayRequest):
    channel_name = api_gateway_request.path_parameters['channel_name']
    connection_class = connection_registry.get(channel_name)
    if not connection_class:
        raise NotFoundRequest("Channel not found")
    channel_class = connection_class.get_channel_class()
    info = channel_class.get_info()
    integration_info = channel_class.get_integration_info()
    features = channel_class.get_features()
    return {
        "key": channel_name,
        **info,
        **integration_info,
        "features": features
    }


@as_api()
def get_default_connections(api_gateway_request: ApiGatewayRequest):
    connection_type = api_gateway_request.path_parameters.get('connection_type')
    if not connection_type:
        raise BadRequest("Missing connection_type")
    connections = ConnectionByCompany.get_connections_by_credentials(connection_type, api_gateway_request.company_id)
    return connections or []


@as_api()
def get_connection_setup_fields(api_gateway_request: ApiGatewayRequest):
    connection_type = api_gateway_request.path_parameters.get('connection_type')
    connection_class = connection_registry.get(connection_type)
    if not connection_class:
        raise NotFoundRequest("Connection type not found")
    return {"setup_fields": connection_class.get_setup_fields()}


@as_api()
def setup_default_connection(api_gateway_request: ApiGatewayRequest):
    connection_type = api_gateway_request.path_parameters.get('connection_type')
    redirect_url = api_gateway_request.body.get('redirect_url')
    base_url = f"https://{api_gateway_request.headers.get('host')}"
    if not connection_type:
        raise BadRequest("Missing connection_type")
    if connection_type == "facebook_oauth":
        connection_credentials = {
            "app_id": FACEBOOK_APP_ID,
            "app_secret": FACEBOOK_APP_SECRET,
            "redirect_uri": BaseAPILibrary.get_callback_url(base_url)
        }
    elif connection_type == "zalo_oa":
        connection_credentials = {
            "app_id": ZALO_OA_APP_ID,
            "secret_key": ZALO_OA_APP_SECRET,
            "oa_secret_key": ZALO_OA_SECRET,
        }
    else:
        raise BadRequest(f"Channel {connection_type} is not supported")

    try:
        processed_connection, auth_url = _create_connection(connection_type, api_gateway_request.company_id,
                                                            connection_credentials, base_url, redirect_url=redirect_url)
        return generate_response(processed_connection, auth_url, is_new=True)
    except BadRequest as e:
        raise ApiExp(str(e))
    except Exception as e:
        logger.error(f"Unexpected error in setup_default_connection: {str(e)}")
        raise ApiExp("An unexpected error occurred while setting up the connection")


@as_api()
def setup_connection(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    connection_type = body.get("connection_type")
    settings = body.get("settings")
    redirect_url = body.get("redirect_url")
    base_url = f"https://{api_gateway_request.headers.get('host')}"
    try:
        processed_connection, auth_url = _create_connection(connection_type, api_gateway_request.company_id, settings,
                                                            base_url, redirect_url)
        return generate_response(processed_connection, auth_url, is_new=True)
    except BadRequest as e:
        raise ApiExp(str(e))
    except Exception as e:
        logger.error(f"Unexpected error in setup_connection: {str(e)}")
        raise ApiExp("An unexpected error occurred while setting up the connection")


@as_api()
def update_connection_settings(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    connection_id = api_gateway_request.path_parameters.get('connection_id')
    new_settings = body.get("settings")
    redirect_url = body.get("redirect_url")

    if not connection_id or not new_settings:
        raise BadRequest("Missing required parameters")

    try:
        connection = get_connection_by_id(connection_id, company_id=api_gateway_request.company_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        # Create a copy of the connection with updated settings
        updated_connection = connection.copy()

        # Manually update the settings attributes
        for key, value in new_settings.items():
            if hasattr(updated_connection.attributes.settings, key):
                setattr(updated_connection.attributes.settings, key, value)

        # Process the updated connection (this will test before saving)
        base_url = f"https://{api_gateway_request.headers.get('host')}"
        processed_connection, auth_url = process_connection(updated_connection, is_new=False, base_url=base_url,
                                                            redirect_url=redirect_url)

        return generate_response(processed_connection, auth_url, is_new=False)
    except BadRequest as e:
        raise ApiExp(str(e))
    except Exception as e:
        logger.error(f"Unexpected error in update_connection_settings: {str(e)}")
        raise ApiExp("An unexpected error occurred while updating the connection")


@as_api()
def oauth2_callback(api_gateway_request: ApiGatewayRequest):
    query_params = api_gateway_request.query_string_parameters
    state = query_params.get('state')  # Assuming you're using the state parameter to store the connection ID
    return process_oauth2_callback(state, query_params)


def extract_code_and_scope(encoded_body):
    decoded_body = base64.b64decode(encoded_body).decode('utf-8')

    # URL decode the body content and parse it
    decoded_body = urllib.parse.unquote_plus(decoded_body)
    params = urllib.parse.parse_qs(decoded_body)

    # Extract 'code' and 'scope' parameters
    code = params.get('code', [None])[0]
    scope = params.get('scope', [None])[0]

    return code, scope


@as_api()
def oauth2_callback_post(api_gateway_request: ApiGatewayRequest):
    query_params = api_gateway_request.query_string_parameters
    state = query_params.get('state')  # Assuming you're using the state parameter to store the connection ID
    code, scope = extract_code_and_scope(api_gateway_request.body)
    query_params['code'] = code
    query_params['scope'] = scope
    return process_oauth2_callback(state, query_params)


@as_api()
def oauth2_callback_path(api_gateway_request: ApiGatewayRequest):
    state = api_gateway_request.path_parameters.get('state')
    query_params = api_gateway_request.query_string_parameters
    return process_oauth2_callback(state, query_params)


@as_api()
def get_connection(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    connection = get_connection_by_id(connection_id, company_id=api_gateway_request.company_id, enrich_setting=True,
                                      redact_data=True)

    if not connection:
        raise NotFoundRequest("Connection not found")
    return connection.attributes_dict


@as_api()
def get_connections_by_company(api_gateway_request: ApiGatewayRequest):
    params = api_gateway_request.query_string_parameters or {}
    return ConnectionModel.search(params, service=RESOURCE_SERVICE, company_id=api_gateway_request.company_id)


@as_api()
def get_authorization_link(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    company_id = api_gateway_request.company_id
    connection = OAuth2Connection.get(company_id, connection_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    connection_class = connection_registry.get(connection.attributes.channel_name)
    if not connection_class or not issubclass(connection_class, OAuth2Connection):
        raise BadRequest("Invalid connection type for OAuth2 authorization")

    oauth2_connection = connection_class(connection.attributes_dict)
    auth_channel: OAuth2Channel = oauth2_connection.get_channel()
    base_url = f"https://{api_gateway_request.headers.get('host')}"
    redirect_url = connection.attributes_dict.get('settings', {}).get('redirect_url')
    return {
        "authorization_url": auth_channel.initiate_oauth_flow(redirect_url, base_url),
        "connection_id": oauth2_connection.attributes.id
    }


@as_api()
def test_connection(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters.get('connection_id')

    if not connection_id:
        raise BadRequest("Missing connection_id parameter")

    try:
        connection = get_connection_by_id(connection_id, company_id=api_gateway_request.company_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        channel = connection.get_channel()
        if hasattr(channel, 'setup_webhooks'):
            channel.setup_webhooks()
        test_result = channel.test_connection()

        if test_result:
            return {"message": "Connection test successful", "status": "success"}
        else:
            return {"message": "Connection test failed", "status": "failure"}

    except ConnectionTestFailedException as e:
        return {"message": f"Connection test failed: {str(e)}", "status": "failure"}
    except Exception as e:
        logger.error(f"Unexpected error in test_connection: {str(e)}")
        raise BadRequest("An unexpected error occurred while testing the connection")


@as_api()
def switch_connection_status(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    connection = get_connection_by_id(connection_id)
    if not connection:
        raise NotFoundRequest("Connection not found")
    if connection.attributes.status == ConnectionStatus.ACTIVE:
        connection.deactivate_connection()
        return api_message("Deactivate connection successfully!")
    try:
        channel = connection.get_channel()
        if hasattr(channel, 'setup_webhooks'):
            channel.setup_webhooks()
        test_result = channel.test_connection()
        if test_result:
            connection.activate_connection()
            return api_message("Activate connection successfully!")
        else:
            raise BadRequest(f"Connection test failed. Activate connection failed!")
    except Exception as e:
        logger.error(f"Unexpected error in switch_connection_status: {str(e)}")
        raise BadRequest(f"An unexpected error occurred while switching the connection status: {str(e)}")
