import uuid
from typing import Dict, Any

from helpers.common import format_date_from_iso8601_string, convert_to_kebab_case
from integrations.channels.action_types import ActionGroup, ActionType
from models.order.order import OrderAttributesSchema
from ...base import DestinationTransformer


class MisaAmisReturnOrderDestinationTransformer(DestinationTransformer):
    channel_name = 'misa_amis'
    object_type = ActionGroup.return_order.value

    def _map_object_type_to_action_type(self) -> str:
        return ActionType.sync_return_order.value

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.return_order

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        branch_id = self.connection_settings.get('dynamic_settings', {}).get('branch_id', None)
        stock_mapping = self.connection_settings.get('dynamic_settings', {}).get('stock_mapping', {})
        account_object_mapping = self.connection_settings.get('dynamic_settings', {}).get('account_object_mapping', {})
        account_mapping = self.connection_settings.get('dynamic_settings', {}).get('account_mapping', {})

        depot_id = data['source'].get('origin_id', '')
        account_object = account_object_mapping.get(depot_id, {})
        shipping_address = data['billing_address']['address1']

        def get_vat_rate(vat_value):
            """
            Map VAT rate to MISA's format
            0%: 0
            5%: 5
            8%: 8
            10%: 10
            KCT: -1 (Không chịu thuế)
            KKKNT: -2 (Không kê khai nộp thuế)
            KHAC: -3 (Khác)
            """
            vat_map = {
                0: 0,  # 0%
                5: 5,  # 5%
                8: 8,  # 8%
                10: 10,  # 10%
                -1: -1,  # KCT
                -2: -2,  # KKKNT
                -3: -3  # KHAC
            }
            return vat_map.get(vat_value, -3)

        def calculate_vat_amount(price: float, quantity: float, vat_rate: int) -> float:
            """Calculate VAT amount based on price, quantity and VAT rate."""
            if vat_rate < 0:  # For special cases (KCT, KKKNT, KHAC)
                return 0
            return (price * quantity * vat_rate) / 100

        def convert_product_detail(product: Dict[str, Any], index: int, order: Dict[str, Any]) -> Dict[str, Any]:
            """Convert a single product from Nhanh format to MISA detail format."""
            price = float(product['unit_price'])
            quantity = float(product['quantity'])
            discount = float(product.get('discount', 0))
            amount = price * quantity
            discount_amount = discount * quantity
            vat_rate = get_vat_rate(product.get('vat', 0))
            vat_amount = calculate_vat_amount(price, quantity, product.get('vat', 0))
            stock_object = stock_mapping.get(depot_id, {})

            new_product = {
                **stock_object,
                "sort_order": index + 1,
                "is_promotion": False,
                "quantity": quantity,
                "unit_price": price,
                "amount_oc": amount,
                "amount": amount,
                "discount_rate": 0.0,
                "main_convert_rate": 1.0,
                "main_quantity": quantity,
                "discount_amount_oc": discount_amount,
                "discount_amount": discount_amount,
                "vat_rate": vat_rate,
                "vat_amount_oc": vat_amount,
                "vat_amount": vat_amount,
                "main_unit_price": price,
                "description": product['name'],
                "inventory_item_name": product['variant_name'],
                "exchange_rate_operator": "*",
                "inventory_item_code": product['sku'],
                "main_unit_name": "Cái",
                "unit_name": "Cái",
                "currency_id": "VND",
                "quantity_remain": quantity,
                "main_quantity_remain": 0.0,
                "status": 0,
                "discount_type": 1,
                "discount_rate_voucher": 0,
                "inventory_item_type": 0,
                "debit_account": "5212",
                "credit_account": "131",
                "cost_account": "632",
                "stock_account": "1561",
                **account_mapping,
                "vat_description": f"Thuế GTGT - {product['name']}",
                "purchase_purpose_code": "1",
                "inventory_resale_type_id": 0,
            }

            if vat_rate == -3:
                new_product['other_vat_rate'] = product.get('vat')
            return new_product

        created_at = format_date_from_iso8601_string(data['created_at'])
        detail = []
        discount_rate_voucher = 0
        for idx, product in enumerate(data['return_order_line_items']):
            detail.append(convert_product_detail(product, idx, data))
            if float(product.get('discount', 0)) > 0:
                discount_rate_voucher += float(product.get('discount', 0))

        org_refid = f"{convert_to_kebab_case(data['source']['name'])}-{data['external_id']}"
        employee_name = data['staff']['name'] if data['staff'] is not None else ""

        misa_order = {
            "detail": detail,
            "pu_invoice": {
                "branch_id": None,
                "reftype": 3403,
                "refdate": created_at,
                "not_in_vat_declaration": False,
                "posted_date": created_at,
                "inv_date": created_at,
                "is_posted_finance": True,
                "include_invoice": 1,
                "account_object_tax_code": "**********",
                **account_object,
                "account_object_address": shipping_address,
                # "inv_link_code": data['external_id'],
                # "inv_link": f"https://app.nhanh.vn/return_order/view/id/{data['external_id']}",
                "inv_no": org_refid
            },
            "in_inward": {
                "reftype": 2013,
                "branch_id": None,
                "refdate": created_at,
                "posted_date": created_at,
                "is_posted_finance": True,
                "is_return_with_inward": True,
                "exchange_rate": 1.0,
                "refno_finance": org_refid,
                **account_object,
                "account_object_address": shipping_address,
                "employee_name": employee_name,
                "journal_memo": data['note'],
                "in_reforder": data['created_at'],
            },
            "branch_id": None,
            "is_posted_finance": True,
            "refdate": created_at,
            "org_refid": org_refid,
            "org_refno": org_refid,
            "employee_name": employee_name,
            "inv_date": created_at,
            **account_object,
            "account_object_address": shipping_address,
            "exchange_rate": 1.0,
            "currency_id": "VND",
            "voucher_type": 12,
            "reftype": 3542,
            "org_reftype": 3542,
            "org_reftype_name": "Đơn trả",
            "posted_date": created_at,
            "is_return_with_inward": True,
            "refno_finance": org_refid,
            "journal_memo": data['note'],
            "created_date": data['created_at'],
            "created_by": data.get('createdByName'),
            "in_reforder": data['created_at'],
            "modified_date": data['created_at'],
            "total_amount": data['total'],
            "total_amount_oc": data['total'],
            "inv_no": org_refid
        }
        # Transform master format to MisaAmis order data
        seen_codes = set()
        dictionary = []
        if branch_id:
            for item in misa_order.get('detail', []):
                item_code = item.get('inventory_item_code')
                if item_code in seen_codes:
                    continue
                seen_codes.add(item_code)
                dictionary.append({
                    "dictionary_type": 3,
                    "inventory_item_name": item['inventory_item_name'],
                    "purchase_description": item['inventory_item_name'],
                    "sale_description": item['inventory_item_name'],
                    "inventory_item_code": item_code,
                    "inventory_item_id": str(uuid.uuid4()),
                    "inventory_item_type": 0,
                    "branch_id": branch_id,  ## thay branch_id để danh mục đc đẩy lên
                    "inactive": False
                })
        misa_order['dictionary'] = dictionary
        return misa_order

    @property
    def input_schema(self):
        return OrderAttributesSchema().dump(OrderAttributesSchema())

    @property
    def output_schema(self):
        # This should reflect the MisaAmis API order schema
        return {
            "type": "object",
            "properties": {
                "detail": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "sort_order": {"type": "integer"},
                            "is_promotion": {"type": "boolean"},
                            "quantity": {"type": "number"},
                            "unit_price": {"type": "number"},
                            "amount_oc": {"type": "number"},
                            "amount": {"type": "number"},
                            "discount_rate": {"type": "number"},
                            "main_convert_rate": {"type": "number"},
                            "main_quantity": {"type": "number"},
                            "discount_amount_oc": {"type": "number"},
                            "discount_amount": {"type": "number"},
                            "vat_rate": {"type": "number"},
                            "vat_amount_oc": {"type": "number"},
                            "vat_amount": {"type": "number"},
                            "main_unit_price": {"type": "number"},
                            "description": {"type": "string"},
                            "inventory_item_name": {"type": "string"},
                            "exchange_rate_operator": {"type": "string"},
                            "inventory_item_code": {"type": "string"},
                            "account_object_name": {"type": "string"},
                            "currency_id": {"type": "string"},
                            "quantity_remain": {"type": "number"},
                            "main_quantity_remain": {"type": "number"},
                            "status": {"type": "integer"},
                            "is_description": {"type": "boolean"},
                            "discount_type": {"type": "integer"},
                            "discount_rate_voucher": {"type": "number"},
                            "inventory_item_type": {"type": "integer"}
                        }
                    }
                },
                "refdate": {"type": "string"},
                "voucher_type": {"type": "integer"},
                "org_refid": {"type": "string"},
                "org_refno": {"type": "string"},
                "org_reftype": {"type": "integer"},
                "org_reftype_name": {"type": "string"},
                "branch_id": {"type": ["null", "string"]},
                "refno": {"type": "string"},
                "delivered_status": {"type": "integer"},
                "account_object_name": {"type": "string"},
                "account_object_address": {"type": "string"},
                "account_object_code": {"type": "string"},
                "account_object_tax_code": {"type": "string"},
                "receiver": {"type": "string"},
                "journal_memo": {"type": "string"},
                "currency_id": {"type": "string"},
                "employee_name": {"type": "string"},
                "reftype": {"type": "integer"},
                "created_date": {"type": "string"},
                "created_by": {"type": "string"},
                "modified_date": {"type": "string"},
                "modified_by": {"type": "string"}
            }
        }
