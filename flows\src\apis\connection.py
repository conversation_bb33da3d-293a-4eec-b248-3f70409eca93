import json
import os
from copy import copy

import pendulum
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import BadRequest, NotFoundRequest
from nolicore.utils.utils import json_dumps, logger

from helpers.destination_data import invoke_import_destination_data, sync_destination_data
from helpers.utils import RESOURCE_SERVICE
from integrations.channels.action_types import ActionGroup, ActionKind, ACTION_GROUP_MAPPING
from integrations.channels.connection_types import ConnectionStatus
from integrations.common.base_api_library import MetaData, FetchAPIResponse
from integrations.common.bucket_helper import get_raw_data, get_transformed_data
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.event import FetchEvent
from integrations.common.queue_helper import enqueue_fetch_event
from models.basic import BasicAttributes
from models.imports.import_record import ImportRecordModel
from models.imports.imports import ImportModel, ImportStatus
from models.integration.connection import ActionType
from models.integration.destination_data import DestinationDataModel
from models.integration.fetch_event import EventSource, FetchEventCompanyIdIndex
from models.integration.sync_record import SyncRecordCompanyIdIndex
from ..libs.helper import get_active_destinations

RAW_DATA_BUCKET = os.environ['RAW_DATA_BUCKET']
TRANSFORMED_DATA_BUCKET = os.environ['TRANSFORMED_DATA_BUCKET']


@as_api()
def sync_record(api_gateway_request: ApiGatewayRequest):
    try:
        payload = api_gateway_request.body
        company_id = api_gateway_request.company_id

        sync_record_id = payload['sync_record_id']
        connection_id, action_type, action_group, filename = sync_record_id.split('/')

        destination_ids = []
        if 'destination_ids' in payload:
            if isinstance(payload['destination_ids'], list):
                raise BadRequest("Destination ids must be a list")
            destination_ids = payload['destination_ids']
            if connection_id in destination_ids:
                raise BadRequest("Can not sync to the same connection")

        connection_obj = get_connection_by_id(connection_id, company_id)
        destinations = get_active_destinations(connection_obj, action_group)
        if not destinations:
            raise BadRequest("No active destinations found")

        for destination_id in destination_ids:
            if destination_id not in [destination['id'] for destination in destinations]:
                raise BadRequest("Invalid destination ids")

        object_id = filename.split('_')[-1].split('.')[0]

        # Convert string values to appropriate enum types
        action_type = ActionType(action_type)
        action_group = ActionGroup(action_group)

        fetch_event = FetchEvent.create_single_object_event(
            channel=connection_obj.attributes.channel_name,
            connection_id=connection_id,
            action_type=action_type,
            action_group=action_group,
            event_time=pendulum.now().to_iso8601_string(),
            object_id=object_id,
            object_data=None,
            event_source=EventSource.sync_record_api,
            destination_ids=destination_ids
        )

        enqueue_fetch_event(fetch_event, company_id)

        return {
            'success': True,
            'message': 'FetchEvent enqueued successfully'
        }

    except BadRequest as e:
        return {
            'success': False,
            'message': str(e)
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'An error occurred: {str(e)}'
        }


@as_api()
def search_records(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    company_id = api_gateway_request.company_id
    if not connection_id:
        raise BadRequest("Missing connection_id parameter")

    connection = get_connection_by_id(connection_id, company_id=company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    params = api_gateway_request.body['params']
    action_type = ActionType(api_gateway_request.body['action_type'])
    action_group = ACTION_GROUP_MAPPING.get(action_type)
    meta = MetaData(
        current_params=params
    )
    event = FetchEvent(
        channel=connection.attributes.channel_name,
        connection_id=connection.attributes.id,
        action_type=action_type,
        action_group=action_group,
        event_time=pendulum.now().to_iso8601_string(),
        is_batch=False,
        meta=meta
    )
    if not connection.should_execute_action(action_type):
        raise BadRequest(f"Connection does not support searching records for action type: {action_type}")
    channel = connection.get_channel()
    fetch_settings = connection.get_fetch_action_settings(event.action_group, action_type)
    result: FetchAPIResponse = channel.execute_action(event.action_type, ActionKind.FETCH, fetch_settings.to_dict(),
                                                      event)
    return result.to_dict()


@as_api()
def search_records_params(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    company_id = api_gateway_request.company_id
    if not connection_id:
        raise BadRequest("Missing connection_id parameter")

    connection = get_connection_by_id(connection_id, company_id=company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    action_type = ActionType(api_gateway_request.body['action_type'])
    action_group = ACTION_GROUP_MAPPING.get(action_type)

    event = FetchEvent(
        channel=connection.attributes.channel_name,
        connection_id=connection.attributes.id,
        action_type=action_type,
        action_group=action_group,
        event_time=pendulum.now().to_iso8601_string(),
        is_batch=False
    )
    channel = connection.get_channel()
    fetch_settings = connection.get_fetch_action_settings(event.action_group, action_type)
    result: FetchAPIResponse = channel.execute_action(event.action_type, ActionKind.PARAMS, fetch_settings.to_dict(),
                                                      event)
    return result.to_dict()


@as_api()
def sync_records(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    company_id = api_gateway_request.company_id
    if not connection_id:
        raise BadRequest("Missing connection_id parameter")

    connection = get_connection_by_id(connection_id, company_id=company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    params = api_gateway_request.body['params']
    action_type = api_gateway_request.body['action_type']
    action_group = ACTION_GROUP_MAPPING[ActionType(action_type)]
    meta = MetaData(
        current_params=params
    )
    event = FetchEvent(
        channel=connection.attributes.channel_name,
        connection_id=connection.attributes.id,
        action_type=action_type,
        action_group=action_group,
        event_time=pendulum.now().to_iso8601_string(),
        is_batch=False,
        meta=meta,
        event_source=EventSource.sync_filter_api
    )
    enqueue_fetch_event(event, company_id)
    return {
        'success': True,
        'message': 'FetchEvent enqueued successfully'
    }


@as_api()
def list_sync_records(api_gateway_request: ApiGatewayRequest):
    params = api_gateway_request.query_string_parameters or {}
    return SyncRecordCompanyIdIndex.search(params, service=RESOURCE_SERVICE, company_id=api_gateway_request.company_id)


@as_api()
def get_sync_records(api_gateway_request: ApiGatewayRequest):
    try:
        payload = api_gateway_request.body
        company_id = api_gateway_request.company_id
        if 'sync_record_id' not in payload:
            raise BadRequest("Missing required field: sync_record_id")

        sync_record_id = payload['sync_record_id']
        show_details = payload.get('show_details', False)

        # Query SyncRecordModel to get all records with the same id
        sync_records = SyncRecordCompanyIdIndex.list({'id': sync_record_id, 'company_id': company_id}, limit=100)[
            'Items']

        if not sync_records:
            return {
                'success': True,
                'message': f'No sync records found for id: {sync_record_id}',
                'data': []
            }

        if show_details:
            raw_data_dict = {}
            transformed_data_dict = {}
            for item in sync_records:
                key = item['id']
                transformed_record_id = item['transformed_record_id']
                raw_record_version = item['raw_record_version']
                transformed_record_version = item['transformed_record_version']

                if raw_record_version in raw_data_dict:
                    raw_data = raw_data_dict[raw_record_version]
                else:
                    try:
                        fetch_event_id, raw_data, standard_data = get_raw_data(RAW_DATA_BUCKET, key, raw_record_version)
                        raw_data_dict[raw_record_version] = raw_data
                    except Exception as e:
                        raw_data_dict[raw_record_version] = None
                        raw_data = None
                if transformed_record_version in transformed_data_dict:
                    transformed_data = transformed_data_dict[transformed_record_version]
                else:
                    try:
                        transformed_data = get_transformed_data(TRANSFORMED_DATA_BUCKET, transformed_record_id,
                                                                transformed_record_version)
                        transformed_data_dict[transformed_record_version] = transformed_data
                    except Exception as e:
                        raw_data_dict[transformed_record_version] = None
                        transformed_data = None

                item['raw_data'] = raw_data
                item['transformed_data'] = transformed_data

        return {
            'success': True,
            'message': f'Sync records retrieved successfully for id: {sync_record_id}',
            'data': {
                'sync_record_id': sync_record_id,
                'record_type': sync_records[0]['record_type'],
                'channels': sync_records
            },
        }

    except BadRequest as e:
        return {
            'success': False,
            'message': str(e)
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'An error occurred: {str(e)}'
        }


@as_api()
def list_fetch_events(api_gateway_request: ApiGatewayRequest):
    params = api_gateway_request.query_string_parameters or {}
    return FetchEventCompanyIdIndex.search(params, service=RESOURCE_SERVICE, company_id=api_gateway_request.company_id)


@as_api()
def get_fetch_events(api_gateway_request: ApiGatewayRequest):
    try:
        company_id = api_gateway_request.company_id
        params = api_gateway_request.query_string_parameters or {}
        fetch_event_id = api_gateway_request.path_parameters['fetch_event_id']
        next_page = params.get('next_page')
        limit = min(params.get('limit', 1000), 3000)
        new_params = {'id': fetch_event_id, 'company_id': company_id}
        if next_page is not None:
            new_params['LastEvaluatedKey'] = next_page
        # Query SyncRecordModel to get all records with the same id
        response = FetchEventCompanyIdIndex.list(new_params, limit=limit)
        fetch_events = response['Items']
        if not fetch_events:
            return {
                'success': True,
                'message': f'No fetch events found for id: {fetch_event_id}',
                'data': []
            }

        return {
            'success': True,
            'message': f'Fetch events retrieved successfully for id: {fetch_event_id}',
            'data': fetch_events,
            'limit': limit,
            'next_page': response.get('LastEvaluatedKey') if len(fetch_events) > 0 else None
        }

    except BadRequest as e:
        return {
            'success': False,
            'message': str(e)
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'An error occurred: {str(e)}'
        }


@as_api()
def import_records(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    company_id = api_gateway_request.company_id
    if not connection_id:
        raise BadRequest("Missing connection_id parameter")

    connection = get_connection_by_id(connection_id, company_id=company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    params = api_gateway_request.body['params']
    action_type = api_gateway_request.body['action_type']
    action_group = ACTION_GROUP_MAPPING[ActionType(action_type)]
    meta = MetaData(
        current_params=params
    )
    action_type = ActionType(action_type)
    if not connection.should_execute_action(action_type):
        raise BadRequest(f"Connection does not support importing data for action type: {action_type}")

    event = FetchEvent(
        channel=connection.attributes.channel_name,
        connection_id=connection.attributes.id,
        action_type=action_type,
        action_group=action_group,
        event_time=pendulum.now().to_iso8601_string(),
        is_batch=False,
        meta=meta,
        event_source=EventSource.import_record_api
    )
    enqueue_fetch_event(event, company_id)
    return {
        'success': True,
        'message': 'FetchEvent enqueued successfully'
    }


@as_api()
def import_records_clone(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    company_id = api_gateway_request.company_id
    if not connection_id:
        raise BadRequest("Missing connection_id parameter")

    connection = get_connection_by_id(connection_id, company_id=company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    params = api_gateway_request.body['params']
    action_type = api_gateway_request.body['action_type']
    action_group = ACTION_GROUP_MAPPING[ActionType(action_type)]
    meta = MetaData(
        current_params=params
    )
    action_type = ActionType(action_type)
    if not connection.should_save_to_storage(action_type):
        raise BadRequest(f"Connection does not support saving data to storage for action type: {action_type}")

    event = FetchEvent(
        channel=connection.attributes.channel_name,
        connection_id=connection.attributes.id,
        action_type=action_type,
        action_group=action_group,
        event_time=pendulum.now().to_iso8601_string(),
        is_batch=False,
        meta=meta,
        event_source=EventSource.import_record_api_clone
    )
    enqueue_fetch_event(event, company_id)
    return {
        'success': True,
        'message': 'FetchEvent enqueued successfully'
    }


def import_destination_data_handle(event, context):
    company_id = event['company_id']
    request_id = event['request_id']
    connection_id = event['connection_id']
    group = event['group']
    start = event.get('start', 0)
    limit = 20
    import_obj = ImportModel.by_key({'id': request_id, 'company_id': company_id})
    destination_data = ImportModel.get_data(company_id, import_obj.attributes.import_type, request_id)
    total = len(destination_data)
    import_record_payload = None
    processed = 0
    failed = 0
    skipped = 0

    try:
        end = start + limit if start + limit < len(destination_data) else len(destination_data)
        processing = destination_data[start:end]
        for destination_data in processing:
            origin_destination_data = copy(destination_data)

            try:
                processed += 1
                record = sync_destination_data(company_id, destination_data, connection_id, group)
                response = json.loads(json_dumps(record))
                response_type = 'success'
                response_code = None
            except BadRequest as ex:
                failed += 1
                response = ex.to_json()
                response_type = 'error'
                response_code = ex.error_code
            except Exception as ex:
                failed += 1
                logger.exception(ex)
                response = {'error': str(ex)}
                response_type = 'error'
                response_code = 'unknown'

            import_record_payload = BasicAttributes.add_basic_attributes({
                'id': request_id,
                'record_id': origin_destination_data['id'],
                'record': origin_destination_data,
                'record_type': import_obj.attributes.import_type.value,
                'response_type': response_type,
                'response_code': response_code,
                'response': response,
                'is_clone': True
            }, company_id)
            ImportRecordModel(import_record_payload).save()

        # invoke self
        if end < total:
            invoke_import_destination_data(request_id, company_id, connection_id, group, start=end)
            status = ImportStatus.PROCESSING.value
        else:
            status = ImportStatus.COMPLETED.value

        import_obj.update({
            'total': total,
            'processed': import_obj.attributes.processed + processed if start != 0 else processed,
            'failed': import_obj.attributes.failed + failed if start != 0 else failed,
            'skipped': import_obj.attributes.skipped + skipped if start != 0 else skipped,
            'status': status,
            'is_clone': True,
            'finished_at': pendulum.now().to_iso8601_string(),
            'message': None
        })

    except Exception as ex:
        logger.exception({
            'ex': ex,
            'import_record_payload': import_record_payload
        })
        import_obj.update({
            'total': total,
            'processed': import_obj.attributes.processed + processed if start != 0 else processed,
            'failed': import_obj.attributes.failed + failed if start != 0 else failed,
            'status': ImportStatus.FAILED.value,
            'is_clone': True,
            'finished_at': pendulum.now().to_iso8601_string(),
            'message': str(ex)
        })


@as_api()
def get_destination_data(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    group = api_gateway_request.path_parameters['group']
    company_id = api_gateway_request.company_id
    query_params = api_gateway_request.query_string_parameters or {}
    if not connection_id:
        raise BadRequest("Missing connection_id parameter")
    if group not in [action.value for action in ActionGroup]:
        raise BadRequest(f"Invalid group parameter {group}")
    connection = get_connection_by_id(connection_id, company_id=company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    if connection.attributes.status == ConnectionStatus.INACTIVE:
        raise BadRequest("Connection is inactive")

    destination_data = DestinationDataModel.search({**query_params, 'connection_id': connection_id, 'type': group},
                                                   service=RESOURCE_SERVICE,
                                                   company_id=company_id)
    return destination_data


@as_api()
def get_details_destination_data(api_gateway_request: ApiGatewayRequest):
    destination_data_id = api_gateway_request.path_parameters['destination_data_id']
    connection_id = api_gateway_request.path_parameters['connection_id']
    group = api_gateway_request.path_parameters['group']
    company_id = api_gateway_request.company_id
    if not connection_id:
        raise BadRequest("Missing connection_id parameter")
    if group not in [action.value for action in ActionGroup]:
        raise BadRequest(f"Invalid type parameter {group}")
    connection = get_connection_by_id(connection_id, company_id=company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    if connection.attributes.status == ConnectionStatus.INACTIVE:
        raise BadRequest("Connection is inactive")

    destination_data = DestinationDataModel.get_detail(destination_data_id, connection_id, group)
    if not destination_data:
        raise NotFoundRequest("Destination data not found")
    return destination_data.attributes_dict
