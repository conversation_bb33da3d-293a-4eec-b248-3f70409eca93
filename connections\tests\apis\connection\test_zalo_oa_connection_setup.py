import json
import unittest
import uuid
import os
from dotenv import load_dotenv
from tests.base import BaseTestCase

from connections.src.apis.connection import setup_connection
from integrations.channels.zalo_oa.zalo_oa_connection import ZaloOAConnection
from connections.src.apis.connection import get_connection_by_id


class ZaloOAConnectionSetupIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.app_id = "1447241861612872028"
        cls.oa_secret_key = "j8Q876s512TQ3HWAnKMc"
        cls.redirect_uri = ""

    def setUp(self):
        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"

    def create_lambda_event(self, body):
        return {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_setup_connection(self):
        event = {'version': '2.0', 'routeKey': 'POST /setup/{connection_type}', 'rawPath': '/setup/zaloOA',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cyPGL9DHSQlwaCy7bSyxYzWY40Slv9PvUGDOcHuC_4htDeG-K_mcvpk7U4JnUalR3h0M_9MlCxUa0khBbTR3hnfNZYbZnUjCogrzdWqy1W4aTCKVuZBRh_xv1nJfqmNNM70P8NZBMabUX2Az8Gu17O8eZTFy91VZPoRpjBTlKM3xyydJqQtUpNXiwGrjZUDrNOZ7W_hiVoSKkPNd7RkIcXZFjt0BjbIUObdcSSEEDg2Xii1qR9jNZSs17ZGqQT1_Th6diNCSvutj1Q8RginIjQOQIi6eJ8X0F9f8lqbuLpKk9GH1eNTUPi7GF02kO6zECsEbFy_1On8B6tM8IpAApw',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-US,en;q=0.9',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VuITeZecWTfrcrH25iqURPqhYHJpAEbgnlZIxL7qYh0dGsOv5eZc_mI7zRzmpp5duzQn0sqFX67KnpyHuRoDCj0ZfVVJd0vyA2nQfULIpHasSkXeihWygEUnN5_5DY3curRo064PrelOX3kBPGuYQ9hI5PQhkVP_NDjKF33VQS2Dq6LOqEqjnqHgB9focBeVGrmQjb4bGnHBY9qhitN1I-VA8pfSZu9CkYbpYDJn5aBmnVGeCts_DoL3_whpqVGNwIjyaIoCDXHyNuGeVVR8vA5uSzJSbY8XueNJxA3iBwvHtmsBRZEBFdQawCHijWloH7scHVg7j0Soqibu_lFifw',
                             'content-length': '147', 'content-type': 'application/json',
                             'host': 'api-staging.optiwarehouse.com',
                             'origin': 'https://staging.optiwarehouse.com', 'priority': 'u=1, i',
                             'referer': 'https://staging.optiwarehouse.com/',
                             'sec-ch-ua': '"Chromium";v="136", "Brave";v="136", "Not.A/Brand";v="99"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"',
                             'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site',
                             'sec-gpc': '1',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                             'x-amzn-trace-id': 'Root=1-68356120-50a7c66f627dbc010b6acd0f',
                             'x-forwarded-for': '************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cyPGL9DHSQlwaCy7bSyxYzWY40Slv9PvUGDOcHuC_4htDeG-K_mcvpk7U4JnUalR3h0M_9MlCxUa0khBbTR3hnfNZYbZnUjCogrzdWqy1W4aTCKVuZBRh_xv1nJfqmNNM70P8NZBMabUX2Az8Gu17O8eZTFy91VZPoRpjBTlKM3xyydJqQtUpNXiwGrjZUDrNOZ7W_hiVoSKkPNd7RkIcXZFjt0BjbIUObdcSSEEDg2Xii1qR9jNZSs17ZGqQT1_Th6diNCSvutj1Q8RginIjQOQIi6eJ8X0F9f8lqbuLpKk9GH1eNTUPi7GF02kO6zECsEbFy_1On8B6tM8IpAApw'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d',
                                'custom:is_init': 'DONE', 'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin',
                                'email': '<EMAIL>', 'event_id': '56689f66-fb35-4565-9268-6afe8b81d46a',
                                'exp': '**********', 'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': '64e0b803-88af-4457-a8de-5003e1dab81e',
                                'origin_jti': '6a7188bb-995a-4c25-89a9-2c54ad7a6e41',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'},
                     'scopes': None}}, 'domainName': 'api-staging.optiwarehouse.com',
                                    'domainPrefix': 'api-staging',
                                    'http': {'method': 'POST', 'path': '/setup/zaloOA', 'protocol': 'HTTP/1.1',
                                             'sourceIp': '************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                    'requestId': 'LNwdGgHkyQ0EPtA=', 'routeKey': 'POST /setup/{connection_type}',
                                    'stage': '$default', 'time': '27/May/2025:06:52:16 +0000',
                                    'timeEpoch': 1748328736289}, 'pathParameters': {'connection_type': 'zaloOA'},
                 'body': '{"connection_type":"zaloOA","settings":{"app_id":"1447241861612872028","code_challenge":"1","redirect_uri":"","secret_key":"j8Q876s512TQ3HWAnKMc"}}',
                 'isBase64Encoded': False}
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

    def test_setup_zalo_oa_connection_success(self):
        # Arrange
        event_body = {
            "connection_type": "zalo_oa",
            "settings": {
                "app_id": self.app_id,
                "secret_key": self.oa_secret_key,
                "redirect_uri": self.redirect_uri
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'zalo oa connection setup successful')
        self.assertIn('connection_id', response_body)

        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = ZaloOAConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.app_id, self.app_id)
        self.assertEqual(created_connection.attributes.secret_key, self.oa_secret_key)
        self.assertEqual(created_connection.attributes.redirect_uri, self.redirect_uri)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status, 'ACTIVE')

        # Clean up
        created_connection.delete()

    def test_setup_zalo_oa_connection_invalid_credentials(self):
        # Arrange
        event_body = {
            "connection_type": "zalo_oa",
            "settings": {
                "app_id": "invalid_app_id",
                "secret_key": "invalid_secret_key",
                "redirect_uri": "invalid_redirect_uri"
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['error'], 'Bad request')
        self.assertIn('Invalid Zalo OA credentials', response_body['message'])

    def test_test_connection(self):
        # Arrange
        event_body = {
            "connection_type": "zalo_oa",
            "settings": {
                "app_id": self.app_id,
                "secret_key": self.oa_secret_key,
                "redirect_uri": self.redirect_uri
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertIn('connection_id', response_body)

        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = ZaloOAConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.app_id, self.app_id)
        self.assertEqual(created_connection.attributes.secret_key, self.oa_secret_key)
        self.assertEqual(created_connection.attributes.redirect_uri, self.redirect_uri)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status, 'ACTIVE')

        # Clean up
        created_connection.delete()

    def test_get_auth_link_channel(self):
        connection_id = "7314ccaa-9230-4590-99db-7590608417df"
        connection = get_connection_by_id(connection_id)
        channel = connection.get_channel()
        auth_link = channel.initiate_oauth_flow(base_url="https://api-dev.onexbots.com")


if __name__ == '__main__':
    unittest.main()
