search_event = {'version': '2.0', 'routeKey': 'POST /connections/{connection_id}/search_records',
                'rawPath': '/connections/6b5ca363-434b-4127-8258-3bbf1afc5e50/search_records',
                'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.d4Lze9cQK6kLY-xFYbi0J8CNKlrtRrZtK3DK-cizOiW9qe0EE6d2cBAjdssxjQ_kNYTms5W9NOgczyc0uwYhreFzfsGJEmTDVLq9RxTeb9bTycJAH30_KJ0PJ8P26x86p51CgIYjC9dS7d8Q8lmsA4_DY0zePp96qyBwtUaSmk3QiiIi5j92QZW_6WnBYGjDmrX0TZYleVSikuLc_bvXUPBC0DN10aXQiye6DLN2v4NKNxWhUqxv8CqhMp-GR01bzyuSmdzlzqu_7hkqa9OZeDvz264Icl1LEgMDXwPx-UcfEtDFsTq3AUt5PsP0svIFy1YRmGikOULcm3A9gPhjBQ',
                'headers': {'accept': 'application/json, text/plain, */*', 'accept-encoding': 'gzip, deflate, br, zstd',
                            'accept-language': 'en,vi;q=0.9,en-US;q=0.8',
                            'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tvFVGbPZa8TOX0v5GEYwm6wTehQjNBJznreR_esPk3W_axNCyH4B5ohahtIsltXdCF39g8g_RCKNDAFr3VLycM0AYu1qUA9ZujSXpwoi9mwj1qn1gOWMxpMDo88zqiG_eZKIrLXFEyKcHwBUjSyoVdpXmgvwIU2Qgin7je4y-YSaqCY3G48gEI3tl3Ov8y-OkgSy9_RdceK5Thowe3-UEILpBB6-fcogYQyjtdbiLHdtL37aXORqdpnuGevzBY82Xq9C00wWK3niiO06nWGE3gxhD6s5HF9nSaSoIdu38FIUEJj_Fn5J6O9H9y9C6WkF0yDZRiUlP7ihwbXlLSXfXA',
                            'content-length': '89', 'content-type': 'application/json', 'host': 'api.optiwarehouse.com',
                            'origin': 'https://app.optiwarehouse.com', 'priority': 'u=1, i',
                            'referer': 'https://app.optiwarehouse.com/',
                            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                            'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                            'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site',
                            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                            'x-amzn-trace-id': 'Root=1-6752a78b-30a2ead03cf1263f4aa3af55',
                            'x-forwarded-for': '*************', 'x-forwarded-port': '443',
                            'x-forwarded-proto': 'https'}, 'queryStringParameters': {
        'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.d4Lze9cQK6kLY-xFYbi0J8CNKlrtRrZtK3DK-cizOiW9qe0EE6d2cBAjdssxjQ_kNYTms5W9NOgczyc0uwYhreFzfsGJEmTDVLq9RxTeb9bTycJAH30_KJ0PJ8P26x86p51CgIYjC9dS7d8Q8lmsA4_DY0zePp96qyBwtUaSmk3QiiIi5j92QZW_6WnBYGjDmrX0TZYleVSikuLc_bvXUPBC0DN10aXQiye6DLN2v4NKNxWhUqxv8CqhMp-GR01bzyuSmdzlzqu_7hkqa9OZeDvz264Icl1LEgMDXwPx-UcfEtDFsTq3AUt5PsP0svIFy1YRmGikOULcm3A9gPhjBQ'},
                'requestContext': {'accountId': '************', 'apiId': 'vxwk2of2ii', 'authorizer': {'jwt': {
                    'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                               'cognito:groups': '[4c8e6154-fba3-4837-909b-8a50dff16a2b-User 4c8e6154-fba3-4837-909b-8a50dff16a2b-Admin]',
                               'cognito:username': 'baababy_admin',
                               'custom:company_id': '4c8e6154-fba3-4837-909b-8a50dff16a2b', 'custom:is_init': 'DONE',
                               'custom:role': 'Admin', 'email': '<EMAIL>',
                               'event_id': 'c1fccdba-e7f1-4057-9626-341687bde084', 'exp': '**********',
                               'iat': '**********',
                               'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                               'jti': '33b5c9a5-2476-422c-a95b-231551020ae7',
                               'origin_jti': 'eca15ed1-64fe-470a-a43b-***********d',
                               'sub': '56c8ca59-2214-4d3e-95f8-0fa3178d0031', 'token_use': 'id'}, 'scopes': None}},
                                   'domainName': 'api.optiwarehouse.com', 'domainPrefix': 'api',
                                   'http': {'method': 'POST',
                                            'path': '/connections/6b5ca363-434b-4127-8258-3bbf1afc5e50/search_records',
                                            'protocol': 'HTTP/1.1', 'sourceIp': '*************',
                                            'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                   'requestId': 'CW8dzji6yQ0EJzA=',
                                   'routeKey': 'POST /connections/{connection_id}/search_records', 'stage': '$default',
                                   'time': '06/Dec/2024:07:28:11 +0000', 'timeEpoch': 1733470091128},
                'pathParameters': {'connection_id': '6b5ca363-434b-4127-8258-3bbf1afc5e50'},
                'body': '{"action_type":"get_product","params":{"updatedDateTimeFrom":"2024-12-05T17:00:00.000Z"}}',
                'isBase64Encoded': False}

sync_event = {'version': '2.0', 'routeKey': 'POST /connections/{connection_id}/sync_records',
              'rawPath': '/connections/6b5ca363-434b-4127-8258-3bbf1afc5e50/sync_records',
              'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.p4fKm0bN9r-mYWAGCePXZizKawfZBJDxBj1oW3lrrVjozpJ07xCf0yVv_famMWIOw1N0twcLnwEUsoAKqn5-v2hR4ujoltDTv0IlCzAI2vOcmjRTf83NspEW0wchL-2kUdbwM_XVI578mDE7yVddPibQPRTb18KJq1UtUx1nGjpaca3fpBvL-WuqWGJjxBpJASjnxWvLTSZ9Wegf324C7HeXas2nxdl1qUD-LpCGugvzhIGJHwlTr-5FK7Wj8YD90LNOr6o5BiCUlAdYUMovFArMPKbFkA_sABNBnhuAVrfLGujV0zvgTfdrf9GiJuwgLzE_D19DYqcoiCiXfUeWbQ',
              'headers': {'accept': 'application/json, text/plain, */*', 'accept-encoding': 'gzip, deflate, br, zstd',
                          'accept-language': 'en,vi;q=0.9,en-US;q=0.8',
                          'authorization': 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
                          'content-length': '119', 'content-type': 'application/json', 'host': 'api.optiwarehouse.com',
                          'origin': 'https://app.optiwarehouse.com', 'priority': 'u=1, i',
                          'referer': 'https://app.optiwarehouse.com/',
                          'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                          'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                          'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site',
                          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                          'x-amzn-trace-id': 'Root=1-6752f4f9-50283c394c5a23bd7e21fb0a',
                          'x-forwarded-for': '*************', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https'},
              'queryStringParameters': {
                  'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.p4fKm0bN9r-mYWAGCePXZizKawfZBJDxBj1oW3lrrVjozpJ07xCf0yVv_famMWIOw1N0twcLnwEUsoAKqn5-v2hR4ujoltDTv0IlCzAI2vOcmjRTf83NspEW0wchL-2kUdbwM_XVI578mDE7yVddPibQPRTb18KJq1UtUx1nGjpaca3fpBvL-WuqWGJjxBpJASjnxWvLTSZ9Wegf324C7HeXas2nxdl1qUD-LpCGugvzhIGJHwlTr-5FK7Wj8YD90LNOr6o5BiCUlAdYUMovFArMPKbFkA_sABNBnhuAVrfLGujV0zvgTfdrf9GiJuwgLzE_D19DYqcoiCiXfUeWbQ'},
              'requestContext': {'accountId': '************', 'apiId': 'vxwk2of2ii', 'authorizer': {'jwt': {
                  'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                             'cognito:groups': '[4c8e6154-fba3-4837-909b-8a50dff16a2b-User 4c8e6154-fba3-4837-909b-8a50dff16a2b-Admin]',
                             'cognito:username': 'baababy_admin',
                             'custom:company_id': '4c8e6154-fba3-4837-909b-8a50dff16a2b', 'custom:is_init': 'DONE',
                             'custom:role': 'Admin', 'email': '<EMAIL>',
                             'event_id': 'c1fccdba-e7f1-4057-9626-341687bde084', 'exp': '**********',
                             'iat': '**********',
                             'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                             'jti': 'faa792a8-0db2-49fb-a0e8-57e38bd3e014',
                             'origin_jti': 'eca15ed1-64fe-470a-a43b-***********d',
                             'sub': '56c8ca59-2214-4d3e-95f8-0fa3178d0031', 'token_use': 'id'}, 'scopes': None}},
                                 'domainName': 'api.optiwarehouse.com', 'domainPrefix': 'api',
                                 'http': {'method': 'POST',
                                          'path': '/connections/6b5ca363-434b-4127-8258-3bbf1afc5e50/sync_records',
                                          'protocol': 'HTTP/1.1', 'sourceIp': '*************',
                                          'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                 'requestId': 'CXs3AjsmyQ0EMDQ=',
                                 'routeKey': 'POST /connections/{connection_id}/sync_records', 'stage': '$default',
                                 'time': '06/Dec/2024:12:58:33 +0000', 'timeEpoch': 1733489913291},
              'pathParameters': {'connection_id': '6b5ca363-434b-4127-8258-3bbf1afc5e50'},
              'body': '{"action_type":"get_return_order","params":{"fromDate":"2024-11-30T17:00:00.000Z","toDate":"2024-12-06T12:56:24.900Z"}}',
              'isBase64Encoded': False}

product_shopify_oauth_test_raw_data = {'category': {'fullName': 'Apparel & Accessories > Clothing'},
                                       'createdAt': '2025-03-18T09:20:21Z', 'description': '',
                                       'id': 'gid://shopify/Product/8116099809325', 'images': {'nodes': [{'altText': '',
                                                                                                          'url': 'https://cdn.shopify.com/s/files/1/0659/4170/0653/files/green-t-shirt.jpg?v=1742289621'}]},
                                       'legacyResourceId': '8116099809325', 'metafields': {
        'nodes': [{'key': 'title_tag', 'value': 'Our awesome T-shirt in 70 characters or less.'},
                  {'key': 'description_tag', 'value': 'A great description of your products in 320 characters or less'},
                  {'key': 'google_product_category', 'value': 'Apparel & Accessories > Clothing'},
                  {'key': 'gender', 'value': 'Unisex'}, {'key': 'age_group', 'value': 'Adult'}]}, 'options': [
        {'name': 'Title', 'values': ['Lithograph - Height: 9" x Width: 12"', 'Small', 'Medium']}],
                                       'publishedAt': '2025-03-18T09:20:19Z', 'tags': ['mens t-shirt example'],
                                       'title': 'Example T-Shirt', 'updatedAt': '2025-03-20T07:39:50Z', 'variants': {
        'nodes': [{'barcode': '', 'displayName': 'Example T-Shirt - Lithograph - Height: 9" x Width: 12"',
                   'id': 'gid://shopify/ProductVariant/43337826402349', 'price': '25',
                   'selectedOptions': [{'name': 'Title', 'value': 'Lithograph - Height: 9" x Width: 12"'}],
                   'sku': 'OWG'}, {'barcode': '', 'displayName': 'Example T-Shirt - Small',
                                   'id': 'gid://shopify/ProductVariant/43337826435117', 'price': '20',
                                   'selectedOptions': [{'name': 'Title', 'value': 'Small'}], 'sku': 'ILW'},
                  {'barcode': None, 'displayName': 'Example T-Shirt - Medium',
                   'id': 'gid://shopify/ProductVariant/43337826467885', 'price': '20',
                   'selectedOptions': [{'name': 'Title', 'value': 'Medium'}], 'sku': 'example-shirt-m'}]}}

order_master_data = {
    "external_id": "5801262003688919142",
    "customer": {
        "addresses": [{
            "name": "a***-vn2",
            "phone": "(+86)123******43",
            "address1": "11******************",
            "address2": "",
            "city": "Cau Giay",
            "country": "Vietnam",
            "district": "",
            "ward": ""
        }],
        "customer_code": "7495499109337171978",
        "company_name": "",
        "billing_address": {
            "name": "a***-vn2",
            "phone": "(+86)123******43",
            "address1": "11******************",
            "address2": "",
            "city": "Cau Giay",
            "country": "Vietnam",
            "district": "",
            "ward": ""
        },
        "last_name": "",
        "first_name": "a***-vn2",
        "id": "7495499109337171978",
        "email": "<EMAIL>",
        "phone": "(+86)123******43",
        "tags": ""
    },
    "status": "READY",
    "billing_address": {
        "name": "a***-vn2",
        "phone": "(+86)123******43",
        "address1": "11******************",
        "address2": "",
        "city": "Cau Giay",
        "country": "Vietnam",
        "district": "",
        "ward": ""
    },
    "shipping_address": {
        "name": "a***-vn2",
        "phone": "(+86)123******43",
        "address1": "11******************",
        "address2": "",
        "city": "Cau Giay",
        "country": "Vietnam",
        "district": "",
        "ward": ""
    },
    "source": {
        "id": "5b8de24a-feda-456c-9c24-88a73ead9a91",
        "name": "TikTok Shop",
        "origin_id": "580126200368891914",
        "origin": "TikTok Shop",
        "channel_name": "tiktok_shop"
    },
    "order_line_items": [
        {
            "unit_price": 100000,
            "sale_price": 100000,
            "discount": 0,
            "sku": "123",
            "name": "Crack K2 PRO Mechanical Computer Desk RGB Led 10 Different Modes, Gaming Office Use Extremely Coollllll",
            "variant_name": "Mặc định",
            "image_url": "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/d46baf7ae8df445f9767c9a71c2388c3~tplv-o3syd03w52-origin-jpeg.jpeg?dr=15568&nonce=44367&refresh_token=fd871154cfe3465a05ab5432568d1dc2&from=1413970683&idc=maliva&ps=933b5bde&shcp=3c3d9ffb&shp=54477afb&t=555f072d",
            "quantity": 1,
            "product_id": "1732144779602725682",
            "variant_id": "1732144786415979314",
            "location": {}
        },
        {
            "unit_price": 100000,
            "sale_price": 100000,
            "discount": 0,
            "sku": "123",
            "name": "Crack K2 PRO Mechanical Computer Desk RGB Led 10 Different Modes, Gaming Office Use Extremely Coollllll",
            "variant_name": "Mặc định",
            "image_url": "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/d46baf7ae8df445f9767c9a71c2388c3~tplv-o3syd03w52-origin-jpeg.jpeg?dr=15568&nonce=77326&refresh_token=4a69e3f4b129bd9e0c5b752cb1145d19&from=1413970683&idc=maliva&ps=933b5bde&shcp=3c3d9ffb&shp=54477afb&t=555f072d",
            "quantity": 1,
            "product_id": "1732144779602725682",
            "variant_id": "1732144786415979314",
            "location": {}
        },
        {
            "unit_price": 100000,
            "sale_price": 100000,
            "discount": 0,
            "sku": "123",
            "name": "Crack K2 PRO Mechanical Computer Desk RGB Led 10 Different Modes, Gaming Office Use Extremely Coollllll",
            "variant_name": "Mặc định",
            "image_url": "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/d46baf7ae8df445f9767c9a71c2388c3~tplv-o3syd03w52-origin-jpeg.jpeg?dr=15568&nonce=59003&refresh_token=3b5f72653915bca22c6baade9a3d4801&from=1413970683&idc=maliva&ps=933b5bde&shcp=3c3d9ffb&shp=54477afb&t=555f072d",
            "quantity": 1,
            "product_id": "1732144779602725682",
            "variant_id": "1732144786415979314",
            "location": {}
        }
    ],
    "note": "maohwigwij wjigwoj",
    "tags": ['swg', 'wgwg', 'wgooj'],
    "total": 300001,
    "sub_total": 300000,
    "discount": 0,
    "shipping_fee": 1,
    "tax": 0,
    "updated_at": "2024-03-25T14:30:00Z",  ## wrong format date from tiktokshop to master_data
    "sync_record_id": "5b8de24a-feda-456c-9c24-88a73ead9a91/get_order/order/order_580126200368891914.json"
}

product_master_data = {"name": "Selling Plans Ski Wax", "sku": "HSDBSV", "barcode": "", "publish": True,
                       "description": "", "shortDescription": "", "tags": "Accessory, Sport, Winter", "brand": None,
                       "category": {"name": "Uncategorized"}, "images": [
        {"src": "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/snowboard_wax.png?v=1743500399",
         "name": "A bar of golden yellow wax"},
        {"src": "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/wax-special.png?v=1743500399",
         "name": "A bar of purple wax"},
        {"src": "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/sample-normal-wax.png?v=1743500399",
         "name": "a small cube of wax"}], "variants": [
        {"sku": "HSDBSV", "name": "Selling Plans Ski Wax - Selling Plans Ski Wax", "barcode": "", "prices": [],
         "measurements": None, "inventories": [], "option1": "Selling Plans Ski Wax", "option2": None, "option3": None,
         "optionTitle1": "Title", "optionTitle2": None, "optionTitle3": None, "images": []},
        {"sku": None, "name": "Selling Plans Ski Wax - Special Selling Plans Ski Wax", "barcode": None, "prices": [],
         "measurements": None, "inventories": [], "option1": "Special Selling Plans Ski Wax", "option2": None,
         "option3": None, "optionTitle1": "Title", "optionTitle2": None, "optionTitle3": None, "images": []},
        {"sku": None, "name": "Selling Plans Ski Wax - Sample Selling Plans Ski Wax", "barcode": None, "prices": [],
         "measurements": None, "inventories": [], "option1": "Sample Selling Plans Ski Wax", "option2": None,
         "option3": None, "optionTitle1": "Title", "optionTitle2": None, "optionTitle3": None, "images": []}],
                       "options": [{"name": "Title",
                                    "values": ["Selling Plans Ski Wax", "Special Selling Plans Ski Wax",
                                               "Sample Selling Plans Ski Wax"]}],
                       "measurements": {"weight_value": 0, "weight_unit": "g", "width_value": 0, "width_unit": "cm",
                                        "length_value": 0, "length_unit": "cm", "height_value": 0, "height_unit": "cm"},
                       "source": {"channel_name": "shopify_oauth", "id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4"},
                       "sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314277096.json"}
