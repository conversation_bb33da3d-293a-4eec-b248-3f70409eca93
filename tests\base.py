import os
import uuid
from unittest import TestCase

from nolicore.utils.utils import json_dumps

environment = 'dev'  # prod|dev|local

os.environ['LOG_LEVEL'] = '20'
os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
os.environ['USER_POOL_ID'] = 'ap-southeast-1_Tmn6Tbm0H'
os.environ['APP_CLIENT_ID'] = '3fh2s28e3g8l23068d6j573l9a'
os.environ['ELASTIC_SEARCH_HOST'] = 'https://**************:9200'
os.environ['ELASTIC_SEARCH_USERNAME'] = 'elastic'
os.environ['ELASTIC_SEARCH_PASSWORD'] = 'OneXAPIES999'
os.environ['RESOURCE_SERVICE'] = 'optiwarehouse-resources-services'
os.environ['SERVICE'] = 'optiwarehouse-resources-services'
os.environ['ONEXAPIS'] = 'https://api-staging.onexapis.com'
os.environ['HARAVAN_API'] = 'https://haravan.onexapis.com'
os.environ['FETCH_QUEUE_URL'] = 'https://haravan.onexapis.com'
os.environ['TRANSFORM_QUEUE_URL'] = 'https://haravan.onexapis.com'
os.environ['PUBLISH_QUEUE_URL'] = 'https://haravan.onexapis.com'
os.environ['RAW_DATA_BUCKET'] = f'optiwarehouse-flow-service-{environment}-raw-data'
os.environ['TRANSFORMED_DATA_BUCKET'] = f'optiwarehouse-flow-service-{environment}-transformed-data'

os.environ[
    'LLM_API_KEY'] = "***********************************************************************************************************************************************************************"
os.environ['LLM_MODEL'] = "gpt-4o-mini"
os.environ['DEFAULT_LLM_TEMPERATURE'] = "0.2"
os.environ['LLM_MAX_TOKENS'] = "2000"
os.environ['MAX_LLM_CALL_RETRIES'] = "3"
os.environ['TOP_P'] = "0.8"

if environment == 'prod':
    # prod
    os.environ['BUCKET_NAME'] = 'optiwarehouse-prod'
    os.environ['AWS_PROFILE'] = 'optiwarehouse-prod'
    os.environ['ENV'] = 'prod'
    os.environ['BASE_URL'] = 'https://api.optiwarehouse.com'
    os.environ['WEBHOOK_BASE_URL'] = 'https://api.optiwarehouse.com'
    os.environ['SHOPIFY_CLIENT_ID'] = 'fe5b9b2d0ac99b3dc23ab2362e54133b'
    os.environ['SHOPIFY_CLIENT_SECRET'] = '281bff9d1eb0f930d70b0b87e7b23067'
    os.environ['TIKTOK_APP_KEY'] = '6emv3rous6f2u'
    os.environ['TIKTOK_APP_SECRET'] = 'b2285d947e5f4e1795124406ab3e415393c5d855'
    os.environ['TIKTOK_SERVICE_ID'] = '7448925715311937285'
    os.environ['APP_URL'] = 'https://app.optiwarehouse.com'
    os.environ['ONEXBOTS_API'] = 'https://agent.onexbots.com'
    os.environ['WEBHOOK_BASE_URL'] = 'https://api.optiwarehouse.com'
    os.environ[
        'FETCH_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/533267084422/optiwarehouse-flow-service-prod-fetch-queue'
    os.environ[
        'TRANSFORM_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/533267084422/optiwarehouse-flow-service-prod-transform-queue'
    os.environ[
        'SYNC_MAPPING_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/533267084422/optiwarehouse-flow-service-prod-sync-mapping-queue'
    os.environ['FACEBOOK_APP_ID'] = ''
    os.environ['FACEBOOK_APP_SECRET'] = ''
    os.environ['ZALO_OA_APP_ID'] = ''
    os.environ['ZALO_OA_APP_SECRET'] = ''
    os.environ['ZALO_OA_SECRET'] = ''
if environment == 'dev':
    # staging
    os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
    os.environ['AWS_PROFILE'] = 'noliwms_staging'
    os.environ['ENV'] = 'dev'
    os.environ['BASE_URL'] = 'https://api-staging.optiwarehouse.com'
    os.environ['WEBHOOK_BASE_URL'] = 'https://api-staging.optiwarehouse.com'
    os.environ['SHOPIFY_CLIENT_ID'] = '08530b114bafc5f05eca180a53598b48'
    os.environ['SHOPIFY_CLIENT_SECRET'] = '0568bfe6f5c5b643016055ed7d6d36ee'
    os.environ['APP_URL'] = 'https://dev.optiwarehouse.com'
    os.environ['ONEXBOTS_API'] = 'https://dev-agent.onexbots.com'
    os.environ['TIKTOK_APP_KEY'] = '6emv3rous6f2u'
    os.environ['TIKTOK_APP_SECRET'] = 'b2285d947e5f4e1795124406ab3e415393c5d855'
    os.environ['TIKTOK_SERVICE_ID'] = '7448925715311937285'
    os.environ['WEBHOOK_BASE_URL'] = 'https://api-staging.optiwarehouse.com'
    os.environ[
        'FETCH_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-flow-service-dev-fetch-queue'
    os.environ[
        'TRANSFORM_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-flow-service-dev-transform-queue'
    os.environ[
        'PUBLISH_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-flow-service-dev-publish-queue.fifo'
    os.environ[
        'SYNC_MAPPING_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-flow-service-dev-sync-mapping-queue'
    os.environ['TIKTOK_SERVICE_ID'] = '7448925715311937285'
    os.environ['ZALO_OA_APP_ID'] = '1447241861612872028'
    os.environ['ZALO_OA_APP_SECRET'] = 'j8Q876s512TQ3HWAnKMc'
    os.environ['ZALO_OA_SECRET'] = 'VXmCK4Q5BzlGLs45lgCg'
    os.environ['FACEBOOK_APP_ID'] = '1224751199126351'
    os.environ['FACEBOOK_APP_SECRET'] = '********************************'

if environment == 'local':
    # local
    os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
    os.environ['AWS_PROFILE'] = 'noliwms_staging'
    os.environ['ENV'] = 'local'
    os.environ['SHOPIFY_CLIENT_ID'] = '08530b114bafc5f05eca180a53598b48'
    os.environ['SHOPIFY_CLIENT_SECRET'] = '0568bfe6f5c5b643016055ed7d6d36ee'
    os.environ['APP_URL'] = 'http://localhost:3000'
    os.environ['TIKTOK_APP_KEY'] = '6emv3rous6f2u'
    os.environ['TIKTOK_APP_SECRET'] = 'b2285d947e5f4e1795124406ab3e415393c5d855'
    os.environ['TIKTOK_SERVICE_ID'] = '7448925715311937285'
    os.environ['FACEBOOK_APP_ID'] = ''
    os.environ['FACEBOOK_APP_SECRET'] = ''
    os.environ['ZALO_OA_APP_ID'] = ''
    os.environ['ZALO_OA_APP_SECRET'] = ''
    os.environ['ZALO_OA_SECRET'] = ''


class BaseTestCase(TestCase):
    def setUp(self, account_type="test_user"):
        super().setUp()
        self.maxDiff = None

        # Account configurations
        accounts = {
            "baababy_admin": {
                "company_id": "4c8e6154-fba3-4837-909b-8a50dff16a2b",
                "user_id": "56c8ca59-2214-4d3e-95f8-0fa3178d0031",
                "username": "baababy_admin",
            },
            "onexapis_admin": {
                "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d",
                "user_id": "906ed4d6-26f9-4056-8f3e-ae00a37c3edb",
                "username": "onexapis_admin",
            },
            "test_user": {
                "company_id": str(uuid.uuid4()),
                "user_id": str(uuid.uuid4()),
                "username": "test_user",
            }
        }

        # Set account info
        account = accounts.get(account_type, accounts["test_user"])
        self.company_id = account["company_id"]
        self.user_id = account["user_id"]
        self.username = account["username"]

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test_function'
                self.memory_limit_in_mb = 128
                self.aws_request_id = str(uuid.uuid4())
                self.log_group_name = '/aws/lambda/test_function'
                self.log_stream_name = str(uuid.uuid4())
                self.identity = None
                self.client_context = None

        return LambdaContext()

    def create_lambda_event(self, method='GET', path='/', body=None, query_params=None, path_params=None,
                            is_authorized=False, is_invoke=False):
        if is_invoke:
            return body
        event = {
            'httpMethod': method,
            'body': json_dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': {},
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
            },
            'isBase64Encoded': False
        }
        if is_authorized:
            event['requestContext']['authorizer'] = {
                'jwt': {
                    'claims': {
                        'sub': self.user_id,
                        'cognito:username': self.username,
                        'custom:company_id': self.company_id,
                    }
                }
            }

        return event
