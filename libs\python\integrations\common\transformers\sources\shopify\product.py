import logging
from typing import Dict, Any

from models.product.product import ProductAttributesSchema
from ...base import SourceTransformer

logger = logging.getLogger(__name__)


class ShopifyProductTransformer(SourceTransformer):
    channel_name = 'shopify'
    object_type = 'product'

    def transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)

            # Transform Shopify product data to master format
            transformed_data = {
                'id': data.get('id'),
                'name': data.get('title'),
                'sku': data.get('variants', [{}])[0].get('sku'),
                'barcode': data.get('variants', [{}])[0].get('barcode'),
                'publish': data.get('status') == 'active',
                'description': data.get('body_html'),
                'shortDescription': data.get('body_html', '')[:100] if data.get('body_html') else None,
                'tags': ','.join(data.get('tags', [])),
                'brand': {'name': data.get('vendor')},
                'category': {'name': data.get('product_type')},
                'images': [{'src': img.get('src')} for img in data.get('images', [])],
                'variants': [
                    {
                        'id': variant.get('id'),
                        'sku': variant.get('sku'),
                        'barcode': variant.get('barcode'),
                        'price': variant.get('price'),
                        'compare_at_price': variant.get('compare_at_price'),
                        'weight': variant.get('weight'),
                        'weight_unit': variant.get('weight_unit'),
                        'inventory_quantity': variant.get('inventory_quantity'),
                    }
                    for variant in data.get('variants', [])
                ],
                'options': [
                    {
                        'name': option.get('name'),
                        'values': option.get('values', []),
                    }
                    for option in data.get('options', [])
                ],
            }

            # Output validation
            self.validate_output(transformed_data)

            return transformed_data
        except KeyError as e:
            logger.error(f"Missing required field in Shopify product data: {str(e)}")
            raise ValueError(f"Missing required field in Shopify product data: {str(e)}")
        except Exception as e:
            logger.error(f"Error transforming Shopify product data: {str(e)}")
            raise

    def validate_input(self, data: Dict[str, Any]):
        # Implement input validation logic using self.input_schema
        pass

    def validate_output(self, data: Dict[str, Any]):
        # Implement output validation logic using self.output_schema
        pass

    @property
    def input_schema(self):
        # This should reflect the Shopify API product schema
        return {
            'type': 'object',
            'properties': {
                'id': {'type': 'string'},
                'title': {'type': 'string'},
                'body_html': {'type': 'string'},
                'vendor': {'type': 'string'},
                'product_type': {'type': 'string'},
                'status': {'type': 'string'},
                'tags': {'type': 'array', 'items': {'type': 'string'}},
                'variants': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'id': {'type': 'string'},
                            'sku': {'type': 'string'},
                            'barcode': {'type': 'string'},
                            'price': {'type': 'string'},
                            'compare_at_price': {'type': 'string'},
                            'weight': {'type': 'number'},
                            'weight_unit': {'type': 'string'},
                            'inventory_quantity': {'type': 'integer'},
                        }
                    }
                },
                'options': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'name': {'type': 'string'},
                            'values': {'type': 'array', 'items': {'type': 'string'}}
                        }
                    }
                },
                'images': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'src': {'type': 'string'}
                        }
                    }
                }
            }
        }

    @property
    def output_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())
