from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import BadRequest, NotFoundRequest

from helpers.utils import RESOURCE_SERVICE
from models.bots.task import TaskModel


@as_api()
def create_task(api_gateway_request: ApiGatewayRequest):
    """Create a new task"""
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id

    # Validate required fields
    required_fields = [
        'name',
        # 'description',
        # 'prompt_content',
        # 'variables'
    ]
    for field in required_fields:
        if field not in body:
            raise BadRequest(f"Missing required field: {field}")

    try:
        # Create task
        task = TaskModel.create(company_id, body)
        return {
            'success': True,
            'message': 'Task created successfully',
            'data': task.attributes_dict
        }
    except ValueError as e:
        raise BadRequest(str(e))


@as_api()
def get_task(api_gateway_request: ApiGatewayRequest):
    """Get a task by ID"""
    task_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id
    if not task_id:
        raise BadRequest("Missing required field: id")

    # Get task
    task = TaskModel.get(company_id=company_id, _id=task_id)
    if not task:
        raise NotFoundRequest("Task not found")

    return {
        'success': True,
        'message': 'Task retrieved successfully',
        'data': task.attributes_dict
    }


@as_api()
def list_tasks(api_gateway_request: ApiGatewayRequest):
    """List tasks with optional filters"""
    params = api_gateway_request.query_string_parameters or {}
    company_id = api_gateway_request.company_id

    # Search using company index
    tasks = TaskModel.search(
        params,
        service=RESOURCE_SERVICE,
        company_id=company_id
    )

    return {
        'success': True,
        'message': 'Tasks retrieved successfully',
        'data': tasks
    }


@as_api()
def update_task(api_gateway_request: ApiGatewayRequest):
    """Update a task"""
    task_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id
    if not task_id:
        raise BadRequest("Missing required field: id")
    if not company_id:
        raise BadRequest("Missing required field: company_id")
    body = api_gateway_request.body
    data = body.get('data', {})
    if not data:
        raise BadRequest("Missing request data")

    # Get existing task
    task = TaskModel.get(company_id=company_id, _id=task_id)
    if not task:
        raise NotFoundRequest("Task not found")

    # Update task
    task.update(data)
    return {
        'success': True,
        'message': 'Task updated successfully',
        'data': task.attributes_dict
    }


@as_api()
def delete_task(api_gateway_request: ApiGatewayRequest):
    """Delete a task"""
    task_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id
    if not company_id:
        raise BadRequest("Missing required field: company_id")
    if not task_id:
        raise BadRequest("Missing required field: id")

    # Get existing task
    task = TaskModel.by_key({'company_id': company_id, 'id': task_id})
    if not task:
        raise NotFoundRequest("Task not found")

    # Delete task
    key = {
        TaskModel.key__id: task_id,
        'company_id': company_id
    }
    TaskModel.delete(key=key)
    return {
        'success': True,
        'message': 'Task deleted successfully'
    }
