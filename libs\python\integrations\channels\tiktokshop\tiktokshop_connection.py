from dataclasses import dataclass, field
from marshmallow import fields
from ..connection_base import OAuth2Connection, OAuth2ConnectionSchema, OAuth2ConnectionAttributes, \
    OAuth2SettingsSchema, OAuth2Settings
from enum import Enum


class Region(Enum):
    us = "us"
    non_us = "non-us"


class TiktokShopSettingsSchema(OAuth2SettingsSchema):
    app_key = fields.Str(required=True)
    app_secret = fields.Str(required=True)
    service_id = fields.Str(required=True)
    region = fields.Str(required=True)


@dataclass
class TiktokShopSettings(OAuth2Settings):
    app_key: str = None
    app_secret: str = None
    service_id: str = None
    region: str = None


@dataclass
class TiktokShopConnectionAttributes(OAuth2ConnectionAttributes):
    channel_name: str = field(default='tiktok_shop')
    settings: TiktokShopSettings = field(default_factory=TiktokShopSettings)


class TiktokShopConnectionSchema(OAuth2ConnectionSchema):
    settings = fields.Nested(TiktokShopSettingsSchema(TiktokShopSettings))


class TiktokShopConnection(OAuth2Connection):
    attributes: TiktokShopConnectionAttributes = None
    attributes_schema = TiktokShopConnectionSchema
    attributes_class = TiktokShopConnectionAttributes
