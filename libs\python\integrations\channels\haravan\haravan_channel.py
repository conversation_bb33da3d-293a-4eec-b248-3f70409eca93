from typing import Dict, Any, Tuple, Optional, List

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.haravan.haravan_library import Haravan<PERSON>ibrary
from integrations.channels.channel_library import AbstractChannel, OAuth2Channel
from integrations.channels.connection_types import ChannelType
from integrations.channels.haravan.haravan_connection import HaravanSettings
from integrations.common.event import FetchEvent


class HaravanChannel(OAuth2Channel, AbstractChannel):
    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        pass

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        pass

    channel_name = "haravan"
    library_class = HaravanLibrary

    def _create_library(self, connection):
        settings: HaravanSettings = connection.attributes.settings
        return HaravanLibrary(
            client_id=settings.client_id,
            client_secret=settings.client_secret,
            org_id=settings.org_id,
            base_url=settings.base_url,
            access_token=connection.attributes.access_token
        )

    def initiate_oauth_flow(self, redirect_url: str = None, base_url: str = None) -> str:
        return self.library.get_authorization_url(str(self.connection.attributes.id), redirect_url, base_url)

    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        access_code = query_params.get('code')
        scope = query_params.get('scope')
        if not access_code:
            raise ValueError("Missing code in query parameters")

        token_data = self.library.fetch_token(access_code, state=self.connection.attributes.id)

        return (
            token_data['external_id'],
            token_data['access_token'],
            token_data['expires_at'],
            token_data['refresh_token'],
            token_data['token_data']
        )

    def refresh_oauth_token(self) -> Dict[str, Any]:
        token = self.library.refresh_token()
        self.connection.update_token(token)
        return token

    def test_connection(self) -> bool:
        return self.library.test_connection()

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            "name": "Haravan",
            "channel_type": ChannelType.ECOMMERCE,
            "description": "Connect your Haravan store to sync products, orders, and customers.",
            "logo": cls._load_image_as_base64(__file__, "haravan.png")
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_order:
            return {
                "page": 1,
                "updated_at_min": None,
                "updated_at_max": None
            }
        elif action == ActionType.get_product:
            return {
                "page": 1,
                "limit": 50,
                "updated_at_min": None,
                "updated_at_max": None
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_order:
            return {}
        elif action == ActionType.sync_product:
            return {}
        else:
            return {}

    def get_order__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        merged_settings = {**settings, **fetch_event.to_dict()}

        if fetch_event.object_id:
            return self.library.get_order(order_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        else:
            return self.library.get_orders(params=merged_settings, fetch_event_id=fetch_event.id)

    def get_product__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        merged_settings = {**settings, **fetch_event.to_dict()}

        if fetch_event.object_id:
            return self.library.get_product(product_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        else:
            return self.library.get_products(params=merged_settings, fetch_event_id=fetch_event.id)

    def sync_order__publish(self, settings: Dict[str, Any], order_data: Dict[str, Any]):
        return self.library.sync_order(order_data)

    def sync_inventory__publish(self, settings: Dict[str, Any], inventory_data: Dict[str, Any]):
        return self.library.sync_inventory(inventory_data)

    def sync_inventory__batch_publish(self, settings: Dict[str, Any], inventory_data: List[Dict[str, Any]]):
        return self.library.batch_sync_inventory(inventory_data)

    def sync_product__publish(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        return self.library.sync_product(product_data)

    def setup_webhooks(self):
        super().setup_webhooks()  # Call the parent class's setup_webhooks method
        self.library.webhook_subscribe()

    @staticmethod
    def verify_webhook_signature(payload: str, headers: dict, settings: HaravanSettings) -> bool:
        return HaravanLibrary.verify_webhook_signature(payload, headers.get('X-Haravan-Hmac-Sha256'),
                                                       settings.webhook_secret)

    @staticmethod
    def extract_merchant_id(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return HaravanLibrary.extract_merchant_id(payload, headers)

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return HaravanLibrary.extract_event_type(payload, headers)

    def webhook(self, payload: Dict[str, Any], headers: Dict[str, str]) -> FetchEvent:
        return self.library.webhook(payload, headers)

    def get_dynamic_settings(self) -> Dict[str, Any]:
        # First, check if we already have dynamic settings stored
        stored_settings = self.connection.get_dynamic_settings()
        if 'stock_split' not in stored_settings:
            stored_settings['stock_split'] = None
        if 'stock_min' not in stored_settings:
            stored_settings['stock_min'] = None
        return stored_settings
