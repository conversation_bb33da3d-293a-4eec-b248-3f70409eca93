from typing import Dict, Any, Tuple, List, Optional

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import AbstractChannel, OAuth2Channel
from integrations.channels.connection_types import ChannelType
from integrations.channels.zalo_oa.zalo_oa_connection import ZaloOASettings
from integrations.channels.zalo_oa.zalo_oa_library import ZaloOA<PERSON>ibrary
from integrations.common.event import FetchEvent


class ZaloOAChannel(OAuth2Channel, AbstractChannel):
    channel_name = 'zalo_oa'
    library_class = ZaloOALibrary

    def _create_library(self, connection):
        settings: ZaloOASettings = connection.attributes.settings
        return ZaloOALibrary(
            expires_at=connection.attributes.expires_at,
            connection=connection,
            app_id=settings.app_id,
            secret_key=settings.secret_key,
            access_token=connection.attributes.access_token,
            refresh_token=connection.attributes.refresh_token,
            code_challenge=settings.code_challenge,
            company_id=connection.attributes.company_id
        )

    @staticmethod
    def is_token_expired(token: Dict[str, Any]) -> bool:
        return super().is_token_expired(token)

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            'name': 'Zalo OA',
            'channel_type': ChannelType.MARKETING,
            'description': 'Connect your Zalo Official Account to manage messages and interactions.',
            'logo': cls._load_image_as_base64(__file__, 'zalo_oa.png')
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.send_message:
            return {}
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.send_message:
            return {}
        else:
            return {}

    def refresh_oauth_token(self) -> Dict[str, Any]:
        token = self.library.refresh_access_token()
        self.connection.update_token(token['external_id'], token['access_token'], token['expires_at'],
                                     token['refresh_token'], token['token_data'])
        return token

    def get_message__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_message_params()

    def get_message__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}

        if fetch_event.object_id:
            # Case 1: Single message
            return self.library.get_message(message_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        else:
            # Case 2: Filter-based query
            return self.library.get_messages(params=params, settings=settings, next_page=fetch_event.next_page,
                                             fetch_event_id=fetch_event.id)

    def send_message__publish(self, settings: Dict[str, Any], message_data: Dict[str, Any]):
        return self.library.send_message(message_data)

    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        access_code = query_params.get('code')
        code_verifier = query_params.get('code_verifier')
        if not access_code:
            raise ValueError("Missing code in query parameters")

        token_data = self.library.fetch_token(access_code, code_verifier, state=self.connection.attributes.id)

        return (
            token_data['external_id'],
            token_data['access_token'],
            token_data['expires_at'],
            token_data['refresh_token'],
            token_data['token_data']
        )

    def initiate_oauth_flow(self, redirect_url: str = None, base_url: str = None) -> str:
        return self.library.get_authorization_url(str(self.connection.attributes.id), redirect_url, base_url)

    def test_connection(self) -> bool:
        try:
            return self.library.test_connection()
        except Exception:
            return False

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        return self.library.extract_object_details(payload, headers, event_type)

    def process_webhook_event(self, event_type, payload) -> bool:
        return self.library.process_webhook_event(event_type, payload, self.connection,
                                                  self.channel_name)

    def webhook__webhook(self, settings: Dict[str, Any], message_data: Dict[str, Any]):
        return self.library.process_webhook(settings, message_data)

    @classmethod
    def verify_webhook_signature(cls, payload: str, headers: dict, settings: ZaloOASettings) -> bool:
        return ZaloOALibrary.verify_webhook_signature(payload, headers.get('x-zevent-signature'),
                                                      settings.oa_secret_key)

    @classmethod
    def extract_merchant_id(cls, payload):
        return cls.library_class.extract_merchant_id(payload)

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        # check if the webhook is from Zalo OA
        if payload:
            message = payload['message']['text']
            event_name = payload.get('event_name', '')
            oa_id = payload.get('app_id', '')
            if message == 'This is testing message':
                return True

        # cannot identify the webhook type
        return False

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return cls.library_class.extract_event_type(payload, headers)
