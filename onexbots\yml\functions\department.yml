create_department:
  handler: src.apis.department.add
  events:
    - httpApi:
        path: /departments
        method: post
        authorizer:
          name: optiAuthorizer

get_department:
  handler: src.apis.department.get
  events:
    - httpApi:
        path: /departments/{department_id}
        method: get
        authorizer:
          name: optiAuthorizer

list_departments:
  handler: src.apis.department._list
  events:
    - httpApi:
        path: /departments
        method: get
        authorizer:
          name: optiAuthorizer

update_department:
  handler: src.apis.department.update
  events:
    - httpApi:
        path: /departments/{department_id}
        method: put
        authorizer:
          name: optiAuthorizer

# delete_department:
#   handler: src.apis.department.delete
#   events:
#     - httpApi:
#         path: /departments/{department_id}
#         method: delete
#         authorizer:
#           name: optiAuthorizer

