from dataclasses import dataclass
from enum import Enum
from typing import List

from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


class VoucherStatus(Enum):
    APPLY = 'APPLY'
    STOP_APPLY = 'STOP_APPLY'
    NOT_ACTIVE = 'NOT_ACTIVE'
    CANCELLED = "CANCELLED"


class ValueCouponType(Enum):
    PERCENT = 'PERCENT'
    VALUE = 'VALUE'


class StatusSource(Enum):
    ACTIVE = 'ACTIVE'


class CustomValueError(Exception):
    def __init__(self, message="Đ<PERSON>y là một lỗi tùy chỉnh."):
        self.message = message
        super().__init__(self.message)


@dataclass
class ValueCouponTypeAttributes(Attributes):
    type: ValueCouponType
    amount: int = 0


class ValueCouponTypeAttributesSchema(AttributesSchema):
    type = EnumField(ValueCouponType, required=True, default=ValueCouponType.VALUE)
    amount = fields.Decimal(allow_none=True)


@dataclass
class Voucher(BasicAttributes):
    id: str = None
    name: str = None
    code: str = None
    description: str = None
    start_at: str = None
    value_coupon: ValueCouponTypeAttributes = None
    condition_order: int = None
    condition_contains_order: dict = None
    company_id: str = None
    locations_ids: str = None
    sources_ids: str = None
    customers_ids: str = None
    status: VoucherStatus = VoucherStatus.NOT_ACTIVE
    max_value_coupon: int = None
    use_voucher_oder: int = None
    use_voucher_customer: int = None
    expire_at: str = None
    locations: List[dict] = None
    sources: List[dict] = None
    customers: List[dict] = None
    apply_with_other_promotion: bool = False


class VoucherSchema(BasicAttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    code = fields.Str(required=True)
    description = fields.Str(required=True)
    start_at = fields.DateTime(required=True)
    expire_at = fields.Str(allow_none=True)
    max_value_coupon = fields.Int(allow_none=True)
    use_voucher_oder = fields.Int(allow_none=True)
    use_voucher_customer = fields.Int(allow_none=True)
    apply_with_other_promotion = fields.Bool(allow_none=True)
    condition_order = fields.Int(required=True)
    condition_contains_order = fields.Dict(required=True)
    company_id = fields.UUID(required=True)
    status = EnumField(VoucherStatus, allow_none=True, default=VoucherStatus.NOT_ACTIVE)
    value_coupon = fields.Nested(ValueCouponTypeAttributesSchema(ValueCouponTypeAttributes), allow_none=True)
    locations = fields.List(fields.Dict(), allow_none=True)
    sources = fields.List(fields.Dict(), allow_none=True)
    customers = fields.List(fields.Dict(), allow_none=True)
    locations_ids = fields.Str(required=True)
    sources_ids = fields.Str(required=True)
    customers_ids = fields.Str(required=True)


class VoucherModel(BasicModel):
    table_name = 'voucher'
    attributes: Voucher
    attributes_schema = VoucherSchema
    attributes_class = Voucher
    query_mapping = {
        'query': ['id', 'name', 'code'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'start_at': 'range',
            'expire_at': 'range',
            'status': 'query',
            'locations_ids': 'query',
            'sources_ids': 'query',
            'customers_ids': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'max_value_coupon': 'max_value_coupon',
            'value_coupon': 'value_coupon',
            'condition_order': 'condition_order'
        }
    }
