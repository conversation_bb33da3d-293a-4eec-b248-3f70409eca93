import os
from dataclasses import dataclass
from enum import Enum
from io import BytesIO

from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import Model, Attributes, AttributesSchema
from nolicore.utils.aws.boto_helper import get_s3_client
from nolicore.utils.utils import compress, decompress

from models.common.source_object import SourceObject, SourceObjectSchema

s3 = get_s3_client()
BUCKET_NAME = os.getenv('BUCKET_NAME')


class ImportStatus(Enum):
    PROCESSING = 'PROCESSING'
    FAILED = 'FAILED'
    COMPLETED = 'COMPLETED'


class ImportType(Enum):
    product = 'product'
    order = 'order'
    purchase_order = 'purchase_order'
    customer = 'customer'
    supplier = 'supplier'
    stock_adjustment = 'stock_adjustment'
    return_order = 'return_order'


@dataclass
class Import(Attributes):
    id: str
    company_id: str
    import_type: ImportType
    imported_at: str
    parent_id: str = None
    finished_at: str = None
    status: ImportStatus = ImportStatus.PROCESSING
    message: str = None
    total: int = 0
    processed: int = 0
    failed: int = 0
    skipped: int = 0
    is_clone: bool = False
    source: SourceObject = None


class ImportSchema(AttributesSchema):
    id = fields.UUID(required=True)
    status = EnumField(ImportStatus, allow_none=True)
    company_id = fields.Str(required=True)
    import_type = EnumField(ImportType, required=True)
    imported_at = fields.Str(required=True)
    parent_id = fields.Str(allow_none=True)
    finished_at = fields.Str(allow_none=True)
    message = fields.Str(allow_none=True)
    total = fields.Int(allow_none=True)
    processed = fields.Int(allow_none=True)
    failed = fields.Int(allow_none=True)
    skipped = fields.Int(allow_none=True)
    source = fields.Nested(SourceObjectSchema(SourceObject), allow_none=True)
    is_clone = fields.Bool(required=False, allow_none=True, default=False)


class ImportModel(Model):
    key__id = 'id'
    key__ranges = ['company_id']
    table_name = 'imports'
    attributes: Import
    attributes_schema = ImportSchema
    attributes_class = Import

    @staticmethod
    def save_data(company_id, import_type: ImportType, import_id, data):
        key = f'{company_id}/import/{import_type.value}/{import_id}.json'
        s3.upload_fileobj(BytesIO(compress(data).encode()), BUCKET_NAME, key)

    @staticmethod
    def get_data(company_id, import_type: ImportType, import_id):
        key = f'{company_id}/import/{import_type.value}/{import_id}.json'
        response = s3.get_object(Bucket=BUCKET_NAME, Key=key)
        data = response['Body'].read().decode('utf-8')
        return decompress(data)

    query_mapping = {
        'query': ['id'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'parent_id': 'query',
            'import_type': 'query',
            'status': 'query',
            'source.id': 'query',
            'source.channel_name': 'query',
            'imported_at': 'range',
            'finished_at': 'range',
            'is_clone': 'boolean'
        },
        'mapping': {
            'message': 'message',
            'total': 'total',
            'processed': 'processed',
            'failed': 'failed',
            'skipped': 'skipped',
            'source': 'source'
        }
    }
