import json
import unittest
import uuid

from dotenv import load_dotenv
from nolicore.utils.utils import compress

from tests.base import BaseTestCase
from flows.src.events.step_2_fetch import fetch_lambda_handler


class TestShopifyOauthFetch<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON>(BaseTestCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.connection_id = "16c17780-12ed-4b6a-aa8d-f2a4aa6b8471"
        cls.destination_id = "5abf07bc-dca7-4c43-872a-4c7f11791566"
        cls.source_key = "6b5ca363-434b-4127-8258-3bbf1afc5e50/get_product/product/product_39105678.json"
        cls.destination_key = "6b5ca363-434b-4127-8258-3bbf1afc5e50/get_product/product/product_39105678.haravan"
        cls.destination_version_id = "aC0vvrN4mRj05_L1oaOr_yF1x.mabNun"
        cls.source_version_id = "cVUR8zVbJCD5fnoxkfzdSX.YW4XjQ0ro"
        cls.object_type = 'product'
        cls.fetch_event_id = "4d702caa-994f-4382-a12c-860d430f52c0"

        if not all(
                [cls.connection_id, cls.destination_id, cls.source_key, cls.destination_key, cls.destination_version_id,
                 cls.source_version_id, cls.object_type, cls.fetch_event_id]):
            raise ValueError("Shopify credentials not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        # self.company_id = str(uuid.uuid4())
        # self.user_id = str(uuid.uuid4())
        # self.username = f"user_{self.user_id}"

        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"

    def create_lambda_event(self, body):
        return {'Records': [{
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            },
        }]}

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_fetch_lambda_handler(self):
        event_body_tiktok_shop = {
            "object_data": None,
            "company_id": "97a0606f-d651-44d9-abb0-faeeae5d0feb",
            "created_at": "2025-05-16T06:18:35.568285+00:00",
            "event_time": "2025-05-15T00:35:25.879855+00:00",
            "meta": {
                "limit": "50",
                "total_page": "0",
                "current_params": {
                    "ModifiedSince": "2025-05-14T23:34:37.674936+00:00",
                    "Page": "1",
                    "Limit": "50",
                    "IncludeAttachments": True,
                    "updated_after": "2025-05-14T23:34:37.674936+00:00"
                },
                "continuation_token": "2",
                "page": "1",
                "total_count": "114"
            },
            "status": "PENDING",
            "user": None,
            "batch_id": "f5e599a4-1a4d-47f6-97c3-266b440b7049",
            "event_source": "scheduler",
            "channel": "cin7",
            "destination_ids": None,
            "updated_at": "2025-05-16T06:18:35.568285+00:00",
            "action_type": "get_product",
            "retry_count": "0",
            "object_ids": None,
            "object_id": None,
            "error_msg": None,
            "id": "236cf9dc-3ec5-40fb-a255-de9c10af811f",
            "continuation_token": "2",
            "is_batch": False,
            "connection_id": "67651bd0-fd91-42e7-8f82-ddd2d83f5827",
            "action_group": "product"
        }
        remove_attributes = ['created_at', 'user', 'updated_at']
        for attribute in remove_attributes:
            event_body_tiktok_shop.pop(attribute, None)

        event = self.create_lambda_event(event_body_tiktok_shop)
        context = self.create_lambda_context()

        # Act
        response = fetch_lambda_handler(event, context)
        print('response', response)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        self.assertEqual(json.loads(response['body']), 'Fetch Lambda execution completed')


if __name__ == '__main__':
    unittest.main()
