from typing import Dict, Any, List
import logging

from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.transformers.base import SourceTransformer
from models.product.product import ProductAttributesSchema
from integrations.channels.shopify_oauth.shopify_oauth_library import ShopifyOAuthLibrary
from helpers.custom_jsonpath import get_value_from_jsonpath

logger = logging.getLogger(__name__)


class ShopifyOauthTransformer(SourceTransformer):
    channel_name = 'shopify_oauth'
    object_type = 'product'

    def __init__(self, connection_settings: Dict[str, Any]):
        super().__init__(connection_settings)

    def _map_object_type_to_action_type(self) -> str:
        return ActionType.get_product.value

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.product

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)

            # Get first variant
            first_variant = get_value_from_jsonpath(data, '$.variants.nodes[0]') or {}

            # Transform Shopify product data to master format
            transformed_data = {
                'name': get_value_from_jsonpath(data, '$.title'),
                'sku': get_value_from_jsonpath(first_variant, '$.sku'),
                'barcode': get_value_from_jsonpath(first_variant, '$.barcode'),
                'publish': get_value_from_jsonpath(data, '$.publishedAt') is not None,
                'description': get_value_from_jsonpath(data, '$.descriptionHtml') or '',
                'shortDescription': get_value_from_jsonpath(data, '$.description') or '',
                'tags': ', '.join(get_value_from_jsonpath(data, '$.tags') or []),
                'brand': None,  # Assuming brand is not available in the data
                'category': {'name': get_value_from_jsonpath(data, '$.category.fullName') or 'Uncategorized'},
                'images': self._transform_images(get_value_from_jsonpath(data, '$.media.nodes') or []),
                'variants': self._transform_variants(get_value_from_jsonpath(data, '$.variants.nodes') or []),
                'options': self._transform_options(get_value_from_jsonpath(data, '$.options') or []),
                'measurements': {
                    'weight_value': float(
                        get_value_from_jsonpath(get_value_from_jsonpath(data, '$.variants.nodes[0]' or {}),
                                                '$.inventoryItem.measurement.weight.value') or 0),
                    'weight_unit': self._transform_weight_unit(
                        get_value_from_jsonpath(get_value_from_jsonpath(data, '$.variants.nodes[0]' or {}),
                                                '$.inventoryItem.measurement.weight.unit') or None),
                    'width_value': float(get_value_from_jsonpath(data, '$.width') or 0),
                    'width_unit': 'cm',
                    'length_value': float(get_value_from_jsonpath(data, '$.length') or 0),
                    'length_unit': 'cm',
                    'height_value': float(get_value_from_jsonpath(data, '$.height') or 0),
                    'height_unit': 'cm'
                } if get_value_from_jsonpath(data, '$.variants.nodes') else None,
                'inventories': self._transform_inventory(
                    get_value_from_jsonpath(first_variant, 'inventoryItem.inventoryLevels.nodes') or []),
            }

            # Output validation
            self.validate_output(transformed_data)
            return transformed_data

        except Exception as e:
            logger.error(f"Error transforming ShopifyOauth product data: {str(e)}")
            raise

    def _transform_inventory(self, inventories: []):
        transformed_inventories = []
        for inventory in inventories:
            transformed_inventories.append({
                'id': 'shopify_inventory_id',
                'location_id': ShopifyOAuthLibrary.get_id_from_gid(get_value_from_jsonpath(inventory, '$.location.id')),
                'sku': 'shopify_sku',
                'product_id': 'shopify_product_id',
                'on_hand': 0,
                'available': get_value_from_jsonpath(inventory, '$.quantities[0].quantity'),
                'cost': 0,
                'shipping': 0,
                'packing': 0,
                'returning': 0,
                'incoming': 0,
                'version': 0,
                'location_name': 'shopify_location_name',
                'variant_id': 'shopify_variant_id',
                'min_value': 'shopify_min_value',
                'max_value': 'shopify_max_value',
                'last_transaction': 'shopify_last_transaction',
            })
        return transformed_inventories

    def _transform_weight_unit(self, shopify_weight_unit):
        transform_weight_unit = {
            'GRAMS': 'g',
            'KILOGRAMS': 'kg',
            'OUNCES': 'g',
            'POUNDS': 'g'
        }
        return transform_weight_unit.get(shopify_weight_unit, 'g')

        # doc:
        # WeightUnit: https://shopify.dev/docs/api/admin-graphql/latest/enums/WeightUnit

    def _transform_images(self, images: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        return [
            {
                'src': get_value_from_jsonpath(image, '$.preview.image.url') or '',
                'altText': get_value_from_jsonpath(image, '$.preview.image.altText') or '',
                'name': f"shopify_image_{ShopifyOAuthLibrary.get_id_from_gid(get_value_from_jsonpath(image, '$.id') or '') if get_value_from_jsonpath(image, '$.id') else 'unknown'}"
            }
            for image in images
            if get_value_from_jsonpath(image, '$.preview.image.url')
        ]

    def _transform_variants(self, variants: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        return [
            {
                'sku': get_value_from_jsonpath(variant, '$.sku'),
                'name': get_value_from_jsonpath(variant, '$.displayName'),
                'barcode': get_value_from_jsonpath(variant, '$.barcode'),
                'prices': [{'price': int(float(get_value_from_jsonpath(variant, '$.price') or 0)),
                            'price_group': {'id': 'RETAILS'}}],
                'measurements': {
                    'weight_value': float(
                        get_value_from_jsonpath(variant, '$.inventoryItem.measurement.weight.value') or 0),
                    'weight_unit': self._transform_weight_unit(
                        get_value_from_jsonpath(variant, '$.inventoryItem.measurement.weight.unit') or None),
                    'width_value': 0,
                    'width_unit': 'cm',
                    'length_value': 0,
                    'length_unit': 'cm',
                    'height_value': 0,
                    'height_unit': 'cm'
                },
                'inventories': self._transform_inventory(
                    get_value_from_jsonpath(variant, '$.inventoryItem.inventoryLevels.nodes') or []),
                'option1': get_value_from_jsonpath(variant, '$.selectedOptions[0].value'),
                'option2': get_value_from_jsonpath(variant, '$.selectedOptions[1].value'),
                'option3': get_value_from_jsonpath(variant, '$.selectedOptions[2].value'),
                'optionTitle1': get_value_from_jsonpath(variant, '$.selectedOptions[0].name'),
                'optionTitle2': get_value_from_jsonpath(variant, '$.selectedOptions[1].name'),
                'optionTitle3': get_value_from_jsonpath(variant, '$.selectedOptions[2].name'),
                'images': self._transform_images(get_value_from_jsonpath(variant, '$.images.nodes') or []),
            }
            for variant in variants
        ]

    def _transform_options(self, options: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        return [
            {
                'name': get_value_from_jsonpath(option, '$.name'),
                'values': get_value_from_jsonpath(option, '$.values') or []
            }
            for option in options
        ]

    def validate_input(self, data: Dict[str, Any]):
        # Check if variants exist and all have SKUs
        variants = get_value_from_jsonpath(data, '$.variants.nodes') or []
        if not variants:
            raise ValueError("No variants found in ShopifyOauth product data")

        # Check each variant for SKU
        for i, variant in enumerate(variants):
            if not get_value_from_jsonpath(variant, '$.sku'):
                raise ValueError(f"Missing required SKU in variant {i + 1} of ShopifyOauth product data")

    def validate_output(self, data: Dict[str, Any]):
        # Check required fields
        required_fields = ['sku']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f"Missing required fields in transformed data: {', '.join(missing_fields)}")

        # Check all variants have SKUs
        variants = data.get('variants', [])
        if not variants:
            raise ValueError("No variants found in transformed data")

        for i, variant in enumerate(variants):
            if not variant.get('sku'):
                raise ValueError(f"Missing required SKU in variant {i + 1} of transformed data")

    @property
    def input_schema(self):
        return {
            'type': 'object',
            'properties': {
                'id': {'type': 'string'},
                'title': {'type': 'string'},
                'raw_data': {
                    'type': 'object',
                    'properties': {
                        'id': {'type': 'string'},
                        'title': {'type': 'string'},
                        'publishedAt': {'type': ['string', 'null']},
                        'description': {'type': 'string'},
                        'tags': {'type': 'array', 'items': {'type': 'string'}},
                        'category': {
                            'type': 'object',
                            'properties': {
                                'fullName': {'type': 'string'}
                            }
                        },
                        'variants': {
                            'type': 'object',
                            'properties': {
                                'nodes': {
                                    'type': 'array',
                                    'items': {
                                        'type': 'object',
                                        'properties': {
                                            'id': {'type': 'string'},
                                            'sku': {'type': 'string'},
                                            'displayName': {'type': 'string'},
                                            'barcode': {'type': ['string', 'null']},
                                            'images': {
                                                'type': 'object',
                                                'properties': {
                                                    'nodes': {
                                                        'type': 'array',
                                                        'items': {
                                                            'type': 'object',
                                                            'properties': {
                                                                'src': {'type': 'string'},
                                                                'altText': {'type': 'string'}
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        'options': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'properties': {
                                    'name': {'type': 'string'},
                                    'values': {'type': 'array', 'items': {'type': 'string'}}
                                }
                            }
                        },
                        'images': {
                            'type': 'object',
                            'properties': {
                                'nodes': {
                                    'type': 'array',
                                    'items': {
                                        'type': 'object',
                                        'properties': {
                                            'src': {'type': 'string'},
                                            'altText': {'type': 'string'}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            'required': ['id', 'title']
        }

    @property
    def output_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.product
