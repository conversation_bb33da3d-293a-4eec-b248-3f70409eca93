from dataclasses import dataclass
from enum import Enum
from typing import List

from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import Attributes, AttributesSchema
from nolicore.utils.utils import compress

from helpers.utils import RESOURCE_SERVICE
from models.actor.customer_group import CustomerGroup, CustomerGroupSchema
from models.actor.person import Person, PersonSchema, PersonAttributes, PersonAttributesSchema
from models.basic import BasicModel, BasicAttributes
from models.common.customer import SaleOrder, SaleOrderSchema, LoyalCustomerPointSchema, LoyalCustomerPoint
from models.common.source_object import SourceObjectSchema, SourceObject


class CustomerGroupEventType(Enum):
    TIER_UP = 'TIER_UP'
    TIER_DOWN = 'TIER_DOWN'
    INIT_TIER = 'INIT_TIER'


@dataclass
class History(Attributes):
    type: CustomerGroupEventType
    updated_at: fields.DateTime


class HistorySchema(AttributesSchema):
    type = EnumField(CustomerGroupEventType, required=True)
    updated_at = fields.DateTime(allow_none=True)


@dataclass
class Customer(Person):
    sub: str = None
    customer_code: str = None
    has_account: bool = False
    customer_group: CustomerGroup = None
    loyal_customer: LoyalCustomerPoint = None
    sale_order: SaleOrder = None
    histories: List[History] = None


@dataclass
class CustomerAttributes(PersonAttributes):
    sub: str = None
    has_account: bool = False
    customer_code: str = None
    customer_group: CustomerGroup = None
    loyal_customer: LoyalCustomerPoint = None
    sale_order: SaleOrder = None
    histories: List[History] = None
    sync_record_id: str = None
    source: SourceObject = None


class CustomerSchema(PersonSchema):
    sub = fields.Str(allow_none=True)
    customer_code = fields.Str(allow_none=True)
    has_account = fields.Bool(allow_none=True)
    customer_group = fields.Nested(CustomerGroupSchema(CustomerGroup), allow_none=True)
    loyal_customer = fields.Nested(LoyalCustomerPointSchema(LoyalCustomerPoint), allow_none=True)
    sale_order = fields.Nested(SaleOrderSchema(SaleOrder), allow_none=True)
    histories = fields.List(fields.Nested(HistorySchema(History)), allow_none=True)


class CustomerAttributesSchema(PersonAttributesSchema):
    sub = fields.Str(allow_none=True)
    customer_code = fields.Str(allow_none=True)
    has_account = fields.Bool(allow_none=True)
    customer_group = fields.Nested(CustomerGroupSchema(CustomerGroup), allow_none=True)
    loyal_customer = fields.Nested(LoyalCustomerPointSchema(LoyalCustomerPoint), allow_none=True)
    sale_order = fields.Nested(SaleOrderSchema(SaleOrder), allow_none=True)
    histories = fields.List(fields.Nested(HistorySchema(History)), allow_none=True)
    sync_record_id = fields.Str(allow_none=True)
    source = fields.Nested(SourceObjectSchema(SourceObject), allow_none=True)

    @staticmethod
    def sample():
        return super().sample()


class CustomerModel(BasicModel):
    table_name = 'customer'
    attributes: CustomerAttributes
    attributes_schema = CustomerAttributesSchema
    attributes_class = CustomerAttributes

    query_mapping = {
        'query': ['id', 'email', 'phone', 'first_name', 'last_name', 'sub', 'customer_code'],
        'filter': {
            'id': 'query',
            'customer_code': 'query',
            'first_name': 'query',
            'last_name': 'query',
            'customer_group.id': 'query',
            'email': 'query',
            'tags': 'tags',
            'gender': 'query',
            'source.id': 'query',
            'source.channel_name': 'query',
            'company_id': 'query',
            'birthday': 'range',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'company_name': 'company_name',
            'loyal_customer': 'loyal_customer',
            'sale_order': 'sale_order',
            'customer_group': 'customer_group',
            'image': 'image',
            'histories': 'histories',
            'source': 'source',
        }

    }

    @classmethod
    def get_hash(cls, customer):
        phone = cls.parse_phone(customer)
        if phone:
            return compress({
                "phone": customer.get('phone'),
            })
        return compress({
            "email": customer.get('email'),
            "phone": phone,
            "first_name": customer.get('first_name'),
            "last_name": customer.get('last_name'),
            "billing_address": customer.get('billing_address')
        })

    @classmethod
    def parse_phone(cls, customer):
        phone = customer.get('phone', '')
        if phone and '*' not in phone:
            return phone

    @classmethod
    def get_customer(cls, company_id, customer):
        customer_id = customer.get('id') or cls.get_hash(customer)
        customer_obj = CustomerModel.by_key({
            'id': str(customer_id),
            'company_id': company_id
        })
        # phone = cls.parse_phone(customer)
        # if customer_obj is None and phone:
        #     customer_obj = CustomerModel.by_key({
        #         'phone': phone,
        #         'company_id': company_id
        #     })
        return customer_obj

    @classmethod
    def get_customer_by_phone(cls, phone):
        customer_id = cls.get_hash({'phone': phone})
        try:
            return cls.list({
                'id': customer_id
            }, limit=1)['Items'][0]
        except IndexError:
            return None

    @classmethod
    def sync(cls, company_id, data):
        customer_obj = CustomerModel.get_customer(company_id, data)
        if customer_obj:
            customer_obj.update(data)
            return customer_obj.attributes_dict

        customer_id = CustomerModel.get_hash(data)
        data['id'] = customer_id
        customer_payload = BasicAttributes.add_basic_attributes(data, company_id)

        customer_obj = CustomerModel(customer_payload)
        customer_obj.save()

        return customer_obj.attributes_dict

    @classmethod
    def customer_count(cls, customer_group_id):
        try:
            search_params = {
                "aggs": {
                    "customer_count": {
                        "filter": {
                            "term": {"customer_group.id.keyword": customer_group_id}
                        }
                    }
                }
            }
            response = cls.report(search_params, service=RESOURCE_SERVICE)
            return response.body['aggregations']['customer_count']['doc_count']
        except IndexError:
            return 0
