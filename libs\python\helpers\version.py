from models.inventory.location import LocationCompanyIdIndex
from models.settings.settings import SettingsModel
from models.settings.template_print import TemplatePrintModel


def fetch_settings(setting_name, company_id):
    settings_obj = SettingsModel.by_key({SettingsModel.key__id: company_id, "setting_name": setting_name})
    return settings_obj.attributes_dict if settings_obj else {}


def get_new_data(company_id):
    shop_info = fetch_settings("shop_info", company_id)
    color = fetch_settings("color", company_id)
    default_print_template = fetch_settings("print_template", company_id)

    limit = 10000
    print_templates = TemplatePrintModel.get_all_templates(company_id, limit)
    locations = LocationCompanyIdIndex.get_all_locations(company_id, limit)

    list_data = {
        "location": locations, "print_template": print_templates
    }
    dict_data = {
        "shop_info": shop_info, "color": color, "default_print_template": default_print_template
    }
    data = {"list": list_data, 'dict': dict_data}
    version = {"data": data}
    return version
