import os
import tempfile
import urllib
from datetime import datetime
from typing import Dict, Any, <PERSON>, Tuple, Optional
from urllib.parse import urlparse

import requests
from nolicore.utils.exceptions import BadRequest

from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, SyncAPIResponse,
    MetaData, StandardProduct, StandardOrder, PublishMetaData, StandardReturnOrder, StandardPurchaseOrder
)
from integrations.common.event import FetchEvent


class SquarespaceEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.PRODUCTS = EndpointType(type="rest", method="GET", path="/1.0/commerce/products")
        self.PRODUCT_DETAIL = EndpointType(type="rest", method="GET", path="/1.0/commerce/products/{product_id}")
        self.CREATE_PRODUCT = EndpointType(type="rest", method="POST", path="/1.0/commerce/products")
        self.UPDATE_PRODUCT = EndpointType(type="rest", method="POST", path="/1.0/commerce/products/{product_id}")
        self.UPLOAD_PRODUCT_IMAGE = EndpointType(type="rest", method="POST",
                                                 path="/1.0/commerce/products/{product_id}/images")
        self.UPDATE_PRODUCT_IMAGE = EndpointType(type="rest", method="POST",
                                                 path="/1.0/commerce/products/{product_id}/images/{image_id}")
        self.DELETE_PRODUCT_IMAGE = EndpointType(type="rest", method="DELETE",
                                                 path="/1.0/commerce/products/{product_id}/images/{image_id}")
        self.UPDATE_VARIANT = EndpointType(type="rest", method="POST",
                                           path="/1.0/commerce/products/{product_id}/variants/{variant_id}")
        self.UPDATE_STOCK = EndpointType(type="rest", method="POST", path="/1.0/commerce/inventory/adjustments")
        self.CREATE_VARIANT = EndpointType(type="rest", method="POST",
                                           path="/1.0/commerce/products/{product_id}/variants")
        self.DELETE_VARIANT = EndpointType(type="rest", method="DELETE",
                                           path="/1.0/commerce/products/{product_id}/variants/{variant_id}")
        self.DELETE_PRODUCT = EndpointType(type="rest", method="DELETE",
                                           path="/1.0/commerce/products/{product_id}")


class SquarespaceLibrary(BaseAPILibrary):

    def __init__(self,
                 api_key: str,
                 base_url: str = "https://api.squarespace.com"):
        super().__init__(base_url)
        self.api_key = api_key
        self.endpoints = SquarespaceEndpoints(self.endpoints.base_url)

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None,
                      path_params: Dict[str, Any] = None, files: Dict[str, Any] = None) -> Dict[str, Any]:
        """
            Makes a request to the Squarespace API.

            Args:
                endpoint: The endpoint configuration
                params: Query parameters
                data: JSON body data
                path_params: URL path parameters
                files: Files to upload (for multipart/form-data requests)

            Returns:
                API response as dictionary
            """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Idempotency-Key": "optiwarehouse"
        }

        # Only add Content-Type for JSON requests, let requests handle it for multipart/form-data
        if not files:
            headers["Content-Type"] = "application/json"

        response = self._make_request_sync(endpoint, headers=headers, params=params, json=data, path_params=path_params,
                                           files=files)
        if 'error' in response.get('type', '').lower():
            raise BadRequest(response)
        return response

    def test_connection(self) -> bool:
        try:
            self._make_request(self.endpoints.PRODUCTS, params={"limit": 1})
            return True
        except Exception:
            return False

    def get_product(self, product_id: str, fetch_event_id: str = None) -> FetchAPIResponse[StandardProduct]:
        response = self._make_request(self.endpoints.PRODUCT_DETAIL, path_params={"product_id": product_id})
        product = self.standardize_product(response['products'][0], fetch_event_id)
        return FetchAPIResponse(
            success=True,
            data=[product],
            meta=MetaData(total_count=1, page=1, limit=1)
        )

    def get_products(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                     fetch_event_id: str = None) -> \
            FetchAPIResponse[
                StandardProduct]:

        response = self._get_products(params)

        products = [self.standardize_product(product, fetch_event_id) for product in response.get("products", [])]

        return FetchAPIResponse(
            success=True,
            data=products,
            meta=MetaData(
                total_count=response.get("pagination", {}).get("total", len(products)),
                page=1,  # Squarespace uses cursor-based pagination
                limit=int(params["limit"]),
                continuation_token=response.get("pagination", {}).get("nextPageCursor"),
                current_params=params
            )
        )

    def _get_products(self, params):
        default_params = {
            "cursor": None,
            "limit": 50,
        }
        params = {**default_params, **(params or {})}

        return self._make_request(self.endpoints.PRODUCTS, params=params)

    def _get_all_products(self, params=None):
        products = []
        params = params or {}
        while True:
            response = self._get_products(params)
            next_page = response['pagination'].get('nextPageCursor')
            products.extend(response.get('products'))
            if not next_page:
                break
            params['cursor'] = next_page
        return products

    def _get_product_id_by_sku(self, sku):
        all_products = self._get_all_products()
        sku_product_id = {v['sku']: p['id'] for p in all_products for v in p['variants']}
        return sku_product_id.get(sku)

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        return StandardProduct(
            id=str(raw_product["id"]),
            fetch_event_id=fetch_event_id,
            title=raw_product["name"],
            raw_data=raw_product
        )

    def sync_product(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardProduct]:
        # Extract images and variants before modifying product_data
        images = product_data.pop('images', [])
        variants = product_data['variants']

        # Get product ID either directly or by SKU lookup
        product_id = product_data.get("id") or product_data.pop('remote_record_id', None)
        if product_id is None and variants:
            for variant in variants:
                product_id = self._get_product_id_by_sku(variant['sku'])
                if product_id:
                    break

        try:
            if product_id:
                read_only_fields = ['type', 'storePageId', 'variants']
                update_data = {k: v for k, v in product_data.items() if k not in read_only_fields}
                # Update existing product
                response = self._make_request(
                    self.endpoints.UPDATE_PRODUCT,
                    path_params={"product_id": product_id},
                    data=update_data
                )
                self._sync_variants(product_id, variants)
            else:
                # Create new product
                response = self._make_request(
                    self.endpoints.CREATE_PRODUCT,
                    data=product_data
                )
                product_id = response.get('id')

            # Sync images if provided
            if images and product_id:
                self._sync_images(product_id, images)

            synced_product = self.standardize_product(response)
            return SyncAPIResponse(
                success=True,
                data=synced_product,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Product {synced_product.title} synced successfully"
            )

        except Exception as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync product: {str(e)}"
            )

    def _sync_variants(self, product_id: str, new_variants: List[Dict[str, Any]]) -> None:
        # Get current product variants
        product_response = self.get_product(product_id)
        current_variants = product_response.data[0].raw_data.get('variants', [])

        # Create dictionaries with SKU as key for easy lookup
        current_variant_map = {var.get('sku', ''): var for var in current_variants}
        new_variant_map = {var.get('sku', ''): var for var in new_variants}

        # Process each new variant
        for sku, new_variant in new_variant_map.items():
            if sku in current_variant_map:
                # Update existing variant if different
                current_variant = current_variant_map[sku]
                if new_variant != current_variant:
                    read_only_field = ['stock']
                    variant_data = {k: v for k, v in new_variant.items() if k not in read_only_field}
                    self._make_request(
                        self.endpoints.UPDATE_VARIANT,
                        path_params={
                            "product_id": product_id,
                            "variant_id": current_variant['id']
                        },
                        data=variant_data
                    )
                    self._make_request(
                        self.endpoints.UPDATE_STOCK,
                        data={
                            "setFiniteOperations": [{
                                "variantId": current_variant['id'],
                                "quantity": new_variant['stock']['quantity']
                            }]
                        }
                    )

            else:
                # Add new variant
                self._make_request(
                    self.endpoints.CREATE_VARIANT,
                    path_params={"product_id": product_id},
                    data=new_variant
                )

        # Delete variants that no longer exist
        for sku, old_variant in current_variant_map.items():
            if sku not in new_variant_map:
                self._make_request(
                    self.endpoints.DELETE_VARIANT,
                    path_params={
                        "product_id": product_id,
                        "variant_id": old_variant['id']
                    }
                )

    def _download_image(self, image_url: str, filename: str) -> Tuple[str, str]:
        """
        Downloads an image from a URL to a temporary file.

        Args:
            image_url: URL of the image to download
            filename: Original filename of the image

        Returns:
            Tuple containing (temporary file path, content type)

        Raises:
            requests.exceptions.RequestException: If download fails
        """
        content_type = f'image/{os.path.splitext(filename)[1][1:].lower()}'

        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
            response = requests.get(image_url, stream=True)
            response.raise_for_status()
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)
            return temp_file.name, content_type

    def _sync_images(self, product_id: str, new_images: List[Dict[str, Any]]) -> None:
        """
        Synchronizes product images with Squarespace.

        Args:
            product_id: The ID of the product to sync images for
            new_images: List of image data dictionaries containing url and filename
        """
        # Get current product images
        product_response = self.get_product(product_id)
        current_images = product_response.data[0].raw_data.get('images', [])

        # Create dictionaries with filename as key for easy lookup
        current_image_map = {urllib.parse.unquote(os.path.basename(urlparse(img['url']).path)): img for img in
                             current_images}
        new_image_map = {img.get('filename', ''): img for img in new_images}

        # Process deletions first
        for filename, current_img in current_image_map.items():
            if filename not in new_image_map:
                try:
                    self._make_request(
                        self.endpoints.DELETE_PRODUCT_IMAGE,
                        path_params={
                            "product_id": product_id,
                            "image_id": current_img['id']
                        }
                    )
                except BadRequest as e:
                    print(f"Failed to delete image {current_img.get('id')}: {str(e)}")

        # Process additions and updates
        for filename, new_img in new_image_map.items():
            if not new_img.get('url') or filename in current_image_map:
                continue

            temp_file_path = None
            try:
                # Download the image
                temp_file_path, content_type = self._download_image(new_img['url'], filename)

                # Prepare the file upload
                with open(temp_file_path, 'rb') as image_file:
                    files = {
                        'file': (filename, image_file, content_type)
                    }

                    # Upload using existing _make_request method
                    self._make_request(
                        self.endpoints.UPLOAD_PRODUCT_IMAGE,
                        path_params={"product_id": product_id},
                        files=files  # The _make_request method will handle this like requests.post
                    )

            except requests.exceptions.RequestException as e:
                print(f"Failed to download image {filename}: {str(e)}")
            except BadRequest as e:
                print(f"Failed to upload image {filename}: {str(e)}")
            except Exception as e:
                print(f"Unexpected error processing image {filename}: {str(e)}")
            finally:
                # Clean up temporary file
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.unlink(temp_file_path)
                    except Exception as e:
                        print(f"Failed to cleanup temporary file {temp_file_path}: {str(e)}")

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def get_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                   fetch_event_id: str = None) -> \
            FetchAPIResponse[StandardOrder]:
        pass

    def sync_order(self, order_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[StandardOrder]:
        pass

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        pass

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> FetchEvent:
        # Implement webhook handling for Squarespace if they support webhooks
        raise NotImplementedError("Squarespace webhook handling not implemented")

    def webhook(self, payload: Dict[str, Any], headers: Dict[str, str]) -> FetchEvent:
        # Implement webhook handling for Squarespace if they support webhooks
        raise NotImplementedError("Squarespace webhook handling not implemented")
