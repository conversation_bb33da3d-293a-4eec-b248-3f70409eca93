from dataclasses import dataclass, field

from marshmallow import fields

from ..connection_base import OAuth2Connection, OAuth2ConnectionAttributes, OAuth2ConnectionSchema, OAuth2Settings, \
    OAuth2SettingsSchema


class BusinessCentralSettingsSchema(OAuth2SettingsSchema):
    tenant_id = fields.Str(default='common')
    environment = fields.Str(default='production')
    company_id = fields.Str(required=True)
    access_token = fields.Str(required=True)
    api_version = fields.Str(required=True)
    token_url = fields.Url(default="https://login.microsoftonline.com/common/oauth2/v2.0/token")
    auth_url = fields.Url(default="https://login.microsoftonline.com/common/oauth2/v2.0/authorize")
    scope = fields.Str(default="https://api.businesscentral.dynamics.com/.default")


@dataclass
class BusinessCentralSettings(OAuth2Settings):
    tenant_id: str = 'common'
    environment: str = 'production'
    company_id: str = None
    access_token: str = None
    api_version: str = None
    token_url: str = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
    auth_url: str = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize"
    scope: str = "https://api.businesscentral.dynamics.com/.default"


@dataclass
class BusinessCentralConnectionAttributes(OAuth2ConnectionAttributes):
    channel_name: str = field(default='business_central')
    settings: BusinessCentralSettings = field(default_factory=BusinessCentralSettings)


class BusinessCentralConnectionSchema(OAuth2ConnectionSchema):
    settings = fields.Nested(BusinessCentralSettingsSchema(BusinessCentralSettings))


class BusinessCentralConnection(OAuth2Connection):
    attributes: BusinessCentralConnectionAttributes = None
    attributes_schema = BusinessCentralConnectionSchema
    attributes_class = BusinessCentralConnectionAttributes
