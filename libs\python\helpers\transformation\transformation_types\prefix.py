from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class PrefixTransformationHandler(BaseTransformationHandler):
    """Handler for prefix transformation"""
    
    config_definition = TransformationConfig(
        label="Prefix",
        description="Add text at the beginning",
        direction=TransformationDirection(
            title="Add Prefix",
            content="Add specified text at the beginning of the value",
            example='"World" → "Hello World" (prefix: "Hello ")',
            value_type="Single Value"
        ),
        config_fields=[
            {
                "key": "prefix",
                "label": "Prefix Text",
                "type": "text",
                "placeholder": "Enter prefix text",
                "description": "The text to add at the beginning"
            }
        ]
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Adds prefix to the value if it's not None"""
        if value is None:
            return None
            
        if not self.transformation.config or 'prefix' not in self.transformation.config:
            return value
            
        prefix = self.transformation.config['prefix']
        return prefix + str(value) 