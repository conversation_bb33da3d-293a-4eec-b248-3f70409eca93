from dataclasses import dataclass
from enum import Enum

from elasticsearch import NotFoundError
from marshmallow import fields, pre_load
from marshmallow_enum import EnumField

from helpers.text import get_slug_by_name
from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.blog.blog_category import BlogCategory, BlogCategorySchema
from models.common.meta_data import MetaDataSchema, MetaData
from models.media.image import Image, ImageSchema


class BlogType(Enum):
    blog = 'blog'
    page = 'page'


class BlogStatus(Enum):
    DEFAULT = 'DEFAULT'
    ARCHIVED = 'ARCHIVED'


@dataclass
class BlogAttributes(BasicAttributes):
    title: str = None
    author_id: str = None
    content: str = None
    description: str = None
    locale: str = None
    org_blog_id: str = None
    blog_type: BlogType = BlogType.blog
    status: BlogStatus = BlogStatus.DEFAULT
    publish: bool = False
    image: Image = None
    tags: str = None
    category: BlogCategory = None
    meta_data: MetaData = None
    slug: str = None


class BlogAttributesSchema(BasicAttributesSchema):
    title = fields.Str(required=True)
    author_id = fields.Str(required=True)
    content = fields.Str(required=True)
    locale = fields.Str(required=True)
    org_blog_id = fields.Str(required=True)
    description = fields.Str(required=True)
    publish = fields.Bool(allow_none=True, default=False)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    tags = fields.Str(allow_none=True)
    category = fields.Nested(BlogCategorySchema(BlogCategory), allow_none=True)
    meta_data = fields.Nested(MetaDataSchema(MetaData), allow_none=True)
    slug = fields.Str(allow_none=True)
    status = EnumField(BlogStatus, allow_none=True, default=BlogStatus.DEFAULT)
    blog_type = EnumField(BlogType, allow_none=True, default=BlogType.blog)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['slug'] = data['slug'] if 'slug' in data else get_slug_by_name(data['title'])
        data['org_blog_id'] = data['org_blog_id'] if 'org_blog_id' in data else data['id']
        data['locale'] = data['locale'] if 'locale' in data else 'en'
        data['status'] = data['status'] if 'status' in data else BlogStatus.DEFAULT.value
        return data


class BlogModel(BasicModel):
    table_name = 'blog'
    attributes: BlogAttributes
    attributes_class = BlogAttributes
    attributes_schema = BlogAttributesSchema

    query_mapping = {
        'query': ['title', 'id', 'slug'],
        'filter': {
            'id': 'query',
            'slug': 'query',
            'locale': 'query',
            'org_blog_id': 'query',
            'blog_type': 'query',
            'category.id': 'query',
            'author_id': 'query',
            'status': 'query',
            'company_id': 'query',
            'publish': 'boolean',
            'tags': 'tags',
            'updated_at': 'range',
            'created_at': 'range'
        },
        'mapping': {
            'image': 'image',
            'description': 'description',
            'category.id': 'category.id',
            'category.name': 'category.name',
            'user': 'user',
        }
    }

    @classmethod
    def blog_count(cls, params):
        try:
            count_params = {
                "query": {
                    "bool": {
                        "filter": {
                            "term":
                                params
                        }
                    }
                }
            }
            file_count = cls.count(count_params, service=RESOURCE_SERVICE).body['count']
            return file_count
        except NotFoundError:
            return 0


class BlogSlugIndex(BlogModel):
    key__id = 'slug'
    index_name = 'blogSlugIndex'

    @classmethod
    def get_blog_by_slug(cls, slug, company_id):
        try:
            return cls(
                BlogSlugIndex.list({'slug': slug, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return


class OriginalBlogIdIndex(BlogModel):
    key__id = 'org_blog_id'
    index_name = 'orgBlogIdIndex'

    @classmethod
    def list_by_org_blog_id(cls, org_blog_id: str, company_id: str, remove_org_blog: bool = False,
                            remove_unpublished: bool = False, remove_archived: bool = False):
        filters = {'org_blog_id': org_blog_id, 'company_id': company_id}
        if remove_unpublished:
            filters['publish'] = True
        if remove_archived:
            filters['status'] = BlogStatus.DEFAULT.value
        try:
            blogs = cls.list(remove_unpublished, limit=200).get('Items', [])
            if remove_org_blog:
                blogs = [blog for blog in blogs if blog['id'] != org_blog_id]
            return blogs
        except IndexError:
            return []

    @classmethod
    def get_by_locale(cls, org_blog_id, company_id, locale):
        try:
            return cls(
                cls.list({'org_blog_id': org_blog_id, 'company_id': company_id, 'locale': locale}, limit=200).get(
                    'Items', [])[0])
        except IndexError:
            return None
