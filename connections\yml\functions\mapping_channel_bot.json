{"get_mapping_channel_bot": {"GET": [{"name": "Get mapping channel bot", "path_params": {"bot_id": "123", "channel_name": "facebook_oauth"}}]}, "create_mapping_channel_bot": {"POST": [{"name": "Create mapping channel bot", "path_params": {"bot_id": "bot_123", "connection_id": "fb_456", "channel_id": "page_789"}, "body": {}, "response": {"id": "mapping_123", "bot_id": "bot_123", "connection_id": "fb_456", "channel_id": "page_789", "channel_name": "facebook_oauth", "company_id": "company_123", "created_at": "2024-03-20T10:00:00Z", "updated_at": "2024-03-20T10:00:00Z"}}]}, "delete_mapping_channel_bot": {"DELETE": [{"name": "Delete mapping channel bot", "path_params": {"connection_id": "fb_456", "channel_id": "page_789"}, "response": {"message": "Mapping deleted successfully"}}]}, "get_pages": {"GET": [{"name": "Get pages with mapping status", "path_params": {"bot_id": "bot_123", "connection_id": "fb_456"}, "response": [{"name": "Page 1", "id": "page_789", "avatar": "https://example.com/avatar1.jpg", "is_active": true, "is_enabled": true}, {"name": "Page 2", "id": "page_101", "avatar": "https://example.com/avatar2.jpg", "is_active": false, "is_enabled": true}]}]}}