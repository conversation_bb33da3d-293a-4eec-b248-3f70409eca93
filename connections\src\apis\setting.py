import os
from datetime import datetime

from nolicore.utils.api import api_message
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import ApiExp, NotFoundRequest, BadRequest

from integrations.channels.action_types import ActionType, FetchActionSettings, PublishActionSettings, \
    ACTION_GROUP_MAPPING, Schedule, ActionStatus
from integrations.channels.connection_types import ConnectionStatus
from integrations.common.connection_helper import get_connection_by_id, process_connection, test_and_setup_connection, \
    update_fetch_settings, validate_connections

APP_URL = os.environ['APP_URL']


@as_api()
def set_fetch_action_settings(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    connection_id = api_gateway_request.path_parameters['connection_id']
    company_id = api_gateway_request.company_id

    actions = body.get('actions')

    if not actions:
        raise BadRequest("Missing required parameter: actions")

    try:
        connection = get_connection_by_id(connection_id, company_id=company_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        for action, settings in actions.items():
            action_type = ActionType(action)
            group = ACTION_GROUP_MAPPING.get(action_type)

            if not group:
                raise BadRequest(f"Invalid action type: {action}")

            # Parse status timestamps
            status_data = settings.get('status', {})

            def parse_datetime(dt_str):
                """Convert datetime string to datetime object if provided"""
                if isinstance(dt_str, str):
                    return datetime.fromisoformat(dt_str)
                return dt_str

            if status_data:
                status_data['last_run'] = parse_datetime(status_data.get('last_run'))
                status_data['next_run'] = parse_datetime(status_data.get('next_run'))

            fetch_settings = FetchActionSettings(
                enabled=settings.get('enabled', False),
                response_mapping=settings.get('response_mapping'),
                rate_limit=settings.get('rate_limit'),
                retry_settings=settings.get('retry_settings'),
                custom_settings=settings.get('custom_settings'),
                schedule=Schedule(**settings['schedule']) if settings.get('schedule') else None,
                status=ActionStatus(**status_data),
                destinations=settings.get('destinations')
            )
            connection.set_fetch_action_settings(group, action_type, fetch_settings)

        connection.save()

        return {"message": "Fetch action settings updated successfully"}
    except Exception as e:
        raise ApiExp(f"Error updating fetch action settings: {str(e)}")


@as_api()
def set_publish_action_settings(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id
    connection_id = api_gateway_request.path_parameters['connection_id']

    actions = body.get('actions')

    if not actions:
        raise BadRequest("Missing required parameter: actions")

    try:
        connection = get_connection_by_id(connection_id, company_id=company_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        for action, settings in actions.items():
            action_type = ActionType(action)
            group = ACTION_GROUP_MAPPING.get(action_type)

            if not group:
                raise BadRequest(f"Invalid action type: {action}")

            publish_settings = PublishActionSettings(
                enabled=settings.get('enabled', False),
                payload_template=settings.get('payload_template'),
                rate_limit=settings.get('rate_limit'),
                retry_settings=settings.get('retry_settings'),
                custom_settings=settings.get('custom_settings')
            )
            connection.set_publish_action_settings(group, action_type, publish_settings)

        connection.save()

        return {"message": "Publish action settings updated successfully"}
    except Exception as e:
        raise ApiExp(f"Error updating publish action settings: {str(e)}")


@as_api()
def update_webhook_settings(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id
    connection_id = api_gateway_request.path_parameters['connection_id']

    new_settings = body.get('webhook_settings')

    if not isinstance(new_settings, dict):
        raise BadRequest("Invalid webhook settings format. Expected a dictionary of webhook topics and their status.")

    try:
        connection = get_connection_by_id(connection_id, company_id=company_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        # Update webhook settings
        connection.update_webhook_settings(new_settings)
        return {"message": "Webhook settings updated successfully"}
    except Exception as e:
        raise ApiExp(f"Error updating webhook settings: {str(e)}")


@as_api()
def update_dynamic_settings(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id
    connection_id = api_gateway_request.path_parameters['connection_id']

    new_settings = body.get('dynamic_settings')

    if not isinstance(new_settings, dict):
        raise BadRequest("Invalid dynamic settings format. Expected a dictionary of dynamic settings and their values.")

    try:
        connection = get_connection_by_id(connection_id, company_id=company_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        # Update dynamic settings
        connection.update_dynamic_settings(new_settings)
        return {"message": "Dynamic settings updated successfully"}
    except Exception as e:
        raise ApiExp(f"Error updating dynamic settings: {str(e)}")


@as_api()
def remove_dynamic_settings(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id
    connection_id = api_gateway_request.path_parameters['connection_id']

    fields = body.get('fields', [])

    if not isinstance(fields, list):
        raise BadRequest("Invalid format. Expected a list of dynamic settings key.")

    if not all(isinstance(field, str) for field in fields):
        raise BadRequest("All elements in fields must be strings.")

    try:
        connection = get_connection_by_id(connection_id, company_id=company_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        # Update dynamic settings
        connection.remove_dynamic_settings(fields)
        return {"message": "Dynamic settings updated successfully"}
    except Exception as e:
        raise ApiExp(f"Error updating dynamic settings: {str(e)}")


@as_api()
def synchronization(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id
    source = body.get('source')
    destination = body.get('destination')
    actions = body.get('actions')
    redirect_url = body.get('redirect_url')
    base_url = f"https://{api_gateway_request.headers.get('host')}"
    if not redirect_url:
        redirect_url = f'{APP_URL}/synchronization'

    if not all([source, destination, actions]):
        raise BadRequest('Missing required parameters')

    try:
        source_connection, destination_connection = validate_connections(source, destination, company_id)

        if destination_connection.attributes.status != ConnectionStatus.ACTIVE:
            connection, auth_url = process_connection(destination_connection, is_new=True, base_url=base_url,
                                                      redirect_url=redirect_url)
            if auth_url:
                return {
                    "authorization_url": auth_url,
                    "connection_id": destination_connection.attributes.id,
                    "message": f"Please authorize the destination connection to continue"
                }

        test_and_setup_connection(source_connection)
        test_and_setup_connection(destination_connection)

        source_failed = {'groups': [], 'types': []}
        destination_failed = {'groups': [], 'types': []}

        for action_group, enabled in actions.items():
            # Update source settings
            group, action_type = update_fetch_settings(
                source_connection, action_group, enabled,
                destination, destination_connection.attributes.channel_name
            )
            if group:
                source_failed['groups'].append(group)
            if action_type:
                source_failed['types'].append(action_type)

            # Update destination settings
            group, action_type = update_fetch_settings(
                destination_connection, action_group, enabled,
                source, source_connection.attributes.channel_name
            )
            if group:
                destination_failed['groups'].append(group)
            if action_type:
                destination_failed['types'].append(action_type)

        source_connection.save()
        destination_connection.save()

        if any([source_failed['groups'], source_failed['types'],
                destination_failed['groups'], destination_failed['types']]):
            message = "Synchronization completed with unsupported action groups and types:\n"
            if source_failed['groups']:
                message += f"Source action groups: {', '.join(source_failed['groups'])} skipped.\n"
            if source_failed['types']:
                message += f"Source action types: {', '.join(source_failed['types'])} skipped.\n"
            if destination_failed['groups']:
                message += f"Destination action groups: {', '.join(destination_failed['groups'])} skipped.\n"
            if destination_failed['types']:
                message += f"Destination action types: {', '.join(destination_failed['types'])} skipped.\n"
        else:
            message = "Synchronization completed successfully"

        return api_message(message)

    except Exception as e:
        raise ApiExp(f"Error updating fetch action settings: {str(e)}")
