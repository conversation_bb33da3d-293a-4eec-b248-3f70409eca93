import os
import uuid

import pendulum

from helpers.inventory import update_inventory
from helpers.lambda_client import lambda_client_invoke_async
from helpers.utils import get_increment_id
from models.basic import BasicAttributes
from models.common.status import StockAdjustmentStatus
from models.inventory.stock_adjustment import StockAdjustmentModel

ENV = os.getenv('ENV')


def invoke_import_stock_adjustment(request_id, company_id, staff=None, source=None, start=0):
    payload = {
        'request_id': request_id,
        'company_id': str(company_id),
        'staff': staff,
        'start': start,
        'source': source
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-inventories-service-{ENV}-importSAdjustmentHandle')


def invoke_format_stock_adjustment(request_id, company_id, header):
    payload = {
        'request_id': request_id,
        'company_id': str(company_id),
        'header': header,
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-inventories-service-{ENV}-formatSAdjustmentHandle')


def import_stock_adjustment(company_id, stock_adjustment, staff):
    stock_adjustment_code = get_increment_id(company_id, StockAdjustmentModel)
    stock_adjustment['code'] = stock_adjustment_code
    stock_adjustment['creating_staff'] = staff
    stock_adjustment['checking_staff'] = staff
    stock_adjustment['balancing_staff'] = staff
    current_time = pendulum.now().to_iso8601_string()
    stock_adjustment['balanced_at'] = current_time
    stock_adjustment['status'] = StockAdjustmentStatus.IMPORTED.value
    for stock_line in stock_adjustment['stock_line_items']:
        if 'id' not in stock_line:
            stock_line['id'] = str(uuid.uuid4())
    stock_adjustment_obj = StockAdjustmentModel(BasicAttributes.add_basic_attributes(stock_adjustment, company_id))
    update_inventory(StockAdjustmentModel, stock_adjustment_obj, company_id, staff, update_type='adjust',
                     location=stock_adjustment_obj.attributes_dict['location'])
    stock_adjustment_obj.save()
    return stock_adjustment_obj.attributes_dict


def format_stock_adjustment(stock_adjustments):
    location_dict = {}
    new_stock_adjustments = []
    for stock_adjustment in stock_adjustments:
        for inventory in stock_adjustment.get('inventories'):
            location_id = inventory.get('location_id')
            available = inventory.get('available')
            new_inventory = {
                'sku': stock_adjustment.get('sku'),
                'available': available or 0
            }
            if location_id in location_dict:
                location_dict[location_id].append(new_inventory)
            else:
                location_dict[location_id] = [new_inventory]
    for key, value in location_dict.items():
        new_stock_adjustments.append({'location_id': key, 'inventories': value})
    return new_stock_adjustments
