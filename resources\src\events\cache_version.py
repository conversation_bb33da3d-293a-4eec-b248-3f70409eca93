import os

from boto3.dynamodb.types import TypeDeserializer
from nolicore.utils.utils import logger

from helpers.version import get_new_data
from models.basic import BasicAttributes
from models.version.version import VersionModel

deserializer = TypeDeserializer()

SERVICE = os.getenv('SERVICE')
ENV = os.getenv('ENV')


def handle(event, context):
    for record in event['Records']:
        if record['eventName'] in ['MODIFY', 'INSERT', 'REMOVE']:
            if record['eventName'] == 'REMOVE':
                record_image = record['dynamodb']['OldImage']
            else:
                record_image = record['dynamodb']['NewImage']
            record_data = {key: deserializer.deserialize(value) for key, value in record_image.items()}
            company_id = record_data['company_id']
            version_obj = VersionModel.by_key({VersionModel.key__id: company_id})
            version_data = get_new_data(company_id)
            if version_obj is None:
                version_number = str(0).zfill(8),
                version_obj = VersionModel(
                    BasicAttributes.add_basic_attributes({**version_data, "version": version_number}, company_id))
                version_obj.save()
                logger.info(f"Add version")
            else:
                old_version_number = version_obj.attributes_dict.get("version", "00000000")
                version_number = str(int(old_version_number) + 1).zfill(8)
                version_obj.update({**version_data, "version": version_number})
                logger.info(f"Update version")
