import unittest

from tests.base import BaseTestCase
from resources.src.events.index_dynamo_table import handle
from resources.tests.events.index_dynamo.data_index_dynamo import index_order_event


class IndexDynamoTestCase(BaseTestCase):
    def test_inventory_report(self):
        response = handle(index_order_event, {})
        self.assertTrue(True)


if __name__ == '__main__':
    # begin the unittest.main()
    unittest.main()
