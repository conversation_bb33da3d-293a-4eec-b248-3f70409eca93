from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class PostfixTransformationHandler(BaseTransformationHandler):
    """Handler for postfix transformation"""
    
    config_definition = TransformationConfig(
        label="Postfix",
        description="Add text at the end",
        direction=TransformationDirection(
            title="Add Postfix",
            content="Add specified text at the end of the value",
            example='"Hello" → "Hello World" (postfix: " World")',
            value_type="Single Value"
        ),
        config_fields=[
            {
                "key": "postfix",
                "label": "Postfix Text",
                "type": "text",
                "placeholder": "Enter postfix text",
                "description": "The text to add at the end"
            }
        ]
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Adds postfix to the value if it's not None"""
        if value is None:
            return None
            
        if not self.transformation.config or 'postfix' not in self.transformation.config:
            return value
            
        postfix = self.transformation.config['postfix']
        return str(value) + postfix 