import os

from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import BadRequest, NotFoundRequest

from helpers.custom_jsonpath import get_value_from_jsonpath
from helpers.mapping import format_sync_record, map_variants, validate_transform_request, get_source_sync_record
from helpers.transformation.factory import TransformationHandlerFactory
from helpers.transformation.handle_transform import process_transformations
from helpers.utils import RESOURCE_SERVICE
from integrations.channels.action_types import ActionGroup
from integrations.common.bucket_helper import get_transformed_data
from integrations.common.connection_helper import get_connection_by_id
from models.integration.destination_data import DestinationDataModel
from models.integration.sync_record import SyncRecordCompanyIdIndex, SyncRecordModel, MappingStatus

ENV = os.getenv('ENV')
TRANSFORMED_DATA_BUCKET = os.environ['TRANSFORMED_DATA_BUCKET']


@as_api()
def get_mapping_list(api_gateway_request: ApiGatewayRequest):
    record_type = api_gateway_request.path_parameters['record_type']
    connection_id = api_gateway_request.query_string_parameters['connection_id']
    if not connection_id:
        raise BadRequest("Missing required field: connection_id")
    if not record_type or record_type not in [action.value for action in ActionGroup]:
        raise BadRequest("Invalid record_type")
    company_id = api_gateway_request.company_id
    connection = get_connection_by_id(connection_id, company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")
    params = api_gateway_request.query_string_parameters or {}
    params['is_source'] = False
    params['record_type'] = record_type
    sync_records = SyncRecordCompanyIdIndex.search(params, service=RESOURCE_SERVICE,
                                                   company_id=company_id)

    items = sync_records.get("items", [])
    updated_items = [format_sync_record(sync_record) for sync_record in items]

    sync_records["items"] = updated_items
    return sync_records


@as_api()
def get_mapping(api_gateway_request: ApiGatewayRequest):
    sync_record_id = api_gateway_request.body['sync_record_id']
    record_type = api_gateway_request.path_parameters['record_type']
    if not record_type:
        raise BadRequest("Missing required field: record_type")
    if not sync_record_id:
        raise BadRequest("Missing required field: sync_record_id")
    params = {
        "id": sync_record_id,
        "is_source": False,
        "company_id": api_gateway_request.company_id,
        "record_type": record_type
    }

    sync_records = SyncRecordCompanyIdIndex.list(params, limit=1000)['Items']
    if not sync_records:
        return {
            'success': True,
            'message': f'No sync records found for id: {sync_record_id}',
            'data': []
        }
    sync_records = [format_sync_record(item) for item in sync_records]
    return {
        'success': True,
        'message': f'Sync records retrieved successfully for id: {sync_record_id}',
        'data': sync_records
    }


@as_api()
def unmap(api_gateway_request: ApiGatewayRequest):
    sync_record_id = api_gateway_request.body['sync_record_id']
    connection_id = api_gateway_request.body['connection_id']
    if not sync_record_id:
        raise BadRequest("Missing required field: sync_record_id")
    if not connection_id:
        raise BadRequest("Missing required field: connection_id")
    params = {"id": sync_record_id, "connection_id": connection_id, "company_id": api_gateway_request.company_id}
    sync_record = SyncRecordCompanyIdIndex.list(params, limit=1000)['Items'][0]
    if not sync_record:
        raise BadRequest("SyncRecord is not found")
    if sync_record['is_source']:
        raise BadRequest("SyncRecord's is_source must be 'False'")
    mapping_status = sync_record.get('mapping_status')
    if mapping_status != MappingStatus.MAPPED.value and mapping_status != MappingStatus.SYNCED.value:
        raise BadRequest("Status is not allowed")

    unmap_data = {
        "remote_record_id": None,
        "mapping_status": MappingStatus.UNMAPPED.value,
        "standard_destination_data": {},
        "other_mappings": {}
    }
    unmapped_sync_record = SyncRecordModel.put(sync_record_id, connection_id,
                                               unmap_data)
    return format_sync_record(unmapped_sync_record.attributes_dict)


@as_api()
def map_sync_records(api_gateway_request: ApiGatewayRequest):
    body = api_gateway_request.body
    sync_record_id = body.get('sync_record_id')
    connection_id = body.get('connection_id')
    destination_data_id = body.get('destination_data_id')
    is_extra_dest_mapping = body.get('is_extra_destination_mapping', False)
    extra_mapping_data = body.get('extra_mapping_data', {})
    required_fields = {
        'sync_record_id': sync_record_id,
        'connection_id': connection_id,
        'destination_data_id': destination_data_id
    }

    for field, value in required_fields.items():
        if not value:
            raise BadRequest(f"Missing required field: {field}")

    sync_record = get_source_sync_record(sync_record_id, connection_id, api_gateway_request.company_id)
    if not sync_record:
        raise BadRequest("SyncRecord is not found")
    if sync_record['is_source']:
        raise BadRequest("SyncRecord's is_source must be 'False'")
    record_type = sync_record.get('record_type')
    mapping_status = sync_record.get('mapping_status')

    if mapping_status != MappingStatus.UNMAPPED.value:
        raise BadRequest("Status is not allowed")

    destination_data_params = {
        'id': destination_data_id,
        'connection_id': connection_id,
        'type': record_type
    }
    try:
        destination_data = DestinationDataModel.list(destination_data_params, limit=1000)['Items'][0]
        if not destination_data:
            raise BadRequest("Destination data is not found")
    except (IndexError, KeyError) as e:
        raise BadRequest(f"Error retrieving destination data: {str(e)}")
    except Exception as e:
        raise BadRequest(f"Unexpected error while retrieving destination data: {str(e)}")

    # Handle variant mapping based on record type
    if record_type == ActionGroup.product.value:
        # For products, handle variant mapping
        if not is_extra_dest_mapping and not extra_mapping_data:
            raise BadRequest("At least one variant mapping is required for products")
        source_variants = sync_record.get('standard_source_data', {}).get('variants', [])
        updated_extra_mapping_data = map_variants(is_extra_dest_mapping, extra_mapping_data, source_variants)
    else:
        is_extra_dest_mapping = False
        updated_extra_mapping_data = {}

    # Get remote_record_id
    source_sync_records = SyncRecordCompanyIdIndex.list({'id': sync_record_id, 'is_source': True},
                                                        limit=1000)
    if not source_sync_records.get('Items'):
        raise Exception(f"No source sync record found for ID: {sync_record_id}")

    source_sync_record = source_sync_records['Items'][0]

    source_remote_record_id = source_sync_record.get('remote_record_id', None)

    map_data = {
        "remote_record_id": destination_data_id,
        "mapping_status": MappingStatus.MAPPED.value,
        "standard_destination_data": destination_data.get('standard_data', {}),
        "other_mappings": {
            "is_extra_destination_mapping": is_extra_dest_mapping,
            "mapping_data": {
                'source_remote_record_id': source_remote_record_id,
                'destination_remote_record_id': destination_data_id,
                'mapping_values': updated_extra_mapping_data
            }
        }
    }
    mapped_sync_record = SyncRecordModel.put(sync_record_id, connection_id, map_data)
    return format_sync_record(mapped_sync_record.attributes_dict)


@as_api()
def get_transformation_list(api_gateway_request: ApiGatewayRequest):
    """Get list of all registered transformations"""

    # Get configurations for all registered transformations
    all_configs = TransformationHandlerFactory.get_all_configs()

    # Format transformations
    transformations = [
        {
            "type": transformation_type.value,
            "label": config.label,
            "description": config.description,
            "config_fields": config.config_fields,
            "multiple_fields": config.multiple_fields,
            "direction": {
                "title": config.direction.title,
                "content": config.direction.content,
                "example": config.direction.example,
                "value_type": config.direction.value_type
            }
        }
        for transformation_type, config in all_configs.items()
    ]

    return {
        "success": True,
        "message": "Transformation list retrieved successfully",
        "data": transformations
    }


@as_api()
def handle_transform(api_gateway_request: ApiGatewayRequest):
    """Handle transformation of source fields based on provided transformations"""
    body = api_gateway_request.body

    # Validate request
    validate_transform_request(body)

    # Get source sync record
    sync_record_id = body['sync_record_id']
    try:
        sync_records = SyncRecordCompanyIdIndex.list({'id': sync_record_id, 'is_source': True}, limit=1000)
        if not sync_records.get('Items'):
            raise NotFoundRequest(f"Source sync record not found for id: {sync_record_id}")
        sync_record = sync_records.get('Items')[0]
    except Exception as e:
        raise NotFoundRequest(f"Error retrieving sync record: {str(e)}")

    # Get source field value
    source_key = sync_record['transformed_record_id']
    source_version_id = sync_record['transformed_record_version']
    master_data = get_transformed_data(TRANSFORMED_DATA_BUCKET, source_key, source_version_id)
    value = get_value_from_jsonpath(master_data, body['source_field'])

    if value is None:
        return {
            "success": True,
            "message": "Source field not found or is null",
            "data": {
                "outputs": [None] * len(body['transformations'])
            }
        }

    # Process transformations
    outputs = process_transformations(
        value,
        body['transformations'],
        body['source_field']
    )

    return {
        "success": True,
        "message": "Transformations applied successfully",
        "data": {
            "outputs": outputs
        }
    }
