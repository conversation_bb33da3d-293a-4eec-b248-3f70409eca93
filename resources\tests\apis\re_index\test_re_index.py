import unittest

from tests.base import BaseTestCase
from resources.tests.apis.re_index.data_index_dynamo import index_order_event
from resources.src.apis.index_dynamo_table import re_index


class ReIndexTestCase(BaseTestCase):
    def test_re_index(self):
        re_index(index_order_event, {'table_name': 'purchaseOrder'})
        self.assertTrue(True)


if __name__ == '__main__':
    # begin the unittest.main()
    unittest.main()
