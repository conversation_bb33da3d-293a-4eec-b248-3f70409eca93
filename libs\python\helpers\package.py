from nolicore.utils.api import api_message

from helpers.inventory import update_inventory
from models.common.status import Status, update_status
from models.order.order import OrderModel
from models.order.package import PackageModel


def cancel_package(company_id, user, package_obj: PackageModel):
    update_inventory(PackageModel, package_obj, company_id, user, update_type="cancelled", status=Status.CANCELLED)
    return api_message('Package cancel.')


def blocking(company_id, user, package_obj: PackageModel, note):
    # update package status
    update_status(user, package_obj, Status.BLOCKING, note)
    return api_message('Package blocking.')


def await_packing(company_id, user, package_obj: PackageModel, packing_staff=None):
    # update package status
    update_status(user, package_obj, Status.AWAIT_PACKING, packing_staff=packing_staff)
    return api_message('Package awaiting.')


def pack(company_id, user, package_obj: PackageModel):
    # update package status
    update_status(user, package_obj, Status.PACKING)

    return api_message('Package packing.')


def ready(company_id, user, package_obj: PackageModel):
    # update package status
    update_status(user, package_obj, Status.READY)

    # update order status
    current_packages = OrderModel.get_packages(company_id, package_obj.attributes.order_id)
    order_obj = OrderModel.get(company_id, package_obj.attributes.order_id)
    if len(current_packages) > 0:
        order_status = Status.READY if all(
            [package['status'] == Status.READY.value for package in current_packages]
        ) else Status.PARTIAL_READY
        update_status(user, order_obj, order_status)

    return api_message('Package ready.')


def ship(company_id, user, package_obj: PackageModel):
    update_inventory(PackageModel, package_obj, company_id, user, update_type="ship", status=Status.SHIPPING)
    # update order status
    current_packages = OrderModel.get_packages(company_id, package_obj.attributes.order_id)
    order_obj = OrderModel.get(company_id, package_obj.attributes.order_id)
    if len(current_packages) > 0:
        order_status = Status.SHIPPING if all(
            [package['status'] == Status.SHIPPING.value for package in current_packages]
        ) else Status.PARTIAL_SHIPPING
        update_status(user, order_obj, order_status)

    return api_message('Package ship.')


def delivered(company_id, user, package_obj: PackageModel):
    update_inventory(PackageModel, package_obj, company_id, user, update_type="delivered", status=Status.DELIVERED)
    # update order status
    current_packages = OrderModel.get_packages(company_id, package_obj.attributes.order_id)
    order_obj = OrderModel.get(company_id, package_obj.attributes.order_id)
    if len(current_packages) > 0:
        order_status = Status.DELIVERED if all(
            [package['status'] == Status.DELIVERED.value for package in current_packages]
        ) else Status.PARTIAL_DELIVERED
        update_status(user, order_obj, order_status)

    return api_message('Package delivered.')


def complete(company_id, user, package_obj: PackageModel):
    # update package status
    update_status(user, package_obj, Status.COMPLETED)

    # update order status
    current_packages = OrderModel.get_packages(company_id, package_obj.attributes.order_id)
    order_obj = OrderModel.get(company_id, package_obj.attributes.order_id)
    if len(current_packages) > 0:
        order_status = Status.COMPLETED if all(
            [package['status'] == Status.COMPLETED.value for package in current_packages]
        ) else Status.PARTIAL_COMPLETED
        update_status(user, order_obj, order_status)

    return api_message('Package delivered.')


def return_(company_id, user, package_obj: PackageModel):
    update_inventory(PackageModel, package_obj, company_id, user, update_type="returning", status=Status.RETURNING)
    # update order status
    current_packages = OrderModel.get_packages(company_id, package_obj.attributes.order_id)
    order_obj = OrderModel.get(company_id, package_obj.attributes.order_id)
    if len(current_packages) > 0:
        order_status = Status.RETURNING if all(
            [package['status'] == Status.RETURNING.value for package in current_packages]
        ) else Status.PARTIAL_RETURNING
        update_status(user, order_obj, order_status)

    return api_message('Package delivered.')


def delivered_return(company_id, user, package_obj: PackageModel):
    update_inventory(PackageModel, package_obj, company_id, user, update_type="returned", status=Status.RETURNED)
    # update order status
    current_packages = OrderModel.get_packages(company_id, package_obj.attributes.order_id)
    order_obj = OrderModel.get(company_id, package_obj.attributes.order_id)

    if len(current_packages) > 0:
        order_status = Status.RETURNED if all(
            [package['status'] == Status.RETURNED.value for package in current_packages]
        ) else Status.PARTIAL_RETURNED
        update_status(user, order_obj, order_status)

    return api_message('Return delivered.')
