import os
import hashlib
import hmac
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, Tuple

import requests
from nolicore.utils.exceptions import UnauthorizedExp, BadRequest
from nolicore.utils.utils import logger
import pendulum

from helpers.utils import ONEXBOTS_API
from helpers.custom_jsonpath import get_value_from_jsonpath
from integrations.channels.action_types import ActionType, ActionGroup
from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, StandardOrder, StandardProduct, MetaData, OAuthLibrary
)
from models.actor.customer import CustomerModel
from models.integration.bot_channel_connection import BotChannelConnectionModel
from models.integration.connection import ConnectionModel


class ZaloOAEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.OA_INFO = EndpointType(type="rest", method="GET", path="/v2.0/oa/getoa")
        self.SEND_MESSAGE = EndpointType(type="rest", method="POST", path="/v3.0/oa/message/cs")
        self.GET_MESSAGE = EndpointType(type="rest", method="GET", path="/v2.0/oa/listrecentchat")
        self.GET_USER = EndpointType(type="rest", method="GET", path="/v3.0/oa/user/detail")
        self.OA_TOKEN = EndpointType(type="rest", method="POST", path="/v4/oa/access_token")
        self.SEND_CHAT_MESSAGE = EndpointType(type="rest", method="POST",
                                              path="/api/v1/staff/staff/da04a7e8-77fe-4c95-a796-d8667f1e0027/chat")


class ZaloOALibrary(OAuthLibrary):
    AVAILABLE_WEBHOOKS = {
        "message": "/zalo_oa/message",
        "user": "/zalo_oa/user",
    }

    def __init__(self,
                 expires_at: str,
                 connection: ConnectionModel,
                 app_id: str,
                 secret_key: str,
                 access_token: Optional[str] = None, refresh_token: Optional[str] = None,
                 code_challenge: Optional[str] = None,
                 company_id: str = None):
        super().__init__("https://openapi.zalo.me")
        self.expires_at = expires_at
        self.connection = connection
        self.auth_url = "https://oauth.zaloapp.com"
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.app_id = app_id
        self.secret_key = secret_key
        self.endpoints = ZaloOAEndpoints(self.endpoints.base_url)
        self.date_time_format = 'YYYY-MM-DD HH:mm:ss'
        self.verify = False
        self.code_challenge = code_challenge
        self.company_id = company_id

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None,
                      path_params: Dict[str, Any] = None) -> Dict[str, Any]:
        headers = {
            'Content-Type': 'application/json',
        }

        if endpoint.path != self.endpoints.OA_TOKEN.path:
            headers['access_token'] = self.get_current_token()['access_token'] or self.access_token

        response = self._make_request_sync(endpoint, headers=headers, params=params, json=data,
                                           path_params=path_params, verify=self.verify)
        if response.get('error', 0) == -216:
            raise UnauthorizedExp("Token expire")
        return response

    def get_authorization_url(self, connection_id: str, redirect_url: str = None, base_url: str = None) -> str:
        app_callback_url = self.get_callback_url(base_url)
        state = self.generate_state(connection_id, redirect_url)
        code_challenge_str = f"code_challenge={self.code_challenge}&" if self.code_challenge else ""
        return (f"https://oauth.zaloapp.com/v4/oa/permission?"
                f"app_id={self.app_id}&"
                f"redirect_uri={app_callback_url}&"
                f"{code_challenge_str}"
                f"state={state}")

    def fetch_token(self, access_code: str, code_verifier: str = None, state: str = None) -> Dict[str, Any]:
        token_url = f"{self.auth_url}{self.endpoints.OA_TOKEN.path}"
        data = {
            "code": access_code,
            "app_id": self.app_id,
            "grant_type": "authorization_code",
        }
        if code_verifier:
            data["code_verifier"] = code_verifier

        response = requests.post(token_url, data=data, verify=self.verify, headers={"secret_key": self.secret_key})

        if response.status_code == 400:
            raise BadRequest(response)

        token_data = response.json()
        if 'access_token' not in token_data:
            raise UnauthorizedExp(token_data)

        # Process the token data
        access_token = token_data['access_token']
        refresh_token = token_data['refresh_token']

        # Update the library's access token, refresh_token
        self.access_token = access_token
        self.refresh_token = refresh_token

        expires_in = token_data.get('expires_in', 3600)  # Default to 1 hour if not provided
        expires_at = datetime.utcnow() + timedelta(seconds=int(expires_in))
        self.expires_at = expires_at

        # Get user info
        user_info = self._make_request(self.endpoints.OA_INFO)

        return {
            'external_id': get_value_from_jsonpath(user_info, '$.data.oa_id') or None,
            'access_token': access_token,
            'expires_at': expires_at,
            'refresh_token': token_data.get('refresh_token', None),
            'token_data': {
                'token_data': token_data,
                'user_info': [user_info]
            }
        }

    def refresh_access_token(self):
        token_url = f"{self.auth_url}{self.endpoints.OA_TOKEN.path}"
        data = {
            "refresh_token": self.refresh_token,
            "app_id": self.app_id,
            "grant_type": "refresh_token",
        }

        response = requests.post(token_url, data=data, verify=self.verify, headers={"secret_key": self.secret_key})

        if response.status_code == 400:
            raise BadRequest(response)

        token_data = response.json()
        if 'access_token' not in token_data:
            raise UnauthorizedExp(token_data)

        # Process the token data
        access_token = token_data['access_token']
        refresh_token = token_data['refresh_token']

        # Update the library's access token, refresh_token
        self.access_token = access_token
        self.refresh_token = refresh_token

        expires_in = token_data.get('expires_in', 3600)  # Default to 1 hour if not provided
        expires_at = datetime.utcnow() + timedelta(seconds=int(expires_in))

        # Get user info
        user_info = self._make_request(self.endpoints.OA_INFO)

        return {
            'external_id': get_value_from_jsonpath(user_info, '$.data.oa_id') or None,
            'access_token': access_token,
            'expires_at': expires_at,
            'refresh_token': token_data.get('refresh_token', None),
            'token_data': {
                'token_data': token_data,
                'user_info': [user_info]
            }
        }

    def test_connection(self) -> bool:
        try:
            self._make_request(self.endpoints.OA_INFO)
            return True
        except Exception:
            return False

    def get_messages(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                     fetch_event_id: str = None) -> FetchAPIResponse[StandardProduct]:
        try:
            response = self._make_request(self.endpoints.GET_MESSAGE, params=params)
            messages = [self.standardize_message(message, fetch_event_id) for message in response.get('data', [])]

            return FetchAPIResponse(
                success=True,
                data=messages,
                meta=MetaData(
                    limit=params.get("limit", 50),
                    continuation_token=response.get('next_page_token'),
                ),
            )
        except Exception as e:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"{e}",
            )

    def get_message(self, message_id: str, fetch_event_id: str) -> FetchAPIResponse[StandardProduct]:
        try:
            response = self._make_request(self.endpoints.GET_MESSAGE, params={"message_id": message_id})
            message = self.standardize_message(response.get('data', {}), fetch_event_id)

            return FetchAPIResponse(
                success=True,
                data=[message],
                meta=MetaData(
                    limit=1,
                    continuation_token=None,
                ),
            )
        except Exception as e:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"{e}",
            )

    def send_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            response = self._make_request(self.endpoints.SEND_MESSAGE, data=message_data)
            return response
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise e

    def standardize_message(self, raw_message: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        return StandardProduct(
            id=str(raw_message.get("message_id", "")),
            fetch_event_id=fetch_event_id,
            title=raw_message.get("text", ""),
            raw_data=raw_message,
            created_at=pendulum.parse(raw_message.get('created_time', datetime.now().isoformat())),
            updated_at=pendulum.parse(raw_message.get('updated_time', datetime.now().isoformat()))
        )

    def process_webhook(self, settings: Dict[str, Any], message_data: Dict[str, Any]) -> Dict[str, Any]:
        pass

    def setup_webhooks(self, connection):
        # Zalo OA webhooks are configured through their developer portal
        pass

    @classmethod
    def verify_webhook_signature(cls, payload: str, signature: str, oa_secret_key: str) -> bool:
        if not signature:
            return False

        # Parse JSON to get app_id and timestamp
        parse_json_payload = json.loads(payload)
        app_id = parse_json_payload.get('app_id')
        time_stamp = parse_json_payload.get('timestamp')

        # Use the raw payload string for signature calculation
        # Remove any whitespace from the payload string
        payload = payload.strip()
        signature_string = f"{app_id}{payload}{time_stamp}{oa_secret_key}"

        # Calculate SHA256 directly as per Zalo docs
        calculated_signature = 'mac=' + hashlib.sha256(signature_string.encode('utf-8')).hexdigest()

        # Compare signatures
        return hmac.compare_digest(calculated_signature, signature)

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[list]]:

        if event_type == 'message':
            return True, payload.get('message_id'), ActionGroup.message, payload, None
        elif event_type == 'user':
            return True, payload.get('user_id'), ActionGroup.user, payload, None

        return False, None, ActionGroup.unknown, None, None

    @classmethod
    def extract_merchant_id(cls, payload):
        return payload['recipient']['id']

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return payload.get('event_name')

    def process_webhook_event(self, event_type: str, payload: Dict[str, Any], connection: ConnectionModel,
                              channel_name: str = None) -> bool:
        if event_type == 'user_send_text':
            return self.process_message(payload, connection)
        return False

    def process_message(self, payload: Dict[str, Any], connection: ConnectionModel) -> bool:
        logger.info(f"Processing message: {payload}")
        text = payload['message']['text']

        user_id = payload['sender']['id']
        # Get or create customer
        customer = CustomerModel.get_customer_by_phone(user_id)
        if not customer:
            # create customer
            remote_customer = self._make_request(self.endpoints.GET_USER,
                                                 params={"data": json.dumps({"user_id": user_id})})
            customer_data = {
                'first_name': get_value_from_jsonpath(remote_customer, '$.data.display_name'),
                'phone': get_value_from_jsonpath(remote_customer, '$.data.user_id'),
            }
            customer = CustomerModel.sync(str(self.company_id), customer_data)

        send_data_chatbot = {
            "name": customer['first_name'],
            "message": text,
            "external_user_id": customer['phone'],
            "phone_number": customer['phone'],
        }
        # get bot id by connection id and channel_id (zalo_oa_id)
        bot_channel_connection = BotChannelConnectionModel.get_bot_channel_connection(str(connection.attributes.id),
                                                                                      self.extract_merchant_id(payload))
        if bot_channel_connection:
            bot_id = bot_channel_connection.attributes_dict.get('bot_id')
            # Send message to the chatbot
            try:
                response = requests.request(
                    method="POST",
                    url=f"{ONEXBOTS_API}/api/v1/staff/staff/{bot_id}/chat",
                    headers={"Content-Type": "application/json"},
                    json=send_data_chatbot
                )
                # response.raise_for_status()
                if response.status_code == 204:
                    return {}
                receive_message = response.json().get('response')
                if receive_message:
                    # Send message to the customer
                    self.send_message({
                        "recipient": {
                            "user_id": user_id
                        },
                        "message": {
                            "text": receive_message
                        }
                    })
            except Exception as e:
                raise BadRequest(e)
        else:
            logger.info(
                f"Mapping not found for: connection_id: {str(connection.attributes.id)}, channel_id: {self.extract_merchant_id(payload)}")
        return True

    def get_message_params(self):
        return FetchAPIResponse(
            success=True,
            data={
                'limit': 'Number',
                'offset': 'Number',
                'message_id': 'String',
                'user_id': 'String',
                'from_time': 'DateTime|ISO',
                'to_time': 'DateTime|ISO',
            },
            meta=None
        )

    def get_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                   fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        pass

    def get_products(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                     fetch_event_id: str = None) -> FetchAPIResponse[StandardProduct]:
        pass

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        pass

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        pass

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        pass

    def standardize_purchase_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        pass

    def standardize_return_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        pass

    def sync_order(self, order_id: str, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        pass

    def sync_product(self, product_id: str, fetch_event_id: str = None) -> FetchAPIResponse[StandardProduct]:
        pass

    def sync_purchase_order(self, order_id: str, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        pass

    def sync_return_order(self, order_id: str, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        pass
