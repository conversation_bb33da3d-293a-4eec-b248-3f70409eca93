from functools import lru_cache
from typing import Dict, Any, Optional, Union

import pendulum

from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse,
    MetaData, StandardProduct, StandardOrder, SyncAPIResponse, StandardReturnOrder, StandardPurchaseOrder
)
from integrations.common.event import FetchEvent


class Cin7Endpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.PRODUCTS = EndpointType(type="rest", method="GET", path="/Product")
        self.PRODUCT_DETAIL = EndpointType(type="rest", method="GET", path="/Product")
        self.PRODUCT_AVAILABILITY = EndpointType(type="rest", method="GET", path="/ref/productavailability")
        self.ATTRIBUTE_SET = EndpointType(type="rest", method="GET", path="/ref/attributeset")


class Cin7Library(BaseAPILibrary):


    def __init__(self,
                 account_id: str,
                 api_key: str,
                 base_url: str = "https://inventory.dearsystems.com/ExternalApi/v2"):
        super().__init__(base_url)
        self.account_id = account_id
        self.api_key = api_key
        self.endpoints = Cin7Endpoints(self.endpoints.base_url)

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None,
                      path_params: Dict[str, Any] = None) -> Dict[str, Any]:
        headers = {
            "Content-Type": "application/json",
            "api-auth-accountid": self.account_id,
            "api-auth-applicationkey": self.api_key
        }

        response = self._make_request_sync(endpoint, headers=headers, params=params, json=data, path_params=path_params)
        return response

    def test_connection(self) -> bool:
        try:
            self._make_request(self.endpoints.PRODUCTS, params={"Page": 1, "Limit": 1})
            return True
        except Exception:
            return False

    def get_product(self, product_id: str, fetch_event_id: str = None) -> FetchAPIResponse[StandardProduct]:
        response = self._make_request(self.endpoints.PRODUCT_DETAIL,
                                      params={"ID": product_id, 'IncludeAttachments': True})
        products = response.get("Products", [])
        if not products:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"Product with ID {product_id} not found"
            )
        product = self.standardize_product(products[0], fetch_event_id)
        return FetchAPIResponse(
            success=True,
            data=[product],
            meta=MetaData(total_count=1, page=1, limit=1)
        )

    def get_products(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                     fetch_event_id=None) -> \
            FetchAPIResponse[StandardProduct]:
        default_params = {"Limit": 5,
                          "Page": 1,
                          "IncludeAttachments": True,
                          **settings.get('custom_settings', {})}
        params = {**default_params, **params}
        limit = params.get("Limit", 50)
        page = params.get("Page", 1)
        if next_page:
            params['Page'] = next_page
        if 'ModifiedSince' not in params:
            updated_after = settings.get('updated_after')
            if updated_after:
                params['ModifiedSince'] = pendulum.parse(updated_after).to_iso8601_string()

        response = self._make_request(self.endpoints.PRODUCTS, params=params)
        products = [self.standardize_product(product, fetch_event_id) for product in response.get("Products", [])]

        return FetchAPIResponse(
            success=True,
            data=products,
            meta=MetaData(
                total_count=response.get("Total", len(products)),
                page=int(page),
                limit=int(limit),
                continuation_token=str(int(page) + 1) if len(products) == int(limit) else None,
                current_params=params
            )
        )

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    @staticmethod
    def get_product_params():
        return FetchAPIResponse(
            success=True,
            data={
                'ID': 'String',
                'Page': 'Number',
                'Limit': 'Number',
                'Name': 'String',
                'Sku': 'String',
                'ModifiedSince': 'DateTime|ISO',
                'IncludeDeprecated': 'Boolean',
                'IncludeAttachments': 'Boolean'
            },
            meta=None
        )

    def _enrich_product(self, product):
        product_id = product['ID']
        response = self._get_availability()
        product['ProductAvailabilityList'] = [item for item in response if item['ID'] == product_id]
        product['AttributeSetList'] = self._get_attributes()
        return product

    @lru_cache(maxsize=128)
    def _get_availability(self):
        all_availability = []
        page = 1
        limit = 1000
        while True:
            params = {
                "Limit": limit,
                "Page": page
            }
            response = self._make_request(
                self.endpoints.PRODUCT_AVAILABILITY,
                params=params
            )['ProductAvailabilityList']

            # Break if no more results
            if not response:
                break

            all_availability.extend(response)
            page += 1

        return all_availability

    @lru_cache(maxsize=128)
    def _get_attributes(self):
        return self._make_request(self.endpoints.ATTRIBUTE_SET,
                                  params={"Name": 'Products'})['AttributeSetList']

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        return StandardProduct(
            id=str(raw_product["ID"]),
            title=raw_product["Name"],
            raw_data=self._enrich_product(raw_product),
            fetch_event_id=fetch_event_id
        )

    def get_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None) -> \
            FetchAPIResponse[StandardOrder]:
        pass

    def sync_order(self, order_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[StandardOrder]:
        pass

    def sync_product(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardProduct]:
        pass

    def standardize_order(self, raw_order: Dict[str, Any]) -> StandardOrder:
        pass

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> FetchEvent:
        # Implement webhook handling for Dear Inventory if they support webhooks
        raise NotImplementedError("Cin7 (Dear Inventory) webhook handling not implemented")

    def webhook(self, payload: Dict[str, Any], headers: Dict[str, str]) -> FetchEvent:
        # Implement webhook handling for Dear Inventory if they support webhooks
        raise NotImplementedError("Cin7 (Dear Inventory) webhook handling not implemented")