import os
import hashlib
import hmac
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, Tuple

import requests
from nolicore.utils.exceptions import UnauthorizedExp, BadRequest
from nolicore.utils.utils import logger

from helpers.utils import ONEXBOTS_API
from integrations.channels.action_types import ActionGroup
from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, StandardOrder, StandardProduct
)
from models.actor.customer import CustomerModel
from models.integration.connection import ConnectionModel
from models.integration.webhook import WebhookModel
from models.integration.bot_channel_connection import BotChannelConnectionModel
from helpers.custom_jsonpath import get_value_from_jsonpath


class FacebookOAuthEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.OAUTH_DIALOG = EndpointType(type="rest", method="GET", path="/v22.0/dialog/oauth")
        self.ACCESS_TOKEN = EndpointType(type="rest", method="GET", path="/v22.0/oauth/access_token")
        self.EXTEND_TOKEN = EndpointType(type="rest", method="GET", path="/v22.0/oauth/access_token")
        self.DEBUG_TOKEN = EndpointType(type="rest", method="GET", path="/debug_token")
        self.GET_USER = EndpointType(type="rest", method="GET", path="/v22.0/{user_id}")
        self.PAGE_ACCOUNTS = EndpointType(type="rest", method="GET",
                                          path="/v22.0/me/accounts?fields=id,name,access_token,picture")
        self.SEND_MESSAGE = EndpointType(type="rest", method="POST", path="/v23.0/{page_id}/messages")
        self.SUBSCRIBE_WEBHOOK = EndpointType(type="rest", method="POST", path="/v22.0/{page_id}/subscribed_apps")
        self.PAGE_INFO = EndpointType(type="rest", method="GET", path="/v22.0/me")  # For testing user token
        self.PAGE_MESSAGES = EndpointType(type="rest", method="GET",
                                          path="/v22.0/me/conversations")  # For testing page token


class FacebookOAuthLibrary(BaseAPILibrary):
    AVAILABLE_WEBHOOKS = {
        "messages": "/facebook_oauth/messages",
        "messaging_postbacks": "/facebook_oauth/messaging_postbacks",
        "messaging_optins": "/facebook_oauth/messaging_optins",
    }

    def __init__(self,
                 app_id: str,
                 app_secret: str,
                 redirect_uri: str,
                 access_token: Optional[str] = None,
                 company_id: str = None, pages: Optional[list] = None):
        super().__init__("https://graph.facebook.com")
        self.auth_url = "https://www.facebook.com"
        self.access_token = access_token
        self.refresh_token = None
        self.app_id = app_id
        self.app_secret = app_secret
        self.redirect_uri = redirect_uri
        self.endpoints = FacebookOAuthEndpoints(self.endpoints.base_url)
        self.date_time_format = 'YYYY-MM-DD HH:mm:ss'
        self.verify = False
        self.company_id = company_id
        self.pages = pages

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None,
                      path_params: Dict[str, Any] = None) -> Dict[str, Any]:
        headers = {
            'Content-Type': 'application/json',
        }

        if self.access_token:
            params = params or {}
            params['access_token'] = self.access_token

        response = self._make_request_sync(endpoint, headers=headers, params=params, json=data,
                                           path_params=path_params, verify=self.verify)
        return response

    def get_authorization_url(self, connection_id: str, redirect_url: str = None, base_url: str = None) -> str:
        app_callback_url = self.get_callback_url(base_url)
        state = self.generate_state(connection_id, redirect_url)

        scopes = [
            'pages_show_list',
            'pages_read_engagement',
            'pages_manage_metadata',
            'pages_messaging',
            'public_profile'
        ]

        return (f"{self.auth_url}{self.endpoints.OAUTH_DIALOG.path}?"
                f"client_id={self.app_id}&"
                f"redirect_uri={app_callback_url}&"
                f"state={state}&"
                f"scope={','.join(scopes)}&"
                f"response_type=code")

    def fetch_token(self, access_code: str, state: str = None) -> Dict[str, Any]:
        # First get short-lived token
        token_url = f"{self.endpoints.base_url}{self.endpoints.ACCESS_TOKEN.path}"
        params = {
            "client_id": self.app_id,
            "redirect_uri": self.redirect_uri,
            "client_secret": self.app_secret,
            "code": access_code
        }

        response = requests.get(token_url, params=params, verify=self.verify)

        if response.status_code == 400:
            raise BadRequest(response)

        token_data = response.json()
        short_lived_token = token_data['access_token']

        # Get long-lived token
        long_lived_token = self.extend_token(short_lived_token)

        debug_token = self._make_request(self.endpoints.DEBUG_TOKEN,
                                         params={"input_token": long_lived_token['access_token'],
                                                 "access_token": f"{self.app_id}|{self.app_secret}"})

        # Update the library's access token to long-lived token for API calls
        self.access_token = long_lived_token['access_token']

        # Get page access token
        pages = self.get_pages()

        # test api call
        test_response = self._make_request(self.endpoints.PAGE_INFO)

        expires_in = long_lived_token.get('expires_in', 5184000)  # Default to 60 days if not provided
        half_expires_in = int(expires_in) // 2  # Calculate half of the expiration time
        expires_at = datetime.utcnow() + timedelta(seconds=half_expires_in)

        return {
            'external_id': debug_token['data']['user_id'],
            'access_token': long_lived_token['access_token'],  # Store long-lived user access token
            'expires_at': expires_at,
            'refresh_token': long_lived_token['access_token'],  # Facebook doesn't use refresh tokens
            'token_data': {
                'long_lived_token': long_lived_token,  # Store long-lived user access token
                "pages": pages
            }
        }

    def extend_token(self, short_lived_token: str) -> Dict[str, Any]:
        token_url = f"{self.endpoints.base_url}{self.endpoints.EXTEND_TOKEN.path}"
        params = {
            "grant_type": "fb_exchange_token",
            "client_id": self.app_id,
            "client_secret": self.app_secret,
            "fb_exchange_token": short_lived_token
        }

        response = requests.get(token_url, params=params, verify=self.verify)

        if response.status_code == 400:
            raise BadRequest(response)

        token_data = response.json()
        if 'access_token' not in token_data:
            raise UnauthorizedExp(token_data)

        return token_data

    def get_pages(self) -> Dict[str, Any]:
        response = self._make_request(self.endpoints.PAGE_ACCOUNTS)
        pages = response.get('data', [])

        # re-assign avatar to 'avatar'
        for page in pages:
            page['avatar'] = get_value_from_jsonpath(page, '$.picture.data.url')

        if not pages:
            raise UnauthorizedExp("No pages found for this user")

        return pages

    def get_page_token_by_page_id(self, page_id) -> str:
        pages = self.pages
        for page in pages:
            if page['id'] == page_id:
                return page['access_token']
        return ''

    def test_connection(self) -> bool:
        try:
            # First test the user access token
            user_response = self._make_request(self.endpoints.PAGE_INFO)
            return True
        except Exception as e:
            logger.error(f"Failed to test connection: {e}")
            return False

    def send_message(self, page_id: str, message_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            response = self._make_request(self.endpoints.SEND_MESSAGE, data=message_data,
                                          path_params={"page_id": page_id})
            return response
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise e

    def setup_webhooks(self, connection):
        for page in connection.attributes.token_data.get('pages', []):
            company_id = connection.attributes.company_id
            page_id = page['id']
            WebhookModel.create(company_id, {
                'id': f'{connection.attributes.channel_name}-{page_id}',
                'connection_id': str(connection.attributes.id),
            })

            # subscribe webhook topics
            try:
                webhook_topics_string = ','.join(self.AVAILABLE_WEBHOOKS.keys())
                response = requests.request(
                    method=self.endpoints.SUBSCRIBE_WEBHOOK.method,
                    url=f"{self.endpoints.base_url}{self.endpoints.SUBSCRIBE_WEBHOOK.path.format(page_id=page_id)}",
                    headers={
                        'Content-Type': 'application/json'
                    },
                    params={
                        "subscribed_fields": webhook_topics_string,
                        'access_token': page['access_token']
                    }
                )
                # response.raise_for_status()
                if response.status_code == 204:
                    return {}

                response = response.json()
                if response['success']:
                    logger.info(f"Successfully created webhook: {webhook_topics_string}")
                else:
                    logger.error(f"Failed to create webhook: {webhook_topics_string}")
            except Exception as e:
                raise BadRequest(e)

    @classmethod
    def verify_webhook_signature(cls, payload: str, signature: str, secret: str) -> bool:
        if not signature:
            return False

        # Remove 'sha256=' prefix if present
        if signature.startswith('sha256='):
            signature = signature[7:]

        # Calculate HMAC SHA256 using the raw payload bytes and secret
        expected_hash = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        # Compare the signatures using constant-time comparison
        return hmac.compare_digest(signature, expected_hash)

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[list]]:

        if event_type == 'messages':
            return True, payload.get('sender', {}).get('id'), ActionGroup.message, payload, None
        elif event_type == 'messaging_postbacks':
            return True, payload.get('sender', {}).get('id'), ActionGroup.message, payload, None
        elif event_type == 'messaging_optins':
            return True, payload.get('sender', {}).get('id'), ActionGroup.user, payload, None

        return False, None, ActionGroup.unknown, None, None

    @classmethod
    def extract_merchant_id(cls, payload):
        return payload['entry'][0]['messaging'][0]['recipient']['id']

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return payload.get('object')

    def process_webhook_event(self, event_type: str, payload: Dict[str, Any], connection: ConnectionModel) -> bool:
        if event_type == 'messages':
            return self.process_message(payload, connection)
        return False

    def process_sender_action_message(self, sender_action, page_id, user_id):
        if sender_action in ['typing_on', 'typing_off', 'mark_seen']:
            # Send sender_action message to the customer
            self.send_message(page_id, message_data={
                "recipient": {"id": user_id},
                "access_token": self.get_page_token_by_page_id(page_id),
                "sender_action": sender_action
            })

    def process_message(self, payload: Dict[str, Any], connection: ConnectionModel) -> bool:
        logger.info(f"Processing message: {payload}")
        page_id = self.extract_merchant_id(payload)
        text = payload['entry'][0]['messaging'][0]['message']['text']
        user_id = payload['entry'][0]['messaging'][0]['sender']['id']

        # Mark message as seen
        self.process_sender_action_message('mark_seen', page_id, user_id)

        # Mark as typing_on message
        self.process_sender_action_message('typing_on', page_id, user_id)

        # Get or create customer
        customer = CustomerModel.get_customer_by_phone(user_id)
        if not customer:
            # create customer
            # remote_customer = self._make_request(self.endpoints.GET_USER,
            #                                      path_params={"user_id": user_id},
            #                                      params={"access_token": self.access_token})
            customer_data = {
                # 'first_name': get_value_from_jsonpath(remote_customer, '$.data.first_name'),
                'first_name': user_id,
                'phone': user_id,
            }
            customer = CustomerModel.sync(str(self.company_id), customer_data)

        send_data_chatbot = {
            "name": user_id,
            "message": text,
            "external_user_id": user_id,
            "phone_number": user_id,
        }
        # get bot id by connection id and channel_id (page_id)
        bot_channel_connection = BotChannelConnectionModel.get_bot_channel_connection(str(connection.attributes.id),
                                                                                      page_id)
        if bot_channel_connection:
            bot_id = bot_channel_connection.attributes_dict.get('bot_id')
            # Send message to the chatbot
            try:
                response = requests.request(
                    method="POST",
                    url=f"{ONEXBOTS_API}/api/v1/staff/staff/{bot_id}/chat",
                    headers={"Content-Type": "application/json"},
                    json=send_data_chatbot
                )
                # response.raise_for_status()
                if response.status_code == 204:
                    return {}
                receive_message = response.json().get('response')
                if receive_message:
                    # Send message to the customer
                    self.send_message(page_id, message_data={
                        "recipient": {"id": user_id},
                        "messaging_type": "RESPONSE",
                        "message": {"text": receive_message},
                        "access_token": self.get_page_token_by_page_id(page_id)
                    })
            except Exception as e:
                raise BadRequest(e)
        else:
            logger.info(f"Mapping not found for: connection_id: {str(connection.attributes.id)}, channel_id: {page_id}")
        return True

    def get_message_params(self):
        return FetchAPIResponse(
            success=True,
            data={
                'recipient': 'Object',
                'message': 'Object',
                'messaging_type': 'String',
            },
            meta=None
        )

    # Implement required abstract methods from BaseAPILibrary
    def get_orders(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def get_products(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def get_purchase_order(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def get_return_order(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def get_purchase_orders(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def get_return_orders(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def standardize_order(self, *args, **kwargs) -> StandardOrder:
        pass

    def standardize_product(self, *args, **kwargs) -> StandardProduct:
        pass

    def standardize_purchase_order(self, *args, **kwargs) -> StandardOrder:
        pass

    def standardize_return_order(self, *args, **kwargs) -> StandardOrder:
        pass

    def sync_order(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def sync_product(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def sync_purchase_order(self, *args, **kwargs) -> FetchAPIResponse:
        pass

    def sync_return_order(self, *args, **kwargs) -> FetchAPIResponse:
        pass
