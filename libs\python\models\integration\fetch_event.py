from dataclasses import dataclass
from enum import Enum

from marshmallow import fields, pre_load
from nolicore.adapters.db.model import AttributesSchema

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.common.base_api_library import MetaData
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.event import FetchEvent, FetchEventStatus, EventSource
from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes
from marshmallow_enum import EnumField


class MetaDataSchema(AttributesSchema):
    total_page = fields.Int(allow_none=True, default=0)
    total_count = fields.Int(allow_none=True, default=0)
    page = fields.Int(allow_none=True, default=0)
    limit = fields.Int(allow_none=True, default=0)
    continuation_token = fields.Str(allow_none=True)
    current_params = fields.Dict(allow_none=True)


@dataclass
class FetchEventAttributes(BasicAttributes, FetchEvent):
    pass


class FetchEventAttributesSchema(BasicAttributesSchema):
    connection_id = fields.Str(required=True)
    channel = fields.Str(allow_none=True)
    action_type = EnumField(ActionType, required=True)
    action_group = EnumField(ActionGroup, required=True)
    event_time = fields.DateTime(required=True)
    batch_id = fields.Str(allow_none=True)
    is_batch = fields.Boolean(required=True, default=False)
    continuation_token = fields.Str(required=True)
    object_id = fields.Str(allow_none=True)
    object_data = fields.Dict(allow_none=True)
    retry_count = fields.Int(allow_none=True, default=0)
    object_ids = fields.List(fields.Str(required=True), allow_none=True)
    error_msg = fields.Str(allow_none=True)
    meta = fields.Nested(MetaDataSchema(MetaData), allow_none=True)
    status = EnumField(FetchEventStatus, allow_none=True, default=FetchEventStatus.PENDING)
    event_source = EnumField(EventSource, allow_none=True, default=EventSource.scheduler)
    destination_ids = fields.List(fields.Str(required=True), allow_none=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        if 'channel' not in data:
            connection = get_connection_by_id(data['connection_id'])
            if connection:
                data['channel'] = connection.attributes.channel_name
        data['status'] = data['status'] if 'status' in data else FetchEventStatus.COMPLETED.value
        return data


class FetchEventModel(BasicModel):
    key__id = 'id'
    key__ranges = ['continuation_token']
    table_name = 'fetchEvent'
    attributes: FetchEventAttributes
    attributes_schema = FetchEventAttributesSchema
    attributes_class = FetchEventAttributes

    query_mapping = {
        'query': ['id'],
        'filter': {
            'id': 'query',
            'connection_id': 'query',
            'continuation_token': 'query',
            'is_batch': 'boolean',
            'channel': 'query',
            'status': 'query',
            'action_type': 'query',
            'action_group': 'query',
            'event_source': 'query',
            'company_id': 'query',
            'event_time': 'range',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'batch_id': 'batch_id',
            'retry_count': 'retry_count',
            'error_msg': 'error_msg',
            'meta': 'meta',
            'user': 'user',
        }
    }

    @classmethod
    def put(cls, data, company_id):
        key = str(data.get('id'))
        continuation_token = str(data.get('continuation_token'))
        record = cls.by_key({
            cls.key__id: key,
            'continuation_token': continuation_token
        })

        if record:
            record.update(data)
            return record
        record = cls.create(company_id, {cls.key__id: key, 'continuation_token': continuation_token, **data})
        return record

    @classmethod
    def get_record(cls, key, connection_id):
        return cls.by_key({
            cls.key__id: key,
            'connection_id': connection_id
        })

    @classmethod
    def get_fetch_event(cls, fetch_event_id):
        try:
            return cls(cls.list({
                cls.key__id: fetch_event_id,
            }, limit=1)['Items'][0])
        except Exception as e:
            return None


class FetchEventCompanyIdIndex(FetchEventModel):
    key__id = 'id'
    key__ranges = ['company_id']
    index_name = 'fetchEventByCompanyIndex'
