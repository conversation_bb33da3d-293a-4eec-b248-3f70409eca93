from typing import Dict, Type

from integrations.channels.business_central.business_central_connection import BusinessCentralConnection
from integrations.channels.cin7.cin7_connection import Cin7Connection
from integrations.channels.haravan.haravan_connection import HaravanConnection
from integrations.channels.misa_amis.misa_amis_connection import MisaAmisConnection
from integrations.channels.nhanh.nhanh_connection import NhanhConnection
from integrations.channels.shopee.shopee_connection import ShopeeConnection
from integrations.channels.shopify.shopify_connection import ShopifyConnection
from integrations.channels.shopify_oauth.shopify_oauth_connection import ShopifyOAuthConnection
from integrations.channels.squarespace.squarespace_connection import SquarespaceConnection
from models.integration.connection import ConnectionModel
from integrations.channels.zalo.zalo_connection import ZaloConnection
from integrations.channels.tiktokshop.tiktokshop_connection import TiktokShopConnection
from integrations.channels.zalo_oa.zalo_oa_connection import ZaloOAConnection
from integrations.channels.facebook_oauth.facebook_oauth_connection import FacebookOAuthConnection

class ConnectionRegistry:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConnectionRegistry, cls).__new__(cls)
            cls._instance._connections = {}
        return cls._instance

    def register(self, key: str, connection_class: Type[ConnectionModel]):
        self._connections[key] = connection_class

    def get(self, key: str) -> Type[ConnectionModel]:
        return self._connections.get(key)

    def get_all(self) -> Dict[str, Type[ConnectionModel]]:
        return self._connections


# Initialize the registry
connection_registry = ConnectionRegistry()
connection_registry.register(FacebookOAuthConnection.attributes_class.channel_name, FacebookOAuthConnection)
connection_registry.register(ZaloOAConnection.attributes_class.channel_name, ZaloOAConnection)
connection_registry.register(ShopifyOAuthConnection.attributes_class.channel_name, ShopifyOAuthConnection)
connection_registry.register(TiktokShopConnection.attributes_class.channel_name, TiktokShopConnection)
connection_registry.register(ShopifyConnection.attributes_class.channel_name, ShopifyConnection)
connection_registry.register(ShopeeConnection.attributes_class.channel_name, ShopeeConnection)
connection_registry.register(BusinessCentralConnection.attributes_class.channel_name, BusinessCentralConnection)
connection_registry.register(NhanhConnection.attributes_class.channel_name, NhanhConnection)
connection_registry.register(HaravanConnection.attributes_class.channel_name, HaravanConnection)
connection_registry.register(ZaloConnection.attributes_class.channel_name, ZaloConnection)
connection_registry.register(SquarespaceConnection.attributes_class.channel_name, SquarespaceConnection)
connection_registry.register(Cin7Connection.attributes_class.channel_name, Cin7Connection)
connection_registry.register(MisaAmisConnection.attributes_class.channel_name, MisaAmisConnection)