Resources:
  KnowledgeQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-knowledge-queue
      VisibilityTimeout: 120

  KnowledgeBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:service}-${self:provider.stage}-knowledge-bucket
      VersioningConfiguration:
        Status: Enabled
      NotificationConfiguration:
        QueueConfigurations:
          - Event: s3:ObjectCreated:*
            Queue: !GetAtt KnowledgeQueue.Arn
            Filter:
              S3Key:
                Rules:
                  - Name: prefix
                    Value: knowledge/

  KnowledgeQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref KnowledgeQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: "AllowS3SendMessage"
            Effect: Allow
            Principal:
              Service: s3.amazonaws.com
            Action: sqs:SendMessage
            Resource: !GetAtt KnowledgeQueue.Arn
            Condition:
              ArnLike:
                aws:SourceArn: !Sub "arn:aws:s3:::${self:service}-${self:provider.stage}-knowledge-bucket"

Outputs:
  KnowledgeQueueUrl:
    Value: !Ref KnowledgeQueue
    Export:
      Name: ${self:service}-${self:provider.stage}-KnowledgeQueueUrl

  KnowledgeBucketName:
    Value: !Ref KnowledgeBucket
    Export:
      Name: ${self:service}-${self:provider.stage}-KnowledgeBucketName
