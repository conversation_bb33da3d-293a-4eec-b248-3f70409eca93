class MasterDataAttribute():
    @classmethod
    def get_object_attributes(cls, type: str):
        attribute_getters = {
            'product': cls.product_attributes
        }

        return attribute_getters.get(type, lambda: {})()

    @classmethod
    def product_attributes(cls):
        attributes = {
            "id": {"label": "id", "description": "A globally-unique ID.", "type": "string", "is_required": True},
            "name": {"label": "name", "description": "The title of the product.", "type": "string",
                     "is_required": True},
            "sku": {"label": "sku",
                    "description": "A case-sensitive identifier for the product variant in the shop. Required in order to connect to a fulfillment service.",
                    "type": "string", "is_required": True},
            "barcode": {"label": "barcode", "description": "", "type": "string", "is_required": False},
            "description": {"label": "description",
                            "description": "A single-line description of the product, with HTML tags removed.",
                            "type": "string", "is_required": False},
            "tags": {"label": "tags",
                     "description": "A list of tags that have been added to the product.",
                     "type": "string", "is_required": False},
            "category": {"label": "category",
                         "description": "The category of a product from Shopify's Standard Product Taxonomy.",
                         "type": "object", "is_required": False,
                         "properties": {
                             "id": {"label": "id", "is_required": True, "type": "string", "description": ""},
                             "parent_category_id": {"label": "parent_category_id", "is_required": False,
                                                    "type": "string", "description": ""},
                             "image": {"label": "id", "is_required": False, "type": "object", "description": "",
                                       "properties": {
                                           "id": {"label": "id", "is_required": False, "type": "string",
                                                  "description": ""},
                                           "url": {"label": "url", "is_required": True, "type": "string",
                                                   "description": ""},
                                           "name": {"label": "name", "is_required": False, "type": "string",
                                                    "description": ""},
                                       }
                                       },
                             "name": {"label": "name", "is_required": True, "type": "string", "description": ""},
                             "has_children": {"label": "has_children", "is_required": False, "type": "bool",
                                              "description": ""},
                             "meta_data": {"label": "meta_data", "is_required": False, "type": "object",
                                           "description": "",
                                           "properties": {
                                               "id": {"label": "id", "is_required": False, "type": "string",
                                                      "description": ""},
                                               "description": {"label": "description", "is_required": False,
                                                               "type": "string", "description": ""},
                                               "keywords": {"label": "keywords", "is_required": False, "type": "string",
                                                            "description": ""},
                                               "author": {"label": "author", "is_required": False, "type": "string",
                                                          "description": ""},
                                               "image": {"label": "image", "is_required": False, "type": "string",
                                                         "description": ""},
                                               "url": {"label": "url", "is_required": False, "type": "string",
                                                       "description": ""},
                                           }
                                           },
                             "slug": {"label": "slug", "is_required": False, "type": "string", "description": ""},
                         }
                         },
            "images": {"label": "images",
                       "description": "The images associated with the product.", "is_required": False, "type": "object",
                       "properties": {
                           "id": {"label": "id", "is_required": False, "type": "string", "description": ""},
                           "url": {"label": "url", "is_required": True, "type": "string", "description": ""},
                           "name": {"label": "name", "is_required": False, "type": "string", "description": ""},
                       }
                       },
            "publish": {"label": "publish", "description": "", "type": "bool", "is_required": False},
            "shortDescription": {"label": "shortDescription", "description": "", "type": "string",
                                 "is_required": False},
            "source": {"label": "source", "description": "", "type": "object", "is_required": False,
                       "properties": {
                           "id": {"label": "id", "is_required": True, "type": "string", "description": ""},
                           "channel_name": {"label": "channel_name", "is_required": True, "type": "string",
                                            "description": ""},
                           "origin": {"label": "origin", "is_required": False, "type": "string", "description": ""},
                           "origin_id": {"label": "origin_id", "is_required": False, "type": "string",
                                         "description": ""},
                           "name": {"label": "name", "is_required": False, "type": "string", "description": ""},
                           "logo": {"label": "logo", "is_required": False, "type": "string", "description": ""},
                       }
                       },
            "brand": {"label": "brand", "description": "", "type": "object", "is_required": False,
                      "properties": {
                          "id": {"label": "id", "is_required": True, "type": "string", "description": ""},
                          "name": {"label": "name", "is_required": True, "type": "string", "description": ""},
                          "image": {"label": "id", "is_required": False, "type": "object", "description": "",
                                    "properties": {
                                        "id": {"label": "id", "is_required": False, "type": "string",
                                               "description": ""},
                                        "url": {"label": "url", "is_required": True, "type": "string",
                                                "description": ""},
                                        "name": {"label": "name", "is_required": False, "type": "string",
                                                 "description": ""},
                                    }
                                    },
                      }
                      },
            "measurements": {"label": "measurements", "description": "", "is_required": False, "type": "object",
                             "properties": {
                                 "weight_unit": {"label": "weight_unit", "is_required": False, "type": "string",
                                                 "description": ""},
                                 "weight_value": {"label": "weight_value", "is_required": False, "type": "decimal",
                                                  "description": ""},
                                 "width_unit": {"label": "width_unit", "is_required": False, "type": "string",
                                                "description": ""},
                                 "width_value": {"label": "width_value", "is_required": False, "type": "decimal",
                                                 "description": ""},
                                 "length_unit": {"label": "length_unit", "is_required": False, "type": "string",
                                                 "description": ""},
                                 "length_value": {"label": "length_value", "is_required": False, "type": "decimal",
                                                  "description": ""},
                                 "height_unit": {"label": "height_unit", "is_required": False, "type": "string",
                                                 "description": ""},
                                 "height_value": {"label": "height_value", "is_required": False, "type": "decimal",
                                                  "description": ""},
                             }
                             },
            "options": {"label": "options", "description": ""},
            "prices": {"label": "prices", "description": "", "is_required": False, "type": "array",
                       "properties": {
                           "price": {"label": "price", "is_required": True, "type": "int", "description": ""},
                           "price_group": {"label": "price_group", "is_required": True, "type": "object",
                                           "description": "",
                                           "properties": {
                                               "id": {"label": "id", "is_required": True, "type": "string",
                                                      "description": ""},
                                               "name": {"name": "price", "is_required": False, "type": "string",
                                                        "description": ""},
                                           }
                                           },
                       }
                       },
            "slug": {"label": "slug", "description": "", "is_required": False, "type": "String"}
        }

        return attributes
