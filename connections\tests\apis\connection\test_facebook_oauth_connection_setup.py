import json
import unittest
import os
from dotenv import load_dotenv
from tests.base import BaseTestCase

from connections.src.apis.connection import setup_connection
from integrations.channels.facebook_oauth.facebook_oauth_connection import FacebookOAuthConnection


class FacebookOAuthConnectionSetupIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.app_id = os.getenv("FACEBOOK_APP_ID", "your_app_id")
        cls.app_secret = os.getenv("FACEBOOK_APP_SECRET", "your_app_secret")
        cls.redirect_uri = os.getenv("FACEBOOK_REDIRECT_URI", "")

    def setUp(self):
        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"

    def create_lambda_event(self, body):
        return {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_setup_connection(self):
        event = {'version': '2.0', 'routeKey': 'POST /setup/{connection_type}', 'rawPath': '/setup/facebook_oauth',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zvRCybDp2-uN45mGrCJr47R09bpgt0MvB-aVDCP-PGRZRAKBvG1oWR1TWJ3J06uvmVoew9a4BtpeDDPnpYWqyTNjnyZ_fICbrb9iqa3am67MRfpYY0Am6-L_KmtkXem9ysonAT0FXgANDFNaELEW5jCQv89ChRRei8ycpWipU7ZWQ5y9qmMj9th2tILUa9IdwfbO_6KhCqIcefFPnwZsjx5nvNkSJX5C2M-TEGIiIrkBAd_-_iR51mjDVrtqvk2q10iVsthi_Xq0NA4ANwRCZ8wEss8OCvUNInL7Y_DpT8L-cJrQbARIAbaNoDQabH2gu81xtvSUJhMBvzERsAt7NA',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-US,en;q=0.5',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QCsNHdTb_en9Q3pV3tYiCbjrIiPwMzUmMoUvd9Xk4eYpcxPGtEah9HxuLDyMwfIlsIvvEf3b8UO1i7K13241cwgaezQGNch0B7gGpGNS0PZA-L3ORpSxwxd2Iw9uBrnRgs2L3keFZ-fF2JTv0C_k6gU_UM2NOcJHJVCjNaPOi4hw9acPfd8IHjs_W4FL9oQMRyc2m96B4c-Z7RwjoB4sc4IcT2ryg9JSIvtvXOTokJODRCX_QPpnu3-e4yQUSZJHrhPzHFs6RTIUx4_i9DWJVzBU5F0I2fdmd4TSSL4r4Cid_wScYs7Qrh0GmOQecMOzsjxenQCd8bCt_VyC6EwgTA',
                             'content-length': '199', 'content-type': 'application/json',
                             'host': 'api-staging.optiwarehouse.com', 'origin': 'https://staging.optiwarehouse.com',
                             'priority': 'u=1, i', 'referer': 'https://staging.optiwarehouse.com/',
                             'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                             'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site', 'sec-gpc': '1',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                             'x-amzn-trace-id': 'Root=1-683d852d-5ce4c2677d10b333046de8af',
                             'x-forwarded-for': '**************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zvRCybDp2-uN45mGrCJr47R09bpgt0MvB-aVDCP-PGRZRAKBvG1oWR1TWJ3J06uvmVoew9a4BtpeDDPnpYWqyTNjnyZ_fICbrb9iqa3am67MRfpYY0Am6-L_KmtkXem9ysonAT0FXgANDFNaELEW5jCQv89ChRRei8ycpWipU7ZWQ5y9qmMj9th2tILUa9IdwfbO_6KhCqIcefFPnwZsjx5nvNkSJX5C2M-TEGIiIrkBAd_-_iR51mjDVrtqvk2q10iVsthi_Xq0NA4ANwRCZ8wEss8OCvUNInL7Y_DpT8L-cJrQbARIAbaNoDQabH2gu81xtvSUJhMBvzERsAt7NA'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': '56689f66-fb35-4565-9268-6afe8b81d46a', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': '5e94bbd3-b62c-4608-9e7c-51a0811047c1',
                                'origin_jti': '6a7188bb-995a-4c25-89a9-2c54ad7a6e41',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging',
                                    'http': {'method': 'POST', 'path': '/setup/facebook_oauth', 'protocol': 'HTTP/1.1',
                                             'sourceIp': '**************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                    'requestId': 'LiG_HjaMSQ0EP5g=', 'routeKey': 'POST /setup/{connection_type}',
                                    'stage': '$default', 'time': '02/Jun/2025:11:04:13 +0000',
                                    'timeEpoch': 1748862253153},
                 'pathParameters': {'connection_type': 'facebook_oauth'},
                 'body': '{"connection_type":"facebook_oauth","settings":{"app_id":"1224751199126351","app_secret":"67300cbc28fcec024f83a880aab74401","redirect_uri":"https://api-dev.onexbots.com/connections/oauth2_callback"}}',
                 'isBase64Encoded': False}

        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

    def test_setup_facebook_oauth_connection_success(self):
        # Arrange
        event_body = {
            "connection_type": "facebook_oauth",
            "settings": {
                "app_id": self.app_id,
                "app_secret": self.app_secret,
                "redirect_uri": self.redirect_uri
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'facebook oauth connection setup successful')
        self.assertIn('connection_id', response_body)

        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = FacebookOAuthConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.settings.app_id, self.app_id)
        self.assertEqual(created_connection.attributes.settings.app_secret, self.app_secret)
        self.assertEqual(created_connection.attributes.settings.redirect_uri, self.redirect_uri)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status, 'ACTIVE')

        # Clean up
        created_connection.delete()

    def test_setup_facebook_oauth_connection_invalid_credentials(self):
        # Arrange
        event_body = {
            "connection_type": "facebook_oauth",
            "settings": {
                "app_id": "invalid_app_id",
                "app_secret": "invalid_app_secret",
                "redirect_uri": "invalid_redirect_uri"
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['error'], 'Bad request')
        self.assertIn('Invalid Facebook OAuth credentials', response_body['message'])

    def test_test_connection(self):
        # Arrange
        event_body = {
            "connection_type": "facebook_oauth",
            "settings": {
                "app_id": self.app_id,
                "app_secret": self.app_secret,
                "redirect_uri": self.redirect_uri
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertIn('connection_id', response_body)

        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = FacebookOAuthConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.settings.app_id, self.app_id)
        self.assertEqual(created_connection.attributes.settings.app_secret, self.app_secret)
        self.assertEqual(created_connection.attributes.settings.redirect_uri, self.redirect_uri)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status, 'ACTIVE')

        # Clean up
        created_connection.delete()


if __name__ == '__main__':
    unittest.main()
