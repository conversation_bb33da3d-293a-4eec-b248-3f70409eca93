import json
from datetime import datetime

from dotenv import load_dotenv

from tests.base import BaseTestCase
from flows.src.events.step_3_transform import transform_lambda_handler
from flows.tests.apis.connection.data_test import product_master_data
from integrations.common.transformers import registry


class TestTransform(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file

        ## update these below variables to test other cases
        cls.buket_name = 'optiwarehouse-flow-service-dev-raw-data'
        cls.key = "764b8784-c48f-4378-80ec-f0085c20f13b/get_product/product/product_1731517316724459102.json"
        cls.versionId = "NRQZbVkGH_Zlacyy1YtSX0S9KL94ai8b"

        if not all(
                [cls.buket_name, cls.key, cls.versionId]):
            raise ValueError("Credentials not found in environment variables")

    def setUp(self):
        super().setUp(account_type="onexapis_admin")

    def create_lambda_event(self):
        return {'Records': [{
            'eventTime': datetime.utcnow().isoformat(),
            's3': {'s3SchemaVersion': '1.0',
                   'configurationId': 'optiwarehouse-flow-service-dev-transform_handler-68a737133cefb25bff959852b8f04754',
                   'bucket': {'name': self.buket_name,
                              'ownerIdentity': {'principalId': 'AFOTVK6KYYU9G'},
                              'arn': 'arn:aws:s3:::optiwarehouse-flow-service-dev-raw-data'},
                   'object': {
                       'key': self.key,
                       'size': 1811, 'eTag': '094116a3ba051bbbc05880fdd68890d4',
                       'versionId': self.versionId,
                       'sequencer': '0067224C4AC5CCD96D'}}
        }]}

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_transform_lambda_handler(self):
        event = self.create_lambda_event()
        context = self.create_lambda_context()

        # Call the lambda handler
        result = transform_lambda_handler(event, context)

        # Assertions
        self.assertEqual(result['statusCode'], 200)
        self.assertEqual(json.loads(result['body']), 'Transform Lambda execution completed')

    def test_destination_transform(self):
        connection_data = {
            "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d",
            "created_at": "2025-04-01T09:46:44.278000+00:00",
            "updated_at": "2025-04-01T09:46:44.278000+00:00",
            "name": "Tiktok Shop",
            "id": "764b8784-c48f-4378-80ec-f0085c20f13b",
            "status": "ACTIVE",
            "url": None,
            "image": ""
        }
        test_master_data = product_master_data
        destination_transformer_class = registry.get_destination_transformer("tiktok_shop", "product", connection_data)
        transformed_data = destination_transformer_class.transform(data=test_master_data)
        print('transformed_data', transformed_data)
