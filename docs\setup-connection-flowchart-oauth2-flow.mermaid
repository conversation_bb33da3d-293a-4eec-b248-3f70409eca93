sequenceDiagram
    participant Client
    participant Server
    participant OAuth2Provider

    Client->>Server: POST /setup_connection
    Note over Server: Extract connection_type and setup_data
    Server->>Server: Create OAuth2Connection instance
    Server->>OAuth2Provider: Generate authorization URL
    Server->>Server: Save connection with pending status
    Server-->>Client: Return authorization URL and connection ID

    Client->>OAuth2Provider: Open authorization URL
    OAuth2Provider-->>Client: Redirect with authorization code
    Client->>Server: GET /oauth2_callback with code and state (connection ID)

    Server->>Server: Retrieve connection by ID
    Server->>OAuth2Provider: Exchange code for access token
    OAuth2Provider-->>Server: Return access and refresh tokens
    Server->>Server: Update connection with tokens
    Server->>Server: Test connection
    alt Connection test successful
        Server->>Server: Activate connection
        Server->>Server: Save updated connection
        Server-->>Client: Return success message
    else Connection test failed
        Server-->>Client: Return error message
    end
