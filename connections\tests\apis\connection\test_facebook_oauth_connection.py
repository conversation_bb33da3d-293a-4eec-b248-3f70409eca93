import json
import unittest
import os
from dotenv import load_dotenv
from tests.base import BaseTestCase

from connections.src.apis.connection import get_connection_types, oauth2_callback, test_connection
from integrations.common.connection_helper import get_connection_by_id
from nolicore.utils.exceptions import UnauthorizedExp, BadRequest
from connections.src.apis.setting import set_fetch_action_settings
from integrations.channels.action_types import ActionGroup, ActionType
from models.integration.connection import ConnectionModel
from integrations.channels.facebook_oauth.facebook_oauth_library import FacebookOAuthLibrary


class TestFacebookOAuthConnection(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.app_id = "718936114147097"
        cls.app_secret = "a86f1e693adc314d2ce9b124b5a6ebde"
        cls.redirect_uri = "https://api-dev.onexbots.com/connections/oauth2_callback"
        cls.facebook_oauth_library = FacebookOAuthLibrary(
            app_id=cls.app_id,
            app_secret=cls.app_secret,
            redirect_uri=cls.redirect_uri
        )
        if not all([cls.app_id, cls.app_secret]):
            raise ValueError("Facebook OAuth credentials not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"

    def create_lambda_event(self, method='GET', path='/', body=None, query_params=None, path_params=None, headers=None):
        event = {
            'httpMethod': method,
            'body': json.dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': headers,
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            },
            'isBase64Encoded': False
        }
        return event

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test_function'

        return LambdaContext()

    def test_get_connection_types(self):
        response = get_connection_types(self.create_lambda_event(), {})
        self.assertTrue(True)

    def test_get_authorization_url(self):
        connection_id = '7888a03a-3526-450d-94c6-67e00afce5f5'
        auth_url = self.facebook_oauth_library.get_authorization_url(connection_id=connection_id)
        self.assertIsNotNone(auth_url)
        self.assertIn(self.app_id, auth_url)
        self.assertIn(connection_id, auth_url)

    def test_oauth2_callback(self):
        # Mock OAuth callback parameters
        callback_params = {
            'code': 'test_auth_code',
            'state': 'test_connection_id'
        }

        event = self.create_lambda_event(query_params=callback_params)
        context = self.create_lambda_context()
        response = oauth2_callback(event, context)
        self.assertTrue(True)

    def test_test_connection(self):
        event = {'version': '2.0', 'routeKey': 'GET /connection/{connection_id}/test',
                 'rawPath': '/connection/766b4d82-3ce7-42e3-b544-4951bce94067/test',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HGqeIKeusQrDs9WgtWdPAs0vc7typbjv4VXUbX0bnlpfOXoiiRYuUAqzhA4DxlmfqMDhl9Qj271dmrPdFw4G52WDNSipEl4a1iOfVRoRLHzXvsMp2OI0hVg-psezROFwxZfcuhK9fTsTLQdGjJVfQGRp_494bmqMxkSmVrq9o6ErUSmv5yZUs9PKpOBOeeiPepuOHYJDhi_cWJ0BJ24xRvwKHLtDGPsmJjkK-ajDQQkuzAbsaVDyRjHO0m3iF7uHXQa984-llPwsUL-Lmls7BrD_XIn43_-oTrHTTSvFyGYad18ozzeVDdEG2SYX4gpdm3bdb4wzSzc1VZCysDXI0w',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-US,en;q=0.9',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GrQqKNt20AYd3uYzjgcSEq5Kg6iIC75chSz0PbFKrKIuqQUejwka6dYbZP0WKkL9KRb1fiFb7cG1xXdCpI1o4Ii57pelebd2juet6FsbvSljF3w8lfsJKGfL2GqzOjEjPH3J8Fkw-8GoN29Y56KK5mzzXZHCMzuK7OLuZM8hywYSqBdEEz1Esw2rXcYq-EaGfytL9fiiN07tSREOuSv3GopTZBUvk9vf8FohPHn9rkZuWnK6hJU2fypoMEopgm3LNpVGeVYqXV9kOk4v-1YA-5NCUdnCnle2jnQsJ-TessFHAvqO63pTyoAi41196AsY8GJStUaYqOigkYE389SACg',
                             'content-length': '0', 'host': 'api-staging.optiwarehouse.com',
                             'origin': 'https://staging.optiwarehouse.com', 'priority': 'u=1, i',
                             'referer': 'https://staging.optiwarehouse.com/',
                             'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                             'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site', 'sec-gpc': '1',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                             'x-amzn-trace-id': 'Root=1-68394c24-109ffca4589ac2822ebc51ff',
                             'x-forwarded-for': '*************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HGqeIKeusQrDs9WgtWdPAs0vc7typbjv4VXUbX0bnlpfOXoiiRYuUAqzhA4DxlmfqMDhl9Qj271dmrPdFw4G52WDNSipEl4a1iOfVRoRLHzXvsMp2OI0hVg-psezROFwxZfcuhK9fTsTLQdGjJVfQGRp_494bmqMxkSmVrq9o6ErUSmv5yZUs9PKpOBOeeiPepuOHYJDhi_cWJ0BJ24xRvwKHLtDGPsmJjkK-ajDQQkuzAbsaVDyRjHO0m3iF7uHXQa984-llPwsUL-Lmls7BrD_XIn43_-oTrHTTSvFyGYad18ozzeVDdEG2SYX4gpdm3bdb4wzSzc1VZCysDXI0w'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': '56689f66-fb35-4565-9268-6afe8b81d46a', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': '712486f3-897c-4e89-9da2-0fc02784dee6',
                                'origin_jti': '6a7188bb-995a-4c25-89a9-2c54ad7a6e41',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging',
                                    'http': {'method': 'GET',
                                             'path': '/connection/766b4d82-3ce7-42e3-b544-4951bce94067/test',
                                             'protocol': 'HTTP/1.1', 'sourceIp': '*************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                    'requestId': 'LXjVxiFHyQ0EJMQ=', 'routeKey': 'GET /connection/{connection_id}/test',
                                    'stage': '$default', 'time': '30/May/2025:06:11:48 +0000',
                                    'timeEpoch': 1748585508533},
                 'pathParameters': {'connection_id': '766b4d82-3ce7-42e3-b544-4951bce94067'}, 'isBase64Encoded': False}

        response = test_connection(event, {})

        # Assert the response
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertTrue(response_body['success'])
        self.assertTrue(response_body['is_connected'])

    def test_refresh_access_token(self):
        connection_id = "7888a03a-3526-450d-94c6-67e00afce5f5"
        connection = get_connection_by_id(connection_id, self.company_id, False)
        channel = connection.get_channel()

        result = channel.refresh_oauth_token()

    def test_set_fetch_action_settings_success(self):
        connection_id = 'test_connection_id'
        event_body = {
            "actions": {
                "get_message": {
                    "enabled": True,
                    "rate_limit": 60,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "schedule": {
                        "type": "interval",
                        "value": "300"
                    },
                    "status": {
                        "last_run": None,
                        "next_run": None
                    }
                }
            }
        }
        event = self.create_lambda_event(
            body=event_body,
            path_params={'connection_id': connection_id}
        )
        context = self.create_lambda_context()

        response = set_fetch_action_settings(event, context)

        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Fetch action settings updated successfully')


if __name__ == '__main__':
    unittest.main()
