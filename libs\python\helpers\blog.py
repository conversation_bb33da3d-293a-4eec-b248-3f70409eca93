from deepdiff import DeepDiff
from models.blog.blog import B<PERSON><PERSON><PERSON><PERSON>


def sync_blog(company_id, blog, remote_blog: BlogModel = None, user=None):
    if remote_blog:
        remote_blog.update(blog)
        return remote_blog.attributes_dict
    return BlogModel.create(company_id, blog, user).attributes_dict


def need_update_blogs(blog, locale_blog) -> bool:
    if not blog:
        return True
    diff = DeepDiff(blog, locale_blog,
                    exclude_regex_paths=[r"root\[.*\]\['created_at'\]", r"root\[.*\]\['updated_at'\]"])
    return any(key in diff for key in (
        'values_changed', 'iterable_item_added', 'iterable_item_removed', 'dictionary_item_added',
        'dictionary_item_removed'))
