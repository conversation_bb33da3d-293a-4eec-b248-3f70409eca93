import os

import boto3
import requests
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import BadRequest
from nolicore.utils.utils import logger

from helpers.auth import login_helper

# Initialize the Amazon Cognito client
client = boto3.client('cognito-idp', region_name='ap-southeast-1')

# Set the user pool and user details
USER_POOL_ID = os.environ['USER_POOL_ID']
CLIENT_ID = os.environ['APP_CLIENT_ID']


def create_user(company_id, username, password, email=None, roles: list = None, user_sub=None):
    user_attributes = [
        {
            'Name': 'custom:company_id',
            'Value': company_id
        },
        {
            'Name': 'custom:role',
            'Value': 'User'
        }
    ]
    if email:
        user_attributes.append({
            'Name': 'email',
            'Value': email
        })
    if user_sub:
        user_attributes.append({
            'Name': 'sub',
            'Value': user_sub
        })
    # Create the user with custom attributes

    response = client.admin_create_user(
        UserPoolId=USER_POOL_ID,
        Username=username,
        UserAttributes=user_attributes,
        TemporaryPassword=password,
        MessageAction='SUPPRESS'
    )
    # Get the newly created user's sub (unique identifier)
    user_sub = response['User']['Username']
    if roles:
        for role in roles:
            client.admin_add_user_to_group(
                UserPoolId=USER_POOL_ID,
                Username=user_sub,
                GroupName=f'{company_id}-{role.title()}'
            )
    else:
        client.admin_add_user_to_group(
            UserPoolId=USER_POOL_ID,
            Username=user_sub,
            GroupName=f'{company_id}-User'
        )
    return response


def create_user_info_by_api(data):
    response = login_helper("", "", is_admin=True)
    token = response['IdToken']
    access_token = response['AccessToken']

    url = f'https://admin.onexapis.com/admin/users?access_token={access_token}'
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    response = requests.post(url, headers=headers,  json=data)

    if response.status_code == 200:
        return response.json()
    else:
        # If the request failed, raise an exception
        raise Exception('Failed to create user: ' + response.text)


def set_password(username, password):
    response = client.admin_set_user_password(
        UserPoolId=USER_POOL_ID,
        Username=username,
        Password=password,
        Permanent=True
    )
    return response


@as_api(roles=['super-Admin', 'company-Admin'])
def list_users(api_gateway_request: ApiGatewayRequest):
    company_id = api_gateway_request.query_string_parameters.get(
        'company_id') if api_gateway_request.is_supper_admin else api_gateway_request.company_id
    if company_id is None:
        raise BadRequest('Company could not be none')
    limit = api_gateway_request.query_string_parameters.get('limit', 10)
    last = api_gateway_request.query_string_parameters.get('nextPage', None)
    params = {
        'UserPoolId': USER_POOL_ID,
        'GroupName': f'{company_id}-User'
    }
    if last:
        params['NextToken'] = last
    if limit:
        params['Limit'] = limit
    response = client.list_users_in_group(**params)
    return response
