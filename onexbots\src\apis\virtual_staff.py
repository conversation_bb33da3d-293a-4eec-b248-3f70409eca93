import os
from typing import Any

from nolicore.utils.aws.decorator import as_api, invoke
from nolicore.utils.aws.request import ApiGatewayRequest, LambdaRequest
from nolicore.utils.exceptions import BadRequest, NotFoundRequest

from helpers.image import upload_image
from helpers.utils import RESOURCE_SERVICE
from models.bots.knowledge import KnowledgeModel
from models.bots.virtual_staff import (
    VirtualStaffModel,
)
from ..helpers.common import deep_update
from ..helpers.knowledge import invoke_remove_staff_from_knowledge
from ..helpers.virtual_staff import prepare_virtual_staff_data, handle_virtual_staff_update, \
    UpdateTypeEnum


def get_env_or_default(key: str, default: Any) -> Any:
    """Get value from environment or return default."""
    return os.getenv(key) or default


@as_api()
def create_virtual_staff(api_gateway_request: ApiGatewayRequest):
    """Create a new virtual staff"""
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id

    # Validate required fields
    required_fields = [
        'name',
        'department_id',
        'role',
        'image'
    ]
    for field in required_fields:
        if field not in body:
            raise BadRequest(f"Missing required field: {field}")

    try:
        # Prepare virtual staff data
        prepared_data = prepare_virtual_staff_data(company_id, body)
        # Create virtual staff
        virtual_staff = VirtualStaffModel.create(company_id, prepared_data)
        return {
            'success': True,
            'message': 'Virtual staff created successfully',
            'data': virtual_staff.attributes_dict
        }
    except ValueError as e:
        raise BadRequest(str(e))


@as_api()
def get_virtual_staff(api_gateway_request: ApiGatewayRequest):
    """Get a virtual staff by ID"""
    virtual_staff_id = api_gateway_request.path_parameters.get('id')
    if not virtual_staff_id:
        raise BadRequest("Missing required field: id")

    # Get virtual staff
    virtual_staff = VirtualStaffModel.get_by_id(virtual_staff_id)
    if not virtual_staff:
        raise NotFoundRequest("Virtual staff not found")

    knowledge_ids = virtual_staff.attributes_dict.get('configuration', {}).get('knowledge_base', {}).get(
        'knowledge_ids', []) or []
    knowledge_list = []
    for knowledge_id in knowledge_ids:
        knowledge = KnowledgeModel.get_by_id(_id=knowledge_id)
        if knowledge:
            knowledge_list.append(knowledge.attributes_dict)
    result = deep_update(virtual_staff.attributes_dict, {
        'configuration': {
            'knowledge_base': {
                'knowledge_list': knowledge_list
            }
        }
    })

    return {
        'success': True,
        'message': 'Virtual staff retrieved successfully',
        'data': result
    }
    
@as_api()
def get_public_virtual_staff(api_gateway_request: ApiGatewayRequest):
    """Get a virtual staff by ID"""
    virtual_staff_id = api_gateway_request.path_parameters.get('id')
    if not virtual_staff_id:
        raise BadRequest("Missing required field: id")

    # Get virtual staff
    virtual_staff_object = VirtualStaffModel.get_by_id(virtual_staff_id)
    if not virtual_staff_object:
        raise NotFoundRequest("Virtual staff not found")
    
    virtual_staff = {
        "id": virtual_staff_object.attributes_dict.get("id"),
        "name": virtual_staff_object.attributes_dict.get("name"),
        "image": virtual_staff_object.attributes_dict.get("image"),
        "role": virtual_staff_object.attributes_dict.get("role"),
        "skills": virtual_staff_object.attributes_dict.get("skills"),
        "greeting": virtual_staff_object.attributes_dict.get("greeting"),
        "color": virtual_staff_object.attributes_dict.get("color"),
    }
    
    return {
        'success': True,
        'message': 'Virtual staff retrieved successfully',
        'data': virtual_staff
    }


@as_api()
def list_virtual_staffs(api_gateway_request: ApiGatewayRequest):
    """List virtual staffs with optional filters"""
    company_id = api_gateway_request.company_id
    params = api_gateway_request.query_string_parameters or {}
    # Search using company index
    virtual_staffs = VirtualStaffModel.search(
        params=params,
        service=RESOURCE_SERVICE,
        company_id=company_id
    )
    return virtual_staffs


@as_api()
def update_virtual_staff(api_gateway_request: ApiGatewayRequest):
    """Update a virtual staff with multiple update types in a single request.
    
    Expected payload format:
    {
        "STYLES": { ... },
        "INFORMATION": { ... },
        "KNOWLEDGE": { ... },
        "TASK": { ... },
        "SKILL": { ... }
    }
    Any combination of update types can be provided.
    """
    virtual_staff_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id
    if not virtual_staff_id:
        raise BadRequest("Missing required field: id")
    if not company_id:
        raise BadRequest("Missing required field: company_id")

    body = api_gateway_request.body
    if not body:
        raise BadRequest("Missing request body")

    # Get existing virtual staff
    virtual_staff = VirtualStaffModel.by_key({'company_id': company_id, 'id': virtual_staff_id})
    if not virtual_staff:
        raise NotFoundRequest("Virtual staff not found")

    # Get current attributes
    current_attributes = virtual_staff.attributes_dict
    updated_data = {}
    
    # Process each update type if present in the request
    for update_type in UpdateTypeEnum:
        if update_type.value in body:
            update_data = body[update_type.value]
            if update_data:  # Only process if there's data to update
                try:
                    # Handle the update for this type
                    type_updated_data = handle_virtual_staff_update(
                        virtual_staff_id=virtual_staff_id,
                        company_id=company_id,
                        data=update_data,
                        update_type=update_type,
                        current_attributes=current_attributes
                    )
                    # Merge the updated data
                    updated_data = deep_update(updated_data, type_updated_data)
                except Exception as e:
                    raise BadRequest(f"Error processing {update_type.value} update: {str(e)}")

    if not updated_data:
        raise BadRequest("No valid update data provided")

    # Deep update the attributes with all changes
    updated_attributes = deep_update(current_attributes, updated_data)

    # Update virtual staff with merged attributes
    virtual_staff.update(updated_attributes)
    return {
        'success': True,
        'message': 'Virtual staff updated successfully',
        'data': virtual_staff.attributes_dict
    }


@as_api()
def delete_virtual_staff(api_gateway_request: ApiGatewayRequest):
    """Delete a virtual staff"""
    virtual_staff_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id
    if not company_id:
        raise BadRequest("Missing required field: company_id")
    if not virtual_staff_id:
        raise BadRequest("Missing required field: id")

    # Get existing virtual staff
    virtual_staff = VirtualStaffModel.by_key(
        {'company_id': company_id, 'id': virtual_staff_id}
    )
    if not virtual_staff:
        raise NotFoundRequest("Virtual staff not found")

    try:
        # Get knowledge IDs from virtual staff
        staff_dict = virtual_staff.attributes_dict
        knowledge_ids = staff_dict.get('knowledge', [])

        # Delete virtual staff
        key = {
            VirtualStaffModel.key__id: virtual_staff_id,
            'company_id': company_id
        }
        VirtualStaffModel.delete(key=key)

        # Remove staff from knowledge records
        invoke_remove_staff_from_knowledge(company_id, virtual_staff_id, knowledge_ids)

        return {
            'success': True,
            'message': 'Virtual staff deleted successfully'
        }
    except Exception as e:
        raise BadRequest(f"Failed to delete virtual staff: {str(e)}")


@invoke()
def remove_knowledge_from_staff_handler(lambda_request: LambdaRequest):
    """
    Remove knowledge ID from virtual staff's knowledge list.
    """
    event = lambda_request.event
    company_id = event['company_id']
    knowledge_id = event['knowledge_id']
    staff_ids = event['staff_ids']

    if not staff_ids:
        return

    for staff_id in staff_ids:
        staff = VirtualStaffModel.by_key({
            'company_id': company_id,
            VirtualStaffModel.key__id: staff_id
        })
        if staff:
            staff_knowledge = staff.attributes_dict.get('configuration', {}).get('knowledge_base', {}).get(
                'knowledge_ids', []) or []
            if knowledge_id in staff_knowledge:
                staff_knowledge.remove(knowledge_id)
                updated_data = {
                    'configuration': {
                        'knowledge_base': {
                            'knowledge_ids': staff_knowledge
                        }
                    }
                }
                updated_data = deep_update(staff.attributes_dict, updated_data)
                staff.update(updated_data)
