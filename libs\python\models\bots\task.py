from dataclasses import dataclass
from typing import Dict, Any

from marshmallow import fields

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


@dataclass
class TaskAttributes(BasicAttributes):
    name: str = None
    description: str = None
    prompt_content: str = None
    variables: Dict[str, Any] = None


class TaskAttributesSchema(BasicAttributesSchema):
    name = fields.Str(required=True)
    description = fields.Str(required=False, allow_none=True)
    prompt_content = fields.Str(required=False,allow_none=True )
    variables = fields.Dict(keys=fields.Str(), values=fields.Raw(), required=False, allow_none=True)


class TaskModel(BasicModel):
    table_name = 'task'
    attributes: TaskAttributes
    attributes_schema = TaskAttributesSchema
    attributes_class = TaskAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
    }

