import os
import time
import unittest
from datetime import datetime
import openpyxl

from tests.base import BaseTestCase
from helpers.utils import RESOURCE_SERVICE
from integrations.common.event import FetchEvent
from integrations.common.queue_helper import enqueue_fetch_event
from models.integration.fetch_event import FetchEventModel
from integrations.channels.haravan.haravan_library import <PERSON><PERSON><PERSON><PERSON><PERSON>
from integrations.channels.nhanh.nhanh_library import NhanhLibrary
from integrations.common.base_api_library import StandardOrder, StandardProduct
from integrations.common.transformers.sources.nhanh.order import NhanhOrderTransformer


class TestNhanhLibrary(BaseTestCase):

    def setUp(self):
        super().setUp(account_type="baababy_admin")
        self.nhanh_library = NhanhLibrary(
            app_id="74307",
            business_id="58863",
            secret_key="GzqIl8r6iiEcvd35QEgodW3huwvkagwehORW2lDLIBBVLi8OO76K9MH40eLzzLa7pr9I8KTYxN7dNnfD4WqCy5yFInw5GHHbEa7xH6EuPlstlpX9YJ5F6cTH9RV92k3D",
            auth_url="https://nhanh.vn/oauth",
            base_url="https://open.nhanh.vn",
            access_token="pAFAI4RemQPMlCHLAMj75oUOMrMyRQpEEMy4y6wq6Qh2hPLGuAlGwQThViHTIFOlNbcrvuDm2WnUKrhgrAMNfrjJEAhViVEwIH0c3gX8n8IbNl2Wk2BizZlUiQiJglyx33FXmBkU0Fqu"
        )
        self.haravan_library = HaravanLibrary(
            client_id="06c110f8a1ca7206a16c87acbcf4eb47",
            org_id="************",
            client_secret="09d491296e9743a5c9e256bfcc7a999b71a8ac29637f5a1c00619c09d16c8f94",
            access_token="8051C6B518A51A5CC9FC387CC5F85052D6C56E95C8D128A43C80C28B986F663F"
        )
        # You might need to set up the access token here if it's required for most API calls
        # self.nhanh_library.fetch_token("your_access_code")

    def test_restock_baababy(self):
        # # Đọc file Excel
        workbook = openpyxl.load_workbook("Nhanh.vn_Inventory_Check_Product20250513_165906.xlsx")
        sheet = workbook.active

        # Lấy cột D (cột thứ 4)
        column_d = []
        for row in sheet.iter_rows(min_row=2, min_col=4, max_col=4):
            for cell in row:
                if cell.value:
                    column_d.append(cell.value)
        remove_text = [
            "18M-2Y",
            "5Y-6Y",
            "4Y-5Y",
            "6Y-7Y",
            "9Y-10Y",
            "3Y-4Y",
            "7Y-8Y",
            "8Y-9Y",
            "2Y-3Y"
        ]

        processed_list = []
        for item in column_d:
            if isinstance(item, str):  # Kiểm tra nếu item là string
                # Kiểm tra nếu item kết thúc bằng một trong các text trong remove_text
                for text in remove_text:
                    if item.endswith(text):
                        processed_list.append(item[:-len(text) - 1].strip())
                        break
                else:  # Nếu không kết thúc bằng text nào trong remove_text
                    # Tìm vị trí dấu - cuối cùng
                    last_dash = item.rfind('-')
                    if last_dash != -1:
                        processed_list.append(item[:last_dash].strip())
                    else:
                        processed_list.append(item)
            else:
                processed_list.append(item)

        # Lọc bỏ các phần tử trùng nhau
        processed_list = list(dict.fromkeys(processed_list))
        line_items = []
        for item in processed_list:
            standard_product = self.nhanh_library.get_products({"name": item}, None)
            product = standard_product.data[0].raw_data
            for variant_id, variant in product.items():
                if variant['parentId'] in [-1, -2, None, ""]:
                    continue
                if variant.get('inventory', {}):
                    depots = variant.get('inventory', {}).get('depots', {})
                    if isinstance(depots, dict):
                        default_depot = variant.get('inventory', {}).get('depots', {}).get("78673")
                    elif isinstance(depots, list):
                        default_depot = next((d for d in depots if d.get("78673")), None)
                    else:
                        continue
                    if default_depot is None:
                        continue
                    available = default_depot.get('available', 0)
                    line_item = {"sku": variant['code'], "quantity": available if available > 0 else 0}
                    line_items.append(line_item)

        # Chia line_items thành các nhóm, mỗi nhóm tối đa 100 dòng
        chunk_size = 100
        result = []
        for i in range(0, len(line_items), chunk_size):
            chunk = line_items[i:i + chunk_size]
            haravan_inventory = {
                "inventory": {
                    "type": "set",
                    "reason": "productionofgoods",
                    "note": "update from onexapis",
                    "line_items": chunk
                }
            }
            time.sleep(1)
            result.append(self.haravan_library.sync_inventory(haravan_inventory))

        print(result)
        # response = self.nhanh_library.restock_baababy(file)
        # self.assertTrue(response.success)

    def test_get_authorization_url(self):
        connection_id = "test_connection_id"
        auth_url = self.nhanh_library.get_authorization_url(connection_id)
        self.assertIn("auth.nhanh.vn", auth_url)
        self.assertIn(self.nhanh_library.app_id, auth_url)
        self.assertIn("returnLink", auth_url)

    def test_fetch_token(self):
        # Note: This test will actually try to fetch a token from the Nhanh API
        token_data = self.nhanh_library.fetch_token("your_access_code")

        self.assertIn("accessToken", token_data)
        self.assertIsNotNone(self.nhanh_library.access_token)

    def test_test_connection(self):
        # This will actually test the connection to the Nhanh API
        self.assertTrue(self.nhanh_library.test_connection())

    def test_get_orders(self):
        response = self.nhanh_library.get_orders({"page": 1})

        self.assertTrue(response.success)
        self.assertGreater(len(response.data), 0)
        self.assertIsInstance(response.data[0], StandardOrder)

    def test_get_products(self):
        response = self.nhanh_library.get_products({"page": 1})

        self.assertTrue(response.success)
        self.assertGreater(len(response.data), 0)
        self.assertIsInstance(response.data[0], StandardProduct)

    def test_sync_order(self):
        order_data = {
            "id": "TEST-001",
            "customerMobile": "1234567890",
            "totalAmount": 100.00,
            "status": "Pending"
        }

        response = self.nhanh_library.sync_order(order_data)

        self.assertTrue(response.success)
        self.assertIsInstance(response.data, StandardOrder)

    def test_standardize_order(self):
        # You'll need to fetch a real order from the API to test this
        raw_order = self.nhanh_library.get_orders({"page": 1}).data[0].raw_data

        standard_order = self.nhanh_library.standardize_order(raw_order)

        self.assertIsInstance(standard_order, StandardOrder)
        self.assertIsInstance(standard_order.id, str)
        self.assertIsInstance(standard_order.order_number, str)
        self.assertIsInstance(standard_order.total_price, float)
        self.assertIsInstance(standard_order.status, str)
        self.assertIsInstance(standard_order.created_at, datetime)

    def test_standardize_product(self):
        # You'll need to fetch a real product from the API to test this
        raw_product = self.nhanh_library.get_products({"page": 1}).data[0].raw_data

        standard_product = self.nhanh_library.standardize_product(raw_product['id'], raw_product)

        self.assertIsInstance(standard_product, StandardProduct)
        self.assertIsInstance(standard_product.id, str)
        self.assertIsInstance(standard_product.title, str)
        self.assertIsInstance(standard_product.price, float)
        self.assertIsInstance(standard_product.inventory_quantity, int)
        self.assertIn(standard_product.status, ["active", "inactive"])
        self.assertIsInstance(standard_product.created_at, datetime)

    def test_verify_webhook_signature(self):
        # This test assumes the current implementation always returns True
        self.assertTrue(self.nhanh_library.verify_webhook_signature("payload", "signature", "secret"))

    def test_extract_merchant_id(self):
        payload = {"businessId": "test_business_id"}
        self.assertEqual(self.nhanh_library.extract_merchant_id(payload), "test_business_id")

    def test_extract_event_type(self):
        payload = {"eventType": "order.created"}
        headers = {"X-Nhanh-Event": "product.updated"}

        self.assertEqual(self.nhanh_library.extract_event_type(payload, headers), "order.created")
        self.assertEqual(self.nhanh_library.extract_event_type({}, headers), "product.updated")

    def test_sync_product(self):
        with self.assertRaises(NotImplementedError):
            self.nhanh_library.sync_product({})

    def test_transform(self):
        payload = {"id": "230395821", "relatedBillId": "230395820", "relatedDepotId": "", "relatedUserName": "",
                   "orderId": "", "requirementBillId": "", "inventoryCheckId": "", "warrantyBillId": "",
                   "depotId": "78671", "date": "2025-04-02", "createdDateTime": "2025-04-02 21:12:10",
                   "customerId": "145844078", "customerName": "MY", "customerMobile": "0767851409", "saleId": "3144544",
                   "saleName": "Nguyễn Thị Thanh Nhàn", "type": "2", "mode": "4", "saleUserName": "baach_thanhnhan",
                   "createdById": "3022533", "createdByName": "Khấu Thị Kim Huệ", "createdByUserName": "baach_kimhue",
                   "technicalId": "", "technicalName": "", "discount": 0, "points": 0, "usedPoints": 0, "money": 199000,
                   "saleBonus": 0, "moneyTransfer": 0, "cash": 0, "installmentMoney": 0, "creditMoney": 0,
                   "usedPointsMoney": 0, "returnFee": 0, "payment": 0, "products": {
                "*********": {"id": "********", "code": "B-BC-VO03D-04-1TR-FS", "name": "Set 2 Đôi Vớ Trắng - FS",
                              "quantity": "1", "price": "199000", "discount": "0", "vat": None, "VATPercent": None,
                              "extendedWarrantyMoney": None, "money": 199000, "imei": []}}, "description": "",
                   "supplierId": "", "supplierName": "", "couponCode": "", "couponValue": "", "customerMoney": "",
                   "moneyReturn": 0, "cashAccount": "", "transferAccount": "", "creditAccount": "", "creditCode": "",
                   "installmentAccount": "", "installmentCode": "", "tags": []}
        response = NhanhOrderTransformer.transform(payload)

    def test_fetch_lambda_handler(self):
        fetch_events = FetchEventModel.search({"event_time_from": "2025-05-29",
                                               "event_time_to": "2025-06-02",
                                               "limit": 10000,
                                               "page": 0,
                                               "event_source": "webhook",
                                               "sort_event_time": "desc",
                                               "action_type": "get_inventory"
                                               }, service=RESOURCE_SERVICE, company_id=self.company_id
                                              )['items']
        for index, fetch_event in enumerate(fetch_events):
            response = FetchEventModel.by_key({FetchEventModel.key__id: fetch_event['id'],
                                               "continuation_token": fetch_event['continuation_token']}).attributes_dict
            event_body = {key: value for key, value in response.items() if
                          key not in ['created_at', 'updated_at', 'user']}
            fetch = FetchEvent.from_dict(event_body)
            enqueue_fetch_event(fetch, self.company_id)

        print("refetch successfully")


if __name__ == '__main__':
    unittest.main()
