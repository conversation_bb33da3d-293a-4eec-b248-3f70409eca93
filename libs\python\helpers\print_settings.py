from models.basic import BasicAttributes
from models.settings.settings import SettingsModel


def handle_update_default_table_settings(company_id, body_request, user):
    settings_obj = SettingsModel.by_key({
        'setting_name': "print_template",
        'company_id': company_id
    })
    update_value = {
        body_request["template_type"]: {
            body_request["location_id"]: body_request["template"]
        }
    }
    setting_value = update_value if not settings_obj else {**settings_obj.attributes.setting_value, **update_value}
    if settings_obj:
        settings_obj.update({"setting_value": setting_value, "user": user})
        return settings_obj.attributes_dict
    new_setting = BasicAttributes.add_basic_attributes(
        {"setting_value": setting_value, "setting_name": "print_template"}, company_id, user)
    settings_obj = SettingsModel(new_setting)
    settings_obj.save()
    return settings_obj.attributes_dict
