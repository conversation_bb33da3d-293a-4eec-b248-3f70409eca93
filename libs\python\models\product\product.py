from dataclasses import dataclass
from typing import List

from marshmallow import fields, pre_load
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.common import get_default_source
from helpers.text import get_slug_by_name
from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.meta_data import MetaData, MetaDataSchema
from models.common.source_object import SourceObject, SourceObjectSchema
from models.inventory.inventory_item import InventoryItem, InventoryItemSchema
from models.media.image import Image, ImageSchema
from models.price.price import PriceAttributes, PriceAttributesSchema
from models.product.brand import Brand, BrandSchema
from models.product.category import Category, CategorySchema
from models.product.measurement import MeasurementAttributes, MeasurementAttributesSchema
from models.product.option import OptionAttributes, OptionAttributesSchema
from models.product.variant import VariantAttributes, VariantAttributesSchema


@dataclass
class Product(Attributes):
    id: str = None
    name: str = None
    sku: str = None
    publish: bool = False
    barcode: str = None
    description: str = None
    shortDescription: str = None
    measurements: MeasurementAttributes = None
    tags: str = None
    brand: Brand = None
    source: SourceObject = None
    category: Category = None
    images: List[Image] = None
    options: List[OptionAttributes] = None
    variants: List[VariantAttributes] = None
    prices: List[PriceAttributes] = None
    inventories: List[InventoryItem] = None
    meta_data: MetaData = None
    slug: str = None


class ProductSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    sku = fields.Str(required=True)
    barcode = fields.Str(allow_none=True)
    publish = fields.Bool(allow_none=True)
    description = fields.Str(allow_none=True)
    shortDescription = fields.Str(allow_none=True)
    tags = fields.Str(allow_none=True)
    source = fields.Nested(SourceObjectSchema(SourceObject), allow_none=True)
    brand = fields.Nested(BrandSchema(Brand), allow_none=True)
    category = fields.Nested(CategorySchema(Category), allow_none=True)
    measurements = fields.Nested(MeasurementAttributesSchema(MeasurementAttributes), allow_none=True)
    images = fields.List(fields.Nested(ImageSchema(Image), allow_none=True))
    options = fields.List(fields.Nested(OptionAttributesSchema(OptionAttributes)), allow_none=True)
    variants = fields.List(fields.Nested(VariantAttributesSchema(VariantAttributes)), allow_none=True)
    prices = fields.List(fields.Nested(PriceAttributesSchema(PriceAttributes)), allow_none=True)
    inventories = fields.List(fields.Nested(InventoryItemSchema(InventoryItem)), allow_none=True)
    meta_data = fields.Nested(MetaDataSchema(MetaData), allow_none=True)
    slug = fields.Str(allow_none=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['slug'] = data['slug'] if 'slug' in data else get_slug_by_name(data['name'], data['sku'])
        data['source'] = data['source'] if data.get('source') is not None else get_default_source()
        return data


@dataclass
class ProductAttributes(Product, BasicAttributes):
    sync_record_id: str = None


class ProductAttributesSchema(ProductSchema, BasicAttributesSchema):
    sync_record_id = fields.Str(allow_none=True)


class ProductModel(BasicModel):
    key__id = 'id'
    table_name = 'product'
    attributes: ProductAttributes
    attributes_class = ProductAttributes
    attributes_schema = ProductAttributesSchema
    query_mapping = {
        'query': ['id', 'name', 'sku', 'barcode', 'variants.sku', 'variants.barcode', 'slug', 'source_channel',
                  'source_raw_data_id', 'destination_channels'],
        'filter': {
            'id': 'query',
            'brand.id': 'query',
            'brand.name': 'query',
            'category.id': 'query',
            'category.name': 'query',
            'name': 'query',
            'sku': 'query',
            'slug': 'query',
            'publish': 'boolean',
            'company_id': 'query',
            'tags': 'tags',
            'updated_at': 'range',
            'created_at': 'range',
            'source_channel': 'query',
            'source_raw_data_id': 'query',
            'destination_channels': 'query',
            'destination_transformed_data_ids': 'query',
            'sync_status': 'query',
            'source.id': 'query',
            'source.channel_name': 'query',
        },
        'mapping': {
            'brand': 'brand',
            'category': 'category',
            'images': 'images',
            'prices': 'prices',
            'variants': 'variants',
            'options': 'options',
            'source_channel': 'source_channel',
            'source_raw_data_id': 'source_raw_data_id',
            'destination_channels': 'destination_channels',
            'destination_transformed_data_ids': 'destination_transformed_data_ids',
            'sync_responses': 'sync_responses',
            'sync_status': 'sync_status',
            'source': 'source',
        }
    }

    @classmethod
    def product_count(cls, params):
        try:
            search_params = {
                "aggs": {
                    "product_count": {
                        "filter": {
                            "term": params
                        }
                    }
                }
            }
            response = cls.report(search_params, service=RESOURCE_SERVICE)
            return response.body['aggregations']['product_count']['doc_count']
        except IndexError:
            return 0


class ProductSkuIndex(ProductModel):
    key__id = 'sku'
    index_name = 'productSkuIndex'

    @classmethod
    def get_product_by_sku(cls, sku, company_id):
        try:
            return cls(ProductSkuIndex.list({'sku': sku, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return


class ProductSlugIndex(ProductModel):
    key__id = 'slug'
    index_name = 'productSlugIndex'

    @classmethod
    def get_product_by_slug(cls, slug, company_id):
        try:
            return cls(ProductSlugIndex.list({'slug': slug, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return
