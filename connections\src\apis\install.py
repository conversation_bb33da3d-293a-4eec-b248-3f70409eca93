import os
from copy import copy

from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest, ApiGatewayResponse
from nolicore.utils.exceptions import BadRequest, NotFoundRequest
from nolicore.utils.utils import logger

from helpers.auth import invoke_init_account
from helpers.install import get_subscription_api, install_api, list_plans_api, subscribe_api
from integrations.channels.connection_registry import connection_registry
from integrations.channels.shopify.shopify_connection import ShopifySettings
from integrations.channels.shopify_oauth.shopify_oauth_channel import ShopifyOAuthChannel
from integrations.channels.shopify_oauth.shopify_oauth_library import ShopifyOAuthLibrary
from integrations.channels.tiktokshop.tiktokshop_channel import TiktokShopChannel
from integrations.common.connection_helper import _create_connection, get_connection_by_id

SHOPIFY_CLIENT_ID = os.environ['SHOPIFY_CLIENT_ID']
SHOPIFY_CLIENT_SECRET = os.environ['SHOPIFY_CLIENT_SECRET']
APP_URL = os.environ['APP_URL']
TIKTOK_APP_KEY = os.environ['TIKTOK_APP_KEY']
TIKTOK_APP_SECRET = os.environ['TIKTOK_APP_SECRET']
TIKTOK_SERVICE_ID = os.environ['TIKTOK_SERVICE_ID']


@as_api()
def install(api_gateway_request: ApiGatewayRequest):
    try:
        # Check and extract channel info
        body = api_gateway_request.body
        destination_channel = body.get('destination_channel', None)
        redirect_url = body.get('redirect_url', None)
        channel_name = body.get('source_channel', None)
        base_url = f"https://{api_gateway_request.headers.get('host')}"
        if not channel_name:
            raise BadRequest("Missing source_channel")

        connection_class = connection_registry.get(channel_name)
        if not connection_class:
            raise NotFoundRequest("Connection type not found")

        if channel_name != ShopifyOAuthChannel.channel_name:
            raise BadRequest(f"{channel_name} is currently not supported")

        # create destination connection
        if destination_channel and destination_channel != channel_name:
            if destination_channel != TiktokShopChannel.channel_name:
                raise BadRequest(f"Destination channel {destination_channel} is currently not supported")

        # Check shop info
        shop_domain = body.get('shop')
        if not shop_domain:
            raise BadRequest("Missing shop parameter")
        shop_url = f"https://{shop_domain}"

        # Verify HMAC signature
        temp_query_params = copy(body)
        if not ShopifyOAuthLibrary.verify_hmac(temp_query_params, SHOPIFY_CLIENT_SECRET):
            raise BadRequest("Invalid HMAC signature")

        # Create company/user/subscription
        install_payload = {
            **body,
            "service_code": "OPTIWAREHOUSE",
            "plan_code": "SHOPIFY-APP"
        }
        response = install_api(install_payload)

        # If exists, return response
        if response.get('exists'):
            return response
        try:
            token_data = response['token']
            user_attributes = response['user_attributes']
            access_token = token_data['AccessToken']
            token = token_data['IdToken']
            username = user_attributes['Username']
            company_id = response['company_id']
            invoke_init_account(access_token, token, username)
        except Exception as e:
            raise BadRequest(f"Invalid response from CRM: {str(e)}")

        # Create Shopify connection
        shopify_credentials = {
            'api_key': SHOPIFY_CLIENT_ID,
            'secret_key': SHOPIFY_CLIENT_SECRET,
            'shop_url': shop_url,
        }
        if not redirect_url:
            redirect_url = f'{APP_URL}/choose-plan'
        shopify_connection, shopify_auth_url = _create_connection(
            channel_name, company_id, shopify_credentials, base_url, redirect_url
        )

        # return result
        result = {
            **response,
            "authorization_url": shopify_auth_url,
            "source_id": str(shopify_connection.attributes.id),
        }
        # create destination connection
        if destination_channel:
            destination_credentials = {
                'app_key': TIKTOK_APP_KEY,
                'app_secret': TIKTOK_APP_SECRET,
                'service_id': TIKTOK_SERVICE_ID,
                'region': 'non-us'
            }
            destination_connection, destination_auth_url = _create_connection(destination_channel, company_id,
                                                                              destination_credentials, base_url)
            result['destination_id'] = str(destination_connection.attributes.id)
        return result
    except Exception as e:
        logger.exception(f"Error in installation process: {str(e)}")
        raise BadRequest(str(e))


@as_api()
def list_plans(api_gateway_request: ApiGatewayRequest):
    try:
        query_params = api_gateway_request.query_string_parameters or {}
        company_id = api_gateway_request.company_id
        user_attributes_dict = api_gateway_request.user_attributes_dict
        subscription_id = user_attributes_dict.get('custom:plan')
        token_id = api_gateway_request.headers.get('authorization').split(' ')[1]
        access_token = query_params.get('access_token')
        connection_id = query_params.get('connection_id')
        if not connection_id:
            raise BadRequest('Missing connection_id')
        connection_obj = get_connection_by_id(connection_id, company_id)
        if not connection_obj:
            raise BadRequest('Connection not found')
        channel_name = connection_obj.attributes.channel_name
        if channel_name != ShopifyOAuthChannel.channel_name:
            raise BadRequest(f'This {channel_name} is currently not supported for charging')

        settings = connection_obj.attributes.settings
        api_key = settings.api_key
        secret_key = settings.secret_key

        if api_key != SHOPIFY_CLIENT_ID or secret_key != SHOPIFY_CLIENT_SECRET:
            raise BadRequest(f"Connection ID {connection_id} is not Optiwarehouse shopify oauth connection")
        if subscription_id:
            subscription = get_subscription_api(subscription_id, token_id, access_token)
            if not subscription:
                raise BadRequest('Subscription not found')
            if subscription.get('plan', {}).get('code') != "SHOPIFY-APP":
                return ApiGatewayResponse(
                    http_code=302,
                    headers={
                        "Location": APP_URL
                    },
                    body=""
                )
        response = list_plans_api("OPTIWAREHOUSE", token_id, access_token)
        return response
    except Exception as e:
        raise BadRequest(f'Error listing plans: {str(e)}')


@as_api()
def subscribe_plan(api_gateway_request: ApiGatewayRequest):
    try:
        payload = api_gateway_request.body
        company_id = api_gateway_request.company_id
        headers = api_gateway_request.headers
        token_id = headers.get('authorization').split(' ')[1]
        access_token = api_gateway_request.query_string_parameters.get('access_token')
        connection_id = payload.pop('connection_id')
        if not connection_id:
            raise BadRequest('Missing connection_id')

        plan_id = payload.get('plan_id')
        duration = payload.get('duration', 'MONTHLY')
        if not all([plan_id]):
            raise BadRequest('Required plan_id')

        connection_obj = get_connection_by_id(connection_id, company_id)
        if connection_obj is None:
            raise BadRequest('Connection not found')
        channel_name = connection_obj.attributes.channel_name
        if channel_name != ShopifyOAuthChannel.channel_name:
            raise BadRequest(f'This {channel_name} is currently not supported for charging')

        settings: ShopifySettings = connection_obj.attributes.settings
        shop_url = settings.shop_url
        api_key = settings.api_key
        secret_key = settings.secret_key
        connection_access_token = connection_obj.attributes.access_token
        shopify_lib = ShopifyOAuthLibrary(
            api_key=api_key,
            secret_key=secret_key,
            shop_url=shop_url,
            access_token=connection_access_token
        )

        # Determine the type and duration of the recurring charge
        data_key = 'recurring_application_charge'
        if duration == 'YEARLY':
            price = float(payload.get('sale_price', 0)) * 12
        else:  # MONTHLY
            price = float(payload.get('price', 0))
        terms = payload.get('description') or 'Terms and Conditions'

        # Create Shopify recurring charge
        charge_data = {
            data_key: {
                'name': f"{payload.get('name', '')}",
                'price': price,
                'return_url': f'{APP_URL}/synchronization',
                'trial_days': payload.get('trial_days', 0),
                'capped_amount': price,
                'terms': terms,
            }
        }

        # Set test to True for dev environment
        if os.environ['ENV'] == 'dev':
            charge_data[data_key]['test'] = True
        # Call API with the correct syntax
        response = shopify_lib._make_request(
            endpoint=shopify_lib.endpoints.RECURRING_CHARGES,
            data=charge_data
        )

        if not response or data_key not in response:
            raise BadRequest("Could not create Shopify charge - invalid response")

        shopify_charge = response.get(data_key, {})
        shopify_charge_id = shopify_charge.get('id')
        confirmation_url = shopify_charge.get('confirmation_url')

        if not shopify_charge_id or not confirmation_url:
            raise BadRequest("Invalid response from Shopify")

        subscription = {
            **payload,
            'external_id': str(shopify_charge_id),
            'charge_info': shopify_charge,
        }

        response = subscribe_api(subscription, token_id, access_token)
        subscription_id = response.get('data', {}).get('id')
        if not subscription_id:
            raise BadRequest("Invalid response from Shopify")

        return {
            'message': 'Shopify charge created successfully',
            'data': {
                'shopify_charge_id': shopify_charge_id,
                'confirmation_url': confirmation_url,  # URL for customer to confirm payment
                'subscription_id': subscription_id
            }
        }
    except Exception as e:
        raise BadRequest(f'Error subscribing plan: {str(e)}')


@as_api()
def subscription_callback(api_gateway_request: ApiGatewayRequest):
    try:
        query_params = api_gateway_request.query_string_parameters or {}
        external_id = query_params.get('external_id')
        connection_id = query_params.get('connection_id')
        if not all([external_id, connection_id]):
            raise BadRequest('Missing external_id or connection_id')
        company_id = api_gateway_request.company_id
        connection_obj = get_connection_by_id(connection_id, company_id)
        channel_name = connection_obj.attributes.channel_name
        if channel_name != ShopifyOAuthChannel.channel_name:
            raise BadRequest(f'This {channel_name} is currently not supported')
        settings: ShopifySettings = connection_obj.attributes.settings
        shop_url = settings.shop_url
        api_key = settings.api_key
        secret_key = settings.secret_key
        access_token = connection_obj.attributes.access_token

        shopify_lib = ShopifyOAuthLibrary(
            api_key=api_key,
            secret_key=secret_key,
            shop_url=shop_url,
            access_token=access_token
        )

        # Call API with the correct syntax
        response = shopify_lib._make_request(
            endpoint=shopify_lib.endpoints.GET_RECURRING_CHARGE,
            path_params={'charge_id': external_id}
        )
        if not response or 'recurring_application_charge' not in response:
            raise BadRequest("Could not create Shopify charge - invalid response")
        if response.get('recurring_application_charge').get('status') == 'active':
            return ApiGatewayResponse(
                http_code=302,
                headers={
                    "Location": f"{APP_URL}/channels/connections/{connection_id}"
                },
                body=""
            )
        else:
            raise BadRequest(f"Shopify charge {external_id} is not active")

    except Exception as e:
        raise BadRequest(f'Error when checking subscription: {str(e)}')
