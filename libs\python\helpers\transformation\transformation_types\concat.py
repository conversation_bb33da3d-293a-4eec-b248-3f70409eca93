from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class ConcatTransformationHandler(BaseTransformationHandler):
    """Handler for concat transformation"""
    
    config_definition = TransformationConfig(
        label="Concatenate",
        description="Join text with a separator",
        direction=TransformationDirection(
            title="Concatenate Text",
            content="Join text with a specified separator",
            example='"Hello" + "World" → "Hello World"',
            value_type="Single Value"
        ),
        config_fields=[
            {
                "key": "separator",
                "label": "Separator",
                "type": "text",
                "placeholder": "Enter separator (e.g., space, comma)",
                "description": "The text to use as a separator between values"
            }
        ]
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Joins the value with the separator if it's not None"""
        if value is None:
            return None
            
        if not self.transformation.config or 'separator' not in self.transformation.config:
            return value
            
        separator = self.transformation.config['separator']
        return str(value) + separator 