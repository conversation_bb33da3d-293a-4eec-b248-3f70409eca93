get_mapping_list:
  handler: src.apis.mapping.get_mapping_list
  events:
    - httpApi:
        path: /mappings/{record_type}
        method: get
        authorizer:
          name: optiAuthorizer

get_mapping:
  handler: src.apis.mapping.get_mapping
  events:
    - httpApi:
        path: /mappings/{record_type}/details
        method: post
        authorizer:
          name: optiAuthorizer

unmap:
  handler: src.apis.mapping.unmap
  events:
    - httpApi:
        path: /mappings/unmap
        method: put
        authorizer:
          name: optiAuthorizer
map_sync_records:
  handler: src.apis.mapping.map_sync_records
  events:
    - httpApi:
        path: /mappings/map
        method: put
        authorizer:
          name: optiAuthorizer
get_transformation_list:
  handler: src.apis.mapping.get_transformation_list
  events:
    - httpApi:
        path: /mappings/transformations
        method: get
        authorizer:
          name: optiAuthorizer
handle_transform:
  handler: src.apis.mapping.handle_transform
  events:
    - httpApi:
        path: /mappings/transform
        method: put
        authorizer:
          name: opti<PERSON>uth<PERSON>zer

