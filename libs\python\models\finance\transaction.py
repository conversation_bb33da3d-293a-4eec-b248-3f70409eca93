from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel, User, UserSchema
from models.finance.account import Account, AccountSchema
from models.finance.payment_method import PaymentMethod, PaymentMethodSchema
from models.finance.voucher import TransactionVoucherSchema, TransactionVoucher


class Status(Enum):
    COMPLETED = 'COMPLETED',
    CANCELLED = 'CANCELLED'


@dataclass
class Transaction(Attributes):
    id: str = None
    payment_method: PaymentMethod = None
    voucher: TransactionVoucher = None
    effective_date: str = None
    status: Status = Status.COMPLETED
    amount: Decimal = 0
    note: str = None
    reference: str = None
    from_account: Account = None
    to_account: Account = None


class TransactionSchema(AttributesSchema):
    id = fields.UUID(required=True)
    status = EnumField(Status, allow_none=True, default=Status.COMPLETED)
    amount = fields.Decimal(required=True, default=0)
    payment_method = fields.Nested(PaymentMethodSchema(PaymentMethod), required=True)
    effective_date = fields.DateTime(required=True)
    voucher = fields.Nested(TransactionVoucherSchema(TransactionVoucher), required=True)
    note = fields.Str(allow_none=True)
    reference = fields.Str(allow_none=True)
    from_account = fields.Nested(AccountSchema(Account), allow_none=True)
    to_account = fields.Nested(AccountSchema(Account), allow_none=True)


@dataclass
class TransactionAttributes(Transaction, BasicAttributes):
    pass


class TransactionAttributesSchema(TransactionSchema, BasicAttributesSchema):
    pass


class TransactionModel(BasicModel):
    table_name = 'transaction'
    attributes: TransactionAttributes
    attributes_schema = TransactionAttributesSchema
    attributes_class = TransactionAttributes

    query_mapping = {
        'query': ['id', 'name', 'user.name_staff', 'reference'],
        'filter': {
            'id': 'query',
            'amount': 'query',
            'user.name_staff': 'query',
            'payment_method.name': 'query',
            'status': 'query',
            'voucher.id': 'query',
            'company_id': 'query',
            'effective_date': 'range',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'from_account': 'from_account',
            'to_account': 'to_account',
            'payment_method': 'payment_method',
            'voucher': 'voucher',
            'user': 'user',
        }
    }
