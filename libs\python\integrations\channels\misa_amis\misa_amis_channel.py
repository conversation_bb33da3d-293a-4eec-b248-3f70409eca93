from typing import Dict, Any, Tu<PERSON>, List, Optional

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import AbstractChannel
from integrations.channels.connection_types import ChannelType
from integrations.channels.misa_amis.misa_amis_connection import MisaAmisSettings
from integrations.channels.misa_amis.misa_amis_library import MisaAmisLibrary
from integrations.common.base_api_library import FetchAPIResponse, StandardInventory
from integrations.common.event import FetchEvent


class MisaAmisChannel(AbstractChannel):
    channel_name = 'misa_amis'
    library_class = MisaAmisLibrary

    def _create_library(self, connection):
        settings: MisaAmisSettings = connection.attributes.settings
        return MisaAmisLibrary(
            app_id=settings.app_id,
            access_code=settings.access_code,
            org_company_code=settings.org_company_code,
            base_url=settings.base_url,
        )

    def get_token(self, query_params: Dict[str, Any]) -> <PERSON><PERSON>[str, str, str, str, Dict[str, Any]]:
        pass

    def test_connection(self) -> bool:
        try:
            return self.library.test_connection()
        except Exception:
            return False

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            'name': 'MisaAmis',
            'channel_type': ChannelType.OMS,
            'description': 'Connect your MisaAmis instance to sync products, orders, and customers.',
            'logo': cls._load_image_as_base64(__file__, 'misa.png')
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_order:
            return {
                'page': 1,
                'updatedDateTimeFrom': None,
                'updatedDateTimeTo': None
            }
        elif action == ActionType.get_product:
            return {
                'page': 1,
                'icpp': 50,
                'updatedDateTimeFrom': None,
                'updatedDateTimeTo': None,

            }
        elif action == ActionType.get_inventory:
            return {
                'page': 1,
                'icpp': 50,
                'updatedDateTimeFrom': None,
                'updatedDateTimeTo': None
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_order:
            return {}
        elif action == ActionType.sync_product:
            return {}
        elif action == ActionType.sync_return_order:
            return {}
        else:
            return {}

    def get_order__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        pass

    def get_return_order__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        pass

    def get_product__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        pass

    def sync_order__publish(self, settings: Dict[str, Any], order_data: Dict[str, Any]):
        return self.library.sync_order(order_data)

    def sync_return_order__publish(self, settings: Dict[str, Any], order_data: Dict[str, Any]):
        return self.library.sync_order(order_data)

    def sync_product__publish(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        pass

    def get_inventory__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent) -> FetchAPIResponse[
        StandardInventory]:
        pass

    @classmethod
    def verify_webhook_signature(cls, payload: str, signature: str, secret: str) -> bool:
        return True

    @classmethod
    def extract_merchant_id(cls, payload):
        return MisaAmisLibrary.extract_merchant_id(payload)

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return MisaAmisLibrary.extract_event_type(payload, headers)

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        pass

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        pass

    def get_dynamic_settings(self) -> Dict[str, Any]:
        # First, check if we already have dynamic settings stored
        stored_settings = self.connection.get_dynamic_settings()
        if 'branch_id' not in stored_settings:
            stored_settings['branch_id'] = None
        if 'stock_mapping' not in stored_settings:
            stored_settings['stock_mapping'] = {}
        if 'account_mapping' not in stored_settings:
            stored_settings['account_mapping'] = {}
        if 'account_object_mapping' not in stored_settings:
            stored_settings['account_object_mapping'] = {}
        return stored_settings

    @classmethod
    def webhook_response_message(cls, success: bool, message: str = '', error_code: str = None) -> Dict[
        str, Any]:
        res = {
            'Success': True,
            'ErrorMessage': ''
        }

        if error_code:
            res['Success'] = False
            res['ErrorCode'] = 'Exception'
            res['ErrorMessage'] = 'Lỗi chưa xác định được nguyên nhân'
        return res
