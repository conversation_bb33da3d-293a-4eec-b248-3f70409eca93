import unicodedata
import re


def get_slug_by_name(text, other_text=None):
    if other_text is not None:
        text += f'-{other_text}'
    # <PERSON><PERSON><PERSON> hóa văn bản thành dạng chữ thường và loại bỏ dấu
    text = unicodedata.normalize('NFD', text)
    text = ''.join([c for c in text if not unicodedata.combining(c)])
    # Thay thế ký tự "đ" thành "d"
    text = text.replace('đ', 'd').replace('Đ', 'D')

    # <PERSON>yển tất cả ký tự thành chữ thường
    text = text.lower()
    # Thay thế các ký tự không phải là chữ cái hoặc số bằng dấu gạch ngang
    text = re.sub(r'[^a-z0-9]+', '-', text)
    # Loại bỏ các dấu gạch ngang dư thừa
    text = re.sub(r'-+', '-', text).strip('-')
    return text
