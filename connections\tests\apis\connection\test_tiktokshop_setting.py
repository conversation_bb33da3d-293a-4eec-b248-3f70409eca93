import json
import unittest
import uuid

from integrations.common.transformers import registry
from tests.base import BaseTestCase
from connections.src.apis.setting import set_fetch_action_settings, set_publish_action_settings, \
    update_webhook_settings
from integrations.channels.action_types import ActionGroup, ActionType
from models.integration.connection import ConnectionModel


class SettingApisIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()

    def setUp(self):
        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"
        self.connection_id = 'feebf543-a080-4469-b301-6e9025c43983'

    def tearDown(self):
        # Clean up the test connection
        connection = ConnectionModel.get(self.company_id, self.connection_id)
        if connection:
            connection.delete()

    def create_lambda_event(self, body, path_parameters=None):
        event = {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }
        if path_parameters:
            event['pathParameters'] = path_parameters
        return event

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_set_fetch_action_settings_success(self):
        # Arrange
        event_body = {
            "actions": {
                "get_order": {
                    "enabled": True,
                    "rate_limit": 60,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "custom_settings": {
                        "limit": 50,
                        "status": "active"
                    },
                    "schedule": {
                        "type": "interval",
                        "value": "3600"
                    },
                    "status": {
                        "last_run": None,
                        "next_run": None
                    }
                }
            }
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = set_fetch_action_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Fetch action settings updated successfully')

        # Verify the settings were actually updated in the database
        updated_connection = ConnectionModel.get(self.company_id, self.connection_id)
        fetch_settings = updated_connection.get_fetch_action_settings(ActionGroup.product, ActionType.get_product)
        self.assertTrue(fetch_settings.enabled)
        self.assertEqual(fetch_settings.rate_limit, 60)
        self.assertEqual(fetch_settings.custom_settings['limit'], 50)

    def test_set_publish_action_settings_success(self):
        # Arrange
        event_body = {
            "actions": {
                "sync_order": {
                    "enabled": True,
                    "payload_template": {
                        "title": "{{product.title}}",
                        "description": "{{product.description}}",
                        "price": "{{product.price}}"
                    },
                    "rate_limit": 30,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "custom_settings": {
                        "update_existing": True,
                        "create_new": False
                    }
                }
            }
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = set_publish_action_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Publish action settings updated successfully')

        # Verify the settings were actually updated in the database
        updated_connection = ConnectionModel.get(self.company_id, self.connection_id)
        publish_settings = updated_connection.get_publish_action_settings(ActionGroup.product, ActionType.sync_product)
        self.assertTrue(publish_settings.enabled)
        self.assertEqual(publish_settings.rate_limit, 30)
        self.assertEqual(publish_settings.custom_settings['update_existing'], True)

    def test_update_webhook_settings_success(self):
        # Arrange
        event_body = {
            "webhook_settings": {
                "orders/create": True,
                "products/create": False,
                "inventory_levels/update": False,
                "orders/updated": False,
                "products/update": False
            }
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = update_webhook_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Webhook settings updated successfully')

        # Verify the settings were actually updated in the database
        updated_connection = ConnectionModel.get(self.company_id, self.connection_id)
        webhook_settings = updated_connection.get_webhook_settings()
        self.assertEqual(webhook_settings, event_body['webhook_settings'])

    def test_set_fetch_action_settings_invalid_action(self):
        # Arrange
        event_body = {
            "actions": {
                "invalid_action": {
                    "enabled": True
                }
            }
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = set_fetch_action_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertIn('Invalid action type: invalid_action', response_body['message'])

    def test_update_webhook_settings_invalid_format(self):
        # Arrange
        event_body = {
            "webhook_settings": "invalid_format"
        }
        event = self.create_lambda_event(event_body, {'connection_id': self.connection_id})
        context = self.create_lambda_context()

        # Act
        response = update_webhook_settings(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertIn('Invalid webhook settings format', response_body['message'])

    @staticmethod
    def test_transformer():
        input_data = {'barcode': None, 'brand': None, 'category': {'name':'Uncategorized'}, 'description': '', 'images': [{'name': 'Top and bottom view of a snowboard. The top view shows a centred hexagonal logo for Hydrogen that appears to radiate outwards, as well as some overlapping hexagons at the bottom. The bottom view shows an abstract angular grid in purples.', 'src': 'https://cdn.shopify.com/s/files/1/0743/0678/1417/files/snowboard_purple_hydrogen.png?v=1745466340'}], 'measurements': {'height_unit': 'cm', 'height_value': 0.0, 'length_unit': 'cm', 'length_value': 0.0, 'weight_unit': 'g', 'weight_value': 0.0, 'width_unit': 'cm', 'width_value': 0.0}, 'name': 'The Inventory Not Tracked Snowboard', 'options': [{'name': 'Title', 'values': ['Default Title']}], 'publish': True, 'shortDescription': '', 'sku': 'sku-untracked-1', 'source': {'channel_name': 'shopify_oauth', 'id': '16c17780-12ed-4b6a-aa8d-f2a4aa6b8471'}, 'tags': 'Accessory, Sport, Winter', 'variants': [{'barcode': None, 'images': [], 'inventories': [], 'measurements': None, 'name': 'The Inventory Not Tracked Snowboard - Default Title', 'option1': 'Default Title', 'option2': None, 'option3': None, 'optionTitle1': 'Title', 'optionTitle2': None, 'optionTitle3': None, 'prices': [], 'sku': 'sku-untracked-1'}]}
        connection_data = {
            "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d",
            "created_at": "2025-03-19T06:01:47.291608+00:00",
            "updated_at": "2025-03-19T06:01:47.291608+00:00",
            "name": "Tiktok Shop",
            "id": "feebf543-a080-4469-b301-6e9025c43983",
            "status": "ACTIVE",
            "url": None,
            "image": "data:image/webp;base64,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"
        }
        source_transformer_class = registry.get_destination_transformer("tiktok_shop", "product", connection_data)
        result = source_transformer_class.transform(data=input_data)


if __name__ == '__main__':
    unittest.main()
