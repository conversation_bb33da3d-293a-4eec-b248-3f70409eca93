from dataclasses import dataclass

from marshmallow import fields, EXCLUDE
from nolicore.adapters.db.model import AttributesSchema, Attributes

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


@dataclass
class Image(Attributes):
    url: str = None
    id: str = None
    name: str = None


class ImageSchema(AttributesSchema):
    class Meta:
        unknown = EXCLUDE

    id = fields.Str(allow_none=True)
    url = fields.Url(required=True)
    name = fields.Str(allow_none=True)


@dataclass
class ImageAttributes(Image, BasicAttributes):
    pass


class ImageAttributesSchema(BasicAttributesSchema, ImageSchema):
    id = fields.Str(allow_none=True)


class ImageModel(BasicModel):
    key__id = 'id'
    table_name = 'image'
    attributes: ImageAttributes
    attributes_schema = ImageAttributesSchema
    attributes_class = ImageAttributes
