graph TD
    A[Start: CloudWatch Event Trigger] --> B[schedule_handler]
    B --> C[Scan Connections]
    C --> D{For Each Connection}
    D --> E[process_connection]
    E --> F[get_due_actions]
    F --> G{For Each Due Action}
    G --> H[Create FetchEvent]
    H --> I[enqueue_fetch_event]
    I --> J[update_action_schedule]
    J --> G
    G --> |No More Actions| K[Save Connection if Updated]
    K --> D
    D --> |No More Connections| L[End: Return Status]

    subgraph "get_due_actions Function"
        M[Start] --> N{For Each ActionType}
        N --> O{should_fetch?}
        O -->|Yes| P[Add to due_actions]
        O -->|No| N
        P --> N
        N --> |No More ActionTypes| Q[Return due_actions]
    end

    subgraph "enqueue_fetch_event Function"
        R[Start] --> S[Convert FetchEvent to JSON]
        S --> T[Send Message to SQS]
        T --> U[End]
    end

    F -.->|Expand| M
    I -.->|Expand| R
