from functools import lru_cache
import hashlib
import hmac
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

import pendulum
import requests
from nolicore.utils.exceptions import UnauthorizedExp, UnknownExp, BadRequest
from nolicore.utils.utils import logger

from helpers.common import safe_compress
from helpers.custom_jsonpath import get_value_from_jsonpath
from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, SyncAPIResponse,
    MetaData, StandardOrder, StandardProduct, StandardOrderLineItem, StandardReturnOrder, StandardPurchaseOrder,
    PublishMetaData, StandardVariant
)
from integrations.common.event import FetchEvent, EventSource


class TikTokShopEndpoints(APIEndpoints):
    def __init__(self, base_url: str = "https://open-api.tiktokglobalshop.com", version: str = "202309"):
        super().__init__(base_url)
        self.version = version
        self.ACCESS_TOKEN = EndpointType(type="rest", method="GET", path=f"token")
        self.AUTH_SHOP = EndpointType(type="rest", method="GET", path=f"/authorization/{self.version}/shops")
        self.ORDER_DETAIL = EndpointType(type="rest", method="GET", path=f"/order/{self.version}/orders")
        self.ORDERS = EndpointType(type="rest", method="POST", path=f"/order/{self.version}/orders/search")
        self.CATEGORIES = EndpointType(type="rest", method="GET", path=f"/product/{self.version}/categories")
        self.BRANDS = EndpointType(type="rest", method="GET", path=f"/product/{self.version}/brands")
        self.CREATE_BRAND = EndpointType(type="rest", method="POST", path=f"/product/{self.version}/brands")
        self.PRODUCTS = EndpointType(type="rest", method="POST", path=f"/product/{self.version}/products/search")
        self.PRODUCT_DETAIL = EndpointType(type="rest", method="GET",
                                           path=f"/product/{self.version}/products/{{product_id}}")
        self.CREATE_PRODUCT = EndpointType(type="rest", method="POST", path=f"/product/{self.version}/products")
        self.UPDATE_PRODUCT = EndpointType(type="rest", method="PUT",
                                           path=f"/product/{self.version}/products/{{product_id}}")
        self.GET_CATEGORY_RULES = EndpointType(type="rest", method="GET",
                                               path=f"/product/{self.version}/categories/{{category_id}}/rules")
        self.GET_GLOBAL_CATEGORY_RULES = EndpointType(type="rest", method="GET",
                                                      path=f"/product/{self.version}/categories/{{category_id}}/global_rules")
        self.GET_CATEGORY_ATTRIBUTES = EndpointType(type="rest", method="GET",
                                                    path=f"/product/{self.version}/categories/{{category_id}}/attributes")
        self.GET_GLOBAL_CATEGORY_ATTRIBUTES = EndpointType(type="rest", method="GET",
                                                           path=f"/product/{self.version}/categories/{{category_id}}/global_attributes")
        self.GET_RECOMMENDED_CATEGORY = EndpointType(type="rest", method="POST",
                                                     path=f"/product/{self.version}/categories/recommend")
        self.GET_RECOMMENDED_GLOBAL_CATEGORY = EndpointType(type="rest", method="POST",
                                                            path=f"/product/{self.version}/global_categories/recommend")
        self.UPLOAD_IMAGE = EndpointType(type="rest", method="POST", path=f"/product/{self.version}/images/upload")
        self.OPTIMIZE_IMAGE = EndpointType(type="rest", method="POST", path=f"/product/202404/images/optimize")
        self.UPDATE_WEBHOOK = EndpointType(type="rest", method="PUT", path=f"/event/{self.version}/webhooks")
        self.REFRESH_ACCESS_TOKEN = EndpointType(type="rest", method="POST", path=f"refresh_access_token")
        self.GET_WAREHOUSES = EndpointType(type="rest", method="GET", path=f"/logistics/{self.version}/warehouses")


class TiktokShopLibrary(BaseAPILibrary):
    AVAILABLE_WEBHOOKS = {"ORDER_STATUS_CHANGE": "tiktokshop/order/updated",
                          "PRODUCT_CREATION": "tiktokshop/product/created",
                          "PRODUCT_INFORMATION_CHANGE": "tiktokshop/product/updated"}

    def __init__(self, app_key: str, app_secret: str, service_id: str, region: str,
                 base_url: str = "https://open-api.tiktokglobalshop.com", shop_id: str = None,
                 shop_cipher: Optional[str] = None, access_token: Optional[str] = None):
        super().__init__(base_url)
        self.access_token = access_token
        self.app_key = app_key
        self.app_secret = app_secret
        self.service_id = service_id
        self.region = region
        self.shop_id = shop_id
        self.shop_cipher = shop_cipher
        self.version = "202309"
        self.endpoints = TikTokShopEndpoints("https://open-api.tiktokglobalshop.com")

    def generate_signature(self, path: str, query_params: dict, body: dict = None, content_type: str = None):
        """
        Generate signature for TikTok Shop API requests
        """
        # Filter and sort parameters
        filtered_keys = [key for key in query_params if key not in ["sign", "access_token"]]
        filtered_keys.sort()
        input_string = path + "".join(f"{key}{query_params[key]}" for key in filtered_keys)

        # Process body based on content_type
        if content_type == "multipart/form-data":
            string_to_sign = f"{self.app_secret}{input_string}{self.app_secret}"
        else:
            # Determine body_string
            if body is None:
                body_string = ""
            elif isinstance(body, dict) and not body:
                body_string = "{}"
            else:
                # IMPORTANT: The JSON format must use separators=(', ', ': ') 
                # with a space after the comma and colon
                # This format is compatible with the TikTok Shop API requirements
                body_string = json.dumps(body, separators=(', ', ': '))

            string_to_sign = f"{self.app_secret}{input_string}{body_string}{self.app_secret}"

        # Create HMAC-SHA256 signature
        h = hmac.new(
            key=self.app_secret.encode('utf-8'),
            msg=string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        )
        return h.hexdigest()

    def update_request_params(self, endpoint: EndpointType, params: Dict[str, Any] = None, body: Dict[str, Any] = None,
                              content_type: str = "application/json", path_params: Dict[str, Any] = None) -> Dict[
        str, Any]:
        """
        Update request parameters with required fields and convert dates to timestamps.

        Args:
            endpoint: The endpoint type
            params: Optional existing parameters
            body: Optional JSON data
            content_type: Optional content type
            path_params: Optional path parameters

        Returns:
            Dict[str, Any]: Updated parameters with all required fields
        """
        # Initialize parameters
        params = params or {}

        # Add base parameters based on endpoint type
        base_params = {
            'app_key': self.app_key,
            'version': self.version,
        }

        # Add additional parameters for non-auth endpoints
        if endpoint.path not in [self.endpoints.AUTH_SHOP.path, self.endpoints.UPLOAD_IMAGE.path]:
            base_params.update({
                'access_token': self.access_token,
                'shop_id': self.shop_id,
                'shop_cipher': self.shop_cipher,
            })

        if endpoint.path in [self.endpoints.GET_RECOMMENDED_GLOBAL_CATEGORY.path,
                             self.endpoints.GET_GLOBAL_CATEGORY_RULES.path,
                             self.endpoints.GET_GLOBAL_CATEGORY_ATTRIBUTES.path]:
            base_params.pop('shop_cipher', None)

        params.update(base_params)

        # Add timestamp
        params["timestamp"] = str(int(time.time()))

        # Update version for specific endpoints
        version_mapping = {
            self.endpoints.OPTIMIZE_IMAGE.path: "202404",
        }

        if endpoint.path in version_mapping:
            params['version'] = version_mapping[endpoint.path]

        endpoint_path = endpoint.path.format(**path_params) if path_params else endpoint.path
        # Generate signature
        signature = self.generate_signature(
            path=endpoint_path,
            query_params=params,
            body=body,
            content_type=content_type
        )
        params["sign"] = signature

        return params

    def _make_request(self, endpoint: EndpointType, params: Optional[Dict[str, Any]] = None,
                      data: Optional[Dict[str, Any]] = None, body: Optional[Dict[str, Any]] = None,
                      path_params: Dict[str, Any] = None, files: Optional[Dict[str, Any]] = None,
                      content_type: str = "application/json") -> Dict[str, Any]:
        headers = {
            "x-tts-access-token": self.access_token,
        }
        params = self.update_request_params(endpoint, params, body, content_type, path_params)

        try:
            response = self._make_request_sync(endpoint, headers=headers, params=params, path_params=path_params,
                                               json=body,
                                               data=data, files=files)
            if response.get("code") == 0:
                return response.get("data", {})
            else:
                raise BadRequest(response['message'])
        except Exception as e:
            raise BadRequest(e)

    def get_authorization_url(self, connection_id: str, redirect_url: str = None, base_url: str = None) -> str:
        state = self.generate_state(connection_id, redirect_url)
        if self.region != "us":
            return (f"https://services.tiktokshop.com/open/authorize?"
                    f"service_id={self.service_id}&"
                    f"state={state}")
        else:
            return (f"https://services.us.tiktokshop.com/open/authorize?"
                    f"service_id={self.service_id}&"
                    f"state={state}")

    def fetch_token(self, auth_code: str):
        grant_type = "authorized_code"
        tiktok_api_url = "https://auth.tiktok-shops.com/api/v2/token/get"
        params = {
            "app_key": self.app_key,
            "app_secret": self.app_secret,
            "grant_type": grant_type,
            "auth_code": auth_code
        }
        if not all([self.app_key, self.app_secret, auth_code]):
            raise UnauthorizedExp("Missing required authentication details.")
        try:
            response = requests.get(tiktok_api_url, params=params)
            if response.status_code != 200:
                raise UnauthorizedExp(f"Failed to fetch token, status code: {response.status_code}")
            token_data = response.json()
            if token_data.get("message") != "success" or "data" not in token_data:
                raise UnauthorizedExp(f"Invalid response: {token_data}")

            data = token_data["data"]
            access_token = data.get("access_token")
            access_token_expire_in = data.get("access_token_expire_in")
            expires_at = pendulum.now('UTC').add(seconds=access_token_expire_in)
            refresh_token = data.get("refresh_token")
            self.access_token = access_token

            # fetch authorization_shop
            try:
                authorization_shop = self.get_authorized_shops()
                authorization_shop_data = authorization_shop.get("shops", [])[0]
                self.shop_cipher = authorization_shop_data.get("cipher")
                self.shop_id = authorization_shop_data.get("id")
            except Exception:
                raise UnauthorizedExp("Failed to fetch authorization shop")
            return {
                'external_id': self.service_id,
                'access_token': access_token,
                'expires_at': expires_at,
                'refresh_token': refresh_token,
                'token_data': {**data, 'shop_id': self.shop_id, 'shop_cipher': self.shop_cipher}
            }
        except Exception as e:
            raise UnknownExp(e)

    def get_authorized_shops(self) -> Dict[str, Any]:
        try:
            response = self._make_request(
                endpoint=self.endpoints.AUTH_SHOP,
            )
            return response
        except Exception as e:
            raise UnknownExp(f"Error fetching authorized shops: {str(e)}")

    def test_connection(self) -> bool:
        try:
            # Use the get_authorized_shops method to test connection
            response = self.get_authorized_shops()
            return True
        except Exception as e:
            logger.error(f"Error testing connection to TikTok Shop: {str(e)}")
            return False

    def setup_webhooks(self, connection):
        webhooks_to_create = [
            {"event_type": event_type,
             "address": connection.get_webhook_url()}
            for event_type in self.AVAILABLE_WEBHOOKS
            if connection.attributes.webhook_settings.get(event_type, False)
        ]

        results = []
        for webhook in webhooks_to_create:
            result = self._create_webhook(webhook)
            results.append((webhook['event_type'], result[0]))

        for event_type, success in results:
            if success:
                logger.info(f"Successfully created webhook: {event_type}")
            else:
                logger.error(f"Failed to create webhook: {event_type}")

    def _create_webhook(self, webhook_data: Dict[str, str]) -> Tuple[bool, str]:
        address = webhook_data['address'],
        event_type = webhook_data['event_type']

        body = {
            "address": address[0],
            "event_type": event_type,
        }

        try:
            response = self._make_request(
                endpoint=self.endpoints.UPDATE_WEBHOOK,
                body=body
            )
            return (True, "Ok")
        except Exception as e:
            return (False, str(e))

    def extract_event_type(self, payload: Dict[str, Any], headers: Dict[str, Any] = None) -> str:
        return payload.get('type')

    def verify_webhook_signature(self, payload: Dict[str, Any], headers: Dict[str, Any],
                                 settings: Dict[str, Any] = None) -> bool:
        """
        Verify TikTok Shop webhook signature

        Args:
            payload: Webhook payload dictionary
            headers: Request headers dictionary
            settings: Connection settings dictionary

        Returns:
            bool: True if signature is valid, False otherwise
        """
        try:
            # Get signature from Authorization header
            received_signature = next(
                (headers[k] for k in headers if k.lower() == 'authorization'),
                None
            )
            if not received_signature:
                logger.warning("Missing Authorization header")
                return False

            # Step 1: Create signature base string by concatenating app_key and payload
            signature_base = f"{self.app_key}{payload}"

            # Step 2: Generate HMAC-SHA256 signature using app_secret as key
            computed_signature = hmac.new(
                key=self.app_secret.encode('utf-8'),
                msg=signature_base.encode('utf-8'),
                digestmod=hashlib.sha256
            ).hexdigest()

            # Step 3: Compare signatures using secure comparison
            if not hmac.compare_digest(received_signature.lower(), computed_signature.lower()):
                logger.warning("Signature mismatch")
                return False

            return True

        except Exception as e:
            logger.error(f"Unexpected error during webhook signature verification: {str(e)}")
            return False

    @staticmethod
    def extract_object_details(payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: str = None) -> FetchEvent:
        """
        Extract object details from TikTok Shop webhook payload

        Args:
            payload: Webhook payload dictionary
            headers: Request headers including TikTok-Signature
            event_type: TikTok topic
        """
        webhook_type = payload.get('type')
        shop_id = payload.get('shop_id')
        timestamp = payload.get('timestamp')
        data = payload.get('data', {})

        event_id = None
        action_type = ActionType.webhook
        action_group = ActionGroup.webhook

        # Map webhook type to appropriate action and object types
        if webhook_type == 1:  # orders/updated
            event_id = str(data.get('order_id'))
            action_type = ActionType.get_order
            action_group = ActionGroup.order
        elif webhook_type in [15, 16]:  # product/info_updated or product/created
            event_id = str(data.get('product_id'))
            action_type = ActionType.get_product
            action_group = ActionGroup.product

        return FetchEvent(
            channel="tiktok_shop",
            connection_id=str(uuid.uuid4()),
            action_type=action_type,
            action_group=action_group,
            event_time=pendulum.from_timestamp(timestamp).to_iso8601_string(),
            is_batch=False,
            object_id=event_id,
            event_source=EventSource.webhook,
            object_data={
                'event': payload,
                'shop_id': shop_id,
                'webhook_type': webhook_type
            }
        )

    @lru_cache(maxsize=128)
    def get_brands(self):
        brand_data = self._make_request(self.endpoints.BRANDS)
        return brand_data.get("brand_list", [])

    @lru_cache(maxsize=128)
    def get_warehouses(self) -> List[Dict[str, Any]]:
        response = self._make_request(self.endpoints.GET_WAREHOUSES)
        return response['warehouses']

    def convert_date_to_timestamp(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform datetime to timestamp
        """
        datetime_keys = {"create_time_ge", "create_time_lt", "update_time_ge", "update_time_lt"}
        for key in datetime_keys:
            if key in params and params[key] is not None:
                try:
                    dt = pendulum.parse(params[key])
                    params[key] = int(dt.timestamp())
                except Exception as e:
                    logger.warning(f"Failed to convert {key} to timestamp: {e}")
        return params

    def split_params_to_query_params_and_body(self, params: Dict[str, Any], body_keys: set) -> Tuple[
        Dict[str, Any], Dict[str, Any]]:
        """
        Split params to query_params and body_params

        Args:
            params: Dictionary contains all params
            body_keys: Set of keys will be in body

        Returns:
            Tuple contains query_params and body_params
        """

        # Default query keys
        default_query_keys = ["sort_order", "sort_field", "page_size", "page_token"]

        # Get required fields for query_params
        query_params = {
            key: params[key]
            for key in default_query_keys
            if key in params and params[key] is not None
        }
        # Get fields for body_params
        body_params = {
            key: params[key]
            for key in body_keys
            if key in params and params[key] is not None
        }
        body_params = self.convert_date_to_timestamp(body_params)
        return query_params, body_params

    def prepare_pagination_meta(self, response: Dict[str, Any], params: Dict[str, Any]) -> MetaData:
        """
        Prepare pagination meta from response
        """
        total_count = response.get("total_count", 0)
        next_cursor = response.get("next_page_token")
        page_size = int(params.get("page_size", 10))

        # Calculate total pages
        if total_count % page_size == 0:
            total_page = total_count // page_size
        else:
            total_page = (total_count // page_size) + 1

        # Get current page
        page = int(params.get("page", 0)) + 1

        return MetaData(
            total_page=total_page,
            total_count=total_count,
            page=page,
            limit=page_size,
            continuation_token=next_cursor if next_cursor else None,
            current_params={**params, "page": page}
        )

    def get_product(self, product_id: str, fetch_event_id: str) -> FetchAPIResponse[StandardProduct]:
        response = self._make_request(self.endpoints.PRODUCT_DETAIL, path_params={"product_id": product_id})

        if not response:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"Product with ID {product_id} not found"
            )

        product = self.standardize_product(response, fetch_event_id)
        return FetchAPIResponse(
            success=True,
            data=[product],
            meta=MetaData(total_count=1, page=1, limit=1)
        )

    def get_products(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                     fetch_event_id: str = None) -> FetchAPIResponse[StandardProduct]:
        """
        Get list of products from TikTok Shop

        Args:
            params: Filter parameters for the products
            settings: Additional settings
            next_page: Next page cursor for pagination
            fetch_event_id: Event ID for tracking

        Returns:
            FetchAPIResponse containing standardized products
        """
        default_params = {"page_size": 10}
        if params is None:
            params = {}
        params = {**default_params, **params}
        updated_after = settings.get('updated_after') if settings is not None else None

        body_keys = {"status", "seller_skus", "create_time_ge", "create_time_lt", "update_time_ge", "update_time_lt",
                     "category_version", "listing_quality_tiers", "listing_platforms", "audit_status", "sku_ids"}

        if updated_after and not params.get('update_time_ge'):
            params['update_time_ge'] = updated_after

        params["page_token"] = next_page if next_page else params.get("page_token", None)

        try:
            # Split params to query_params and body_params
            query_params, body_params = self.split_params_to_query_params_and_body(
                params=params,
                body_keys=body_keys,
            )

            # Make API request
            response = self._make_request(
                endpoint=self.endpoints.PRODUCTS,
                params=query_params,
                body=body_params
            )

            product_list = response.get("products", [])

            # Standardize products
            standardized_products = [
                self.standardize_product(product, fetch_event_id)
                for product in product_list
            ]

            # Prepare metadata
            meta = self.prepare_pagination_meta(response, params)

            return FetchAPIResponse(
                success=True,
                data=standardized_products,
                meta=meta,
                message=response.get("message", "")
            )

        except Exception as e:
            raise UnknownExp(f"Error fetching products: {str(e)}")

    def get_order(self, order_id: str, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        """
        Get a single order by ID from TikTok Shop

        Args:
            order_id: TikTok Shop order ID
            fetch_event_id: Event ID for tracking

        Returns:
            FetchAPIResponse containing standardized order
        """
        if not order_id:
            raise UnknownExp("Order ID is required")

        try:
            # Prepare query parameters for signature
            query_params = {
                "ids": order_id  # Pass order ID as query parameter
            }
            # Make API request
            response = self._make_request(
                endpoint=self.endpoints.ORDER_DETAIL,
                params=query_params
            )

            # The response structure might contain a list of orders, but we're only requesting one
            order_list = response.get("orders", [])

            if not order_list:
                return FetchAPIResponse(
                    success=False,
                    data=[],
                    meta=MetaData(total_count=0, page=1, limit=1),
                    message=f"Order with ID {order_id} not found"
                )

            # Get the first (and should be only) order
            order_data = order_list[0]

            # Standardize order
            standardized_order = self.standardize_order(order_data, fetch_event_id)

            # Return response with standardized order
            return FetchAPIResponse(
                success=True,
                data=[standardized_order],  # Return as list for consistency with other methods
                meta=MetaData(total_count=1, page=1, limit=1)
            )

        except Exception as e:
            raise UnknownExp(f"Error fetching order details: {str(e)}")

    def get_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                   fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        """
        Get list of orders from TikTok Shop

        Args:
            params: Filter parameters for the orders
            settings: Additional settings
            next_page: Next page cursor for pagination
            fetch_event_id: Event ID for tracking

        Returns:
            FetchAPIResponse containing standardized orders
        """
        default_params = {"page_size": 10}
        if params is None:
            params = {}
        params = {**default_params, **params}
        updated_after = settings.get('updated_after') if settings is not None else None

        body_keys = {"order_status", "create_time_ge", "create_time_lt", "update_time_ge", "update_time_lt",
                     "shipping_type", "buyer_user_id", "is_buyer_request_cancel", "warehouse_ids"}

        if updated_after and not params.get('update_time_ge'):
            params['update_time_ge'] = updated_after

        params["page_token"] = next_page if next_page else params.get("page_token", None)

        try:
            # Split params to query_params and body_params
            query_params, body_params = self.split_params_to_query_params_and_body(
                params=params,
                body_keys=body_keys,
            )

            # Make API request
            response = self._make_request(
                endpoint=self.endpoints.ORDERS,
                params=query_params,
                body=body_params
            )

            order_list = response.get("orders", [])

            # Standardize orders
            standardized_orders = [
                self.standardize_order(order, fetch_event_id)
                for order in order_list
            ]

            # Prepare metadata
            meta = self.prepare_pagination_meta(response, params)

            return FetchAPIResponse(
                success=True,
                data=standardized_orders,
                meta=meta,
                message=response.get("message", "")
            )

        except Exception as e:
            raise UnknownExp(f"Error fetching orders: {str(e)}")

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def sync_brand(self, brand_name: Optional[str]) -> Dict[str, Any]:
        brands = self.get_brands()
        for brand in brands:
            if brand.get('name') == brand_name:
                return brand
        brand_response = self._make_request(
            endpoint=self.endpoints.CREATE_BRAND,
            body={
                'name': brand_name
            }
        )
        return brand_response

    def get_recommended_category(self, product_data: Dict[str, Any], is_global: bool = False) -> Dict[str, Any]:
        try:
            product_title = product_data.get('title')
            if len(product_title) < 25 or len(product_title) > 255:
                raise Exception("The product name must be between 25 and 255 characters, inclusive.")

            body = {
                "product_title": product_title,
                "description": product_data.get('description'),
                "images": [{"uri": image['uri']} for image in product_data.get('main_images', []) if image.get('uri')],
                "category_version": "v1" if self.region != "us" else "v2",
            }
            if not is_global:
                body['listing_platform'] = "TIKTOK_SHOP"
            response = self._make_request(
                endpoint=self.endpoints.GET_RECOMMENDED_CATEGORY if not is_global else self.endpoints.GET_RECOMMENDED_GLOBAL_CATEGORY,
                body=body
            )

            return response
        except Exception as e:
            logger.error(f"Error getting recommended category: {str(e)}")
            raise

    def get_category_rules(self, category_id: str, is_global: bool = False) -> Dict[str, Any]:
        try:
            response = self._make_request(
                endpoint=self.endpoints.GET_CATEGORY_RULES if not is_global else self.endpoints.GET_GLOBAL_CATEGORY_RULES,
                path_params={"category_id": category_id},
                params={"category_version": "v1" if self.region != "us" else "v2"}
            )

            return response
        except Exception as e:
            logger.error(f"Error getting category rules: {str(e)}")
            raise

    def get_category_attributes(self, category_id: str, is_global: bool = False) -> Dict[str, Any]:
        try:
            response = self._make_request(
                endpoint=self.endpoints.GET_CATEGORY_ATTRIBUTES if not is_global else self.endpoints.GET_GLOBAL_CATEGORY_ATTRIBUTES,
                path_params={'category_id': category_id},
                params={"category_version": "v1" if self.region != "us" else "v2"}
            )

            return response
        except Exception as e:
            logger.error(f"Error getting category attributes: {str(e)}")
            raise

    def optimize_image(self, images: List[str]):
        try:
            if len(images) == 0:
                return
            body = {
                "images": [{
                    "uri": image,
                    "optimization_mode": ["WHITE_BACKGROUND"]
                } for image in images],
            }
            response = self._make_request(
                endpoint=self.endpoints.OPTIMIZE_IMAGE,
                body=body
            )
            return response

        except Exception as e:
            logger.error(f"Error optimizing image after upload: {str(e)}")
            raise

    def upload_image(self, image: Dict[str, Any], use_case: str) -> Dict[str, Any]:
        try:
            # Get image from URL
            image_url = image['src']
            response = requests.get(image_url)
            if response.status_code != 200:
                raise Exception(f"Failed to download image from {image_url}")

            # Prepare file for multipart/form-data
            image_name = image['name']
            image_extension = image_name.split('.')[-1]
            files = {
                'data': (image_name, response.content, f'image/{image_extension}'),
            }
            data = {
                'use_case': use_case
            }

            # Call API upload with multipart/form-data
            response = self._make_request(
                content_type="multipart/form-data",
                endpoint=self.endpoints.UPLOAD_IMAGE,
                files=files,
                data=data
            )

            return response

        except Exception as e:
            logger.error(f"Error uploading image: {str(e)}")
            raise

    def sync_images(self, images: List[Dict[str, Any]], use_case: str = 'MAIN_IMAGE', is_optimize: bool = False) -> \
            List[Dict[str, Any]]:
        synced_images = []

        for index, image in enumerate(images):
            try:
                # Get image URL
                image_url = image.get('src')
                if not image_url:
                    continue

                # Upload image
                upload_response = self.upload_image(image, use_case)

                # Save image info
                synced_images.append({
                    'uri': upload_response.get('uri')
                })

            except Exception as e:
                logger.error(f"Error syncing image {image.get('name')}: {str(e)}")
                continue
        if is_optimize and synced_images:
            self.optimize_image([image['uri'] for image in synced_images])
        return synced_images

    def validate_category_rules(self, product: Dict[str, Any], category_rules: Dict[str, Any],
                                images: List[Dict[str, Any]]) -> Dict[str, Any]:
        if category_rules.get('product_certifications', []):
            product['certifications'] = [
                {
                    'id': certification['id'],
                    'images': [
                        {
                            'uri': image['uri'],
                        }
                        for image in self.sync_images(product['main_images'], 'CERTIFICATION_IMAGE')
                    ],
                }
                for certification in category_rules['product_certifications']
            ]
        if category_rules.get('size_chart', {}).get('is_required'):
            size_chart_images = self.sync_images([images[-1]], 'SIZE_CHART_IMAGE')
            if size_chart_images:
                product['size_chart'] = {
                    'image': {'uri': size_chart_images[0]['uri']}
                }
        is_cod_allowed = product.pop('is_cod_allowed', True)
        if category_rules.get('cod', {}).get('is_supported'):
            product['is_cod_allowed'] = is_cod_allowed
        if not category_rules.get('package_dimension', {}).get('is_required'):
            product.pop('package_dimensions', None)
        return product

    def format_variants(self, variants: List[Dict[str, Any]], variant_attributes: List[Dict[str, Any]]) -> List[
        Dict[str, Any]]:
        """
        Format variants to the new structure of TikTok Shop API v202309
        
        Args:
            variants: List of variants
            variant_attributes: List of variant attributes
            
        Returns:
            List[Dict[str, Any]]: List of variants formatted to the new structure
        """
        for variant in variants:
            for index, attr in enumerate(variant.get('sales_attributes', [])):
                supplementary_sku_images = self.sync_images(attr.get('supplementary_sku_images', []), 'ATTRIBUTE_IMAGE')
                if index == 0:
                    attr['supplementary_sku_images'] = supplementary_sku_images
                    if supplementary_sku_images:
                        attr['sku_img'] = {
                            'uri': supplementary_sku_images[0]['uri']
                        }
                available_attr = next((var_attr for var_attr in variant_attributes if var_attr['id'] == attr['id']),
                                      None)
                if available_attr:
                    attr['id'] = available_attr['id']
                    available_attr_value = next((var_attr_value for var_attr_value in available_attr['values'] if
                                                 var_attr_value['name'] == attr['value_name']), None)
                    if available_attr_value:
                        attr['value_id'] = available_attr_value['id']
            # Prepare inventory

            inventories = variant.pop('inventory', [])
            new_inventories = []
            list_warehouse = self.get_warehouses()
            warehouse_ids = [warehouse['id'] for warehouse in list_warehouse]
            for location_info in inventories:
                if location_info.get('warehouse_id') in warehouse_ids:
                    new_inventories.append(
                        {
                            "warehouse_id": location_info.get('warehouse_id'),
                            "quantity": int(location_info.get('quantity', 1))
                        }
                    )
            if not new_inventories:
                default_warehouse = next((warehouse for warehouse in list_warehouse if warehouse['is_default']),
                                         None)
                if not default_warehouse:
                    raise BadRequest('Default warehouse not found')
                new_inventories.append(
                    {
                        "warehouse_id": default_warehouse['id'],
                        "quantity": 1
                    }
                )
            variant['inventory'] = new_inventories
        return variants

    def sync_product(self, product: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardProduct]:
        try:
            # Sync images before
            images = product.pop('main_images', [])
            # if not images:
            #     raise BadRequest('Main images are required')
            product['main_images'] = self.sync_images(images, is_optimize=True)
            is_global = False
            # Step A: Get recommended category
            recommended_category = self.get_recommended_category(product, is_global=is_global)
            category_id = recommended_category['leaf_category_id']
            product.pop('category', None)
            product['category_id'] = category_id

            # Step B: Get category rules
            category_rules = self.get_category_rules(category_id, is_global=is_global)
            product = self.validate_category_rules(product, category_rules, images)

            # Step C: Get category attributes
            category_attributes = self.get_category_attributes(category_id, is_global=is_global)
            attributes = category_attributes.get('attributes', [])
            if not attributes:
                raise BadRequest('Required category attributes')
            product['product_attributes'] = [{'id': item['id'], 'values': item['values']} for item in attributes if
                                             item.get('values') and item.get('id') and item.get(
                                                 'type') == 'PRODUCT_PROPERTY']
            variant_attributes = [{'id': item['id'], 'values': item['values']} for item in attributes if
                                  item.get('values') and item.get('id') and item.get('type') == 'SALES_PROPERTY']

            # Step D: Generate TikTok product data
            brand_name = product.pop('brand', {}).get('name', None)
            brand = self.sync_brand(brand_name) if brand_name else None
            if brand:
                product['brand_id'] = brand['id']

            product['skus'] = self.format_variants(product['skus'], variant_attributes)

            remote_products = []
            remote_product_ids = set()
            skus = [v['seller_sku'] for v in product['skus']]
            products_response = self.get_products({'seller_skus': skus})
            products = products_response.data
            for p in products:
                if p.id not in remote_product_ids:
                    remote_products.append(p.raw_data)
                    remote_product_ids.add(p.id)

            if len(remote_products) > 2:
                raise BadRequest('SKUs belong to two products')
            # Step F: Create product on TikTok Shop

            if remote_products:
                product_id = remote_products[0]['id']
                # Update existing product
                response = self._make_request(
                    endpoint=self.endpoints.UPDATE_PRODUCT,
                    path_params={'product_id': product_id},
                    body=product
                )
            else:
                # Create new product
                response = self._make_request(
                    endpoint=self.endpoints.CREATE_PRODUCT,
                    body=product
                )

            # get product
            data = self._make_request(self.endpoints.PRODUCT_DETAIL,
                                      path_params={"product_id": get_value_from_jsonpath(response,
                                                                                         '$.product_id') or get_value_from_jsonpath(
                                          response, '$.id')})

            standardized_product = self.standardize_product(data)
            return SyncAPIResponse(
                success=True,
                data=standardized_product,
                meta=PublishMetaData(updated_at=datetime.now())
            )

        except Exception as e:
            logger.error(f"Failed to sync product: {str(e)}")
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync product: {str(e)}"
            )

    def sync_order(self, order_data: Dict[str, Any], settings: Dict[str, Any] = None) -> \
            SyncAPIResponse[StandardOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def standardize_variant(self, raw_variant: Dict[str, Any]) -> StandardVariant:
        sales_attributes = raw_variant.get('sales_attributes', [])
        images = sales_attributes[0].get('sku_img', {}).get('urls', []) if sales_attributes else []
        image = images[0] if images else ''
        return StandardVariant(
            id=raw_variant['id'],
            title=raw_variant.get('name', ''),
            sku=raw_variant['seller_sku'],
            price=str(raw_variant.get('price', {}).get('unit_price', '')),
            image=image
        )

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        variants = [self.standardize_variant(variant) for variant in raw_product['skus']]
        images = []
        for image in raw_product.get('main_images', []):
            images.extend(image.get('urls', []))

        now = pendulum.now()
        create_time = pendulum.from_timestamp(raw_product.get('create_time')) if raw_product.get('create_time') else now
        update_time = pendulum.from_timestamp(raw_product.get('update_time')) if raw_product.get('update_time') else now

        id = str(raw_product.get('id') or raw_product.get('product_id'))
        return StandardProduct(
            id=id,
            fetch_event_id=fetch_event_id,
            title=raw_product.get('title'),
            raw_data=raw_product,
            images=images,
            sku=raw_product['skus'][0]['seller_sku'],
            variants=variants,
            created_at=create_time,
            updated_at=update_time
        )

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str) -> StandardOrder:
        """
        Convert TikTok Shop order format to standard format

        Args:
            raw_order: Raw order data from TikTok Shop
            fetch_event_id: Event ID for tracking

        Returns:
            StandardOrder object
        """
        # Extract order items
        line_items = []
        for item in raw_order.get("line_items", []):
            price = float(item.get("sale_price", 0))
            quantity = 1  # TikTok Shop appears to list each item separately
            line_items.append(StandardOrderLineItem(
                id=item.get("id"),
                product_id=item.get("product_id"),
                variant_id=item.get("sku_id"),
                sku=item.get("seller_sku"),
                name=item.get("product_name"),
                quantity=quantity,
                price=price,
                total_price=price * quantity
            ))

        # Extract shipping address
        shipping_address = raw_order.get("recipient_address", {})

        # Get full address details
        address_detail = shipping_address.get("address_detail", "")
        address_line1 = shipping_address.get("address_line1", "")
        address_line2 = shipping_address.get("address_line2", "")

        # Extract district info for city, state, country
        district_info = shipping_address.get("district_info", [])
        city = ""
        state = ""
        country = ""

        for info in district_info:
            level = info.get("address_level")
            if level == "L0":  # Country
                country = info.get("address_name", "")
            elif level == "L1":  # Province/State
                state = info.get("address_name", "")
            elif level == "L2":  # District/City
                city = info.get("address_name", "")

        # Parse timestamps - handle both string and integer formats
        created_at = None
        updated_at = None

        if raw_order.get("create_time"):
            if isinstance(raw_order["create_time"], int):
                created_at = datetime.fromtimestamp(raw_order["create_time"])
            elif isinstance(raw_order["create_time"], str):
                try:
                    created_at = datetime.strptime(raw_order["create_time"], "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    # Try alternative formats if needed
                    pass

        if raw_order.get("update_time"):
            if isinstance(raw_order["update_time"], int):
                updated_at = datetime.fromtimestamp(raw_order["update_time"])
            elif isinstance(raw_order["update_time"], str):
                try:
                    updated_at = datetime.strptime(raw_order["update_time"], "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    # Try alternative formats if needed
                    pass

        # Extract payment information
        payment_info = raw_order.get("payment", {})
        return StandardOrder(
            id=raw_order.get("id"),
            fetch_event_id=fetch_event_id,
            raw_data=raw_order,
            order_number=raw_order.get("id"),
            total_price=float(payment_info.get("total_amount", 0)),
            currency=payment_info.get("currency", ""),
            status=raw_order.get("status", ""),
            created_at=created_at,
            updated_at=updated_at,
            line_items=line_items,
            customer_id=raw_order.get("user_id"),
            customer_name=shipping_address.get("name"),
            customer_email=raw_order.get("buyer_email"),
            customer_phone=shipping_address.get("phone_number"),
            shipping_address={
                "name": shipping_address.get("name"),
                "phone": shipping_address.get("phone_number"),
                "address1": address_detail or address_line1,
                "address2": address_line2,
                "city": city,
                "state": state,
                "country": country,
                "postal_code": shipping_address.get("postal_code", "")
            },
            billing_address=None,  # TikTok Shop doesn't provide separate billing address
            shipping_method=raw_order.get("delivery_option_name"),
            payment_method=raw_order.get("payment_method_name"),
            notes=raw_order.get("buyer_message", ""),
            tags=[]
        )

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    @staticmethod
    def get_product_params():
        return FetchAPIResponse(
            success=True,
            data={
                'page_size': 'Number',
                'page_token': 'String',
                'status': 'String',
                'seller_skus': 'Array',
                'create_time_ge': 'DateTime|ISO',
                'create_time_lt': 'DateTime|ISO',
                'update_time_ge': 'DateTime|ISO',
                'update_time_lt': 'DateTime|ISO',
                'category_version': 'String',
                'listing_quality_tiers': 'Array',
                'listing_platforms': 'Array',
                'audit_status': 'Array',
                'sku_ids': 'Array',
            },
            meta=None
        )

    @staticmethod
    def get_order_params():
        return FetchAPIResponse(
            success=True,
            data={
                'page_size': 'Number',
                'sort_order': 'String',
                'page_token': 'String',
                'sort_field': 'String',
                'order_status': 'String',
                'create_time_ge': 'DateTime|ISO',
                'create_time_lt': 'DateTime|ISO',
                'update_time_ge': 'DateTime|ISO',
                'update_time_lt': 'DateTime|ISO',
                'shipping_type': 'String',
                'buyer_user_id': 'String',
                'is_buyer_request_cancel': 'Boolean',
                'warehouse_ids': 'Array',
            },
            meta=None
        )

    @classmethod
    def _get_product_attributes(self):
        attributes = {
            "title": {"label": "title",
                      "description": "The product title", "is_required": True,
                      "type": "string"},
            "description": {"label": "Description",
                            "description": "The product description in HTML format.", "is_required": True,
                            "type": "string"},
            "category_id": {"label": "Category ID",
                            "description": "The category ID to which the product belongs.", "is_required": True,
                            "type": "string"},
            "brand_id": {"label": "Brand ID",
                         "description": "The brand ID associated with the product.", "is_required": False,
                         "type": "string"},
            "main_images": {"label": "main_images",
                            "description": "A list of images to display in the product image gallery",
                            "is_required": True,
                            "type": "array",
                            "properties": {
                                "uri": {"label": "uri", "description": "image id", "is_required": True,
                                        "type": "string"}
                            }
                            },
            "skus": {"label": "skus", "description": "The number of sku in a product cannot exceed 100",
                     "is_required": True, "type": "array",
                     "properties": {
                         "sales_attributes": {"label": "sales_attributes",
                                              "description": "If this structure is empty means the product has no sales attribute.",
                                              "is_required": False, "type": "array",
                                              "properties": {
                                                  "id": {"label": "id", "is_required": False, "type": "string",
                                                         "description": "The ID of a built-in sales attribute",
                                                         },
                                                  "value_name": {"label": "value_name", "is_required": False,
                                                                 "type": "string",
                                                                 "description": "A self-defined custom sales attribute value if the built-in values do not satisfy your needs",
                                                                 "validate": {"max": 50},
                                                                 },
                                                  "value_id": {"label": "value_id", "is_required": False,
                                                               "type": "string",
                                                               "description": "The ID of a built-in sales attribute value"},
                                                  "sku_img": {"label": "sku_img", "is_required": False,
                                                              "type": "object",
                                                              "description": "sales attribute value image displayed to buyers",
                                                              "properties": {
                                                                  "uri": {"label": "uri", "is_required": True,
                                                                          "type": "string",
                                                                          "description": "The URI of the image"}
                                                              }
                                                              },
                                              }
                                              },
                         "price": {"label": "price", "is_required": True, "description": "SKU pricing information.",
                                   "type": "object",
                                   "properties": {
                                       "amount": {"label": "amount", "is_required": True,
                                                  "description": "The SKU's selling price displayed on the product page before any discounts.",
                                                  "type": "string"},
                                       "currency": {"label": "currency", "is_required": True,
                                                    "description": "SKU pricing information.", "type": "string"}
                                   }
                                   },
                         "seller_sku": {"label": "seller_sku", "is_required": False, "type": "string",
                                        "description": "",
                                        "validate": {"max": 50}},
                         "list_price": {"label": "list_price", "is_required": False, "type": "object",
                                        "description": "",
                                        "properties": {
                                            "amount": {"label": "amount", "is_required": True, "type": "string",
                                                       "description": "",
                                                       "validate": {"min": 0.01, "max": 7600}},
                                            "currency": {"label": "currency", "is_required": True, "type": "string",
                                                         "description": ""},
                                        }
                                        }
                     }
                     },
            "package_weight": {"label": "Weight", "description": "The weight of the product package",
                               "is_required": True, "type": "object",
                               "properties": {
                                   "value": {"label": "value", "is_required": True, "type": "string",
                                             "description": "The package weight"},
                                   "unit": {"label": "unit", "is_required": True, "type": "string",
                                            "description": "The unit for the package weight"},
                               }
                               },
            "certifications": {"label": "certifications", "description": "product certification files",
                               "is_required": False, "type": "array",
                               "properties": {
                                   "id": {"label": "id",
                                          "description": "This is the certification id , you can get this id by 'GetCategoryRule'API.",
                                          "is_required": True, "type": "string"},
                                   "images": {"label": "images", "description": "certificate images",
                                              "is_required": False,
                                              "type": "array",
                                              "properties": {
                                                  "uri": {"label": "uri",
                                                          "description": "You can only use the response parameters of the UploadImg API as this request parameter",
                                                          "is_required": True, "type": "string"}
                                              }
                                              },
                                   "files": {"label": "files", "description": "certification files",
                                             "is_required": False,
                                             "type": "",
                                             "properties": {
                                                 "id": {"label": "id",
                                                        "description": "You can only use the request parameters of the UploadFile API as the request parameters.",
                                                        "is_required": True, "type": "string"},
                                                 "name": {"label": "name",
                                                          "description": "The file name, you can use your own file name",
                                                          "is_required": True, "type": "string"},
                                                 "format": {"label": "format",
                                                            "description": "The file type, you can use your own file type",
                                                            "is_required": True, "type": "string"},
                                             }
                                             },
                               }
                               },
            "is_cod_allowed": {"label": "is_cod_allowed", "is_required": True, "type": "bool",
                               "description": "The category rule determines whether this parameter is required. If the category rule is 'true', you can choose to turn on or off the cod. If the category rule is 'false', you are not allowed to turn on the cod. COD means cash on delivery."},

        }

        return attributes
