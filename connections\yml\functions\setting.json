{"set_fetch_action_settings": {"POST": [{"name": "Update get product settings", "body": {"actions": {"get_product": {"enabled": false, "status": {"last_run": null, "next_run": null}, "retry_settings": {"max_retries": "3", "retry_delay": "5", "retry_backoff": "2"}, "rate_limit": 60, "custom_settings": {"limit": "50", "collection_id": null, "status": "active"}, "schedule": {"type": "interval", "value": "3600"}, "response_mapping": {}}}}}]}, "set_publish_action_settings": {"POST": [{"name": "Update sync product settings", "body": {"actions": {"sync_product": {"enabled": false, "retry_settings": {"max_retries": "3", "retry_delay": "5", "retry_backoff": "2"}, "payload_template": {}, "rate_limit": 60, "custom_settings": {"update_variants": true, "create_new_products": true}}}}}]}, "update_webhook_settings": {"PUT": [{"name": "Update webhook settings", "body": {"webhook_settings": {"orders/create": true, "products/create": false, "inventory_levels/update": false, "orders/updated": false, "products/update": false}}}]}, "update_dynamic_settings": {"PUT": [{"name": "Update dynamic settings", "body": {"dynamic_settings": {"location_mapping": {"mapping": {"57620": "9f7b5896-69d2-4ef2-8d90-5f9d1e3e3c6f", "57621": "32585a19-a3f8-4b72-b33c-0734c50a8b4c", "78666": "9f7b5896-69d2-4ef2-8d90-5f9d1e3e3c6f", "78667": "32585a19-a3f8-4b72-b33c-0734c50a8b4c", "78668": "c8f62c0e-a3a2-4b78-8e96-c30617a8930c"}}}}}]}}