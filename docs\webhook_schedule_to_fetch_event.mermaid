graph TD
    A[Start] --> B{Event Source}
    B -->|Webhook| C[API Gateway]
    B -->|Scheduled| D[CloudWatch Event]

    C --> E[Determine Webhook Type]
    E -->|Type 1| F[API Endpoint for Type 1]
    E -->|Type 2| G[API Endpoint for Type 2]
    F --> H[Lambda Function for Type 1]
    G --> I[Lambda Function for Type 2]

    D --> J[schedule_handler]
    J --> K[Scan Connections]
    K --> L{For Each Connection}
    L --> M[process_connection]
    M --> N[get_due_actions]

    H --> O[Get Connection by ID]
    I --> P[Get Connection by Merchant ID]
    O --> Q[Verify Webhook Signature]
    P --> Q
    Q -->|Invalid| R[Return 401 Unauthorized]
    Q -->|Valid| S[Process Webhook]

    N --> T{For Each Due Action}
    S --> U[Determine Action Type]

    T --> V[Create FetchEvent]
    U --> V

    V --> W[enqueue_fetch_event]
    W --> X[Send Message to SQS]

    X --> Y[Fetch Queue]
    Y --> Z[Fetch Lambda]

    Z --> AA[Parse Message]
    AA --> AB[Retrieve Connection Settings]
    AB --> AC[Initialize Channel]
    AC --> AD[Get Fetch Settings]
    AD --> AE[Execute Fetch and Process]
    AE --> AF[Store Raw Data in S3]
    AF --> AG[Update Fetch Progress]

    T --> AH[update_action_schedule]
    AH --> T

    L --> AI[Save Connection if Updated]
    AI --> L

    AG --> AJ{More Data?}
    AJ -->|Yes| AK[Enqueue Continuation Message]
    AJ -->|No| AL[Update Connection Status]

    AK --> Y
    AL --> AM[End]