import math
from typing import Dict, Any

from ...base import DestinationTransformer
from integrations.channels.action_types import ActionGroup
from models.product.product import ProductAttributesSchema


class HaravanProductTransformer(DestinationTransformer):
    channel_name = 'haravan'
    object_type = 'product'

    def _map_object_type_to_action_type(self) -> str:
        return 'create_product'

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        dynamic_settings = self.connection_settings.get('dynamic_settings', {})
        default_location_id = dynamic_settings.get('default_location_id', '')
        stock_split = dynamic_settings.get('stock_split')
        stock_min = dynamic_settings.get('stock_min')
        sync_unpublished = dynamic_settings.get('sync_unpublished', True)
        sync_without_images = dynamic_settings.get('sync_without_images', True)

        if stock_min is not None and stock_split is not None:
            try:
                stock_split = float(stock_split)
                stock_min = float(stock_min)
            except (TypeError, ValueError):
                raise TypeError("stock_split and stock_min must be numeric values")

        options = data.get('options')
        if options is None:
            options_dict = {}
            for variant in data.get('variants', []):
                for index in range(1, 4):
                    option_title = variant.get(f'optionTitle{index}') or f'Attribute {index}'
                    options_dict.setdefault(option_title, [])
                    options_dict[option_title].append(variant[f'option{index}'])
            options = [{'name': option_title} for option_title in options_dict.keys()]

        product = {
            "title": data.get('name'),
            "body_html": data.get('description'),
            "published": data.get('publish', False),
            "tags": data.get('tags', ''),
            "images": data.get('images', []),
            "variants": [
                {
                    "option1": variant.get('option1'),
                    "option2": variant.get('option2'),
                    "price": next(
                        (v['price'] for v in variant.get('prices', []) if v.get('price_group', {}).get('id') in [
                            "RETAILS"
                        ]), None),
                    "grams": variant.get('measurements', {}).get('weight_value'),
                    "requires_shipping": True,
                    "inventory_management": "haravan",
                    "title": variant['name'],
                    "sku": variant.get('sku'),
                    "image": variant['images'][0] if len(variant['images']) else None,
                    "inventory": {
                        "type": "set",
                        "reason": "productionofgoods",
                        "note": "update from onexapis",
                        "line_items": [
                            {
                                "sku": variant['sku'],
                                "quantity": math.floor(stock_split * inventory['available']) if inventory[
                                                                                                    'available'] >= stock_min else 0,
                            } for inventory in variant['inventories'].get('inventories', []) if
                            inventory['location_id'] == default_location_id
                        ]
                    }
                } for variant in data.get('variants', [])
            ],
            "options": [
                {
                    "name": option['name'],
                    "position": index + 1
                } for index, option in enumerate(options[:2])
            ]
        }

        brand = data.get('brand', {})
        vendor = brand.get('name') if isinstance(brand, dict) else None
        if vendor:
            product['vendor'] = vendor

        category = data.get('category', {})
        product_type = category.get('name') if isinstance(category, dict) else None
        if product_type is None and data.get('variants'):
            first_variant = data['variants'][0]
            variant_category = data.get('category', {})
            product_type = first_variant.get('category', {}).get('name') if variant_category is not None else None
        product['product_type'] = product_type
        opti_published = True
        if not sync_unpublished and not product['published']:
            opti_published = False

        if not sync_without_images and not product['images']:
            opti_published = False

        return {
            "product": product,
            "opti_published": opti_published
        }

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.product

    @property
    def input_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())

    @property
    def output_schema(self):
        # This should reflect the Haravan API product schema
        return {
            'type': 'object',
            'properties': {
                'product': {
                    'type': 'object',
                    'properties': {
                        'title': {'type': 'string'},
                        'body_html': {'type': 'string'},
                        'vendor': {'type': 'string'},
                        'product_type': {'type': 'string'},
                        'published': {'type': 'boolean'},
                        'tags': {'type': 'string'},
                        'variants': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'properties': {
                                    'sku': {'type': 'string'},
                                    'barcode': {'type': 'string'},
                                    'price': {'type': 'number'},
                                    'compare_at_price': {'type': 'number'},
                                    'weight': {'type': 'number'},
                                    'weight_unit': {'type': 'string'},
                                    'inventory_quantity': {'type': 'integer'},
                                    'requires_shipping': {'type': 'boolean'},
                                    'inventory_management': {'type': 'string'},
                                }
                            }
                        },
                        'options': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'properties': {
                                    'name': {'type': 'string'},
                                    'values': {'type': 'array', 'items': {'type': 'string'}}
                                }
                            }
                        },
                        'images': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'properties': {
                                    'src': {'type': 'string'}
                                }
                            }
                        }
                    }
                }
            }
        }
