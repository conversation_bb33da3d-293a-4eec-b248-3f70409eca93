graph TB
    subgraph "Event Sources"
        A1[CloudWatch Event] -->|Triggers| B1(Schedule Handler)
        A2[Webhook Event] -->|Triggers| B2(Webhook Handler)
        A3[API Gateway] -->|Triggers| B3(Sync Record APIs)
    end

    subgraph "Event Creation & Queue"
        B1 -->|Creates| C1[FetchEvent]
        B2 -->|Creates| C2[FetchEvent]
        B3 -->|Creates| C3[FetchEvent]

        C1 -->|Enqueues| D{SQS Fetch Queue}
        C2 -->|Enqueues| D
        C3 -->|Enqueues| D
    end

    subgraph "Processing Flow"
        D -->|Triggers| E(Fetcher Lambda)
        E -->|Stores| F[S3 Raw Data]
        F -->|Triggers| G(Transformer Lambda)
        G -->|Stores| H[S3 Transformed Data]
        G -->|Enqueues| I{SQS Publish Queue}
        I -->|Triggers| J(Publisher Lambda)
    end

    subgraph "Data Stores"
        K[(DynamoDB - Connections)]
        L[(DynamoDB - SyncRecords)]
        M[(DynamoDB - SyncResponses)]
    end

    subgraph "External Systems"
        N[Source APIs]
        O[Destination APIs]
    end

    E -->|Fetches| N
    J -->|Publishes| O
    K -.->|Config| B1
    K -.->|Config| B2
    K -.->|Config| B3
    J -->|Updates| L
    J -->|Updates| M

    classDef sourceNode fill:#f9f,stroke:#333,stroke-width:2px;
    classDef queueNode fill:#ff9,stroke:#333,stroke-width:2px;
    classDef lambdaNode fill:#9ff,stroke:#333,stroke-width:2px;
    classDef storageNode fill:#f96,stroke:#333,stroke-width:2px;
    classDef dbNode fill:#69f,stroke:#333,stroke-width:2px;

    class A1,A2,A3 sourceNode;
    class D,I queueNode;
    class E,G,J lambdaNode;
    class F,H storageNode;
    class K,L,M dbNode;