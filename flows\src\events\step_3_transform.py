import os

import pendulum
from nolicore.utils.aws.boto_helper import get_client
from nolicore.utils.aws.decorator import invoke
from nolicore.utils.aws.request import LambdaRequest
from nolicore.utils.utils import logger

from helpers.imports import import_destination_data, import_master_data
from integrations.channels.action_types import ActionType
from integrations.common.bucket_helper import get_raw_data, generate_s3_key, store_transformed_data, extract_file_id
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.event import EventSource
from integrations.common.queue_helper import enqueue_publish_message
from integrations.common.transformers import registry
from models.integration.fetch_event import FetchEventModel
from models.integration.mapping_attribute import MappingAttributeTypeIndex
from models.integration.sync_record import SyncRecordModel, SyncRecordStatus
from ..libs.helper import get_active_destinations

s3 = get_client('s3')
sqs = get_client('sqs')

RAW_DATA_BUCKET = os.environ['RAW_DATA_BUCKET']
TRANSFORMED_DATA_BUCKET = os.environ['TRANSFORMED_DATA_BUCKET']
TRANSFORM_QUEUE_URL = os.environ['TRANSFORM_QUEUE_URL']

MAX_RETRIES = 3


@invoke()
def transform_lambda_handler(lambda_request: LambdaRequest):
    for record in lambda_request.event['Records']:
        bucket = record['s3']['bucket']['name']
        key = record['s3']['object']['key']
        version_id = record['s3']['object']['versionId']

        try:
            connection_id, action_type, object_type, filename = key.split('/')
            file_id = extract_file_id(key)
            fetch_event_id, raw_data, standard_data = get_raw_data(bucket, key, version_id)
            remote_source_record_id = standard_data.get('id')
            fetch_event_obj = FetchEventModel.get_fetch_event(fetch_event_id)
            if fetch_event_obj is None:
                logger.error(f"Fetch event {fetch_event_id} not found")
                raise
            connection = get_connection_by_id(connection_id, enrich_setting=True)
            channel_name = connection.attributes.channel_name
            transformer = registry.get_source_transformer(channel_name, object_type, connection.attributes_dict)
            if transformer is None:
                raise ValueError(f"No transformer found for channel {channel_name} and object type {object_type}")

            company_id = str(connection.attributes.company_id)

            should_save_to_storage = connection.should_save_to_storage(ActionType(action_type))
            if should_save_to_storage:
                # import destination data
                import_destination_data(company_id, object_type, connection_id, standard_data)
            if fetch_event_obj.attributes.event_source in [EventSource.import_record_api_clone]:
                continue

            sync_record_obj = SyncRecordModel.put(key, connection_id, {
                'channel': channel_name,
                'remote_record_id': None,
                'response': None,
                'response_message': None,
                'raw_record_version': version_id,
                'record_type': object_type,
                'fetch_event_id': fetch_event_id,
                'company_id': company_id,
                'fetched_at': record['eventTime'],
            })

            try:
                master_data = transformer.transform(raw_data)
            except Exception as e:
                sync_record_obj.update({
                    'response_message': f"Transform failed: {str(e)}",
                    'transformed_at': pendulum.now().to_iso8601_string(),
                    'finished_at': pendulum.now().to_iso8601_string(),
                    'status': SyncRecordStatus.ERROR.value,
                })
                raise

            # Add sync_record_id to master_data
            master_data['sync_record_id'] = key

            if connection.should_import_data(ActionType(action_type)):
                # Import master data and create new version
                import_result = import_master_data(company_id, object_type, master_data)
                logger.info(f"Import result: {import_result}")
                response_message = f'Import {"successfully" if import_result else "failed"}'
            else:
                response_message = f'Import skipped'
            master_data_key = generate_s3_key(connection_id, action_type, object_type, file_id, 'master')
            master_version_id = store_transformed_data(TRANSFORMED_DATA_BUCKET, master_data_key, master_data)
            logger.info(f"Master Version ID: {master_version_id}")
            sync_record_obj.update({
                    'remote_record_id': remote_source_record_id,
                    'response': standard_data,
                    'response_message': response_message,
                    'transformed_record_id': master_data_key,
                    'transformed_record_version': master_version_id,
                    'transformed_at': pendulum.now().to_iso8601_string(),
                    'finished_at': pendulum.now().to_iso8601_string(),
                    'status': SyncRecordStatus.COMPLETED.value,
            })
            if fetch_event_obj.attributes.event_source in [EventSource.import_record_api]:
                continue

            destinations = get_active_destinations(connection, object_type)

            # If destination ids are provided, use them to filter destinations
            if fetch_event_obj.attributes.destination_ids and fetch_event_obj.attributes.event_source == EventSource.sync_record_api:
                destinations = [destination for destination in destinations if
                                destination['id'] in fetch_event_obj.attributes.destination_ids]

            for destination in destinations:
                # Get mapping info
                mapping_data = MappingAttributeTypeIndex.get_mapping_attribute(destination['id'], object_type)
                mappings = mapping_data.attributes_dict.get('mappings', []) if mapping_data else []

                destination_instance = get_connection_by_id(destination['id'])
                destination_sync_record_obj = SyncRecordModel.put(key, destination['id'], {
                    'record_type': object_type,
                    'response_message': None,
                    'response': None,
                    'remote_record_id': None,
                    'fetch_event_id': fetch_event_id,
                    'channel': destination['type'],
                    'raw_record_version': version_id,
                    'company_id': company_id,
                    'fetched_at': record['eventTime'],
                    'standard_source_data': standard_data,
                    'is_source': False,
                })
                try:
                    destination_data = transform_to_destination_format(master_data, destination['type'], object_type,
                                                                       destination_instance.attributes_dict, mappings)
                except Exception as e:
                    destination_sync_record_obj.update({
                        'response_message': f"Transform failed: {str(e)}",
                        'transformed_at': pendulum.now().to_iso8601_string(),
                        'finished_at': pendulum.now().to_iso8601_string(),
                        'status': SyncRecordStatus.ERROR.value,
                    })
                    raise
                if destination_data:
                    destination_key = generate_s3_key(connection_id, action_type, object_type, destination['type'])

                    destination_version_id = store_transformed_data(TRANSFORMED_DATA_BUCKET, destination_key,
                                                                    destination_data)
                    updated_destination_sync_record = {
                        'transformed_record_id': destination_key,
                        'transformed_record_version': destination_version_id,
                        'transformed_at': pendulum.now().to_iso8601_string(),
                        'status': SyncRecordStatus.TRANSFORMED.value,
                    }

                    opti_published = destination_data.pop('opti_published', True)

                    if not opti_published:
                        updated_destination_sync_record.update({
                            'remote_record_id': None,
                            'response': {'status': 'skipped',
                                         'error_message': 'Publish skipped'},
                            'response_message': f"Publish skipped",
                            'status': SyncRecordStatus.SKIPPED.value,
                            'finished_at': pendulum.now().to_iso8601_string()
                        })
                        destination_sync_record_obj.update(updated_destination_sync_record)
                    else:
                        destination_sync_record_obj.update(updated_destination_sync_record)
                        enqueue_publish_message(connection_id, destination['id'], key, destination_key,
                                                destination_version_id, version_id, object_type, fetch_event_id)
                else:
                    now = pendulum.now().to_iso8601_string()
                    destination_sync_record_obj.update({
                        'transformed_record_id': None,
                        'transformed_record_version': None,
                        'transformed_at': now,
                        'finished_at': now,
                        'response': {
                            'status': 'skipped',
                            'error_message': 'Empty data'
                        },
                        'status': SyncRecordStatus.SKIPPED.value,
                        'response_message': f"Publish skipped: Empty data",
                    })

        except Exception as e:
            logger.error(f"Error processing record {key}: {str(e)}")


def transform_to_destination_format(master_data, destination_type, object_type, connection_settings, mappings):
    transformer = registry.get_destination_transformer(destination_type, object_type, connection_settings)
    if not transformer:
        raise ValueError(f"No transformer found for destination: {destination_type}, object_type: {object_type}")

    return transformer.transform(master_data, object_type, mappings, is_source=False)
