import json
import os

import boto3
import botocore
import botocore.exceptions
import requests
from nolicore.utils.exceptions import BadRequest, UnauthorizedExp, NotFoundRequest, UnknownExp

from helpers.index_dynamo_table import table_mappings
from helpers.lambda_client import lambda_client_invoke_async
from models.common.status import AccountInitStatus

client = boto3.client('cognito-idp', region_name='ap-southeast-1')

ENV = os.getenv("ENV")
SERVICE = os.getenv("SERVICE")
CLIENT_ID = os.environ['APP_CLIENT_ID']
USER_POOL_ID = os.environ['USER_POOL_ID']
ADMIN_ACCOUNT = 'onexapis_admin'
ADMIN_PASSWORD = 'Admin@123'


def login_helper(username, password, is_admin=False):
    try:
        auth_response = client.initiate_auth(
            AuthFlow='USER_PASSWORD_AUTH',
            AuthParameters={
                'USERNAME': ADMIN_ACCOUNT if is_admin else username,
                'PASSWORD': ADMIN_PASSWORD if is_admin else password
            },
            ClientId=CLIENT_ID
        )
        if auth_response.get('ChallengeName') == 'NEW_PASSWORD_REQUIRED':
            return auth_response
        return auth_response['AuthenticationResult']

    except botocore.exceptions.ClientError as e:
        if 'UserNotConfirmedException' in str(e):
            exp = UnauthorizedExp('User is not confirmed')
            exp.error_code = 'UserNotConfirmed'
        else:
            exp = UnauthorizedExp('Incorrect username or password.')
            exp.error_code = 'InvalidCredential'
        raise exp


def update_user_by_api(token, access_token, username, new_attributes):
    # Check if username is not None and is a string
    if not isinstance(username, str):
        raise ValueError("User name must be a non-empty string.")
    url = f'https://admin.onexapis.com/admin/users/{username}?access_token={access_token}'
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        response = requests.post(url, headers=headers, json=new_attributes)
        if response.status_code == 200:
            return response.json()
        else:
            # If the request failed, raise an exception
            raise Exception('Failed to update user attributes: ' + response.json()['message'])
    except Exception as e:
        raise Exception(f'Failed to update user attributes: {e}')


def get_user_info_by_client(access_token):
    try:
        user_attributes = client.get_user(
            AccessToken=access_token
        )
        return user_attributes
    except botocore.exceptions.ClientError as e:
        handle_cognito_error(e)


def get_user_info_by_username_api(token, access_token, username):
    # Check if username is not None and is a string
    if not isinstance(username, str):
        raise ValueError("User name must be a non-empty string.")
    url = f'https://admin.onexapis.com/admin/users/{username}?access_token={access_token}'
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            # If the request failed, raise an exception
            raise Exception(response.json()['message'])
    except Exception as e:
        raise Exception(e)


def get_user_info_by_sub_api(token, access_token, sub):
    # Check if username is not None and is a string
    if not isinstance(sub, str):
        raise ValueError("Sub must be a non-empty string.")
    url = f'https://admin.onexapis.com/admin/users/by_sub/{sub}?access_token={access_token}'
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            # If the request failed, raise an exception
            raise Exception(response.json()['message'])
    except Exception as e:
        raise Exception(e)


def register_api(register_data):
    url = f'https://admin.onexapis.com/auth/register'
    headers = {
        'Content-Type': 'application/json'
    }
    try:
        response = requests.post(url, headers=headers, json=register_data)

        if response.status_code == 200:
            return response.json()
        else:
            # If the request failed, raise an exception
            raise Exception(response.json()['message'])
    except Exception as e:
        raise Exception(e)


def confirm_email_api(username, code):
    url = f'https://admin.onexapis.com/auth/confirm'
    headers = {
        'Content-Type': 'application/json'
    }
    try:
        response = requests.post(url, headers=headers, json={"username": username, "code": code})

        if response.status_code == 200:
            return response.json()
        else:
            # If the request failed, raise an exception
            raise Exception(response.json()['message'])
    except Exception as e:
        raise Exception(e)


def resend_code_api(username):
    url = f'https://admin.onexapis.com/auth/resendCode'
    headers = {
        'Content-Type': 'application/json'
    }
    try:
        response = requests.post(url, headers=headers, json={"username": username})

        if response.status_code == 200:
            return response.json()
        else:
            # If the request failed, raise an exception
            raise Exception(response.json()['message'])
    except Exception as e:
        raise Exception(e)


def forgot_api(username):
    url = f'https://admin.onexapis.com/auth/forgot'
    headers = {
        'Content-Type': 'application/json'
    }
    try:
        response = requests.post(url, headers=headers, json={"username": username})

        if response.status_code == 200:
            return response.json()
        else:
            # If the request failed, raise an exception
            raise Exception(response.json()['message'])
    except Exception as e:
        raise Exception(e)


def invoke_init_account(access_token, token, username):
    user_attributes = get_user_info_by_client(access_token)
    user_attributes_obj = {item['Name']: item['Value'] for item in user_attributes['UserAttributes']}
    role = user_attributes_obj.get('custom:role', None)
    if role != 'Admin':
        return user_attributes

    is_init_account = user_attributes_obj.get('custom:is_init', None)

    # If account initialization is pending or not specified, proceed with initialization
    if is_init_account == AccountInitStatus.PENDING.value or is_init_account is None:
        payload = {
            'access_token': access_token,
            'token': token,
            'username': username,
        }
        lambda_client_invoke_async(payload, function_name=f'optiwarehouse-auths-service-{ENV}-initAccountHandle')
        updated_attributes = {
            **user_attributes_obj,
            'custom:is_init': AccountInitStatus.RUNNING.value,
        }
        update_user_by_api(token, access_token, username, updated_attributes)
        user_attributes = get_user_info_by_client(access_token)
    return user_attributes


def init_account(username, access_token, token):
    user_attributes = get_user_info_by_client(access_token)
    user_attributes_obj = {'Username': user_attributes['Username']}
    for item in user_attributes['UserAttributes']:
        user_attributes_obj[item['Name']] = item['Value']
    initialized_data = {}
    try:
        # Initialize data for each table if necessary
        for table, model in table_mappings.items():
            if hasattr(model, 'initialize_data'):
                model.initialize_data(user_attributes_obj, initialized_data)
    except Exception as e:
        raise BadRequest(e)
    finally:
        # Set the account initialization status to DONE
        updated_attributes = {
            **user_attributes_obj,
            'custom:is_init': AccountInitStatus.DONE.value,
        }
        update_user_by_api(token, access_token, username, updated_attributes)


def create_or_update_zalo_user(zalo_user_info, company_id, phone_number):
    zalo_user_name = zalo_user_info.get('name')
    zalo_user_id = zalo_user_info.get('id')
    zalo_user_picture = zalo_user_info.get('avatar')
    if zalo_user_id is None:
        raise Exception(f'zalo_user_info must have id')
    username = f"zalo_{zalo_user_id}"
    try:
        response = login_helper("", "", is_admin=True)
        token = response['IdToken']
        access_token = response['AccessToken']
        user_attributes = get_user_info_by_username_api(token, access_token, username)
        user_attributes_obj = {item['Name']: item['Value'] for item in user_attributes['UserAttributes']}
        updated_attributes = {
            **user_attributes_obj,
            "name": zalo_user_name,
            "picture": zalo_user_picture,
            "company_id": company_id,
        }
        # User exists, update attributes
        update_user_by_api(token, access_token, username, updated_attributes)
        return username

    except Exception as e:
        error_info = json.loads(str(e))
        error_message = error_info.get("message", "Unknown error")
        if error_message == f"User with id {username} not found.":

            # User doesn't exist, create new user
            response = login_helper("", "", is_admin=True)
            token = response['IdToken']
            access_token = response['AccessToken']

            default_pass = "Admin@123!"
            url = f'https://admin.onexapis.com/admin/users?access_token={access_token}'
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            new_account = {
                'company_id': company_id,
                'username': username,
                'password': default_pass,
                'name': zalo_user_name,
                'picture': zalo_user_picture,
                'phone_number': f'+{phone_number}',
                'is_customer': True,
                "zalo_user_id": zalo_user_id,
            }
            response = requests.post(url, headers=headers, json=new_account)

            if response.status_code == 200:
                data = response.json()
                if data['User']['UserStatus'] == 'FORCE_CHANGE_PASSWORD':
                    auth_response = client.initiate_auth(
                        AuthFlow='USER_PASSWORD_AUTH',
                        AuthParameters={
                            'USERNAME': username,
                            'PASSWORD': default_pass
                        },
                        ClientId=CLIENT_ID
                    )
                    # Check if a new password is required
                    if auth_response.get('ChallengeName') == 'NEW_PASSWORD_REQUIRED':
                        client.respond_to_auth_challenge(
                            ClientId=CLIENT_ID,
                            ChallengeName='NEW_PASSWORD_REQUIRED',
                            ChallengeResponses={
                                'USERNAME': username,
                                'NEW_PASSWORD': default_pass
                            },
                            Session=auth_response['Session']
                        )
                return username
            else:
                # If the request failed, raise an exception
                raise Exception('Failed to create user: ' + response.json()['message'])


def format_user_attributes(data, company_id, has_sub=True):
    is_admin = data.get('is_admin', False)
    is_customer = data.get('is_customer', False)

    sub = data.get('sub')
    zalo_user_id = data.get('zalo_user_id')
    email = data.get('email')
    address = data.get('address')
    birthdate = data.get('birthdate')
    picture = data.get('picture')
    name = data.get('name')
    phone_number = data.get('phone_number')
    given_name = data.get('given_name')
    family_name = data.get('family_name')
    middle_name = data.get('middle_name')
    nickname = data.get('nickname')
    preferred_username = data.get('preferred_username')
    profile = data.get('profile')
    website = data.get('website')
    gender = data.get('gender')
    locale = data.get('locale')
    updated_at = data.get('updated_at')
    zoneinfo = data.get('zoneinfo')
    is_init = data.get('custom:is_init')
    role = data.get('custom:role')
    custom_company_id = data.get('custom:company_id')
    plan_id = data.get('custom:plan', "FREE")
    if custom_company_id:
        company_id = custom_company_id
    if role is None:
        role = "Admin" if is_admin or company_id is None else "Customer" if is_customer else "User"

    user_attributes = [
        {
            'Name': 'custom:role',
            'Value': role
        },
    ]

    # Add all other attributes if they exist
    attributes_to_add = [
        ('custom:company_id', company_id),
        ('custom:plan', plan_id),
        ('custom:zalo_user_id', zalo_user_id),
        ('custom:is_init', is_init),
        ('email', email),
        ('address', address),
        ('birthdate', birthdate),
        ('picture', picture),
        ('name', name),
        ('phone_number', phone_number),
        ('given_name', given_name),
        ('family_name', family_name),
        ('middle_name', middle_name),
        ('nickname', nickname),
        ('preferred_username', preferred_username),
        ('profile', profile),
        ('website', website),
        ('gender', gender),
        ('locale', locale),
        ('updated_at', updated_at),
        ('zoneinfo', zoneinfo)
    ]

    if has_sub:
        attributes_to_add.append(('sub', sub))

    for attr_name, attr_value in attributes_to_add:
        if attr_value is not None:
            if not isinstance(attr_value, str):
                raise ValueError(f"Attribute '{attr_name}' must be a string. Got {type(attr_value).__name__} instead.")
            user_attributes.append({
                'Name': attr_name,
                'Value': attr_value
            })

    return user_attributes


def get_user_by_sub(user_id):
    try:
        # Get user details using sub
        response = client.admin_get_user(
            UserPoolId=USER_POOL_ID,
            Username=user_id
        )
        return response
    except client.exceptions.UserNotFoundException:
        raise NotFoundRequest(f"User with id {user_id} not found.")


def handle_cognito_error(error):
    """
    Xử lý các lỗi từ Cognito API bằng cách trích xuất message gốc từ AWS
    Args:
        error: Lỗi từ botocore.exceptions.ClientError
    """
    error_message = str(error)

    # Lấy error code từ error message
    error_code = None
    if '(' in error_message and ')' in error_message:
        error_code = error_message[error_message.find('(') + 1:error_message.find(')')]

    # Parse message từ error string
    # Format: "An error occurred (ErrorCode) when calling the Operation operation: Actual message"
    if ': ' in error_message:
        error_message = error_message.split(': ')[-1]

    # Map exception class dựa vào error code
    exception_mappings = {
        'NotAuthorizedException': UnauthorizedExp,
        'UserNotConfirmedException': UnauthorizedExp,
        'ExpiredTokenException': UnauthorizedExp,
        'UserNotFoundException': NotFoundRequest,
        'UsernameExistsException': UnauthorizedExp,
        'AliasExistsException': UnauthorizedExp,
        'InvalidPasswordException': BadRequest,
        'PasswordResetRequiredException': UnauthorizedExp,
        'InvalidParameterException': BadRequest,
        'CodeMismatchException': UnauthorizedExp,
        'ExpiredCodeException': UnauthorizedExp,
        'CodeDeliveryFailureException': UnauthorizedExp,
        'LimitExceededException': UnauthorizedExp,
        'TooManyRequestsException': UnauthorizedExp,
        'InternalErrorException': UnknownExp
    }

    # Lấy exception class phù hợp hoặc mặc định là UnknownExp
    exception_class = exception_mappings.get(error_code, UnknownExp)

    # Tạo và raise exception
    exp = exception_class(error_message)
    exp.error_code = error_code
    raise exp
