import os
import time

from botocore.exceptions import ClientError

environment = 'staging'  # prod|staging|local

os.environ['LOG_LEVEL'] = '20'
os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
os.environ['USER_POOL_ID'] = 'ap-southeast-1_Tmn6Tbm0H'
os.environ['APP_CLIENT_ID'] = '3fh2s28e3g8l23068d6j573l9a'
os.environ['ELASTIC_SEARCH_HOST'] = 'https://**************:9200'
os.environ['ELASTIC_SEARCH_USERNAME'] = 'elastic'
os.environ['ELASTIC_SEARCH_PASSWORD'] = 'OneXAPIES999'
os.environ['RESOURCE_SERVICE'] = 'optiwarehouse-resources-services'
os.environ['SERVICE'] = 'optiwarehouse-resources-services'
os.environ['ONEXAPIS'] = 'https://api-staging.onexapis.com'
os.environ['HARAVAN_API'] = 'https://haravan.onexapis.com'
os.environ['FETCH_QUEUE_URL'] = 'https://haravan.onexapis.com'
os.environ['TRANSFORM_QUEUE_URL'] = 'https://haravan.onexapis.com'
os.environ['PUBLISH_QUEUE_URL'] = 'https://haravan.onexapis.com'
os.environ['RAW_DATA_BUCKET'] = f'optiwarehouse-flow-service-{environment}-raw-data'
os.environ['TRANSFORMED_DATA_BUCKET'] = f'optiwarehouse-flow-service-{environment}-transformed-data'

if environment == 'prod':
    # prod
    os.environ['BUCKET_NAME'] = 'optiwarehouse-prod'
    os.environ['AWS_PROFILE'] = 'optiwarehouse-prod'
    os.environ['ENV'] = 'prod'
    os.environ['BASE_URL'] = 'https://api.optiwarehouse.com'
if environment == 'staging':
    # staging
    os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
    os.environ['AWS_PROFILE'] = 'noliwms_staging'
    os.environ['ENV'] = 'dev'
    os.environ['BASE_URL'] = 'https://api-staging.optiwarehouse.com'

if environment == 'local':
    # local
    os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
    os.environ['AWS_PROFILE'] = 'noliwms_staging'
    os.environ['ENV'] = 'local'


from boto3.dynamodb.conditions import Key
from nolicore.adapters.db.aws import Dynamo
from models.accounts.permission import PermissionModel
from models.accounts.roles import RoleModel
from models.actor.customer import CustomerModel
from models.actor.customer_group import CustomerGroupModel
from models.actor.supplier import SupplierModel
from models.blog.blog import BlogModel
from models.blog.blog_category import BlogCategoryModel
from models.blog.comment import CommentModel
from models.filter.filter import FilterModel
from models.finance.account import AccountModel
from models.finance.payment_method import PaymentMethodModel
from models.finance.transaction import TransactionModel
from models.finance.voucher import TransactionVoucherModel
from models.history.history import HistoryModel
from models.imports.import_record import ImportRecordModel
from models.imports.imports import ImportModel
from models.inventory.inventory_item import InventoryItemModel
from models.inventory.inventory_transaction import InventoryTransactionModel
from models.inventory.location import LocationModel
from models.inventory.stock_adjustment import StockAdjustmentModel
from models.inventory.stock_relocate import StockRelocateModel
from models.logistic.shipping_provider import ShippingProviderModel
from models.notification.pos_socket import PosSocketModel
from models.notification.screen_socket import ScreenSocketModel
from models.order.order import OrderModel
from models.order.order_return import ReturnModel
from models.order.package import PackageModel
from models.pos.draft_order import DraftOrderModel
from models.pos.shift_pay_line import ShiftPayLineItemModel
from models.price.price_group import PriceGroupModel
from models.product.brand import BrandModel
from models.product.category import CategoryModel
from models.product.product import ProductModel
from models.product.unit import UnitModel
from models.product.variant import VariantModel
from models.promotion.discount import DiscountModel
from models.promotion.used_voucher import UsedVoucherModel
from models.promotion.voucher import VoucherModel
from models.promotion.voucher_code import VoucherCodeModel
from models.purchase_order.purchase_order import PurchaseOrderModel
from models.sale_channel.sale_channel import SaleChannelModel
from models.sale_channel.store import StoreModel
from models.pos.shift import ShiftModel
from models.pos.terminal import TerminalModel
from models.vn_public.district import DistrictModel
from models.vn_public.province import ProvinceModel
from models.vn_public.ward import WardModel
from models.zalo_app.wishlist import WishlistModel
from models.loyalty.loyalty_program import LoyaltyProgramModel
from models.loyalty.reward_program import RewardProgramModel

table_mappings = {
    # 'location': LocationModel,
    # 'account': AccountModel,
    # 'order': OrderModel,
    # 'returnOrder': ReturnModel,
    # 'product': ProductModel,
    # 'imports': ImportModel,
    # 'importRecord': ImportRecordModel,
    # 'customer': CustomerModel,
    # 'package': PackageModel,
    # 'purchaseOrder': PurchaseOrderModel,
    # 'variant': VariantModel,
    # 'priceGroup': PriceGroupModel,
    # 'brand': BrandModel,
    # 'category': CategoryModel,
    # 'filter': FilterModel,
    # 'voucher': VoucherModel,
    # 'transaction': TransactionModel,
    # 'transactionVoucher': TransactionVoucherModel,
    # 'paymentMethod': PaymentMethodModel,
    # 'saleChannel': SaleChannelModel,
    # 'store': StoreModel,
    # 'shippingProvider': ShippingProviderModel,
    # 'supplier': SupplierModel,
    # 'discount': DiscountModel,
    # 'wishlist': WishlistModel,
    # 'history': HistoryModel,
    # 'voucherCode': VoucherCodeModel,
    # 'usedVoucher': UsedVoucherModel,
    # 'customerGroup': CustomerGroupModel,
    # "role": RoleModel,
    # 'permission': PermissionModel,
    # 'blog': BlogModel,
    # 'blogCategory': BlogCategoryModel,
    # 'comment': CommentModel,
    # 'province': ProvinceModel,
    # 'district': DistrictModel,
    # 'ward': WardModel,
    # 'posSocket': PosSocketModel,
    # 'screenSocket': ScreenSocketModel,
    # 'shift': ShiftModel,
    # 'shiftPayLineItem': ShiftPayLineItemModel,
    # 'terminal': TerminalModel,
    # 'draftOrder': DraftOrderModel,
    # 'stockAdjustment': StockAdjustmentModel,
    # 'stockRelocate': StockRelocateModel,
    # 'inventoryItem': InventoryItemModel,
    # 'inventoryTransaction': InventoryTransactionModel,
    # 'unit': UnitModel,
    # 'loyaltyProgram': LoyaltyProgramModel,
    # 'rewardProgram': RewardProgramModel,
}

RESOURCE_SERVICE = "optiwarehouse-resources-services"
company_id = "4c8e6154-fba3-4837-909b-8a50dff16a2b"  # baababy


def batch_delete_with_retry(model, items, items_deleted, max_retries=7):
    old_items_deleted = items_deleted
    table_data = Dynamo(model.table_name)

    for attempt in range(max_retries):
        try:
            with table_data.table.batch_writer() as batch:
                for item in items:
                    key = {model.key__id: item[model.key__id]}
                    key.update({kr: item[kr] for kr in model.key__ranges})
                    batch.delete_item(Key=key)
                    items_deleted += 1
                    print(f'Delete item {items_deleted} of table {model.table_name}')
        except ClientError as e:
            print(f'Reset Delete item {items_deleted} of table {model.table_name}')
            items_deleted = old_items_deleted
            if e.response['Error']['Code'] == 'ProvisionedThroughputExceededException':
                time.sleep(2 ** attempt)  # Exponential backoff
            else:
                raise
    return items_deleted


def get_and_delete(model, filters, limit, items_deleted):
    table_data = Dynamo(model.table_name)

    p = {
        'TableName': model.table_name,
        'Limit': limit
    }

    filter_params = None
    for att, value in filters.items():
        filter_params = filter_params & Key(att).eq(value) if filter_params else Key(att).eq(value)
    if filter_params:
        p['FilterExpression'] = filter_params

    response = table_data.table.scan(**p)
    items = response.get('Items', [])
    if len(items) == 0:
        return

    items_deleted = batch_delete_with_retry(model, items, items_deleted)

    if 'LastEvaluatedKey' in response:
        get_and_delete(model, filters, limit, items_deleted)


for table, model in table_mappings.items():
    print(f'Deleting {model.table_name}')
    if not model.key__ranges:
        print(f'{model.table_name} does not have any key ranges')
        continue
    items_deleted = 0
    get_and_delete(model, {"company_id": company_id}, 300, items_deleted)
