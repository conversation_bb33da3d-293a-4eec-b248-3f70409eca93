from dataclasses import dataclass, field

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema

from models.integration.connection import ConnectionModel
from ..connection_base import ConnectionAttributes, ConnectionSchema


class SquarespaceSettingsSchema(AttributesSchema):
    api_key = fields.Str(required=True)
    base_url = fields.Url(default="https://api.squarespace.com")


@dataclass
class SquarespaceSettings:
    api_key: str = None
    base_url: str = "https://api.squarespace.com"


@dataclass
class SquarespaceConnectionAttributes(ConnectionAttributes):
    channel_name: str = field(default='squarespace')
    settings: SquarespaceSettings = field(default_factory=SquarespaceSettings)


class SquarespaceConnectionSchema(ConnectionSchema):
    settings = fields.Nested(SquarespaceSettingsSchema(SquarespaceSettings))


class SquarespaceConnection(ConnectionModel):
    attributes: SquarespaceConnectionAttributes = None
    attributes_schema = SquarespaceConnectionSchema
    attributes_class = SquarespaceConnectionAttributes
