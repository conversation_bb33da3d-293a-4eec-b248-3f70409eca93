from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from models.common.settings import NameSettings
from dataclasses import dataclass

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


@dataclass
class Settings(BasicAttributes):
    company_id: str = None
    setting_name: str = None
    setting_value: dict = None


class SettingsSchema(BasicAttributesSchema):
    company_id = fields.UUID(required=True)
    setting_name = EnumField(NameSettings, allow_none=True, default=NameSettings.shop_info.value)
    setting_value = fields.Dict(required=True)


class SettingsModel(BasicModel):
    key__id = 'company_id'
    key__ranges = ['setting_name']
    table_name = 'settings'
    attributes: Settings
    attributes_schema = SettingsSchema
    attributes_class = Settings

    query_mapping = {
        'query': ['name'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }
