org: lebinhnguyen1991
app: ${env:SERVICE}
useDotenv: true
service: ${env:SERVICE}

params:
  default:
    user_pool_id: ap-southeast-1_Tmn6Tbm0H
    app_client_id: 3fh2s28e3g8l23068d6j573l9a
    bucket_name: optiwarehouse-staging
    es_host: https://**************:9200
    es_username: elastic
    es_password: OneXAPIES999
    profile: noliwms_staging
    onexapis: https://api-staging.onexapis.com
    haravan_api: https://haravan-staging.onexapis.com
    onexbots_api: https://dev-agent.onexbots.com
    shopify_client_id: fe5b9b2d0ac99b3dc23ab2362e54133b
    shopify_client_secret: 281bff9d1eb0f930d70b0b87e7b23067
    tiktok_app_key: 6fmsibjotg0ie
    tiktok_app_secret: 20c754183c10ce6687b3012507c598d57717ce65
    tiktok_service_id: "7485312862710859525"
    env: dev
  prod:
    user_pool_id: ap-southeast-1_Tmn6Tbm0H
    app_client_id: 3fh2s28e3g8l23068d6j573l9a
    bucket_name: optiwarehouse-prod
    es_host: https://**************:9200
    es_username: elastic
    es_password: OneXAPIES999
    profile: optiwarehouse-prod
    onexapis: https://api.onexapis.com
    haravan_api: https://haravan.onexapis.com
    onexbots_api: https://agent.onexbots.com
    shopify_client_id: fe5b9b2d0ac99b3dc23ab2362e54133b
    shopify_client_secret: 281bff9d1eb0f930d70b0b87e7b23067
    tiktok_app_key: 6fmsibjotg0ie
    tiktok_app_secret: 20c754183c10ce6687b3012507c598d57717ce65
    tiktok_service_id: "7485312862710859525"
    env: prod

provider:
  name: aws
  runtime: python3.9
  region: ap-southeast-1
  profile: ${env:PROFILE}
  stage: ${opt:stage, env:STAGE, 'dev'}
  memorySize: 256
  timeout: ${env:TIMEOUT}
  deploymentBucket:
    name: ${self:custom.deploymentBucketName}
  environment:
    LOG_LEVEL: 40
    ENV: ${param:env}
    USER_POOL_ID: ${param:user_pool_id}
    APP_CLIENT_ID: ${param:app_client_id}
    BUCKET_NAME: ${param:bucket_name}
    ONEXAPIS: ${param:onexapis}
    HARAVAN_API: ${param:haravan_api}
    ONEXBOTS_API: ${param:onexbots_api}
    SHOPIFY_CLIENT_ID: ${param:shopify_client_id}
    SHOPIFY_CLIENT_SECRET: ${param:shopify_client_secret}
    ELASTIC_SEARCH_HOST: ${param:es_host}
    ELASTIC_SEARCH_USERNAME: ${param:es_username}
    ELASTIC_SEARCH_PASSWORD: ${param:es_password}
    TIKTOK_APP_KEY: ${param:tiktok_app_key}
    TIKTOK_APP_SECRET: ${param:tiktok_app_secret}
    TIKTOK_SERVICE_ID: ${param:tiktok_service_id}
    PYTHONWARNINGS: "ignore:Unverified HTTPS request"
    FETCH_QUEUE_URL:
      Fn::GetAtt: [ FetchQueue, QueueUrl ]
    RAW_DATA_BUCKET: ${self:service}-${self:provider.stage}-raw-data
    TRANSFORMED_DATA_BUCKET: ${self:service}-${self:provider.stage}-transformed-data
    TRANSFORM_QUEUE_URL:
      Fn::GetAtt: [ TransformQueue, QueueUrl ]
    PUBLISH_QUEUE_URL:
      Fn::GetAtt: [ PublishQueue, QueueUrl ]
    PUBLISH_DEAD_LETTER_QUEUE_URL:
      Fn::GetAtt: [ PublishDeadLetterQueue, QueueUrl ]
    SYNC_MAPPING_QUEUE_URL:
      Fn::GetAtt: [ SyncMappingQueue, QueueUrl ]

  iam: ${file(yml/iam.yml)}
  layers:
    - Ref: PythonRequirementsLambdaLayer
    - Ref: IntegrationLibsLambdaLayer

  httpApi:
    cors: true
    authorizers:
      optiAuthorizer:
        name: optiAuthorizer
        type: jwt
        identitySource: "$request.header.Authorization"
        issuerUrl: "https://cognito-idp.${opt:region, self:provider.region}.amazonaws.com/${opt:aws.cognito.userPoolId, self:provider.environment.USER_POOL_ID}"
        audience:
          - "${opt:aws.cognito.userPoolClientId, self:provider.environment.APP_CLIENT_ID}"

layers:
  IntegrationLibs:
    path: ../libs

functions:
  - ${file(yml/functions/events.yml)}
  - ${file(yml/functions/webhook.yml)}
  - ${file(yml/functions/connection.yml)}
  - ${file(yml/functions/dashboard.yml)}
  - ${file(yml/functions/mapping.yml)}
  - ${file(yml/functions/mapping_attribute.yml)}
  - ${file(yml/functions/sync_flow.yml)}

plugins:
  - serverless-python-requirements
  - serverless-dotenv-plugin
  - serverless-prune-plugin
  - serverless-dynamodb-autoscaling
package:
  patterns:
    - src/**
    - "!.git/**"
    - "!node_modules/**"

custom: ${file(yml/custom.yml)}
resources: ${file(yml/resources.yml)}
