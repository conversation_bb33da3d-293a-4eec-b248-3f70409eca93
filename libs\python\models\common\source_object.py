from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema


@dataclass
class SourceObject(Attributes):
    id: str
    channel_name: str
    origin: str = None
    origin_id: str = None
    name: str = None
    logo: str = None


class SourceObjectSchema(AttributesSchema):
    id = fields.Str(required=True)
    channel_name = fields.Str(required=True)
    origin = fields.Str(allow_none=True)
    origin_id = fields.Str(allow_none=True)
    name = fields.Str(allow_none=True)
    logo = fields.Str(allow_none=True)
