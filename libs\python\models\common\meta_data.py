from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema


@dataclass
class MetaData(Attributes):
    title: str = None
    description: str = None
    keywords: str = None
    author: str = None
    image: str = None
    url: str = None


class MetaDataSchema(AttributesSchema):
    title = fields.Str(allow_none=True)
    description = fields.Str(allow_none=True)
    keywords = fields.Str(allow_none=True)
    author = fields.Str(allow_none=True)
    image = fields.Str(allow_none=True)
    url = fields.Str(allow_none=True)
