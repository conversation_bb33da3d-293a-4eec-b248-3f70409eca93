import pendulum
from cachetools.func import ttl_cache

from models.integration.connection import ConnectionByCompany, ConnectionModel


# Function to get start and end dates based on time period or custom dates
def get_time_period(period, from_date=None, to_date=None):
    today_date = pendulum.today('UTC')
    if period == 'today':
        start_date = today_date
        end_date = today_date.add(days=1)
        comparison_start_date = start_date.subtract(days=1)
        comparison_end_date = start_date
    elif period == 'this_week':
        start_date = today_date.start_of('week')
        end_date = start_date.add(weeks=1)
        comparison_start_date = start_date.subtract(weeks=1)
        comparison_end_date = end_date.subtract(weeks=1)
    elif period == 'this_month':
        start_date = today_date.start_of('month')
        end_date = start_date.add(months=1)
        comparison_start_date = start_date.subtract(months=1)
        comparison_end_date = start_date
    elif period == 'this_year':
        start_date = today_date.start_of('year')
        end_date = start_date.add(years=1)
        comparison_start_date = start_date.subtract(years=1)
        comparison_end_date = start_date
    elif period == 'custom':
        if not from_date or not to_date:
            raise ValueError("Custom period requires 'from_date' and 'to_date'")
        start_date = pendulum.parse(from_date).date()
        end_date = pendulum.parse(to_date).date()
        comparison_start_date = start_date.subtract(days=(end_date - start_date).days)
        comparison_end_date = start_date
    else:
        raise ValueError(
            "Invalid period. Please choose from 'today', 'this_week', 'this_month', 'this_year', or 'custom'.")
    start_date = start_date.format("YYYY-MM-DD")
    end_date = end_date.format("YYYY-MM-DD")
    comparison_start_date = comparison_start_date.format("YYYY-MM-DD")
    comparison_end_date = comparison_end_date.format("YYYY-MM-DD")
    return start_date, end_date, comparison_start_date, comparison_end_date


@ttl_cache(ttl=300, maxsize=128)
def get_active_destinations(connection: ConnectionModel, object_type):
    fetch_actions_destination_ids = []
    company_id = str(connection.attributes.company_id)
    action_group = connection.attributes_dict['action_groups'].get(object_type, {})
    fetch_actions = action_group.get('fetch_actions', {})
    for action_name, action_data in fetch_actions.items():
        if not action_data.get('enabled', False):
            continue
        destinations = action_data.get('destinations', {})
        for destination_id, destination_data in destinations.items():
            if not destination_data.get('enable', False) or destination_id in fetch_actions_destination_ids:
                continue
            fetch_actions_destination_ids.append(destination_id)

    destination_connections = ConnectionByCompany.get_active_publish_actions_by_company(str(company_id))
    active_destinations = []

    for destination_connection in destination_connections:
        if destination_connection['id'] not in fetch_actions_destination_ids:
            continue
        if str(connection.attributes.id) == destination_connection['id']:
            continue
        action_group = destination_connection['action_groups'].get(object_type, {})
        publish_actions = action_group.get('publish_actions', {})

        for action_name, action_data in publish_actions.items():
            if action_data.get('enabled', False):
                active_destinations.append({
                    'id': destination_connection['id'],
                    'type': destination_connection['channel_name'],
                    'action_name': action_name,
                    'action_data': action_data
                })

    return active_destinations