from dataclasses import dataclass
from enum import Enum

import pendulum
from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import User, UserSchema


class AccountInitStatus(Enum):
    PENDING = 'PENDING'
    RUNNING = 'RUNNING'
    DONE = 'DONE'


class PaymentStatus(Enum):
    UNPAID = 'UNPAID'
    PARTIAL_PAID = 'PARTIAL_PAID'
    COD = 'COD'
    PENDING_ESCROW = 'PENDING_ESCROW'
    PAID = 'PAID'
    PARTIAL_REFUNDED = 'PARTIAL_REFUNDED'
    REFUNDED = 'REFUNDED'
    VOIDED = 'VOIDED'


class Priority(Enum):
    URGENT = 4
    HIGH = 3
    NORMAL = 2
    LOW = 1


class Status(Enum):
    DRAFT = 'DRAFT'
    PENDING = 'PENDING'  # chưa sẵn sàng
    BLOCKING = 'BLOCKING'  # chờ giải quyết / chờ xử lý

    AWAIT_PACKING = 'AWAIT_PACKING'  # chờ gói
    PACKING = 'PACKING'  # đang gói
    PARTIAL_PACKING = 'PARTIAL_PACKING'  # đang gói 1 phần

    READY = 'READY'  # đã gói xong hoặc là chờ gửi
    PARTIAL_READY = 'PARTIAL_READY'  # đã gói xong 1 phần

    IN_CANCEL = 'IN_CANCEL'  # chờ xử lý huỷ đơn
    CANCELLED = 'CANCELLED'  # đã huỷ

    SHIPPING = 'SHIPPING'  # đang ship
    PARTIAL_SHIPPING = 'PARTIAL_SHIPPING'  # đang ship 1 phần

    DELIVERED = 'DELIVERED'  # đã giao
    PARTIAL_DELIVERED = 'PARTIAL_DELIVERED'  # đã giao 1 phần

    RETURNING = 'RETURNING'  # đang hoàn về
    PARTIAL_RETURNING = 'PARTIAL_RETURNING'  # đang hoàn 1 phần

    RETURNED = 'RETURNED'  # đã hoàn
    PARTIAL_RETURNED = 'PARTIAL_RETURNED'  # đã hoàn 1 phần

    PARTIAL_COMPLETED = 'PARTIAL_COMPLETED'  # đã hoàn thành 1 phần
    COMPLETED = 'COMPLETED'  # đã hoàn thành
    SYNC_ERROR = 'SYNC_ERROR'  # lỗi đồng bộ
    IMPORTED = 'IMPORTED'


class PurchaseOrderType(Enum):
    IMPORTED = 'IMPORTED'
    STANDARD = 'STANDARD'


class OrderType(Enum):
    POS = 'POS'
    IMPORTED = 'IMPORTED'
    ECOMMERCE = 'ECOMMERCE'


class ReturnReason(Enum):
    OTHER = 'OTHER'
    DEFECT = 'DEFECT'  # hàng lỗi
    DAMAGED = 'DAMAGED'  # hảng hỏng


class ReturnStatus(Enum):
    RECEIVED = 'RECEIVED'  # đã nhận
    NOT_RECEIVED = 'NOT_RECEIVED'  # chưa nhận
    CANCELLED = 'CANCELLED'  # đã huỷ


class ReturnType(Enum):
    IMPORTED = 'IMPORTED'
    STANDARD = 'STANDARD'


class StockAdjustmentStatus(Enum):
    AWAIT_CHECKING = 'AWAIT_CHECKING'
    BALANCED = 'BALANCED'
    DELETED = 'DELETED'
    IMPORTED = 'IMPORTED'


class StockAdjustmentReason(Enum):
    OTHER = 'OTHER'
    DAMAGED = 'DAMAGED'  # hư hỏng
    DEPRECIATION = 'DEPRECIATION'  # hao mòn
    RETURN = 'RETURN'  # trả hàng
    RELOCATE = 'RELOCATE'  # chuyển hàng
    PRODUCTION = 'PRODUCTION'  # trả hàng
    API_SYNC = 'API_SYNC'  # đồng bộ từ API


class StockRelocateStatus(Enum):
    AWAIT_DELIVERY = 'AWAIT_DELIVERY'
    DELIVERING = 'DELIVERING'
    RECEIVED = 'RECEIVED'
    CANCELLED = 'CANCELLED'


@dataclass
class History(Attributes):
    status: Status
    updated_at: fields.DateTime
    staff: User
    note: str = None


class HistorySchema(AttributesSchema):
    status = EnumField(Status, allow_none=True, default=Status.DRAFT)
    staff = fields.Nested(UserSchema(User), allow_none=True, default=None)
    updated_at = fields.DateTime(allow_none=True)
    note = fields.Str(allow_none=True)


@dataclass
class PaymentHistory(Attributes):
    status: PaymentStatus
    updated_at: fields.DateTime
    staff_id: str
    note: str = None


class PaymentHistorySchema(AttributesSchema):
    status = EnumField(PaymentStatus, allow_none=True, default=PaymentStatus.UNPAID)
    staff_id = fields.UUID(required=True)
    updated_at = fields.DateTime(allow_none=True)
    note = fields.Str(allow_none=True)


def update_payment_status(staff_id, order_obj, status: PaymentStatus, note=None, extras: dict = None):
    current = PaymentHistory(
        status=status,
        staff_id=staff_id or order_obj.attributes.staff_id,
        updated_at=pendulum.now(),
        note=note
    )
    histories = order_obj.attributes.payment_histories + [current] if order_obj.attributes.payment_histories else [
        current]

    update_payload = {
        'payment_status': status.value,
        'payment_histories': [PaymentHistorySchema(PaymentHistory).dump(history) for history in histories]
    }

    if extras:
        update_payload.update(extras)
    order_obj.update(update_payload)


def update_status(user, model_obj, status: Status, note=None, extras: dict = None, packing_staff=None,
                  prepared_request=False):
    """

    :param user:
    :param model_obj: OrderModel/PackageModel/PurchaseOrder object
    :param status:
    :param note:
    :param packing_staff:
    :param extras:
    :param prepared_request:
    """
    current = History(
        status=status,
        staff=user or model_obj.attributes_dict.get("user"),
        updated_at=pendulum.now(),
        note=note
    )
    histories = model_obj.attributes.histories + [current] if model_obj.attributes.histories else [current]
    update_payload = {
        'status': status.value,
        'histories': [HistorySchema(History).dump(history) for history in histories]
    }
    if packing_staff is not None:
        update_payload['packing_staff'] = packing_staff

    if extras:
        update_payload.update(extras)
    return model_obj.update(update_payload, user, prepared_request=prepared_request)
