from dataclasses import dataclass

from marshmallow import fields

from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


@dataclass
class TerminalAttributes(BasicAttributes):
    location_id: str = None
    name: str = None
    is_activate: bool = False


class TerminalAttributesSchema(BasicAttributesSchema):
    location_id = fields.Str(required=True)
    name = fields.Str(required=True)
    is_activate = fields.Bool(allow_none=True, default=False)


class TerminalModel(BasicModel):
    table_name = 'terminal'
    attributes: TerminalAttributes
    attributes_schema = TerminalAttributesSchema
    attributes_class = TerminalAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'location_id': 'query',
            'is_activate': 'boolean',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
    }

    @classmethod
    def terminal_count(cls, params):
        try:
            search_params = {
                "aggs": {
                    "terminal_count": {
                        "filter": {
                            "term":
                                params
                        }
                    }
                }
            }
            response = cls.report(search_params, service=RESOURCE_SERVICE)
            return response.body['aggregations']['terminal_count']['doc_count']
        except IndexError:
            return 0

    @classmethod
    def initialize_data(cls, user_attributes_obj, initialized_data):
        company_id = user_attributes_obj.get('custom:company_id')
        data = [
            {"name": "POS"}
        ]
        locations = initialized_data.get("location", [])
        location_id = locations[0]['id']
        init_data = [BasicAttributes.add_basic_attributes({**item, "location_id": location_id}, company_id) for item in
                     data]
        initialized_data[cls.table_name] = init_data
        cls.batch_add(init_data)
