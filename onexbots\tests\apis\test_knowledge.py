import unittest
from tests.base import BaseTestCase

from onexbots.src.apis.knowledge import (
    create_knowledge_from_text_api,
    create_knowledge_from_files_api,
    create_knowledge_from_urls_api,
    get_knowledge,
    list_knowledge,
    generate_upload_url,
    check_knowledge_status,
    update_knowledge_status,
    update_knowledge,
    delete_knowledge
)
from nolicore.utils.exceptions import BadRequest, NotFoundRequest
from models.bots.virtual_staff import VirtualStaffModel


class TestKnowledge(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    @classmethod
    def setUpClass(cls):
        super().setUpClass()


    def test_create_knowledge_from_url(self):
        """Test creating knowledge from URLs and updating staff knowledge_ids"""
        staff_id = "b370b708-4d7c-41d6-bfb2-609174574691"
        test_data = {"staff_id": staff_id, "urls": ["https://example.com1", "https://example.com2"]}
        event = self.create_lambda_event(body=test_data, is_authorized=True)
        context = self.create_lambda_context()
        response = create_knowledge_from_urls_api(event, context)
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Knowledge created successfully')
        self.assertIsNotNone(response['data'])
        # Check staff knowledge_ids
        staff = VirtualStaffModel.by_key({'company_id': self.company_id, 'id': staff_id})
        knowledge_ids = staff.attributes_dict.get('configuration', {}).get('knowledge_base', {}).get('knowledge_ids', [])
        created_ids = [k['id'] for k in response['data']]
        for kid in created_ids:
            self.assertIn(kid, knowledge_ids)

    def test_create_knowledge_from_text(self):
        """Test creating knowledge from text and updating staff knowledge_ids"""
        staff_id = "b370b708-4d7c-41d6-bfb2-609174574691"
        test_data = {"staff_id": staff_id, "texts": [{"knowledge_name": "tran duong thieu information", "content": "Tran Duong Thieu 21 tuoi"}]}
        event = self.create_lambda_event(body=test_data, is_authorized=True)
        context = self.create_lambda_context()
        response = create_knowledge_from_text_api(event, context)
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Knowledge created successfully')
        self.assertIsNotNone(response['data'])
        # Check staff knowledge_ids
        staff = VirtualStaffModel.by_key({'company_id': self.company_id, 'id': staff_id})
        knowledge_ids = staff.attributes_dict.get('configuration', {}).get('knowledge_base', {}).get('knowledge_ids', [])
        created_ids = [k['id'] for k in response['data']]
        for kid in created_ids:
            self.assertIn(kid, knowledge_ids)

    def test_create_knowledge_from_file(self):
        """Test creating knowledge from files and updating staff knowledge_ids"""
        staff_id = "b370b708-4d7c-41d6-bfb2-609174574691"
        test_data = {
            "staff_id": staff_id,
            "files": [
                {
                    "file_name": "test.txt",
                    "type": "text/plain",
                    "size": 1024,
                    "s3_key": "9e61d187-426a-45ec-914d-7aea8ca7d42d/knowledge/5962a800-a617-48af-abf9-60589655657e.url"
                }
            ]
        }
        event = self.create_lambda_event(body=test_data, is_authorized=True)
        context = self.create_lambda_context()
        response = create_knowledge_from_files_api(event, context)
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Knowledge created successfully')
        self.assertIsInstance(response['data'], list)
        # Check staff knowledge_ids
        staff = VirtualStaffModel.by_key({'company_id': self.company_id, 'id': staff_id})
        knowledge_ids = staff.attributes_dict.get('configuration', {}).get('knowledge_base', {}).get('knowledge_ids', [])
        created_ids = [k['id'] for k in response['data']]
        for kid in created_ids:
            self.assertIn(kid, knowledge_ids)

    def test_get_knowledge(self):
        """Test getting a knowledge by ID"""
        # First create a knowledge
        test_data = {
            "content": "Test knowledge content",
            "knowledge_name": "test_knowledge_for_get"
        }
        create_event = self.create_lambda_event(body=test_data, is_authorized=True)
        created_knowledge = create_knowledge_from_text_api(create_event, self.create_lambda_context())
        knowledge_id = created_knowledge['data']['id']

        # Test getting the created knowledge
        event = self.create_lambda_event(
            path_params={'id': knowledge_id}
        )
        response = get_knowledge(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Knowledge retrieved successfully')
        self.assertEqual(response['data']['id'], knowledge_id)

        # Test getting non-existent knowledge
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'}
        )
        with self.assertRaises(NotFoundRequest):
            get_knowledge(event, self.create_lambda_context())

    def test_list_knowledge(self):
        """Test listing knowledge"""
        # Create test knowledge

        event = self.create_lambda_event(
            query_params={"page": "0", "limit": "20"},
            is_authorized=True
        )
        response = list_knowledge(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Knowledge list retrieved successfully')
        self.assertIsInstance(response['data'], dict)
        self.assertIn('items', response['data'])

    def test_generate_upload_url(self):
        """Test generating upload URL for files"""
        # Test with valid file data
        test_data = {
            "files": [
                {
                    "file_name": "test.txt",
                    "type": "text/plain",
                    "size": 1024
                },
                {
                    "file_name": "test.pdf",
                    "type": "application/pdf",
                    "size": 15728649
                }
            ]
        }
        context = self.create_lambda_context()
        event = self.create_lambda_event(body=test_data, is_authorized=True)
        response = generate_upload_url(event, context)

        # Verify response structure
        self.assertIsInstance(response, list)
        self.assertEqual(len(response), 2)
        for url in response:
            self.assertIsInstance(url, dict)
            self.assertIn('url', url)
            self.assertIn('key', url)
            self.assertIn('bucket', url)

        # Test with missing required fields
        invalid_data = {
            "files": [
                {
                    "file_name": "test.txt",
                    # Missing type and size
                }
            ]
        }
        event = self.create_lambda_event(body=invalid_data, is_authorized=True)
        with self.assertRaises(BadRequest) as context:
            generate_upload_url(event)
        self.assertIn("Missing required field", str(context.exception))

        # Test with invalid file type
        invalid_type_data = {
            "files": [
                {
                    "file_name": "test.txt",
                    "type": "invalid/type",
                    "size": 1024
                }
            ]
        }
        event = self.create_lambda_event(body=invalid_type_data, is_authorized=True)
        with self.assertRaises(ValueError) as context:
            generate_upload_url(event)
        self.assertIn("Unsupported file type", str(context.exception))

        # Test with file size exceeding limit
        large_file_data = {
            "files": [
                {
                    "file_name": "test.txt",
                    "type": "text/plain",
                    "size": 16 * 1024 * 1024  # 16MB > 15MB limit
                }
            ]
        }
        event = self.create_lambda_event(body=large_file_data, is_authorized=True)
        with self.assertRaises(ValueError) as context:
            generate_upload_url(event)
        self.assertIn("File size exceeds limit", str(context.exception))

    def test_check_knowledge_status(self):
        """Test checking knowledge status"""
        # First create a knowledge

        # Test getting status of existing knowledge
        event = self.create_lambda_event(
            path_params={'id': "cdf308bb-eed7-4c07-96d9-79831433f859"},
            is_authorized=True
        )
        status = check_knowledge_status(event, self.create_lambda_context())
        self.assertEqual(status, "PENDING")  # Default status when created

        # Test getting status of non-existent knowledge
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'},
            is_authorized=True
        )
        with self.assertRaises(NotFoundRequest):
            check_knowledge_status(event)

    def test_update_knowledge_status(self):
        # Test updating to valid status
        update_data = {
            "status": "READY"
        }
        event = self.create_lambda_event(
            path_params={'id': "cdf308bb-eed7-4c07-96d9-79831433f859"},
            body=update_data,
            is_authorized=True
        )
        response = update_knowledge_status(event, self.create_lambda_context())

    def test_update_knowledge(self):

        # Test updating valid fields
        update_data = {
            "description": "updated_updated"
        }
        event = self.create_lambda_event(
            path_params={'id': "c218c38e-76be-4323-ae99-e49dd6c75a8e"},
            body=update_data,
            is_authorized=True
        )
        response = update_knowledge(event, self.create_lambda_context())


    def test_delete_knowledge(self):

        # Test successful deletion
        event = self.create_lambda_event(
            path_params={'id': "b66255da-596f-4237-97ca-285078e3b9b8"},
            is_authorized=True
        )
        response = delete_knowledge(event, self.create_lambda_context())

if __name__ == '__main__':
    unittest.main()
