from dataclasses import dataclass, field

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema

from models.integration.connection import ConnectionModel
from ..connection_types import ConnectionAttributes, ConnectionSchema


class MisaAmisSettingsSchema(AttributesSchema):
    app_id = fields.Str(required=True)
    access_code = fields.Str(required=True)
    org_company_code = fields.Str(required=True)
    base_url = fields.Url(default="https://actapp.misa.vn")


@dataclass
class MisaAmisSettings:
    app_id: str = None
    access_code: str = None
    org_company_code: str = None
    base_url: str = "https://actapp.misa.vn"


@dataclass
class MisaAmisConnectionAttributes(ConnectionAttributes):
    channel_name: str = field(default='misa_amis')
    settings: MisaAmisSettings = field(default_factory=MisaAmisSettings)


class MisaAmisConnectionSchema(ConnectionSchema):
    settings = fields.Nested(MisaAmisSettingsSchema(MisaAmisSettings))


class MisaAmisConnection(ConnectionModel):
    attributes: MisaAmisConnectionAttributes = None
    attributes_schema = MisaAmisConnectionSchema
    attributes_class = MisaAmisConnectionAttributes
