from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema


class ScheduleType(str, Enum):
    INTERVAL = "interval"
    CRON = "cron"


@dataclass
class Schedule:
    type: ScheduleType
    value: str  # For interval, this will be number of seconds. For cron, this will be the cron expression.


class ScheduleSchema(AttributesSchema):
    type = fields.Str(required=True)
    value = fields.Str(required=True)


@dataclass
class ActionStatus:
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None


class ActionStatusSchema(AttributesSchema):
    last_run = fields.DateTime(allow_none=True)
    next_run = fields.DateTime(allow_none=True)


@dataclass
class FetchActionSettings:
    enabled: bool = False
    storage_enabled: bool = False
    import_enabled: bool = True
    response_mapping: Optional[Dict] = None
    rate_limit: Optional[int] = None
    retry_settings: Optional[Dict] = None
    custom_settings: Optional[Dict[str, Any]] = field(default_factory=dict)
    schedule: Optional[Schedule] = None
    status: ActionStatus = field(default_factory=ActionStatus)
    destinations: Optional[Dict] = None

    def to_dict(self):
        return {k: v for k, v in self.__dict__.items() if v is not None}


class FetchActionSettingsSchema(AttributesSchema):
    enabled = fields.Bool(required=True, default=False)
    storage_enabled = fields.Bool(required=False, allow_none=True, default=False)
    import_enabled = fields.Bool(required=False, allow_none=True, default=True)
    response_mapping = fields.Dict(allow_none=True)
    rate_limit = fields.Int(allow_none=True)
    retry_settings = fields.Dict(allow_none=True)
    custom_settings = fields.Dict(allow_none=True)
    schedule = fields.Nested(ScheduleSchema(Schedule), allow_none=True)
    status = fields.Nested(ActionStatusSchema(ActionStatus))
    destinations = fields.Dict(allow_none=True)


@dataclass
class PublishActionSettings:
    enabled: bool = False
    payload_template: Optional[Dict] = None
    rate_limit: Optional[int] = None
    retry_settings: Optional[Dict] = None
    custom_settings: Optional[Dict[str, Any]] = field(default_factory=dict)

    def to_dict(self):
        return {k: v for k, v in self.__dict__.items() if v is not None}


class PublishActionSettingsSchema(AttributesSchema):
    enabled = fields.Bool(required=True, default=False)
    payload_template = fields.Dict(allow_none=True)
    rate_limit = fields.Int(allow_none=True)
    retry_settings = fields.Dict(allow_none=True)
    custom_settings = fields.Dict(allow_none=True)


class ActionKind(str, Enum):
    FETCH = "fetch"
    PUBLISH = "publish"
    BATCH_PUBLISH = "batch_publish"
    WEBHOOK = "webhook"
    PARAMS = "params"


class ActionType(str, Enum):
    get_order = "get_order"
    get_purchase_order = "get_purchase_order"
    sync_order = "sync_order"
    get_product = "get_product"
    sync_product = "sync_product"
    sync_purchase_order = "sync_purchase_order"
    get_inventory = "get_inventory"
    sync_inventory = "sync_inventory"
    sync_image = "sync_image"
    webhook = "webhook"
    get_return_order = "get_return_order"
    sync_return_order = "sync_return_order"
    send_message = "send_message"
    get_message = "get_message"


class ActionGroup(str, Enum):
    order = "order"
    purchase_order = "purchase_order"
    product = "product"
    return_order = "return_order"
    inventory = "inventory"
    image = "image"
    webhook = "webhook"
    unknown = "unknown"


ACTION_GROUP_MAPPING = {
    ActionType.get_order: ActionGroup.order,
    ActionType.sync_order: ActionGroup.order,
    ActionType.get_purchase_order: ActionGroup.purchase_order,
    ActionType.sync_purchase_order: ActionGroup.purchase_order,
    ActionType.get_product: ActionGroup.product,
    ActionType.sync_product: ActionGroup.product,
    ActionType.get_inventory: ActionGroup.inventory,
    ActionType.sync_inventory: ActionGroup.inventory,
    ActionType.sync_image: ActionGroup.image,
    ActionType.webhook: ActionGroup.webhook,
    ActionType.get_return_order: ActionGroup.return_order,
    ActionType.sync_return_order: ActionGroup.return_order,
}

ACTION_TYPE_MAPPING = {
    ActionType.get_order: ActionType.sync_order,
    ActionType.get_purchase_order: ActionType.sync_purchase_order,
    ActionType.get_product: ActionType.sync_product,
    ActionType.get_inventory: ActionType.sync_inventory,
    ActionType.get_return_order: ActionType.sync_return_order,
    ActionType.sync_order: ActionType.get_order,
    ActionType.sync_product: ActionType.get_product,
    ActionType.sync_inventory: ActionType.get_inventory,
    ActionType.sync_return_order: ActionType.get_return_order,
}


@dataclass
class ActionSettings:
    enabled: bool = False
    payload_template: Optional[Dict] = None
    response_mapping: Optional[Dict] = None
    rate_limit: Optional[int] = None
    retry_settings: Optional[Dict] = None
    custom_settings: Optional[Dict[str, Any]] = field(default_factory=dict)

    def to_dict(self):
        return {k: v for k, v in self.__dict__.items() if v is not None}


class ActionSettingsSchema(AttributesSchema):
    enabled = fields.Bool(required=True, default=False)
    payload_template = fields.Dict(allow_none=True)
    response_mapping = fields.Dict(allow_none=True)
    rate_limit = fields.Int(allow_none=True)
    retry_settings = fields.Dict(allow_none=True)
    custom_settings = fields.Dict(allow_none=True)


@dataclass
class ActionGroupSettings:
    group_name: ActionGroup
    fetch_actions: Dict[ActionType, FetchActionSettings] = field(default_factory=dict)
    publish_actions: Dict[ActionType, PublishActionSettings] = field(default_factory=dict)


class ActionGroupSettingsSchema(AttributesSchema):
    group_name = fields.Str(required=True)
    fetch_actions = fields.Dict(keys=fields.Str(), values=fields.Nested(FetchActionSettingsSchema(FetchActionSettings)))
    publish_actions = fields.Dict(keys=fields.Str(),
                                  values=fields.Nested(PublishActionSettingsSchema(PublishActionSettings)))
