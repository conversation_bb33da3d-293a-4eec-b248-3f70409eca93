from dataclasses import dataclass

from marshmallow import fields, EXCLUDE, pre_load
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.text import get_slug_by_name
from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.meta_data import MetaData, MetaDataSchema
from models.media.image import Image, ImageSchema


@dataclass
class Category(Attributes):
    id: str = None
    name: str = None
    image: Image = None
    parent_category_id: str = None
    has_children: bool = False
    meta_data: MetaData = None
    slug: str = None


class CategorySchema(AttributesSchema):
    class Meta:
        unknown = EXCLUDE

    id = fields.Str(required=True)
    parent_category_id = fields.Str(allow_none=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    name = fields.Str(required=True)
    has_children = fields.Bool(allow_none=True)
    meta_data = fields.Nested(MetaDataSchema(MetaData), allow_none=True)
    slug = fields.Str(allow_none=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['slug'] = data['slug'] if 'slug' in data else get_slug_by_name(data['name'])
        return data


@dataclass
class CategoryAttributes(Category, BasicAttributes):
    pass


class CategoryAttributesSchema(CategorySchema, BasicAttributesSchema):
    pass


class CategoryModel(BasicModel):
    table_name = 'category'
    attributes: CategoryAttributes
    attributes_class = CategoryAttributes
    attributes_schema = CategoryAttributesSchema

    query_mapping = {
        'query': ['name', 'id', 'slug'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'slug': 'query',
            'has_children': 'query',
            'parent_category_id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range'
        },
        'mapping': {
            'image': 'image',
        }
    }

    @classmethod
    def put(cls, data, company_id):
        key = str(data.get('id'))
        record = cls.by_key({
            'id': key,
            'company_id': company_id
        })

        if record:
            record.update(data)
            return record
        record = cls.create(company_id, {'id': key, 'company_id': company_id, **data})
        return record

    @classmethod
    def get_leaf_category_ids(cls, company_id, category_ids):
        if not category_ids:
            return []

        parent_category_ids = []
        leaf_category_ids = []

        # Lấy thông tin các category ban đầu
        categories = CategoryModel.search(
            {
                "company_id": company_id,
                "id": ','.join(category_ids),
                "limit": len(category_ids)
            },
            service=RESOURCE_SERVICE
        )

        for category in categories['items']:
            if category['id'] == category['parent_category_id']:
                continue
            if category['has_children']:
                if category['id'] not in parent_category_ids:
                    parent_category_ids.append(category['id'])
            else:
                if category['id'] not in leaf_category_ids:
                    leaf_category_ids.append(category['id'])

        while parent_category_ids:
            current_parent_category_id = parent_category_ids.pop(0)
            child_categories = CategoryModel.search(
                {
                    "company_id": company_id,
                    "parent_category_id": current_parent_category_id,
                    "limit": 1000
                },
                service=RESOURCE_SERVICE
            )

            for child_category in child_categories['items']:
                if child_category['id'] == child_category['parent_category_id']:
                    continue
                if child_category['has_children']:
                    if child_category['id'] not in parent_category_ids:
                        parent_category_ids.append(child_category['id'])
                else:
                    if child_category['id'] not in leaf_category_ids:
                        leaf_category_ids.append(child_category['id'])

        return leaf_category_ids


class CategorySlugIndex(CategoryModel):
    key__id = 'slug'
    index_name = 'categorySlugIndex'

    @classmethod
    def get_category_by_slug(cls, slug, company_id):
        try:
            return cls(CategorySlugIndex.list({'slug': slug, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return
