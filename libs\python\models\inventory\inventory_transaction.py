from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


class TransactionType(Enum):
    PURCHASE = 'PURCHASE'
    ADJUSTMENT = 'ADJUSTMENT'
    RETURN = 'RETURN'
    ORDER = 'ORDER'
    RELOCATE = 'RELOCATE'
    PACKAGE = 'PACKAGE'


@dataclass
class InventoryTransaction(Attributes):
    id: str = None
    inventory_item_id: str = None
    location_id: str = None
    note: str = None
    reference: str = None
    staff_id: str = None
    transaction_type: TransactionType = None
    quantity: Decimal = 0
    cost: Decimal = 0
    change: Decimal = 0


@dataclass
class InventoryTransactionAttributes(InventoryTransaction, BasicAttributes):
    pass


class InventoryTransactionSchema(AttributesSchema):
    id = fields.Str(required=True)
    inventory_item_id = fields.Str(required=True)
    location_id = fields.UUID(required=True)
    note = fields.Str(allow_none=True)
    reference = fields.Str(required=True)
    staff_id = fields.UUID(allow_none=True)
    transaction_type = EnumField(TransactionType, required=True)
    quantity = fields.Decimal(allow_none=True)
    cost = fields.Decimal(allow_none=True)
    change = fields.Decimal(allow_none=True)


class InventoryTransactionAttributesSchema(InventoryTransactionSchema, BasicAttributesSchema):
    pass


class InventoryTransactionModel(BasicModel):
    key__id = 'id'  # uuid
    key_range_id = 'company_location_item'
    key__ranges = ['company_id', 'location_id', 'inventory_item_id']
    table_name = 'inventoryTransaction'
    attributes: InventoryTransactionAttributes
    attributes_schema = InventoryTransactionAttributesSchema
    attributes_class = InventoryTransactionAttributes

    query_mapping = {
        'query': ['id', 'inventory_item_id', 'location_id', 'staff_id'],
        'filter': {
            'id': 'query',
            'inventory_item_id': 'query',
            'location_id': 'query',
            'reference': 'query',
            'staff_id': 'query',
            'transaction_type': 'query',
            'quantity': 'query',
            'cost': 'query',
            'change': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }


class ItemLocationTransactionIndex(InventoryTransactionModel):
    index_name = 'itemLocationTransactionIndex'
    key__id = 'company_location_item'
    key__ids = ['company_id', 'location_id', 'inventory_item_id']
    key_range_id = 'id'
    key__ranges = ['id']
