from dataclasses import dataclass, field

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema

from integrations.channels.connection_types import ConnectionAttributes, ConnectionSchema
from models.integration.connection import ConnectionModel


class ShopeeSettingsSchema(AttributesSchema):
    partner_id = fields.Int(required=True)
    partner_key = fields.Str(required=True)
    shop_id = fields.Int(required=True)
    access_token = fields.Str(required=True)
    base_url = fields.Url(required=True)


@dataclass
class ShopeeSettings:
    partner_id: int
    partner_key: str
    shop_id: int
    access_token: str
    base_url: str


@dataclass
class ShopeeConnectionAttributes(ConnectionAttributes):
    channel_name: str = field(default='shopee')
    settings: ShopeeSettings = field(default_factory=ShopeeSettings)


class ShopeeConnectionSchema(ConnectionSchema):
    settings = fields.Nested(ShopeeSettingsSchema(ShopeeSettings))


class ShopeeConnection(ConnectionModel):
    attributes: ShopeeConnectionAttributes = None
    attributes_schema = ShopeeConnectionSchema
    attributes_class = ShopeeConnectionAttributes
