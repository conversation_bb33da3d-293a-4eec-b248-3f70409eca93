from typing import Dict, Any, Tu<PERSON>, Optional, List

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import OAuth2Channel
from integrations.channels.connection_types import ChannelType
from integrations.channels.zalo.zalo_connection import ZaloSettings
from integrations.channels.zalo.zalo_library import ZaloLibrary


class ZaloChannel(OAuth2Channel):
    channel_name = "zalo"
    library_class = ZaloLibrary

    def _create_library(self, connection):
        settings: ZaloSettings = connection.attributes.settings
        return ZaloLibrary(
            app_id=settings.app_id,
            app_secret=settings.app_secret,
            api_key=settings.api_key,
            access_token=connection.attributes.access_token
        )

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            "name": "Zalo",
            "channel_type": ChannelType.MARKETING,
            "description": "Connect your Zalo account to manage marketing campaigns and customer interactions.",
            "logo": cls._load_image_as_base64(__file__, "zalo.png")
        }

    @classmethod
    def get_integration_info(cls) -> Dict[str, str]:
        """
        Returns a dictionary containing channel information.
        Must include 'name', 'description', and 'image' keys.
        """
        return {
            "title": "Engage Your Audience with Zalo Integration",
            "description":
                "Leverage Zalo’s messaging platform to connect with your customers and boost sales.",
            "integration": {
                "Messaging API": {
                    "description":
                        "Send targeted messages and engage with your Zalo followers.",
                    "apis": [
                        "POST /api/v1/zalo/message/send",
                        "POST /api/v1/zalo/message/broadcast",
                    ],
                },
                "Order API": {
                    "description":
                        "Manage orders directly through Zalo, including tracking and status updates.",
                    "apis": [
                        "GET /api/v1/zalo/orders",
                        "POST /api/v1/zalo/orders/update",
                        "GET /api/v1/zalo/orders/details",
                    ],
                },
                "Customer API": {
                    "description":
                        "Manage customer interactions and data through Zalo’s platform.",
                    "apis": [
                        "GET /api/v1/zalo/customers",
                        "POST /api/v1/zalo/customers/update",
                    ],
                },
            },
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        # Implement default fetch settings for Zalo actions
        pass

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        # Implement default publish settings for Zalo actions
        pass

    def initiate_oauth_flow(self, redirect_url: str = None, base_url: str = None) -> str:
        return self.library.get_authorization_url(str(self.connection.attributes.id), redirect_url, base_url)

    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        code = query_params.get('code')
        if not code:
            raise ValueError("Missing code in query parameters")

        token_data = self.library.fetch_token(code)

        return (
            token_data['user_id'],
            token_data['access_token'],
            token_data['expires_at'],
            token_data.get('refresh_token'),
            token_data
        )

    def refresh_oauth_token(self) -> Dict[str, Any]:
        token = self.library.refresh_token(self.connection.attributes.refresh_token)
        self.connection.update_token(token)
        return token

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        # Implement test event handling for Zalo
        pass

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        # Implement object details extraction for Zalo webhooks
        pass

    # Implement other necessary methods for Zalo integration
