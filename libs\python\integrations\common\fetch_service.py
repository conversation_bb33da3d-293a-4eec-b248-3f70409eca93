import boto3
import requests
import pendulum
import json
from botocore.exceptions import ClientError

dynamodb = boto3.resource('dynamodb')
s3 = boto3.client('s3')


class FetcherError(Exception):
    pass


def get_connection(table_name, connection_id):
    table = dynamodb.Table(table_name)
    try:
        response = table.get_item(Key={'id': connection_id})
        return response['Item']
    except ClientError as e:
        raise FetcherError(f"Failed to get connection: {str(e)}")


def fetch_data(connection):
    url = connection['source']['url']
    method = connection['source'].get('method', 'GET')
    headers = connection['source'].get('headers', {})
    body = connection['source'].get('body')

    try:
        if method == 'GET':
            response = requests.get(url, headers=headers)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=body)
        else:
            raise FetcherError(f"Unsupported HTTP method: {method}")

        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        raise FetcherError(f"Failed to fetch data: {str(e)}")


def store_raw_data(bucket_name, connection_id, data):
    key = f"{connection_id}/{data['id']}.json"
    try:
        s3.put_object(
            Bucket=bucket_name,
            Key=key,
            Body=json.dumps(data),
            ContentType='application/json'
        )
        return f"s3://{bucket_name}/{key}"
    except ClientError as e:
        raise FetcherError(f"Failed to store raw data: {str(e)}")


def update_connection_status(table_name, connection_id, status):
    table = dynamodb.Table(table_name)
    try:
        table.update_item(
            Key={'id': connection_id},
            UpdateExpression="SET status.last_fetch = :now, status.last_fetch_status = :status",
            ExpressionAttributeValues={
                ':now': pendulum.now('UTC').isoformat(),
                ':status': status
            }
        )
    except ClientError as e:
        raise FetcherError(f"Failed to update connection status: {str(e)}")


def run_fetcher(table_name, bucket_name, connection_id):
    try:
        connection = get_connection(table_name, connection_id)
        data = fetch_data(connection)
        s3_location = store_raw_data(bucket_name, connection_id, data)
        update_connection_status(table_name, connection_id, 'FETCHED')
        return s3_location
    except Exception as e:
        print(f"Error fetching data for connection {connection_id}: {str(e)}")
        update_connection_status(table_name, connection_id, 'FETCH_FAILED')
        raise
