import json
from nolicore.utils.aws.boto_helper import get_client
from nolicore.utils.utils import json_dumps

from models.actor.customer import CustomerModel
from models.order.order import OrderModel
from models.product.product import ProductModel

s3 = get_client('s3')


def generate_s3_key(connection_id, action_type, object_type, file_id, file_type='json'):
    """
    Generate a consistent S3 key structure.

    :param connection_id: The ID of the connection
    :param action_type: The type of action (e.g., 'sync', 'publish')
    :param object_type: The type of object (e.g., 'product', 'order')
    :param file_id: The unique identifier for the file
    :param file_type: The file type (default is 'json')
    :return: A formatted S3 key string
    """
    return f"{connection_id}/{action_type}/{object_type}/{file_id}.{file_type}"


def get_raw_data(bucket, key, version_id='null'):
    """
    Retrieve raw data from S3.

    :param bucket: The S3 bucket name
    :param key: The S3 object key
    :param version_id: The version ID of the object
    :return: The decoded JSON data
    """
    response = s3.get_object(Bucket=bucket, Key=key, VersionId=version_id)
    standard_data = json.loads(response['Body'].read().decode('utf-8'))
    raw_data = standard_data['raw_data']
    fetch_event_id = standard_data['fetch_event_id']
    return fetch_event_id, raw_data, standard_data


def store_transformed_data(bucket, key, data):
    """
    Store transformed data in S3.

    :param bucket: The S3 bucket name
    :param key: The S3 object key
    :param data: The data to store
    :return: The version ID of the stored object
    """
    response = s3.put_object(
        Bucket=bucket,
        Key=key,
        Body=json_dumps(data),
        ContentType='application/json'
    )
    return response['VersionId']


def get_model_by_type(object_type):
    if object_type == 'product':
        return ProductModel
    elif object_type == 'order':
        return OrderModel
    elif object_type == 'customer':
        return CustomerModel
    else:
        raise ValueError(f"Unknown object type: {object_type}")


def extract_file_id(s3_key: str) -> str:
    """
    Extract the file_id from the S3 key.

    :param s3_key: The S3 key string
    :return: The extracted file_id
    """
    # Split the S3 key by '/' and get the last part (which contains the file_id and file_type)
    parts = s3_key.split('/')
    if len(parts) < 4:
        raise ValueError("Invalid S3 key format. Unable to extract file_id.")

    # Get the last part which contains file_id and file_type
    file_id_with_type = parts[-1]

    # Split by '.' to separate file_id from file_type
    file_id = file_id_with_type.split('.')[0]

    return file_id


def get_transformed_data(bucket, key, version_id):
    """
    Retrieve transformed data from S3.

    :param bucket: The S3 bucket name
    :param key: The S3 object key
    :param version_id: The version ID of the object
    :return: The decoded JSON data
    """
    response = s3.get_object(Bucket=bucket, Key=key, VersionId=version_id)
    return json.loads(response['Body'].read().decode('utf-8'))
