import os
from abc import ABC, abstractmethod
from dataclasses import dataclass, field, asdict
from datetime import datetime, timezone, timedelta
import json
from typing import Dict, Any, List, Literal, Generic, TypeVar, Optional, Union, Tuple

import aiohttp
import pendulum
import requests
from nolicore.utils.exceptions import BadRequest
from nolicore.utils.utils import json_dumps
from helpers.common import safe_compress, safe_decompress
from helpers.utils import BASE_URL
from models.basic import BasicAttributes
from models.integration.sync_record import MappingStatus, SyncRecordModel, SyncRecordStatus

T = TypeVar('T')
APP_URL = os.getenv('APP_URL')


@dataclass
class StandardObject:
    def to_dict(self) -> Dict[str, Any]:
        def convert(value):
            if isinstance(value, datetime):
                return value.isoformat()
            if hasattr(value, 'to_dict'):
                return value.to_dict()
            if isinstance(value, list):
                return [convert(item) for item in value]
            if isinstance(value, dict):
                return {k: convert(v) for k, v in value.items()}
            return value

        return {k: convert(v) for k, v in asdict(self).items()}


@dataclass
class InventoryLocation(StandardObject):
    location_id: str
    location_name: str
    quantity: int
    available_quantity: int


@dataclass
class MetaData(StandardObject):
    total_page: int = 0
    total_count: int = 0
    page: int = 0
    limit: int = 0
    continuation_token: Optional[str] = None
    current_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PublishMetaData(StandardObject):
    updated_at: datetime


@dataclass
class AbstractStandardObject(StandardObject, ABC):
    id: str
    raw_data: Dict[str, Any]
    fetch_event_id: str = None
    created_at: datetime = None
    updated_at: datetime = None

    @abstractmethod
    def get_unique_identifier(self) -> str:
        pass

    @abstractmethod
    def get_object_type(self) -> str:
        pass


@dataclass
class StandardVariant(StandardObject):
    id: str = None
    title: str = None
    sku: str = None
    price: str = None
    image: str = None


@dataclass
class StandardProduct(AbstractStandardObject):
    title: str = None
    sku: str = None
    images: List[str] = None
    variants: List[StandardVariant] = None

    def get_unique_identifier(self) -> str:
        return f"product_{self.id}"

    def get_object_type(self) -> str:
        return "product"


@dataclass
class StandardInventory(AbstractStandardObject):
    locations: List[InventoryLocation] = None

    def get_unique_identifier(self) -> str:
        return f"inventory_{self.id}"

    def get_object_type(self) -> str:
        return "inventory"


@dataclass
class StandardSubscription(AbstractStandardObject):
    name: str = None

    def get_unique_identifier(self) -> str:
        return f"webhook_{self.id}"

    def get_object_type(self) -> str:
        return "webhook"


@dataclass
class StandardOrderLineItem(StandardObject):
    id: str
    product_id: str
    variant_id: Optional[str]
    sku: str
    name: str
    quantity: int
    price: float
    total_price: float


@dataclass
class StandardOrder(AbstractStandardObject):
    order_number: str = None
    total_price: float = None
    currency: str = None
    status: str = None
    line_items: List[StandardOrderLineItem] = None
    customer_id: Optional[str] = None
    customer_name: Optional[str] = None
    customer_email: Optional[str] = None
    customer_phone: Optional[str] = None
    shipping_address: Optional[Dict[str, Any]] = None
    billing_address: Optional[Dict[str, Any]] = None
    shipping_method: Optional[str] = None
    payment_method: Optional[str] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None

    def get_unique_identifier(self) -> str:
        return f"order_{self.order_number}"

    def get_object_type(self) -> str:
        return "order"


@dataclass
class StandardReturnOrder(AbstractStandardObject):
    order_number: str = None
    total_price: float = None
    currency: str = None
    status: str = None
    line_items: List[StandardOrderLineItem] = None
    customer_id: Optional[str] = None
    customer_name: Optional[str] = None
    customer_email: Optional[str] = None
    customer_phone: Optional[str] = None
    shipping_address: Optional[Dict[str, Any]] = None
    billing_address: Optional[Dict[str, Any]] = None
    shipping_method: Optional[str] = None
    payment_method: Optional[str] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None

    def get_unique_identifier(self) -> str:
        return f"return_order_{self.order_number}"

    def get_object_type(self) -> str:
        return "return_order"


@dataclass
class StandardPurchaseOrder(AbstractStandardObject):
    order_number: str = None
    total_price: float = None
    currency: str = None
    status: str = None
    line_items: List[StandardOrderLineItem] = None
    customer_id: Optional[str] = None
    customer_name: Optional[str] = None
    customer_email: Optional[str] = None
    customer_phone: Optional[str] = None
    shipping_address: Optional[Dict[str, Any]] = None
    billing_address: Optional[Dict[str, Any]] = None
    shipping_method: Optional[str] = None
    payment_method: Optional[str] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None

    def get_unique_identifier(self) -> str:
        return f"purchase_order_{self.order_number}"

    def get_object_type(self) -> str:
        return "purchase_order"


@dataclass
class FetchAPIResponse(StandardObject, Generic[T]):
    success: bool
    data: Union[List[T], Dict[str, Any]]
    meta: Optional[MetaData] = None
    message: str = ""


@dataclass
class SyncAPIResponse(StandardObject, Generic[T]):
    success: bool
    data: Optional[AbstractStandardObject] = None
    meta: Optional[PublishMetaData] = None
    message: str = ""

    def update_sync_record(self, sync_record: SyncRecordModel):
        now = pendulum.now().to_iso8601_string()
        if self.success:
            update_data = {
                'remote_record_id': self.data.id if self.data else None,
                'response': json.loads(json_dumps(asdict(self))),
                'response_message': "Publish successfully",
                'status': SyncRecordStatus.COMPLETED.value,
                'mapping_status': MappingStatus.SYNCED.value,
                'published_at': now,
                'finished_at': now,
                'standard_destination_data': self.data.to_dict() if self.data else None
            }
        else:
            update_data = {
                'response': {
                    'status': 'failed',
                    'error_message': self.message
                },
                'status': SyncRecordStatus.ERROR.value,
                'mapping_status': MappingStatus.ERROR.value,
                'response_message': f"Publish failed: {self.message}",
                'finished_at': now
            }
        sync_record.update(update_data)


@dataclass
class SyncAPIResponseList(StandardObject, Generic[T]):
    data: List[SyncAPIResponse[T]]
    success: bool
    message: str

    def update_sync_records(self, sync_record_objs: List[SyncRecordModel]):
        if not self.data:
            return
        now = pendulum.now().to_iso8601_string()
        is_result_for_each_record = len(self.data) == len(sync_record_objs)
        for i in range(0, len(sync_record_objs), 25):
            if is_result_for_each_record:
                new_sync_record = {
                    'remote_record_id': self.data[i].data.id if self.data[i].data else None,
                    'response': json.loads(json_dumps(asdict(self.data[i]))),
                    'response_message': "Batch publish successfully",
                    'status': SyncRecordStatus.COMPLETED.value if self.data[
                        i].success else SyncRecordStatus.ERROR.value,
                    'mapping_status': MappingStatus.SYNCED.value if self.data[
                        i].success else MappingStatus.ERROR.value,
                    'published_at': now,
                    'finished_at': now,
                    'standard_destination_data': self.data[i].data if self.data[i].data else None
                }
            else:
                new_sync_record = {
                    'response': {
                        "message": [str(item.data or item.message) for item in self.data]} if self.data else None,
                    'response_message': self.message,
                    'status': SyncRecordStatus.ERROR.value if not self.success else SyncRecordStatus.COMPLETED.value,
                    'mapping_status': MappingStatus.ERROR.value if not self.success else MappingStatus.SYNCED.value,
                    'published_at': now,
                    'finished_at': now
                }
            items = [BasicAttributes.add_basic_attributes({**item.attributes_dict, **new_sync_record},
                                                          item.attributes_dict.get('company_id')) for item in
                     sync_record_objs[i:i + 25]]
            SyncRecordModel.batch_add(items)


@dataclass
class EndpointType:
    type: Literal["rest", "graphql"]
    method: str
    path: str


class APIEndpoints:
    def __init__(self, base_url: str):
        self.base_url = base_url


class BaseAPILibrary(ABC):
    AVAILABLE_WEBHOOKS: Dict[str, Any] = {}

    def __init__(self, base_url: str, connection=None):
        self.endpoints = APIEndpoints(base_url)
        self.connection = connection

    async def _make_request_async(self,
                                  endpoint: EndpointType,
                                  headers: Optional[Dict[str, str]] = None,
                                  params: Optional[Dict[str, Any]] = None,
                                  data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        with aiohttp.ClientSession() as session:
            with session.request(endpoint.method,
                                 f"{self.endpoints.base_url}{endpoint.path}",
                                 headers=headers,
                                 params=params,
                                 json=data if endpoint.type == "json" else None,
                                 data=data if endpoint.type != "json" else None) as response:
                response.raise_for_status()
                return await response.json()

    def _make_request_sync(self,
                           endpoint: EndpointType,
                           headers: Optional[Dict[str, str]] = None,
                           params: Optional[Dict[str, Any]] = None,
                           path_params: Optional[Dict[str, Any]] = None,
                           data: Optional[Dict[str, Any]] = None,
                           json: Optional[Dict[str, Any]] = None,
                           files: Optional[Dict[str, Any]] = None,
                           **kwargs) -> Dict[str, Any]:
        endpoint_path = endpoint.path.format(**path_params) if path_params else endpoint.path

        try:
            response = requests.request(
                method=endpoint.method,
                url=f"{self.endpoints.base_url}{endpoint_path}" if endpoint.path.startswith(
                    "/") else f"{self.endpoints.base_url}/{endpoint_path}",
                headers=headers,
                params=params,
                json=json,
                data=data,
                files=files,
                **kwargs
            )
            # response.raise_for_status()
            if response.status_code == 204:
                return {}
            return response.json()
        except Exception as e:
            raise BadRequest(e)

    @abstractmethod
    def test_connection(self) -> bool:
        pass

    @staticmethod
    def get_callback_url(base_url: str = None, state: str = None) -> str:
        if not base_url:
            base_url = BASE_URL
        if state is None:
            return f'{base_url}/connections/oauth2_callback'
        return f'{base_url}/connections/oauth2_callback/{state}'

    @staticmethod
    def generate_state(connection_id: str, redirect_url: str = None) -> str:
        state = {
            "connection_id": connection_id,
        }
        if redirect_url:
            state["redirect_url"] = redirect_url
        return safe_compress(state)

    @staticmethod
    def get_redirect_url(state: str = None, query_params: dict = None) -> Tuple[str, str]:
        # Try to decompress state first
        try:
            decompressed_state = safe_decompress(state)
            connection_id = decompressed_state.get('connection_id')
            redirect_url = decompressed_state.get('redirect_url')
        except Exception as e:
            connection_id = state
            redirect_url = None

        # Determine redirect location
        if redirect_url:
            location = redirect_url
        elif query_params and 'redirect_url' in query_params:
            location = query_params['redirect_url']
        else:
            location = f"{APP_URL}/channels/connections/{connection_id}"
        return connection_id, location

    @abstractmethod
    def get_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                   fetch_event_id: str = None) -> \
            FetchAPIResponse[
                StandardOrder]:
        pass

    @abstractmethod
    def get_products(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                     fetch_event_id: str = None) -> FetchAPIResponse[StandardProduct]:
        pass

    @abstractmethod
    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    @abstractmethod
    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    @abstractmethod
    def sync_order(self, order_data: Dict[str, Any], settings: Dict[str, Any] = None) -> \
            SyncAPIResponse[StandardOrder]:
        pass

    @abstractmethod
    def sync_product(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardProduct]:
        pass

    @abstractmethod
    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    @abstractmethod
    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    @abstractmethod
    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str) -> StandardOrder:
        pass

    @abstractmethod
    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardProduct:
        pass

    @abstractmethod
    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    @abstractmethod
    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardPurchaseOrder:
        pass


class OAuthLibrary(BaseAPILibrary):
    def __init__(self, base_url: str = None,
                 access_token: Optional[str] = None,
                 refresh_token: Optional[str] = None,
                 expires_at: Optional[datetime] = None):
        super().__init__(base_url)
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.expires_at = expires_at

    @abstractmethod
    def get_authorization_url(self, connection_id: str, redirect_url: str = None, base_url: str = None) -> str:
        pass

    @abstractmethod
    def fetch_token(self, code: str) -> Dict[str, Any]:
        pass

    @abstractmethod
    def refresh_access_token(self) -> Dict[str, Any]:
        pass

    def set_tokens(self, access_token: str, expires_in: int, refresh_token: Optional[str] = None):
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)

    def clear_tokens(self):
        self.access_token = None
        self.refresh_token = None
        self.expires_at = None

    def is_token_expired(self) -> bool:
        if self.access_token is None:
            return False
        if self.expires_at is None:
            return False
        return datetime.now() >= self.expires_at

    def ensure_token_valid(self):
        if self.is_token_expired():
            self.refresh_access_token()

    def get_current_token(self) -> Dict[str, Any]:
        token = self.connection.get_token()
        if self.is_token_expired():
            token = self.refresh_access_token()
            self.connection.update_token(token['external_id'], token['access_token'], token['expires_at'],
                                         token['refresh_token'], token['token_data'])
        return token


__all__ = [
    'AbstractStandardObject',
    'APIEndpoints',
    'EndpointType',
    'BaseAPILibrary',
    'FetchAPIResponse',
    'SyncAPIResponse',
    'SyncAPIResponseList',
    'MetaData',
    'PublishMetaData',
    'StandardOrder',
    'StandardReturnOrder',
    'StandardVariant',
    'StandardProduct',
    'StandardPurchaseOrder',
    'StandardOrderLineItem',
    'StandardInventory',
    'InventoryLocation',
    'StandardSubscription',
]
