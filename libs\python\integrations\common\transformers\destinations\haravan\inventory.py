import math
from typing import Dict, Any

from ...base import DestinationTransformer
from integrations.channels.action_types import ActionGroup


class HaravanInventoryTransformer(DestinationTransformer):
    channel_name = 'haravan'
    object_type = 'inventory'

    def _map_object_type_to_action_type(self) -> str:
        return 'create_inventory'

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        dynamic_settings = self.connection_settings.get('dynamic_settings', {})
        default_location_id = dynamic_settings.get('default_location_id', '')
        stock_split = dynamic_settings.get('stock_split')
        stock_min = dynamic_settings.get('stock_min')

        if not default_location_id:
            raise ValueError("No default_location_id found in the dynamic_settings")

        if stock_min is not None and stock_split is not None:
            try:
                stock_split = float(stock_split)
                stock_min = float(stock_min)
            except (TypeError, ValueError):
                raise TypeError("stock_split and stock_min must be numeric values")

        haravan_data = {
            "inventory": {
                "type": "set",
                "reason": "productionofgoods",
                "note": "update from onexapis",
                "line_items": []
            }
        }
        for inventory in data['inventories']:
            if inventory['location_id'] != default_location_id:
                continue
            quantity = inventory['available']
            if stock_min is not None and stock_split is not None:
                quantity = math.floor(stock_split * quantity) if quantity >= stock_min else 0
            haravan_data['inventory']['line_items'].append({
                "sku": data['sku'],
                "quantity": quantity,
            })
        if not haravan_data['inventory']['line_items']:
            raise ValueError("line_items must contain at least one item")

        return haravan_data

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.inventory

    @property
    def input_schema(self):
        return
        # return InventoryAttributesSchema().dump(InventoryAttributesSchema())

    @property
    def output_schema(self):
        # This should reflect the Haravan API inventory schema
        return {
            'type': 'object',
            'properties': {
                'inventory': {
                    'type': 'object',
                    'properties': {
                        'type': {'type': 'string'},
                        'reason': {'type': 'string'},
                        'note': {'type': 'string'},
                        'line_items': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'properties': {
                                    'sku': {'type': 'string'},
                                    'quantity': {'type': 'number'},
                                }
                            }
                        },
                    }
                }
            }
        }
