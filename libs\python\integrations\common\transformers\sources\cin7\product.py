from typing import Dict, Any, List
import logging

from integrations.channels.action_types import ActionGroup
from integrations.common.transformers.base import SourceTransformer
from integrations.common.transformers.sources.cin7.inventory import Cin7InventoryTransformer
from models.product.product import ProductAttributesSchema

logger = logging.getLogger(__name__)


class Cin7ProductTransformer(SourceTransformer):
    channel_name = 'cin7'
    object_type = 'product'

    def __init__(self, connection_settings: Dict[str, Any]):
        super().__init__(connection_settings)
        self.inventory_transformer = Cin7InventoryTransformer(connection_settings)

    def _map_object_type_to_action_type(self) -> str:
        return 'get_product'

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)

            # Transform Cin7 product data to master format
            transformed_data = {
                'name': data['Name'],
                'sku': data['SKU'],
                'barcode': data['Barcode'],
                'publish': data['Status'] == 'Active' and data['Category'] == 'AUTO',
                'description': self._get_description(data),
                'shortDescription': data['ShortDescription'],
                'tags': data.get('Tags', ''),
                'brand': {'name': data['Brand'].strip() if data.get('Brand') else None},
                'category': {'name': data['Category']},
                'images': self._transform_images(data.get('Attachments', [])),
                'variants': [self._transform_variant(data)],  # Cin7 products are flat, each product is its own variant
                'options': self._transform_options(data),
                'measurements': {
                    'weight_value': float(data.get('Weight', 0)),
                    'weight_unit': data.get('WeightUnits', 'kg').lower(),
                    'width_value': float(data.get('Width', 0)),
                    'width_unit': data.get('DimensionsUnits', 'mm').lower(),
                    'length_value': float(data.get('Length', 0)),
                    'length_unit': data.get('DimensionsUnits', 'mm').lower(),
                    'height_value': float(data.get('Height', 0)),
                    'height_unit': data.get('DimensionsUnits', 'mm').lower(),
                }
            }

            # Output validation
            self.validate_output(transformed_data)
            return transformed_data

        except Exception as e:
            logger.error(f"Error transforming Cin7 product data: {str(e)}")
            raise

    def _transform_images(self, attachments: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        images = []
        for attachment in attachments:
            if attachment['ContentType'].startswith('image/'):
                images.append({
                    'src': attachment['DownloadUrl'],
                    'name': attachment['FileName'],
                    'thumbnail': attachment.get('IsDefault', False)
                })
        return images

    def _get_description(self, data):
        description = data['Description']
        if 'AttributeSetList' not in data:
            return description
        attribute_set = data['AttributeSetList'][0]
        for i in range(1, 11):
            if data[f'AdditionalAttribute{i}'] and attribute_set[f'Attribute{i}Name'].lower().strip() not in ['bay', 'curved/flat']:
                description += "<br><strong>" + (attribute_set[f'Attribute{i}Name'].strip() or '') + "</strong>: " + (
                        data[f'AdditionalAttribute{i}'] or '')
        return description

    def _transform_variant(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # Transform prices from PriceTiers
        prices = []
        for price_group, price_value in data['PriceTiers'].items():
            if price_value > 0:  # Only include non-zero prices
                prices.append({
                    'price': float(price_value),
                    'price_group': {'id': price_group.upper().replace(' ', '_')}
                })

        return {
            'sku': data['SKU'],
            'barcode': data['Barcode'],
            'prices': prices,
            'measurements': {
                'weight_value': float(data.get('Weight', 0)),
                'weight_unit': data.get('WeightUnits', 'kg').lower(),
                'width_value': float(data.get('Width', 0)),
                'width_unit': data.get('DimensionsUnits', 'mm').lower(),
                'length_value': float(data.get('Length', 0)),
                'length_unit': data.get('DimensionsUnits', 'mm').lower(),
                'height_value': float(data.get('Height', 0)),
                'height_unit': data.get('DimensionsUnits', 'mm').lower(),
            },
            'option1': data.get('AdditionalAttribute1'),
            'option2': data.get('AdditionalAttribute2'),
            'option3': data.get('AdditionalAttribute3'),
            'optionTitle1': 'Make',
            'optionTitle2': 'Model',
            'optionTitle3': 'Year',
            'images': self._transform_images(data.get('Attachments', [])),
            'inventories': self.inventory_transformer.transform(data),
        }

    def _transform_options(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        options = []
        # Map additional attributes to options if they have values
        option_mappings = {
            'AdditionalAttribute1': 'Make',
            'AdditionalAttribute2': 'Model',
            'AdditionalAttribute3': 'Year'
        }

        for attr_key, option_name in option_mappings.items():
            if data.get(attr_key):
                options.append({
                    'name': option_name,
                    'values': [data[attr_key]]
                })

        return options

    def validate_input(self, data: Dict[str, Any]):
        required_fields = ['ID', 'SKU', 'Name', 'Category', 'Status']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f"Missing required fields in Cin7 product data: {', '.join(missing_fields)}")

    def validate_output(self, data: Dict[str, Any]):
        required_fields = ['name', 'sku', 'variants']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f"Missing required fields in transformed data: {', '.join(missing_fields)}")

    @property
    def input_schema(self):
        return {
            'type': 'object',
            'properties': {
                'ID': {'type': 'string'},
                'SKU': {'type': 'string'},
                'Name': {'type': 'string'},
                'Category': {'type': 'string'},
                'Brand': {'type': ['string', 'null']},
                'Status': {'type': 'string'},
                'Description': {'type': ['string', 'null']},
                'ShortDescription': {'type': ['string', 'null']},
                'Weight': {'type': ['number', 'null']},
                'WeightUnits': {'type': ['string', 'null']},
                'Width': {'type': ['number', 'null']},
                'Height': {'type': ['number', 'null']},
                'Length': {'type': ['number', 'null']},
                'DimensionsUnits': {'type': ['string', 'null']},
                'Barcode': {'type': ['string', 'null']},
                'PriceTiers': {
                    'type': 'object',
                    'additionalProperties': {'type': 'number'}
                },
                'Tags': {'type': ['string', 'null']},
                'Attachments': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'ContentType': {'type': 'string'},
                            'FileName': {'type': 'string'},
                            'DownloadUrl': {'type': 'string'}
                        }
                    }
                }
            },
            'required': ['ID', 'SKU', 'Name', 'Category', 'Status']
        }

    @property
    def output_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.product
