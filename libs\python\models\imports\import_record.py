from dataclasses import dataclass
from enum import Enum

from marshmallow import fields
from marshmallow_enum import EnumField

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


class RecordType(Enum):
    product = 'product'
    customer = 'customer'
    order = 'order'
    inventory = 'inventory'
    purchase_order = 'purchase_order'
    return_order = 'return_order'
    stock_adjustment = 'stock_adjustment'


class ResponseType(Enum):
    success = 'success'
    error = 'error'
    warning = 'warning'


@dataclass
class ImportRecord(BasicAttributes):
    id: str = None
    record_id: str = None
    record_type: RecordType = None
    record: dict = None
    is_clone: bool = False
    response_type: str = None
    response: dict = None
    message: str = None
    app_id: str = None
    response_code: str = None


class ImportRecordSchema(BasicAttributesSchema):
    id = fields.UUID(required=True)
    record_id = fields.Str(required=True)
    record_type = EnumField(RecordType, required=True)
    app_id = fields.Str(allow_none=True)
    message = fields.Str(allow_none=True)
    record = fields.Dict(required=True)
    response_type = EnumField(ResponseType, required=True)
    is_clone = fields.Bool(required=False, allow_none=True, default=False)
    response_code = fields.Str(allow_none=True)
    response = fields.Dict(required=True)


class ImportRecordModel(BasicModel):
    table_name = 'importRecord'
    key__ranges = ['record_id']
    attributes: ImportRecord
    attributes_schema = ImportRecordSchema
    attributes_class = ImportRecord

    query_mapping = {
        'query': ['id', 'record_id', 'message'],
        'filter': {
            'id': 'query',
            'record_type': 'query',
            'app_id': 'query',
            'company_id': 'query',
            'response_type': 'query',
            'response_code': 'query',
            'is_clone': 'boolean',
            'updated_at': 'range',
            'created_at': 'range'
        },
        'mapping': {
            'message': 'message'
        }
    }
