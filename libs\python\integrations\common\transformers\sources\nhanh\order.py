import logging
from typing import Dict, Any

import pendulum

from helpers.common import is_valid_uuid
from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.transformers.base import SourceTransformer
from models.common.status import Status, OrderType
from models.order.order import OrderAttributesSchema

logger = logging.getLogger(__name__)


class NhanhOrderTransformer(SourceTransformer):
    channel_name = 'nhanh'
    object_type = ActionGroup.order.value

    def _map_object_type_to_action_type(self) -> str:
        return ActionType.get_order.value

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.order

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        if 'type' in data and 'mode' in data:
            return self._transform_bill(data)
        else:
            return self._transform_order(data)

    def _transform_order(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)

            location_mapping = self.dynamic_settings.get('location_mapping', {}).get(
                'mapping', {})

            location_external = self.dynamic_settings.get('location_mapping', {}).get(
                'external', [])

            staff_id = self.dynamic_settings.get('staff_mapping', {}).get("staff_id")

            location_id = location_mapping.get(str(data['depotId']))

            location = next((item for item in location_external if str(item['id']) == str(data['depotId'])), {})

            origin_id = str(data['depotId'])
            origin_name = location.get('name') or 'POS'

            nhanh_channel_id = data['channel']
            source_name = map_channel_code(data['channel'])

            customer_code = ""
            customer_name = data['customerName']

            address = {
                "address2": None,
                "province_code": None,
                "country": "",
                "province": data.get('customerCity', ''),
                "name": customer_name,
                "last_name": None,
                "country_code": "",
                "address1": data.get('customerAddress', ''),
                "first_name": customer_name,
                "default_billing": False,
                "default_shipping": False,
                "phone": data['customerMobile'],
                "district": data.get('customerDistrict', ''),
                "city": data.get('customerCity', ''),
                "zip": None,
                "ward": data.get('customerWard', '')
            }

            created_at = pendulum.from_format(data['createdDateTime'], 'YYYY-MM-DD HH:mm:ss',
                                              tz="Asia/Bangkok").to_iso8601_string()
            updated_at = pendulum.from_timestamp(int(data['updatedAt']), tz="Asia/Bangkok").to_iso8601_string()
            if data.get('deliveryDate'):
                delivery_date = pendulum.from_format(data['deliveryDate'], 'YYYY-MM-DD', tz="Asia/Bangkok")
                updated_date = pendulum.from_timestamp(int(data['updatedAt']))
                if delivery_date.year == updated_date.year and delivery_date.month == updated_date.month and delivery_date.day == updated_date.day:
                    delivered_at = updated_at
                else:
                    delivered_at = delivery_date.to_iso8601_string()
            else:
                delivered_at = None
            order_line_items = [self._transform_order_line_items(p, location_id, transform_type="order") for p in
                                data['products']]
            sub_total = sum(
                [float(item['quantity']) * float(item['sale_price']) for item in
                 order_line_items])
            product_discount = sum([float(item['discount']) * float(item['quantity']) for item in
                                    order_line_items])
            order_discount = data['moneyDiscount'] - product_discount
            external_id = str(data['id'])
            staff = {
                "id": staff_id,
                "name": data['saleName']
            } if data.get('saleId') and is_valid_uuid(staff_id, 4) else None
            # Transform Nhanh order data to master format
            transformed_data = {
                "order_type": OrderType.IMPORTED.value,
                "external_id": external_id,
                "customer": {
                    "addresses": [address],
                    "customer_code": customer_code,
                    "company_name": "",
                    "billing_address": address,
                    "last_name": None,
                    "first_name": customer_name,
                    "id": "",
                    "email": data.get('customerEmail'),
                    "phone": data['customerMobile'],
                    "tags": None
                },
                "status": map_status(data.get('statusCode'), data.get('typeId')),
                "billing_address": address,
                "shipping_address": address,
                "location": {
                    "id": location_id,  # Default location ID
                    "name": data['depotName']
                },
                "staff": staff,
                "source": {
                    "id": nhanh_channel_id,
                    "name": source_name,
                    "origin_id": origin_id,
                    "origin": origin_name
                },
                "order_line_items": order_line_items,
                "note": data['description'],
                "tags": data['tags'].join() if data['tags'] else None,
                "total": data['calcTotalMoney'],
                "sub_total": sub_total,
                "discount": order_discount,
                "redeem_point": data.get('usedPoints', 0),
                "redeem_money": data.get('moneyUsedPoints', 0),
                "payments": None,
                "other_fees": None,
                "shipping_fee": data['customerShipFee'],
                "tax": 0,
                "created_at": created_at,
                "updated_at": updated_at,
                "delivered_at": delivered_at,
            }

            # Output validation
            self.validate_output(transformed_data)
            return transformed_data
        except KeyError as e:
            logger.error(f"Missing required field in Nhanh order data: {str(e)}")
            raise ValueError(f"Missing required field in Nhanh order data: {str(e)}")
        except Exception as e:
            logger.error(f"Error transforming Nhanh order data: {str(e)}")
            raise

    def _transform_bill(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)

            location_mapping = self.connection_settings.get('dynamic_settings', {}).get('location_mapping', {}).get(
                'mapping', {})

            location_external = self.connection_settings.get('dynamic_settings', {}).get('location_mapping', {}).get(
                'external', [])

            staff_id = self.dynamic_settings.get('staff_mapping', {}).get("staff_id")

            location_id = location_mapping.get(str(data['depotId']))

            location = next((item for item in location_external if str(item['id']) == str(data['depotId'])), {})

            origin_id = str(data['depotId'])
            origin_name = location.get('name') or 'POS'

            customer_code = ""
            customer_name = data['customerName']

            address = {
                "address2": None,
                "province_code": None,
                "country": "",
                "province": data.get('customerCity', ''),
                "name": customer_name,
                "last_name": None,
                "country_code": "",
                "address1": data.get('customerAddress', ''),
                "first_name": customer_name,
                "default_billing": False,
                "default_shipping": False,
                "phone": data['customerMobile'],
                "district": data.get('customerDistrict', ''),
                "city": data.get('customerCity', ''),
                "zip": None,
                "ward": data.get('customerWard', '')
            }

            created_at = pendulum.from_format(data['createdDateTime'], 'YYYY-MM-DD HH:mm:ss',
                                              tz="Asia/Bangkok").to_iso8601_string()
            updated_at = created_at
            delivered_at = created_at
            order_line_items = [self._transform_order_line_items(p, location_id, transform_type="bill") for p in
                                data['products'].values()]
            sub_total = sum(
                [float(item['quantity']) * float(item['sale_price']) for item in
                 order_line_items])
            product_discount = sum([float(item['discount']) * float(item['quantity']) for item in
                                    order_line_items])
            order_discount = float(data['discount']) - product_discount
            if data['mode'] == "4":
                order_discount = data['money']
            external_id = str(data['id'])
            staff = {
                "id": staff_id,
                "name": data['saleName']
            } if data.get('saleId') and is_valid_uuid(staff_id, 4) else None

            # nhanh_payments = [{
            #     'account': 'transferAccount',
            #     'method': 'moneyTransfer',
            # }, {'account': 'cashAccount',
            #     'method': 'cash'},
            #     {'account': 'installmentAccount',
            #      'method': 'installmentMoney'},
            #     {'account': 'creditAccount',
            #      'method': 'creditMoney'}
            # ]
            payments = []

            # for item in nhanh_payments:
            #     amount = float(data[item['method']])
            #     account = data[item['account']]
            #     if amount > 0:
            #         payments.append({
            #             "payment_method": {
            #                 "name": account,
            #                 "id": "CASH",
            #                 "account": {
            #                     "type": "CASH",
            #                     "account_number": None,
            #                     "user_id": "",
            #                     "account_name": None,
            #                     "name": account,
            #                     "id": "",
            #                     "note": None,
            #                     "balance": "0",
            #                     "version": 0,
            #                     "bank_id": None,
            #                     "bank_name": None,
            #                     "last_transaction": None
            #                 },
            #                 "note": "",
            #                 "settings": None
            #             },
            #             "from_account": None,
            #             "to_account": {
            #                 "type": "CASH",
            #                 "account_number": None,
            #                 "user_id": "",
            #                 "account_name": None,
            #                 "name": account,
            #                 "id": "",
            #                 "note": None,
            #                 "balance": "-********",
            #                 "version": 19,
            #                 "bank_id": None,
            #                 "bank_name": None,
            #                 "last_transaction": "a59beb3b-e9b1-4b91-b7b4-0d6a17e4247d"
            #             },
            #             "reference": "172c545a-ce3a-4e43-b280-4aee5dfeb99f",
            #             "id": "bf42f16b-12ee-48b7-8d48-80a5e15c7c53",
            #             "note": "",
            #             "amount": amount,
            #             "voucher": {
            #                 "name": "Order payment",
            #                 "type": "PAYMENT",
            #                 "description": None,
            #                 "id": "ORDER_PAYMENT_VOUCHER"
            #             },
            #             "status": "COMPLETED",
            #             "effective_date": "2024-08-02T13:49:41.298771+00:00"
            #         })

            # Transform Nhanh order data to master format
            transformed_data = {
                "order_type": OrderType.IMPORTED.value,
                "payments": payments,
                "external_id": external_id,
                "customer": {
                    "addresses": [address],
                    "customer_code": customer_code,
                    "company_name": "",
                    "billing_address": address,
                    "last_name": None,
                    "first_name": customer_name,
                    "id": "",
                    "email": data.get('customerEmail'),
                    "phone": data['customerMobile'],
                    "tags": None
                },
                "status": map_status(data.get('statusCode', 'Success')),
                "billing_address": address,
                "shipping_address": address,
                "location": {
                    "id": location_id,  # Default location ID
                    "name": location.get('name')
                },
                "staff": staff,
                "source": {
                    "id": origin_id,
                    "name": origin_name,
                    "origin_id": origin_id,
                    "origin": origin_name
                },
                "order_line_items": order_line_items,
                "note": data['description'],
                "tags": data['tags'].join() if data['tags'] else None,
                "total": data['money'],
                "sub_total": sub_total,
                "discount": order_discount,
                "other_fees": None,
                "shipping_fee": data.get('shipFee', 0),
                "tax": 0,
                "redeem_point": data.get('usedPoints', 0),
                "redeem_money": data.get('usedPointsMoney', 0),
                "created_at": created_at,
                "updated_at": updated_at,
                "delivered_at": delivered_at,
            }

            # Output validation
            self.validate_output(transformed_data)
            return transformed_data
        except KeyError as e:
            logger.error(f"Missing required field in Nhanh bill data: {str(e)}")
            raise ValueError(f"Missing required field in Nhanh bill data: {str(e)}")
        except Exception as e:
            logger.error(f"Error transforming Nhanh bill data: {str(e)}")
            raise

    def _transform_order_line_items(self, data: Dict[str, Any], location_id, transform_type="bill") -> Dict[str, Any]:
        """Convert product format"""
        sku = data['productCode'] if transform_type == 'order' else data['code']
        name = data['productName'] if transform_type == 'order' else data['name']
        image_url = data['productImage'] if transform_type == 'order' else None
        discount = float(data['discount']) if transform_type == 'order' else float(data['discount']) / float(
            data['quantity'])
        return {
            "unit_price": data['price'],
            "sale_price": float(data['price']) - discount,
            "discount": discount,
            "sku": sku,
            "name": name,
            "variant_name": name,
            "image_url": image_url,
            "quantity": data['quantity'],
            "product_id": "",  # Not available in Nhanh data
            "variant_id": "",  # Not available in Nhanh data
            "location": {
                "id": location_id,
            }
        }

    def validate_input(self, data: Dict[str, Any]):
        # Implement input validation logic using self.input_schema
        pass

    def validate_output(self, data: Dict[str, Any]):
        # Implement output validation logic using self.output_schema
        pass

    @property
    def input_schema(self, transform_type='bill'):
        # This should reflect the Nhanh API order schema
        if transform_type == 'order':
            return {
                'type': 'object',
                'properties': {
                    'id': {'type': 'integer'},
                    'privateId': {'type': 'string'},
                    'shopOrderId': {'type': 'string'},
                    'channel': {'type': 'integer'},
                    'ecomShopId': {'type': 'string'},
                    'saleChannel': {'type': 'integer'},
                    'merchantTrackingNumber': {'type': ['string', 'null']},
                    'depotId': {'type': 'integer'},
                    'handoverId': {'type': ['string', 'null']},
                    'depotName': {'type': 'string'},
                    'type': {'type': 'string'},
                    'typeId': {'type': 'integer'},
                    'moneyDiscount': {'type': 'number'},
                    'moneyDeposit': {'type': 'number'},
                    'moneyTransfer': {'type': 'number'},
                    'depositAccount': {'type': 'string'},
                    'transferAccount': {'type': 'string'},
                    'usedPoints': {'type': 'integer'},
                    'moneyUsedPoints': {'type': 'number'},
                    'carrierId': {'type': 'integer'},
                    'carrierName': {'type': 'string'},
                    'serviceId': {'type': ['integer', 'null']},
                    'serviceName': {'type': 'string'},
                    'carrierCode': {'type': ['string', 'null']},
                    'shipFee': {'type': 'number'},
                    'codFee': {'type': 'number'},
                    'customerShipFee': {'type': 'number'},
                    'returnFee': {'type': 'number'},
                    'overWeightShipFee': {'type': 'number'},
                    'declaredFee': {'type': 'number'},
                    'description': {'type': 'string'},
                    'privateDescription': {'type': 'string'},
                    'customerId': {'type': ['integer', 'null']},
                    'customerName': {'type': 'string'},
                    'customerEmail': {'type': ['string', 'null']},
                    'customerMobile': {'type': ['string', 'null']},
                    'customerAddress': {'type': 'string'},
                    'shipToCityLocationId': {'type': 'integer'},
                    'shipToDistrictLocationId': {'type': 'string'},
                    'customerCityId': {'type': 'integer'},
                    'customerCity': {'type': 'string'},
                    'customerDistrictId': {'type': 'integer'},
                    'customerDistrict': {'type': 'string'},
                    'shipToWardLocationId': {'type': ['string', 'null']},
                    'customerWard': {'type': ['string', 'null']},
                    'createdById': {'type': ['integer', 'null']},
                    'createdByUserName': {'type': 'string'},
                    'createdByName': {'type': 'string'},
                    'saleId': {'type': ['integer', 'null']},
                    'saleName': {'type': 'string'},
                    'createdDateTime': {'type': 'string'},
                    'deliveryDate': {'type': ['string', 'null']},
                    'sendCarrierDate': {'type': ['string', 'null']},
                    'statusName': {'type': 'string'},
                    'statusCode': {'type': 'string'},
                    'calcTotalMoney': {'type': 'number'},
                    'trafficSourceId': {'type': ['integer', 'null']},
                    'trafficSourceName': {'type': 'string'},
                    'affiliateCode': {'type': 'string'},
                    'affiliateBonusCash': {'type': 'number'},
                    'affiliateBonusPercent': {'type': 'number'},
                    'products': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'productId': {'type': 'string'},
                                'productName': {'type': 'string'},
                                'productCode': {'type': 'string'},
                                'productBarcode': {'type': ['string', 'null']},
                                'productImage': {'type': 'string'},
                                'price': {'type': 'string'},
                                'quantity': {'type': 'string'},
                                'weight': {'type': ['string', 'null']},
                                'vat': {'type': 'number'},
                                'discount': {'type': 'string'},
                                'description': {'type': 'string'},
                                'imei': {'type': 'string'},
                                'giftProducts': {'type': 'array'},
                                'batch': {'type': 'array'}
                            }
                        }
                    },
                    'tags': {'type': 'array', 'items': {'type': 'string'}},
                    'couponCode': {'type': ['string', 'null']},
                    'returnFromOrderId': {'type': ['string', 'null']},
                    'utmSource': {'type': 'string'},
                    'utmMedium': {'type': 'string'},
                    'utmCampaign': {'type': 'string'},
                    'facebook': {
                        'type': 'object',
                        'properties': {
                            'fanpageId': {'type': ['string', 'null']},
                            'adId': {'type': ['string', 'null']}
                        }
                    }
                }
            }
        else:
            return {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "relatedBillId": {"type": "string"},
                    "relatedDepotId": {"type": "string"},
                    "relatedUserName": {"type": "string"},
                    "orderId": {"type": "string"},
                    "requirementBillId": {"type": "string"},
                    "inventoryCheckId": {"type": "string"},
                    "warrantyBillId": {"type": "string"},
                    "depotId": {"type": "string"},
                    "date": {"type": "string"},
                    "createdDateTime": {"type": "string"},
                    "customerId": {"type": "string"},
                    "customerName": {"type": "string"},
                    "customerMobile": {"type": "string"},
                    "saleId": {"type": "string"},
                    "saleName": {"type": "string"},
                    "type": {"type": "string"},
                    "mode": {"type": "string"},
                    "saleUserName": {"type": "string"},
                    "createdById": {"type": "string"},
                    "createdByName": {"type": "string"},
                    "createdByUserName": {"type": "string"},
                    "technicalId": {"type": ["string", "null"]},
                    "technicalName": {"type": ["string", "null"]},
                    "discount": {"type": "string"},
                    "points": {"type": "string"},
                    "usedPoints": {"type": "integer"},
                    "money": {"type": "number"},
                    "saleBonus": {"type": "integer"},
                    "moneyTransfer": {"type": "string"},
                    "cash": {"type": "number"},
                    "installmentMoney": {"type": "number"},
                    "creditMoney": {"type": "number"},
                    "usedPointsMoney": {"type": "number"},
                    "returnFee": {"type": "number"},
                    "payment": {
                        "type": "object",
                        "properties": {}
                    },
                    "products": {
                        "type": "object",
                        "additionalProperties": {
                            "type": "object",
                            "properties": {
                                "id": {"type": "string"},
                                "code": {"type": "string"},
                                "name": {"type": "string"},
                                "quantity": {"type": "string"},
                                "price": {"type": "string"},
                                "discount": {"type": "string"},
                                "vat": {"type": ["number", "null"]},
                                "extendedWarrantyMoney": {"type": ["number", "null"]},
                                "money": {"type": "number"},
                                "imei": {"type": "array", "items": {"type": "string"}}
                            }
                        }
                    },
                    "description": {"type": "string"},
                    "supplierId": {"type": ["string", "null"]},
                    "supplierName": {"type": ["string", "null"]},
                    "couponCode": {"type": ["string", "null"]},
                    "couponValue": {"type": ["string", "null"]},
                    "customerMoney": {"type": "number"},
                    "moneyReturn": {"type": "number"},
                    "cashAccount": {"type": ["string", "null"]},
                    "transferAccount": {"type": "string"},
                    "creditAccount": {"type": ["string", "null"]},
                    "creditCode": {"type": ["string", "null"]},
                    "installmentAccount": {"type": ["string", "null"]},
                    "installmentCode": {"type": ["string", "null"]},
                    "tags": {
                        "type": "array",
                        "items": {"type": "string"}
                    },
                }
            }

    @property
    def output_schema(self):
        return OrderAttributesSchema().dump(OrderAttributesSchema())


def map_status(nhanh_status: str, order_type: int = None) -> str:
    """Map Nhanh status to the corresponding status in the enum."""
    status_map = {
        "New": Status.DRAFT,  # Đơn mới
        "Confirming": Status.PENDING,  # Đang xác nhận
        "CustomerConfirming": Status.PENDING,  # Chờ khách xác nhận
        "Confirmed": Status.READY,  # Đã xác nhận
        "Packing": Status.PACKING,  # Đang đóng gói
        "Packed": Status.READY,  # Đã đóng gói
        "ChangeDepot": Status.BLOCKING,  # Đổi kho xuất hàng
        "Pickup": Status.AWAIT_PACKING,  # Chờ thu gom
        "Shipping": Status.SHIPPING,  # Đang chuyển
        "Success": Status.COMPLETED,  # Thành công
        "Failed": Status.CANCELLED,  # Thất bại
        "Canceled": Status.CANCELLED,  # Khách hủy
        "Aborted": Status.CANCELLED,  # Hệ thống hủy
        "CarrierCanceled": Status.CANCELLED,  # Hãng vận chuyển hủy đơn
        "SoldOut": Status.CANCELLED,  # Hết hàng
        "Returning": Status.RETURNING,  # Đang chuyển hoàn
        "Returned": Status.RETURNED  # Đã chuyển hoàn
    }
    if order_type == 14 and nhanh_status == "Success":
        return Status.RETURNED.value
    return status_map.get(nhanh_status, Status.DRAFT).value


def map_channel_code(channel: int) -> str:
    """Map Nhanh status to MISA status."""
    customer_code_map = {
        1: "Admin",
        2: "Website",
        10: "API",
        20: "Facebook Chat",
        21: "Instagram Chat",
        41: "Lazada",
        42: "Shopee",
        43: "Sendo",
        45: "Tiki",
        46: "Zalo Shop",
        47: "1Landing",
        48: "Tiktok",
        49: "Zalo OA Chat",
        50: "Shopee Chat",
        51: "Lazada Chat"
    }
    return customer_code_map.get(channel, "")
