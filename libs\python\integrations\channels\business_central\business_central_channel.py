import json
from typing import Dict, Any, <PERSON><PERSON>, Optional, List

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.business_central.business_central_library import BusinessCentralLibrary
from integrations.channels.channel_library import AbstractChannel, OAuth2Channel
from integrations.channels.connection_types import ChannelType
from integrations.channels.business_central.business_central_connection import BusinessCentralSettings
from integrations.common.event import FetchEvent


class BusinessCentralChannel(OAuth2Channel, AbstractChannel):
    def initiate_oauth_flow(self, redirect_path: str = None, base_url: str = None) -> str:
        pass

    def refresh_oauth_token(self) -> Dict[str, Any]:
        pass

    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        pass

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> <PERSON><PERSON>[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        pass

    channel_name = "business_central"
    library_class = BusinessCentralLibrary

    def _create_library(self, connection):
        settings: BusinessCentralSettings = connection.attributes.settings
        return BusinessCentralLibrary(
            tenant_id=settings.tenant_id,
            environment=settings.environment,
            company_id=settings.company_id,
            access_token=settings.access_token,
            api_version=settings.api_version
        )

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            "name": "Business Central",
            "channel_type": ChannelType.ERP,
            "description": "Connect your Business Central instance to sync products, orders, and inventory.",
            "logo": cls._load_image_as_base64(__file__, "business_central.png")
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_order:
            return {
                "limit": 50,
                "filter": "",
                "orderby": "number desc"
            }
        elif action == ActionType.get_product:
            return {
                "limit": 50,
                "filter": "",
                "orderby": "number asc"
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_order:
            return {
                "update_inventory": True,
            }
        elif action == ActionType.sync_product:
            return {
                "update_pricing": True,
            }
        else:
            return {}

    async def get_order__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return await self.library.get_orders(settings, fetch_event.id)

    async def get_product__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return await self.library.get_products(settings, fetch_event.id)

    async def sync_order__publish(self, settings: Dict[str, Any], order_data: Dict[str, Any]):
        return await self.library.sync_order(settings, order_data)

    async def sync_product__publish(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        return await self.library.sync_product(settings, product_data)

    async def setup_webhooks(self):
        await self.library.setup_webhooks(self.connection)

    @staticmethod
    def verify_webhook_signature(payload: str, headers: dict, settings: BusinessCentralSettings) -> bool:
        return BusinessCentralLibrary.verify_webhook_signature(payload, headers.get('X-BC-Signature'),
                                                               settings.webhook_secret)

    @staticmethod
    def extract_merchant_id(payload):
        return BusinessCentralLibrary.extract_merchant_id(payload)

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return BusinessCentralLibrary.extract_event_type(payload, headers)
