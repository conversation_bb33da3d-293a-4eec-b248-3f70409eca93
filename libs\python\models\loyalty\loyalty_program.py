from dataclasses import dataclass
from decimal import Decimal
from typing import List

from elasticsearch import NotFoundError
from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.duration import DurationType
from models.media.image import Image, ImageSchema


@dataclass
class TierRule(Attributes):
    id: str = None
    name: str = None
    ratio: Decimal = None


class TierRuleSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    ratio = fields.Decimal(required=True)


@dataclass
class ProductRule(Attributes):
    variant_id: str = None
    name: str = None
    point: Decimal = None
    sku: str = None
    product_id: str = None
    money: Decimal = None


class ProductRuleSchema(AttributesSchema):
    variant_id = fields.Str(required=True)
    name = fields.Str(required=True)
    point = fields.Decimal(required=True)
    sku = fields.Str(required=True)
    product_id = fields.Str(required=True)
    money = fields.Decimal(allow_none=True)


@dataclass
class LoyaltyProgram(Attributes):
    id: str = None
    name: str = None
    money: Decimal = None
    start_date: str = None
    expiry_date: str = None
    number_used: int = 0
    image: Image = None
    diff_tier: bool = False
    diff_product: bool = False
    customer_groups: List[TierRule] = None
    products: List[ProductRule] = None
    accumulate_limit: bool = False
    duration_limit: DurationType = None
    number_limit: int = None


class LoyaltyProgramSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    money = fields.Decimal(required=True)
    start_date = fields.Str(required=True)
    expiry_date = fields.Str(allow_none=True)
    number_used = fields.Int(allow_none=True, default=0)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    diff_tier = fields.Bool(allow_none=True, default=False)
    diff_product = fields.Bool(allow_none=True, default=False)
    customer_groups = fields.List(fields.Nested(TierRuleSchema(TierRule)), allow_none=True)
    products = fields.List(fields.Nested(ProductRuleSchema(ProductRule)), allow_none=True)
    accumulate_limit = fields.Bool(allow_none=True)
    duration_limit = EnumField(DurationType, allow_none=True)
    number_limit = fields.Int(allow_none=True)


@dataclass
class LoyaltyProgramAttributes(LoyaltyProgram, BasicAttributes):
    pass


class LoyaltyProgramAttributesSchema(LoyaltyProgramSchema, BasicAttributesSchema):
    pass


class LoyaltyProgramModel(BasicModel):
    table_name = 'loyaltyProgram'
    attributes: LoyaltyProgramAttributes
    attributes_class = LoyaltyProgramAttributes
    attributes_schema = LoyaltyProgramAttributesSchema

    query_mapping = {
        'query': ['name', 'id'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'money': 'number',
            'number_used': 'number',
            'company_id': 'query',
            'start_date': 'range',
            'expiry_date': 'range',
            'updated_at': 'range',
            'created_at': 'range'
        },
        'mapping': {
            'image': 'image',
            'user': 'user',
        }
    }

    @classmethod
    def increase_used_number(cls, company_id, loyalty_program_id):
        loyalty_program_obj = cls.by_key({cls.key__id: loyalty_program_id, "company_id": company_id})
        loyalty_program_obj.update({"number_used": loyalty_program_obj.attributes.number_used + 1})

    @classmethod
    def get_effective_program(cls, company_id):
        try:
            search_params = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "term": {
                                    "company_id.keyword": company_id
                                }
                            },
                            {
                                "range": {
                                    "start_date": {
                                        "lte": "now",
                                    }
                                }
                            }
                        ],
                        "should": [
                            {
                                "range": {
                                    "expiry_date": {
                                        "gte": "now",
                                    }
                                }
                            },
                            {
                                "bool": {
                                    "must_not": {
                                        "exists": {
                                            "field": "expiry_date"
                                        }
                                    }
                                }
                            }
                        ],
                        "minimum_should_match": 1
                    }
                }
            }
            response = cls.report(search_params, service=RESOURCE_SERVICE).body['hits']['hits']
        except NotFoundError as ex:
            response = []
        result = [item['_source'] for item in response]
        if len(result) == 0:
            return None
        return cls(result[0])

