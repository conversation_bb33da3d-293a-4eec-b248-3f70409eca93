from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

from marshmallow import fields
from marshmallow_enum import Enum<PERSON><PERSON>
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


class Type(Enum):
    CASH = 'CASH'
    DIGITAL_WALLET = 'DIGITAL_WALLET'
    BANK = 'BANK'
    POINT = 'POINT'


@dataclass
class Account(Attributes):
    id: str = None
    user_id: str = None
    name: str = None
    type: Type = None
    balance: Decimal = 0
    version: int = 0
    last_transaction: str = None
    account_number: str = None
    account_name: str = None
    bank_name: str = None
    bank_id: str = None
    note: str = None


class AccountSchema(AttributesSchema):
    id = fields.Str(required=True)
    user_id = fields.Str(required=True)
    name = fields.Str(required=True)
    type = EnumField(Type, required=True, default=Type.CASH)
    account_number = fields.Str(allow_none=True)
    version = fields.Int(allow_none=True)
    balance = fields.Decimal(allow_none=True)
    last_transaction = fields.UUID(allow_none=True)
    account_name = fields.Str(allow_none=True)
    bank_name = fields.Str(allow_none=True)
    bank_id = fields.Str(allow_none=True)
    note = fields.Str(allow_none=True)


@dataclass
class AccountAttributes(Account, BasicAttributes):
    pass


class AccountAttributesSchema(AccountSchema, BasicAttributesSchema):

    @staticmethod
    def sample():
        return {
            "account_number": "asd",
            "note": "asd",
            "company_id": "e9da356c-b0c1-70f6-4290-8ca64e679fc5",
            "user_id": "e9da356c-b0c1-70f6-4290-8ca64e679fc5",
            "updated_at": "2023-09-07T08:44:49.790228+00:00",
            "account_name": "asd",
            "bank_name": "Vietcombank",
            "name": "Viettinbank",
            "created_at": "2023-09-06T08:11:20.940505+00:00",
            "id": "*************-452d-b3c7-3d46361d56c7",
            "type": "BANK"
        }


class AccountModel(BasicModel):
    table_name = 'account'
    version = True
    attributes: AccountAttributes
    attributes_schema = AccountAttributesSchema
    attributes_class = AccountAttributes

    query_mapping = {
        'query': ['id', 'name', 'account_number', 'account_name', 'bank_name', 'user_id'],
        'filter': {
            'id': 'query',
            'type': 'query',
            'bank_id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'balance': 'balance'
        }
    }

    @classmethod
    def initialize_data(cls, user_attributes_obj, initialized_data):
        company_id = user_attributes_obj.get('custom:company_id')
        user_id = user_attributes_obj.get('sub')
        data = [
            {"name": "Cash", "type": Type.CASH.value}
        ]
        init_data = [BasicAttributes.add_basic_attributes({**item, "user_id": user_id}, company_id) for item in data]
        initialized_data[cls.table_name] = init_data
        cls.batch_add(init_data)
