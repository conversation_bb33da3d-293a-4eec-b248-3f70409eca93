import os

from elasticsearch.helpers import bulk
from nolicore.adapters.db.model import Model
from nolicore.utils.api import api_message
from nolicore.utils.aws.boto_helper import get_elastic_search_client
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.utils import logger

from helpers.index_dynamo_table import table_mappings, create_index_not_existed
from helpers.lambda_client import lambda_client_invoke_async

INDEXES = {}
SERVICE = os.getenv('SERVICE')
ENV = os.getenv('ENV')


@as_api()
def re_index(api_gateway_request: ApiGatewayRequest):
    table_name = api_gateway_request.body['table_name']
    payload = {
        'table_name': table_name,
    }
    lambda_client_invoke_async(payload, function_name=f'optiwarehouse-resources-services-{ENV}-reIndexHandle')
    return api_message(f'Re Index {table_name} successfully!')


def re_index_handle(event, context):
    table_name = event['table_name']
    table_model: Model = table_mappings.get(table_name)
    index_name = table_model.get_search_index_name(SERVICE).lower()
    indexer = create_index_not_existed(table_name)
    last_evaluated_key = None
    while True:
        documents = []
        response = table_model.list({}, limit=50, last_evaluated_key=last_evaluated_key)
        last_evaluated_key = response.get('LastEvaluatedKey')

        for record in response.get('Items', []):
            key = table_model.get_key(record)
            documents.append({
                "_op_type": "update",
                "_index": index_name,
                "_id": indexer.get_search_key(key),
                "doc": table_model.get_index_data(record),
                "doc_as_upsert": True
            })

        if len(documents) > 0:
            elastic_search = get_elastic_search_client()
            success, failed = bulk(elastic_search, documents, raise_on_error=False, refresh="false")
            logger.info(f"Successful upserts: {success}")
            logger.info(f"Failed upserts: {failed}")

        if last_evaluated_key is None:
            break