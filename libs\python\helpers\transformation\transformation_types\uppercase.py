from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class UppercaseTransformationHandler(BaseTransformationHandler):
    """Handler for uppercase transformation"""
    
    config_definition = TransformationConfig(
        label="Uppercase",
        description="Convert text to uppercase",
        direction=TransformationDirection(
            title="Uppercase",
            content="Convert text to uppercase",
            example='"Blue Denim" → "BLUE DENIM"',
            value_type="Single Value"
        )
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Converts the value to uppercase if it's not None"""
        return str(value).upper() if value is not None else None 