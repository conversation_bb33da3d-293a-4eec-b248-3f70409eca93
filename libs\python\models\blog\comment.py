from dataclasses import dataclass

from elasticsearch import NotFoundError
from marshmallow import fields

from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


@dataclass
class CommentAttributes(BasicAttributes):
    blog_id: str = None
    user_id: str = None
    comment: str = None


class CommentAttributesSchema(BasicAttributesSchema):
    blog_id = fields.Str(required=True)
    user_id = fields.Str(required=True)
    comment = fields.Str(required=True)


class CommentModel(BasicModel):
    table_name = 'comment'
    key__id = 'blog_id'
    key__ranges = ['id']
    attributes: CommentAttributes
    attributes_class = CommentAttributes
    attributes_schema = CommentAttributesSchema
    query_mapping = {
        'query': ['id', 'user_id', 'blog_id'],
        'filter': {
            'id': 'query',
            'blog_id': 'query',
            'user_id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range'
        },
        "mapping": {
            "comment": "comment",
            "user": "user"
        }
    }

    @classmethod
    def comment_count(cls, params):
        try:
            count_params = {
                "query": {
                    "bool": {
                        "filter": {
                            "term":
                                params
                        }
                    }
                }
            }
            file_count = cls.count(count_params, service=RESOURCE_SERVICE).body['count']
            return file_count
        except NotFoundError:
            return 0
