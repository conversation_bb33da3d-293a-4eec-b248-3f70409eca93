from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class PadTransformationHandler(BaseTransformationHandler):
    """Handler for pad transformation"""
    
    config_definition = TransformationConfig(
        label="Pad",
        description="Add padding to text",
        direction=TransformationDirection(
            title="Pad Text",
            content="Add padding characters to the start or end of text",
            example='"Hello" → "  Hello  " (length: 9, char: space)',
            value_type="Single Value"
        ),
        config_fields=[
            {
                "key": "length",
                "label": "Total Length",
                "type": "number",
                "placeholder": "10",
                "description": "The total length of the padded string"
            },
            {
                "key": "char",
                "label": "Padding Character",
                "type": "text",
                "placeholder": "space",
                "description": "The character to use for padding"
            },
            {
                "key": "side",
                "label": "Padding Side",
                "type": "select",
                "options": ["start", "end", "both"],
                "placeholder": "both",
                "description": "Where to add the padding"
            }
        ]
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Adds padding to the value if it's not None"""
        if value is None:
            return None
            
        if not self.transformation.config or 'length' not in self.transformation.config:
            return value
            
        try:
            length = int(self.transformation.config['length'])
            char = self.transformation.config.get('char', ' ')
            side = self.transformation.config.get('side', 'both')
            
            value_str = str(value)
            padding_length = max(0, length - len(value_str))
            
            if side == 'start':
                return (char * padding_length) + value_str
            elif side == 'end':
                return value_str + (char * padding_length)
            else:  # both
                left_pad = padding_length // 2
                right_pad = padding_length - left_pad
                return (char * left_pad) + value_str + (char * right_pad)
                
        except (ValueError, TypeError):
            return value 