import os

import elasticsearch
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest

from ..libs.dashboard import connection_execute_query, generate_connection_report, \
    fetch_event_execute_query, generate_fetch_event_report, sync_record_execute_query, generate_sync_record_report
from ..libs.helper import get_time_period

RESOURCE_SERVICE = os.getenv('RESOURCE_SERVICE')


@as_api()
def connection_summary(api_gateway_request: ApiGatewayRequest):
    # Get time period or custom dates from query parameters
    company_id = api_gateway_request.company_id

    try:
        # Execute Elasticsearch queries
        result = connection_execute_query(company_id=company_id)
        connection_report = generate_connection_report(result)
        # Return report
        return connection_report
    except elasticsearch.NotFoundError:
        return {
            "num_connections": 0,
            "num_active": 0,
            "num_inactive": 0,
            "num_pending": 0,
        }


@as_api()
def fetch_event_summary(api_gateway_request: ApiGatewayRequest):
    # Get time period or custom dates from query parameters
    period = api_gateway_request.body.get('period')
    from_date = api_gateway_request.body.get('date_from')
    to_date = api_gateway_request.body.get('date_to')
    company_id = api_gateway_request.company_id

    start_date, end_date, comparison_start_date, comparison_end_date = get_time_period(period, from_date, to_date)

    try:
        # Execute Elasticsearch queries
        result = fetch_event_execute_query(company_id=company_id, start_date=start_date, end_date=end_date)
        comparison_result = fetch_event_execute_query(company_id=company_id, start_date=comparison_start_date,
                                                      end_date=comparison_end_date)
        # Generate report
        report = generate_fetch_event_report(period if period else 'custom', result, comparison_result)

        # Return report
        return report
    except elasticsearch.NotFoundError:
        return {
            "period": period.capitalize(),
            "num_fetch_events": 0,
            "status_counts": {
                "num_pending": 0,
                "num_processing": 0,
                "num_completed": 0,
                "num_failed": 0,
            },
            "event_source_counts": {
                "num_webhook": 0,
                "num_scheduler": 0,
                "num_sync_record_api": 0,
                "num_sync_filter_api": 0,
            }
        }


@as_api()
def sync_record_summary(api_gateway_request: ApiGatewayRequest):
    # Get time period or custom dates from query parameters
    period = api_gateway_request.body.get('period')
    from_date = api_gateway_request.body.get('date_from')
    to_date = api_gateway_request.body.get('date_to')
    company_id = api_gateway_request.company_id

    start_date, end_date, comparison_start_date, comparison_end_date = get_time_period(period, from_date, to_date)
    try:
        # Execute Elasticsearch queries
        result = sync_record_execute_query(company_id=company_id, start_date=start_date, end_date=end_date)
        comparison_result = sync_record_execute_query(company_id=company_id, start_date=comparison_start_date,
                                                      end_date=comparison_end_date)
        # Generate report
        report = generate_sync_record_report(period if period else 'custom', result, company_id, comparison_result)

        # Return report
        return report
    except elasticsearch.NotFoundError:
        return {
            "period": period.capitalize(),
            "by_connection": {}
        }
