from helpers.errors import UnitNameExisted, UnitRatioExisted, UnitDataInvalid
from helpers.utils import RESOURCE_SERVICE
from models.product.unit import UnitModel


def validate_unit(name, ratio, company_id, unit_obj: UnitModel = None):
    if not name or not ratio:
        raise UnitDataInvalid()
    units = UnitModel.search({"name": name}, service=RESOURCE_SERVICE, company_id=company_id).get("items", [])
    if len(units) > 0:
        if unit_obj is None:
            raise UnitNameExisted()
        if unit_obj and unit_obj.attributes.name != name:
            raise UnitNameExisted()
    units = UnitModel.search({"ratio": ratio}, service=RESOURCE_SERVICE, company_id=company_id).get("items", [])
    if len(units) > 0:
        if unit_obj is None:
            raise UnitRatioExisted()
        if unit_obj and unit_obj.attributes.ratio != int(ratio):
            raise UnitRatioExisted()
