{"name": "Optiwarehouse.com", "version": "1.0.0", "description": "", "main": "handler.py", "scripts": {"test": "python test.py"}, "author": "", "license": "ISC", "dependencies": {"serverless-dynamodb-local": "^0.2.40", "serverless-python-requirements": "^5.4.0"}, "devDependencies": {"@serverless/compose": "^1.3.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-dynamodb-autoscaling": "^0.6.2", "serverless-offline": "^12.0.4", "serverless-openapi-documentation": "^0.4.0", "serverless-prune-plugin": "^2.1.0", "serverless-s3-local": "^0.7.2"}}