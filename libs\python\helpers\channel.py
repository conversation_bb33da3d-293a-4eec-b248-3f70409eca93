from nolicore.utils.exceptions import NotFoundRequest

from integrations.common.connection_helper import get_connection_by_id


def get_channel(connection_id: str, company_id: str):
    # Get the connection object
    connection = get_connection_by_id(connection_id, company_id)
    if not connection:
        raise NotFoundRequest("Connection not found")

    # Get the channel instance
    channel = connection.get_channel()

    # Get channel information
    channel_info = {
        "channel_name": connection.attributes.channel_name,
        "channel_type": connection.attributes.channel_type,
        "status": connection.attributes.status,
        "name": connection.attributes.name,
        "description": connection.attributes.description,
        "logo": connection.attributes.logo,
        "settings": connection.attributes.settings,
        "dynamic_settings": channel.get_dynamic_settings() if hasattr(channel, 'get_dynamic_settings') else {}
    }

    return {"channel_info": channel_info, "channel": channel}
