import json
import unittest
import uuid
from unittest.mock import patch
from dotenv import load_dotenv

from tests.base import BaseTestCase
from connections.src.apis.connection import setup_connection
from integrations.channels.nhanh.nhanh_connection import NhanhConnection


class NhanhConnectionSetupIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.app_id = '74307'
        cls.business_id = 'NHANH_BUSINESS_ID'
        cls.secret = '9336ddcdee3c276b47687ec44091b821'

        if not all([cls.app_id, cls.business_id, cls.secret]):
            raise ValueError("Nhanh credentials not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        self.company_id = str(uuid.uuid4())
        self.user_id = str(uuid.uuid4())
        self.username = f"user_{self.user_id}"

    def create_lambda_event(self, body):
        return {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_setup_nhanh_connection_success(self):
        # Arrange

        event_body = {
            "connection_type": "nhanh",
            "settings": {
                "app_id": self.app_id,
                "business_id": self.business_id,
                "secret": self.secret,
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'nhanh OAuth2 flow initiated')
        self.assertIn('authorization_url', response_body)
        self.assertIn('connection_id', response_body)

        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = NhanhConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.settings.app_id, self.app_id)
        self.assertEqual(created_connection.attributes.settings.business_id, self.business_id)
        self.assertEqual(created_connection.attributes.settings.secret, self.secret)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status,
                         'PENDING')  # Status should be PENDING until OAuth flow is completed

        # Clean up
        created_connection.delete()

    def test_setup_nhanh_connection_missing_credentials(self):
        # Arrange
        event_body = {
            "connection_type": "nhanh",
            "settings": {
                "app_id": self.app_id,
                # Missing business_id and secret
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['error'], 'Bad request')
        self.assertIn('Missing required fields', response_body['message'])

    @patch('integrations.channels.nhanh.nhanh_connection.NhanhConnection.get_channel')
    def test_setup_nhanh_connection_oauth_initiation_failure(self, mock_get_channel):
        # Arrange
        mock_channel = mock_get_channel.return_value
        mock_channel.library_class.test_connection.side_effect = Exception("Failed to initiate OAuth flow")

        event_body = {
            "connection_type": "nhanh",
            "settings": {
                "app_id": self.app_id,
                "business_id": self.business_id,
                "secret": self.secret,
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['error'], 'Bad request')
        self.assertIn('Failed to initiate OAuth flow', response_body['message'])


if __name__ == '__main__':
    unittest.main()
