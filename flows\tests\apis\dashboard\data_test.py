search_event = {'version': '2.0', 'routeKey': 'POST /connections/{connection_id}/search_records', 'rawPath': '/connections/6b5ca363-434b-4127-8258-3bbf1afc5e50/search_records', 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.d4Lze9cQK6kLY-xFYbi0J8CNKlrtRrZtK3DK-cizOiW9qe0EE6d2cBAjdssxjQ_kNYTms5W9NOgczyc0uwYhreFzfsGJEmTDVLq9RxTeb9bTycJAH30_KJ0PJ8P26x86p51CgIYjC9dS7d8Q8lmsA4_DY0zePp96qyBwtUaSmk3QiiIi5j92QZW_6WnBYGjDmrX0TZYleVSikuLc_bvXUPBC0DN10aXQiye6DLN2v4NKNxWhUqxv8CqhMp-GR01bzyuSmdzlzqu_7hkqa9OZeDvz264Icl1LEgMDXwPx-UcfEtDFsTq3AUt5PsP0svIFy1YRmGikOULcm3A9gPhjBQ', 'headers': {'accept': 'application/json, text/plain, */*', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en,vi;q=0.9,en-US;q=0.8', 'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tvFVGbPZa8TOX0v5GEYwm6wTehQjNBJznreR_esPk3W_axNCyH4B5ohahtIsltXdCF39g8g_RCKNDAFr3VLycM0AYu1qUA9ZujSXpwoi9mwj1qn1gOWMxpMDo88zqiG_eZKIrLXFEyKcHwBUjSyoVdpXmgvwIU2Qgin7je4y-YSaqCY3G48gEI3tl3Ov8y-OkgSy9_RdceK5Thowe3-UEILpBB6-fcogYQyjtdbiLHdtL37aXORqdpnuGevzBY82Xq9C00wWK3niiO06nWGE3gxhD6s5HF9nSaSoIdu38FIUEJj_Fn5J6O9H9y9C6WkF0yDZRiUlP7ihwbXlLSXfXA', 'content-length': '89', 'content-type': 'application/json', 'host': 'api.optiwarehouse.com', 'origin': 'https://app.optiwarehouse.com', 'priority': 'u=1, i', 'referer': 'https://app.optiwarehouse.com/', 'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'x-amzn-trace-id': 'Root=1-6752a78b-30a2ead03cf1263f4aa3af55', 'x-forwarded-for': '*************', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https'}, 'queryStringParameters': {'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.d4Lze9cQK6kLY-xFYbi0J8CNKlrtRrZtK3DK-cizOiW9qe0EE6d2cBAjdssxjQ_kNYTms5W9NOgczyc0uwYhreFzfsGJEmTDVLq9RxTeb9bTycJAH30_KJ0PJ8P26x86p51CgIYjC9dS7d8Q8lmsA4_DY0zePp96qyBwtUaSmk3QiiIi5j92QZW_6WnBYGjDmrX0TZYleVSikuLc_bvXUPBC0DN10aXQiye6DLN2v4NKNxWhUqxv8CqhMp-GR01bzyuSmdzlzqu_7hkqa9OZeDvz264Icl1LEgMDXwPx-UcfEtDFsTq3AUt5PsP0svIFy1YRmGikOULcm3A9gPhjBQ'}, 'requestContext': {'accountId': '************', 'apiId': 'vxwk2of2ii', 'authorizer': {'jwt': {'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********', 'cognito:groups': '[4c8e6154-fba3-4837-909b-8a50dff16a2b-User 4c8e6154-fba3-4837-909b-8a50dff16a2b-Admin]', 'cognito:username': 'baababy_admin', 'custom:company_id': '4c8e6154-fba3-4837-909b-8a50dff16a2b', 'custom:is_init': 'DONE', 'custom:role': 'Admin', 'email': '<EMAIL>', 'event_id': 'c1fccdba-e7f1-4057-9626-341687bde084', 'exp': '**********', 'iat': '**********', 'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H', 'jti': '33b5c9a5-2476-422c-a95b-231551020ae7', 'origin_jti': 'eca15ed1-64fe-470a-a43b-21957693770d', 'sub': '56c8ca59-2214-4d3e-95f8-0fa3178d0031', 'token_use': 'id'}, 'scopes': None}}, 'domainName': 'api.optiwarehouse.com', 'domainPrefix': 'api', 'http': {'method': 'POST', 'path': '/connections/6b5ca363-434b-4127-8258-3bbf1afc5e50/search_records', 'protocol': 'HTTP/1.1', 'sourceIp': '*************', 'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}, 'requestId': 'CW8dzji6yQ0EJzA=', 'routeKey': 'POST /connections/{connection_id}/search_records', 'stage': '$default', 'time': '06/Dec/2024:07:28:11 +0000', 'timeEpoch': 1733470091128}, 'pathParameters': {'connection_id': '6b5ca363-434b-4127-8258-3bbf1afc5e50'}, 'body': '{"action_type":"get_product","params":{"updatedDateTimeFrom":"2024-12-05T17:00:00.000Z"}}', 'isBase64Encoded': False}

sync_event = {'version': '2.0', 'routeKey': 'POST /connections/{connection_id}/sync_records', 'rawPath': '/connections/6b5ca363-434b-4127-8258-3bbf1afc5e50/sync_records', 'rawQueryString': 'access_token=**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'headers': {'accept': 'application/json, text/plain, */*', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en,vi;q=0.9,en-US;q=0.8', 'authorization': 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'content-length': '119', 'content-type': 'application/json', 'host': 'api.optiwarehouse.com', 'origin': 'https://app.optiwarehouse.com', 'priority': 'u=1, i', 'referer': 'https://app.optiwarehouse.com/', 'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'x-amzn-trace-id': 'Root=1-6752f4f9-50283c394c5a23bd7e21fb0a', 'x-forwarded-for': '*************', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https'}, 'queryStringParameters': {'access_token': '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'}, 'requestContext': {'accountId': '************', 'apiId': 'vxwk2of2ii', 'authorizer': {'jwt': {'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********', 'cognito:groups': '[4c8e6154-fba3-4837-909b-8a50dff16a2b-User 4c8e6154-fba3-4837-909b-8a50dff16a2b-Admin]', 'cognito:username': 'baababy_admin', 'custom:company_id': '4c8e6154-fba3-4837-909b-8a50dff16a2b', 'custom:is_init': 'DONE', 'custom:role': 'Admin', 'email': '<EMAIL>', 'event_id': 'c1fccdba-e7f1-4057-9626-341687bde084', 'exp': '**********', 'iat': '**********', 'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H', 'jti': 'faa792a8-0db2-49fb-a0e8-57e38bd3e014', 'origin_jti': 'eca15ed1-64fe-470a-a43b-21957693770d', 'sub': '56c8ca59-2214-4d3e-95f8-0fa3178d0031', 'token_use': 'id'}, 'scopes': None}}, 'domainName': 'api.optiwarehouse.com', 'domainPrefix': 'api', 'http': {'method': 'POST', 'path': '/connections/6b5ca363-434b-4127-8258-3bbf1afc5e50/sync_records', 'protocol': 'HTTP/1.1', 'sourceIp': '*************', 'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}, 'requestId': 'CXs3AjsmyQ0EMDQ=', 'routeKey': 'POST /connections/{connection_id}/sync_records', 'stage': '$default', 'time': '06/Dec/2024:12:58:33 +0000', 'timeEpoch': 1733489913291}, 'pathParameters': {'connection_id': '6b5ca363-434b-4127-8258-3bbf1afc5e50'}, 'body': '{"action_type":"get_return_order","params":{"fromDate":"2024-11-30T17:00:00.000Z","toDate":"2024-12-06T12:56:24.900Z"}}', 'isBase64Encoded': False}
