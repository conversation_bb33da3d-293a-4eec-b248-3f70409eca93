from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import BadRequest

from helpers.utils import RESOURCE_SERVICE
from models.actor.customer import CustomerModel
from models.bots.conversation import ConversationModel
from models.bots.virtual_staff import VirtualStaffModel
from ..helpers.conversation import (
    create_conversation as create_conversation_helper,
    get_conversation as get_conversation_helper,
    delete_conversation as delete_conversation_helper
)
from ..helpers.message import invoke_delete_messages_by_conversation


@as_api()
def create_conversation(api_gateway_request: ApiGatewayRequest):
    """Create a new conversation or return existing one.
    
    Request body:
    {
        "name": "string",
        "customer_name": "string",
        "customer_phone_number": "string",
        "image": "string",  # optional
        "assignee_id": "string",
        "staff_id": string
        "role": "USER" or "VIRTUAL_STAFF"  # optional, defaults to VIRTUAL_STAFF
    }
    """
    company_id = api_gateway_request.company_id
    body = api_gateway_request.body
    if body.get('role') == 'VIRTUAL_STAFF':
        virtual_staff = VirtualStaffModel.get_by_id(body['assignee_id'])
        if not virtual_staff:
            raise BadRequest("Virtual staff not found")
        company_id = str(virtual_staff.attributes.company_id)
    

    # Validate required fields
    required_fields = ['customer_name', 'customer_phone_number', 'staff_id','assignee_id']
    for field in required_fields:
        if field not in body:
            raise BadRequest(f"Missing required field: {field}")

    try:
        # Create or get customer
        customer_data = {
            'first_name': body['customer_name'],
            'phone': body['customer_phone_number']
        }

        # Get or create customer
        customer = CustomerModel.get_customer_by_phone(body['customer_phone_number'])
        if not customer:
            customer = CustomerModel.sync(company_id, customer_data)

        # Add customer_id to conversation data
        conversation_data = body.copy()
        conversation_data['customer_id'] = customer['id']

        # Remove customer-specific fields
        conversation_data.pop('customer_name', None)
        conversation_data.pop('customer_phone_number', None)

        # Set members to include assignee_id and customer_id
        conversation_data['members'] = [
            conversation_data['assignee_id'] or conversation_data['staff_id'],
            conversation_data['customer_id']
        ]

        return create_conversation_helper(company_id, conversation_data)
    except Exception as e:
        raise BadRequest(f"Failed to create conversation: {str(e)}")
    
@as_api()
def create_public_conversation(api_gateway_request: ApiGatewayRequest):
    """Create a new conversation or return existing one.
    
    Request body:
    {
        "name": "string",
        "customer_name": "string",
        "customer_phone_number": "string",
        "assignee_id": "string",
        "staff_id": "string"
    }
    """
    body = api_gateway_request.body
    required_fields = ['customer_name', 'customer_phone_number', 'assignee_id', "staff_id"]
    for field in required_fields:
        if field not in body:
            raise BadRequest(f"Missing required field: {field}")
    virtual_staff = VirtualStaffModel.get_by_id(body['assignee_id'])
    if not virtual_staff:
        raise BadRequest("Virtual staff not found")
        
    company_id = str(virtual_staff.attributes.company_id)

    try:
        # Create or get customer
        customer_data = {
            'first_name': body['customer_name'],
            'phone': body['customer_phone_number']
        }

        # Get or create customer
        customer = CustomerModel.get_customer_by_phone(body['customer_phone_number'])
        if not customer:
            customer = CustomerModel.sync(company_id, customer_data)

        # Add customer_id to conversation data
        conversation_data = body.copy()
        conversation_data['customer_id'] = customer['id']

        # Remove customer-specific fields
        conversation_data.pop('customer_name', None)
        conversation_data.pop('customer_phone_number', None)

        # Set members to include assignee_id and customer_id
        conversation_data['members'] = [
            conversation_data['assignee_id'],
            conversation_data['customer_id']
        ]
        return create_conversation_helper(company_id, conversation_data)
    except Exception as e:
        raise BadRequest(f"Failed to create conversation: {str(e)}")

@as_api()
def get_conversation(api_gateway_request: ApiGatewayRequest):
    """Get a conversation by ID."""
    conversation_id = api_gateway_request.path_parameters.get('id')

    if not conversation_id:
        raise BadRequest("Missing required field: id")

    try:
        return get_conversation_helper(conversation_id)
    except Exception as e:
        raise BadRequest(str(e))


@as_api()
def list_conversations(api_gateway_request: ApiGatewayRequest):
    """List conversations with optional filters.
    
    Query parameters:
    - id: Filter by conversation ID
    - name: Filter by conversation name
    - assignee_id: Filter by assignee ID
    - role: Filter by role (USER or VIRTUAL_STAFF)
    - customer_id: Filter by customer ID
    - created_at_start: Start date for created_at range
    - created_at_end: End date for created_at range
    - updated_at_start: Start date for updated_at range
    - updated_at_end: End date for updated_at range
    """
    company_id = api_gateway_request.company_id or api_gateway_request.query_string_parameters.get('company_id')
    params = api_gateway_request.query_string_parameters or {}

    try:
        return ConversationModel.search(
            params=params,
            service=RESOURCE_SERVICE,
            company_id=company_id
        )
    except Exception as e:
        raise BadRequest(f"Failed to list conversations: {str(e)}")


@as_api()
def delete_conversation(api_gateway_request: ApiGatewayRequest):
    """Delete a conversation and its associated messages."""
    conversation_id = api_gateway_request.path_parameters.get('id')

    if not conversation_id:
        raise BadRequest("Missing required field: id")

    try:
        # Delete all messages associated with the conversation
        invoke_delete_messages_by_conversation(conversation_id)

        # Delete the conversation
        return delete_conversation_helper(conversation_id)
    except Exception as e:
        raise BadRequest(f"Failed to delete conversation: {str(e)}")
