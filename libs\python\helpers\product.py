import os
import uuid

from nolicore.utils.exceptions import BadRequest, NotFoundRequest
from nolicore.utils.utils import compress, logger

from helpers.common import get_default_source
from helpers.errors import SKUExisted, SlugExisted
from helpers.image import upload_image
from helpers.lambda_client import lambda_client_invoke_async
from helpers.text import get_slug_by_name
from helpers.variant import sync_variant, need_update_variants
from models.basic import BasicAttributes
from models.product.brand import BrandModel
from models.product.category import CategoryModel
from models.product.product import ProductSkuIndex, ProductModel, ProductSlugIndex
from models.product.variant import ProductIdIndex, VariantModel, VariantSkuIndex, VariantSlugIndex

ENV = os.getenv('ENV')


def invoke_import_product(request_id, company_id, start=0, source=None):
    payload = {
        'request_id': request_id,
        'company_id': str(company_id),
        'start': start,
        'source': source
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-products-service-{ENV}-importProductHandle')


def invoke_format_product(request_id, company_id, header):
    payload = {
        'request_id': request_id,
        'company_id': str(company_id),
        'header': header,
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-products-service-{ENV}-formatProductHandle')


def import_product(company_id, product, product_id=None, source=None):
    logger.info({
        'company_id': str(company_id),
        'product': product,
        'product_id': product_id
    })
    # validate variant sku
    remote_variants = {}
    variants = product.get('variants') or [{
        "option1": "Product",
        "name": product['name'],
        "product_id": product_id,
        "sku": product['sku'],
        "images": product.get('images', []),
        "prices": product.get('prices', []),
    }]
    if source is not None:
        product['source'] = source
    for variant in variants:
        remote_variant = VariantSkuIndex.get_variant_by_sku(variant['sku'], company_id)
        if remote_variant:
            remote_variants[remote_variant.attributes.sku] = remote_variant
    product_ids = {v.attributes.product_id for v in remote_variants.values()}
    if len(product_ids) > 1:
        raise SKUExisted(f'Sku exist in different products {product_ids}')
    if len(product_ids) == 1:
        remote_product_id = list(product_ids)[0]
        if product_id and remote_product_id != product_id:
            raise SKUExisted(f'Sku exist in different product {product_id}')
        product_id = remote_product_id

    if product_id is None:
        # find product by sku
        sku = product['sku']
        logger.info('Get product by sku')
        product_obj = ProductSkuIndex.get_product_by_sku(sku, company_id)

        # create new product_id if product not found
        if product_obj is None:
            product_id = str(uuid.uuid4())
            if ProductModel.key__id not in product:
                product[ProductModel.key__id] = product_id
        else:
            logger.info('Found product by sku')
            product_id = str(product_obj.attributes.id)
    else:
        product_obj = ProductModel.by_key({
            ProductModel.key__id: product_id,
            'company_id': company_id
        })
        if product_obj is None:
            product['id'] = product_id

    # handle brand attribute
    brand = product.pop('brand')
    brand_obj = None
    if brand is not None:
        if isinstance(brand, dict):
            if 'id' not in brand and brand.get('name'):
                if not brand.get('name'):
                    raise BadRequest("Invalid brand name")
                brand['id'] = compress(brand['name'])
                logger.info('Save brand')
                brand_obj = BrandModel.put(brand, company_id)
            else:
                brand_id = brand.get('id')
                if not brand_id:
                    raise BadRequest("Invalid brand id")
                else:
                    brand_obj = BrandModel.by_key({
                        BrandModel.key__id: brand_id,
                        "company_id": company_id
                    })
                    if brand_obj is None:
                        raise NotFoundRequest('Band could not found')
        elif isinstance(brand, str):
            brand_obj = BrandModel.put({'id': compress(brand), "name": brand}, company_id)
    product['brand'] = {k: v for k, v in brand_obj.attributes_dict.items() if k not in {
        'created_at', 'company_id', 'updated_at', 'user'
    }} if brand_obj else None

    # handle category attribute
    category = product.pop('category')
    category_obj = None
    if category is not None:
        if isinstance(category, dict):
            if 'id' not in category and category.get('name'):
                if not category.get('name'):
                    raise BadRequest("Invalid category name")
                category['id'] = compress(category['name'])
                logger.info('Save category')
                category_obj = CategoryModel.put(category, company_id)
            else:
                category_id = category.get('id')
                if not category_id:
                    raise BadRequest("Invalid category id")
                else:
                    category_obj = CategoryModel.by_key({
                        CategoryModel.key__id: category_id,
                        "company_id": company_id
                    })
                    if category_obj is None:
                        raise NotFoundRequest('Category could not found')
        elif isinstance(category, str):
            category_obj = CategoryModel.put({'id': compress(category), "name": category}, company_id)
    product['category'] = {k: v for k, v in category_obj.attributes_dict.items() if k not in {
        'created_at', 'company_id', 'updated_at', 'user'
    }} if category_obj else None

    # handle image update
    images = []
    for image in product.get('images', []):
        if "url" in image:
            if image['url'].startswith('http'):
                images.append(upload_image(company_id, 'products', image))
        else:
            images.append(upload_image(company_id, 'products', image))
    logger.info('Finish process image')
    product['images'] = images
    product['publish'] = product.get('publish', False)
    slug = product.get('slug')
    if not slug:
        slug = get_slug_by_name(product['name'], product['sku'])
    if product_obj:
        if product_obj.attributes.slug != slug:
            product_obj_check = ProductSlugIndex.get_product_by_slug(slug, company_id)
            if product_obj_check:
                raise SlugExisted()
    else:
        product_obj_check = ProductSlugIndex.get_product_by_slug(slug, company_id)
        if product_obj_check:
            raise SlugExisted()
    product['slug'] = slug

    variant_skus = {v['sku'] for v in variants}
    logger.info(variant_skus)
    current_variants = ProductIdIndex.get_variants(product_id, company_id)
    logger.info(current_variants)
    current_variant_skus = {v['sku']: v for v in current_variants}
    # delete old variant
    variant_to_delete = current_variant_skus.keys() - variant_skus
    logger.info(variant_to_delete)
    for sku_to_delete in variant_to_delete:
        VariantModel.delete({
            'id': current_variant_skus[sku_to_delete]['id'],
            'company_id': company_id
        })

    if need_update_variants(product_obj, variants):
        logger.info('Sync variants')
        synced_variants = []
        variant_slugs = []
        for variant in variants:
            variant['product_id'] = product_id
            variant['measurements'] = variant.get('measurements') or product['measurements']
            variant['brand'] = variant.get('brand') or product.get('brand')
            variant['category'] = variant.get('category') or product.get('category')
            variant_slug = variant.get('slug')
            if not variant_slug:
                variant_slug = get_slug_by_name(variant['name'], variant['sku'])
            remote_variant = remote_variants.get(variant['sku'])
            if remote_variant:
                if remote_variant.attributes.slug != variant_slug:
                    # check existed
                    variant_obj_check = VariantSlugIndex.get_variant_by_slug(variant_slug, company_id)
                    if variant_obj_check:
                        raise BadRequest(f'Variant Slug existed: {variant_slug}')
            else:
                # check existed
                variant_obj_check = VariantSlugIndex.get_variant_by_slug(variant_slug, company_id)
                if variant_obj_check:
                    raise BadRequest(f'Variant Slug existed: {variant_slug}')
            # check duplicated
            if variant_slug in variant_slugs:
                raise BadRequest(f'Slug {variant_slug} duplicated')
            variant_slugs.append(variant_slug)
            variant['slug'] = variant_slug

            # not allow updating inventories
            variant.pop('inventories', None)
            synced_variants.append(
                sync_variant(company_id, variant, remote_variant=remote_variant))

        logger.info('Finish sync variants')
        product['variants'] = synced_variants
        if 'options' not in product:
            options = {}
            for variant in synced_variants:
                for index in range(1, 4):
                    option_title = variant.get(f'optionTitle{index}') or f'Attribute {index}'
                    options.setdefault(option_title, [])
                    if variant[f'option{index}']:
                        option_value = variant[f'option{index}']
                        if option_value not in options[option_title]:
                            options[option_title].append(option_value)

            product['options'] = [{'name': option_title, 'values': values} for option_title, values in options.items()
                                  if len(values) > 0]
    if product.get('source') is None:
        product['source'] = get_default_source()
    if product_obj:
        logger.info(product)
        product_obj.update(product)
        logger.info('Update product')
    else:
        logger.info('Create new product')
        product = BasicAttributes.add_basic_attributes(product, company_id)
        product_obj = ProductModel(product)
        product_obj.save()
    return product_obj.attributes_dict

def invoke_bulk_delete_products_handler(company_id, product_ids):
    payload = {
        'company_id': str(company_id),
        'product_ids': product_ids
    }
    return lambda_client_invoke_async(payload, function_name=f'optiwarehouse-products-service-{ENV}-bulkDeleteProductsHandler')

