from typing import Dict, Any

from integrations.channels.action_types import ActionType
from integrations.channels.channel_library import AbstractChannel
from integrations.channels.connection_types import ChannelType
from integrations.channels.shopee.shopee_library import ShopeeLibrary
from integrations.channels.shopee.shopee_connection import ShopeeSettings


class ShopeeChannel(AbstractChannel):
    channel_name = "shopee"
    library_class = ShopeeLibrary

    def _create_library(self, connection):
        settings: ShopeeSettings = connection.attributes.settings
        return ShopeeLibrary(
            partner_id=settings.partner_id,
            partner_key=settings.partner_key,
            shop_id=settings.shop_id,
            access_token=settings.access_token,
            base_url=settings.base_url
        )

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            "name": "Shopee",
            "channel_type": ChannelType.MARKETPLACE,
            "description": "Connect your Shopee store to sync products, orders, and inventory.",
            "logo": cls._load_image_as_base64(__file__, "shopee.png")
        }

    @classmethod
    def get_integration_info(cls) -> Dict[str, str]:
        """
        Returns a dictionary containing channel information.
        Must include 'name', 'description', and 'image' keys.
        """
        return {
            "title": "Seamless Integration with Shopee",
            "description":
                "Effortlessly sync your e-commerce data with Shopee through our standardized API.",
            "integration": {
                "Product API": {
                    "description":
                        "Manage your product listings, including creating, updating, and deleting products.",
                    "apis": [
                        "POST /api/v1/product/add",
                        "PUT /api/v1/product/update",
                        "DELETE /api/v1/product/delete",
                    ],
                },
                "Order API": {
                    "description":
                        "Handle order management, from retrieving order details to updating order statuses.",
                    "apis": [
                        "GET /api/v1/orders",
                        "POST /api/v1/orders/update",
                        "GET /api/v1/orders/details",
                    ],
                },
                "Inventory API": {
                    "description":
                        "Keep your inventory levels synchronized with real-time updates.",
                    "apis": ["GET /api/v1/inventory", "POST /api/v1/inventory/update"],
                },
                "Logistics API": {
                    "description":
                        "Manage shipping and logistics, including tracking shipments and updating statuses.",
                    "apis": ["GET /api/v1/logistics/track", "POST /api/v1/logistics/update"],
                },
                "Payment API": {
                    "description":
                        "Handle payment processing and transaction details securely.",
                    "apis": ["GET /api/v1/payments", "POST /api/v1/payments/verify"],
                },
            },
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_order:
            return {
                "limit": 50,
                "order_status": "READY_TO_SHIP"
            }
        elif action == ActionType.get_product:
            return {
                "limit": 50,
                "item_status": "NORMAL"
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_order:
            return {
                "status": "READY_TO_SHIP",
                "update_inventory": True,
            }
        elif action == ActionType.sync_product:
            return {
                "update_stock": True,
                "update_price": True,
            }
        else:
            return {}

    async def get_order__fetch(self, settings: Dict[str, Any]):
        return await self.library.get_orders(settings)

    async def get_product__fetch(self, settings: Dict[str, Any]):
        return await self.library.get_products(settings)

    async def sync_order__publish(self, settings: Dict[str, Any], order_data: Dict[str, Any]):
        return await self.library.sync_order(settings, order_data)

    async def sync_product__publish(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        return await self.library.sync_product(settings, product_data)

    async def setup_webhooks(self):
        await self.library.setup_webhooks(self.connection)

    @staticmethod
    def verify_webhook_signature(payload: str, headers: dict, settings: ShopeeSettings) -> bool:
        return ShopeeLibrary.verify_webhook_signature(payload, headers.get('X-Shopee-Signature'),
                                                      settings.partner_key)

    @staticmethod
    def extract_merchant_id(payload):
        return ShopeeLibrary.extract_merchant_id(payload)

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return ShopeeLibrary.extract_event_type(payload, headers)
