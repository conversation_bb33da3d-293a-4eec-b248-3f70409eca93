from typing import Dict, Any, Tuple, Optional

from integrations.channels.action_types import ActionType
from integrations.channels.channel_library import AbstractChannel, OAuth2Channel
from integrations.channels.connection_types import ChannelType
from integrations.channels.tiktokshop.tiktokshop_connection import TiktokShopSettings
from integrations.channels.tiktokshop.tiktokshop_library import Tiktok<PERSON>hopLibrary
from integrations.common.event import FetchEvent


class TiktokShopChannel(OAuth2Channel, AbstractChannel):
    def refresh_oauth_token(self) -> Dict[str, Any]:
        pass

    channel_name = 'tiktok_shop'
    library_class = TiktokShopLibrary

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            'name': 'Tiktok Shop',
            'channel_type': ChannelType.ECOMMERCE,
            'description': 'Connect your Tiktok Shop instance to sync products, orders, and customers.',
            'logo': cls._load_image_as_base64(__file__, 'tiktokshop.png')
        }

    def _create_library(self, connection):
        settings: TiktokShopSettings = connection.attributes.settings
        return TiktokShopLibrary(
            app_key=settings.app_key,
            app_secret=settings.app_secret,
            service_id=settings.service_id,
            region=settings.region,
            shop_id=connection.attributes.token_data[
                'shop_id'] if connection.attributes.token_data is not None and 'shop_id' in connection.attributes.token_data else None,
            shop_cipher=connection.attributes.token_data[
                'shop_cipher'] if connection.attributes.token_data is not None and 'shop_cipher' in connection.attributes.token_data else None,
            access_token=connection.attributes.access_token,
        )

    def initiate_oauth_flow(self, redirect_url: str = None, base_url: str = None) -> str:
        return self.library.get_authorization_url(str(self.connection.attributes.id), redirect_url, base_url)

    def test_connection(self) -> bool:
        try:
            return self.library.test_connection()
        except Exception:
            return False

    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        auth_code = query_params.get('code')

        if not auth_code:
            raise ValueError("Missing auth_code in query parameters")

        token_data = self.library.fetch_token(auth_code)

        return (
            token_data['external_id'],
            token_data['access_token'],
            token_data['expires_at'],
            token_data['refresh_token'],
            token_data['token_data']
        )

    def setup_webhooks(self):
        self.library.setup_webhooks(self.connection)

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_order:
            return {
                "limit": 50,
                "status": "any",
                "financial_status": "any",
                "fulfillment_status": "any",
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_order:
            return {
                "update_inventory": True,
                "send_fulfillment_email": False,
            }
        else:
            return {}

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> FetchEvent:
        return self.library.extract_object_details(payload, headers, event_type)

    def verify_webhook_signature(self, payload: Dict[str, Any], headers: Dict[str, Any],
                                 settings: Dict[str, Any] = None) -> bool:
        return self.library.verify_webhook_signature(payload, headers, settings)

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        # # check if the webhook is from Shopify
        # if payload:
        #     webhook_type = payload.get('type', '')
        #     shop_id = payload.get('shop_id', '')
        #
        #     if webhook_type and shop_id:
        #         return {
        #             "status": "success",
        #             "message": f"Tiktok webhook received successfully",
        #             "shop_id": shop_id,
        #             "webhook_type": webhook_type
        #         }
        #
        # # cannot identify the webhook type
        # return {
        #     "status": "error",
        #     "message": "Unknown webhook format"
        # }
        pass

    def extract_event_type(self, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return self.library.extract_event_type(payload, headers)

    def process_webhook_event(self, event_type, payload) -> bool:
        pass

    def get_product__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}

        if fetch_event.object_id:
            # Case 1: Single product
            return self.library.get_product(product_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        else:
            # Case 2: Filter-based query
            return self.library.get_products(params=params, settings=settings, next_page=fetch_event.next_page,
                                             fetch_event_id=fetch_event.id)

    def get_order__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}
        # Get single order if object_id (order_id) exists
        if fetch_event.object_id:
            return self.library.get_order(order_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        return self.library.get_orders(params=params, settings=settings, next_page=fetch_event.next_page,
                                       fetch_event_id=fetch_event.id)

    def sync_product__publish(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        return self.library.sync_product(product_data)

    def get_product__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_product_params()

    def get_order__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_order_params()

    @classmethod
    def _get_attributes(cls, type: str):
        attribute_getters = {
            'product': cls.library_class._get_product_attributes
        }
        return attribute_getters.get(type, lambda: {})()
