import json

from tests.base import BaseTestCase
from helpers.transformation.transformation import TransformationType
from flows.src.apis.sync_mapping import sync_mapping, invoke_sync_records_handle, mapping_handler
from models.integration.sync_record import SyncRecordCompanyIdIndex, MappingStatus
from libs.python.integrations.common.transformers.base import BaseTransformer


class TestMappingAttribute(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        pass

    def setUp(self):
        self.connection_id = '8b0972ca-762b-4cfa-87bd-9920bcd134c4'
        self.destination_id = '764b8784-c48f-4378-80ec-f0085c20f13b'
        self.object_type = 'product'
        self.sync_record_ids = ['8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314735848.json']
        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"
        pass

    def create_lambda_event(self, body=None, method="GET", path='/', query_params=None, path_params=None):
        event = {
            'httpMethod': method,
            'body': json.dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': {},
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            },
            'isBase64Encoded': False
        }
        return event

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test_function'

        return LambdaContext()

    def create_lambda_event_invoke(self, body):
        return {'Records': [{
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            },
        }]}

    def test_sync_mapping(self):
        event_body = {
            'sync_record_ids': ['8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314735848.json']}
        event = self.create_lambda_event(event_body, query_params={},
                                         path_params={'destination_id': self.destination_id, 'type': 'product'})
        context = self.create_lambda_context()

        result = sync_mapping(event, context)
        print('result', result)

    def test_apply_custom_mapping(self):
        example_master_data = {"id": "9114314604776", "name": "The Collection Snowboard: Oxygen", "sku": "G9GE",
                               "barcode": None, "publish": True, "description": "", "tags": "Accessory, Sport, Winter",
                               "brand": None, "category": {"name": "Uncategorized"}, "images": [{
                "src": "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/Main_d624f226-0a89-4fe1-b333-0d1548b43c06.jpg?v=1743500399",
                "name": "Top and bottom view of a snowboard. The top view shows a stylized scene of trees, mountains, sky and\n        a sun in red colours. The bottom view has blue wavy lines in the background with the text 'Oxygen' in a\n        stylized script typeface."}],
                               "variants": [{"sku": "G9GE", "name": "The Collection Snowboard: Oxygen - Default Title",
                                             "barcode": None, "prices": [], "measurements": None, "inventories": [],
                                             "option1": "Default Title", "option2": None, "option3": None,
                                             "optionTitle1": "Title", "optionTitle2": None, "optionTitle3": None,
                                             "images": []}],
                               "options": [{"name": "Title", "values": ["Default Title"]}],
                               "measurements": {"weight_value": 0, "weight_unit": "g", "width_value": 0,
                                                "width_unit": "cm", "length_value": 0, "length_unit": "cm",
                                                "height_value": 0, "height_unit": "cm"},
                               "source": {"channel_name": "shopify_oauth",
                                          "id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4"},
                               "sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json"}
        destination_format_data = {"save_mode": "LISTING", "description": "", "brand": {"name": None},
                                   "category": {"name": "Uncategorized"}, "main_images": [{
                "url": "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/Main_d624f226-0a89-4fe1-b333-0d1548b43c06.jpg?v=1743500399",
                "name": "Top and bottom view of a snowboard. The top view shows a stylized scene of trees, mountains, sky and\n        a sun in red colours. The bottom view has blue wavy lines in the background with the text 'Oxygen' in a\n        stylized script typeface."}],
                                   "skus": [{"price": {"amount": "1", "currency": "USD"}, "seller_sku": "G9GE",
                                             "sales_attributes": [{"value_name": "Default Title", "name": "Title",
                                                                   "supplementary_sku_images": []}],
                                             "external_sku_id": "G9GE"}], "title": "The Collection Snowboard: Oxygen",
                                   "is_cod_allowed": True,
                                   "product_attributes": [{"name": "Title", "values": [{"name": "Default Title"}]}],
                                   "package_dimensions": {"length": "1", "width": "1", "height": "1",
                                                          "unit": "CENTIMETER"},
                                   "package_weight": {"value": "1", "unit": "KILOGRAM"}}
        example_mappings = [
            {'source_field': 'category.name', 'destination_field': 'title', 'error_message': '', 'enabled': True,
             'transformations': [
                 {
                     "type": TransformationType.UPPERCASE.value,
                     "config": {}
                 },
                 {
                     "type": TransformationType.PREFIX.value,
                     "config": {
                         "prefix": "Pre-"
                     }
                 },
             ]}
        ]

        output_data = BaseTransformer.apply_custom_mapping(example_master_data, destination_format_data,
                                                           example_mappings)
        print('output_data', output_data)

    def test_invoke_sync_records_handle(self):
        event = {
            'destination_id': self.destination_id,
            'type': self.object_type,
            'sync_record_ids': self.sync_record_ids,
            'company_id': self.company_id,
            'next_token': None,
        }
        context = {}
        invoke_sync_records_handle(event, context)

    def test_mapping_handler(self):
        old_sync_record = {
            "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d",
            "status": "COMPLETED",
            "user": None,
            "transformed_record_version": "yvdZ320owyKHbFKnETjrTQoXMzrT1Jcz",
            "channel": "tiktok_shop",
            "other_mappings": {
                "mapping_data": {
                    "destination_key": "seller_sku",
                    "mapping_values": {
                        "sku-hosted-1": "sku-hosted-2"
                    }
                },
                "is_extra_destination_mapping": False
            },
            "mapping_status": "MAPPED",
            "transformed_record_id": "b686e98e-788d-4050-a9bd-a644121a15b2/get_product/product/product_8920696520937.tiktok_shop",
            "standard_destination_data": {
                "external_updated_at": "2025-05-13T10:57:04+00:00",
                "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d",
                "updated_at": "2025-05-13T11:59:39.243093+00:00",
                "external_created_at": "2025-04-28T18:44:42+00:00",
                "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
                "extra": "sku-hosted-1",
                "name": "The 3p Fulfilled Snowboard",
                "created_at": "2025-04-28T19:27:51.253161+00:00",
                "id": "1731292238864221790",
                "type": "product",
                "user": None,
                "standard_data": {
                    "images": [],
                    "updated_at": "2025-05-13T10:57:04+00:00",
                    "created_at": "2025-04-28T18:44:42+00:00",
                    "id": "1731292238864221790",
                    "fetch_event_id": "ff5ee60d-9902-4d4f-887d-4a53cf044d85",
                    "variants": [
                        {
                            "image": "",
                            "id": "1731408756898694750",
                            "title": "",
                            "sku": "sku-hosted-1",
                            "price": ""
                        },
                        {
                            "image": "",
                            "id": "1731408756898760286",
                            "title": "",
                            "sku": "sku-hosted-2",
                            "price": ""
                        },
                        {
                            "image": "",
                            "id": "1731408756898825822",
                            "title": "",
                            "sku": "sku-hosted-3",
                            "price": ""
                        },
                        {
                            "image": "",
                            "id": "1731408756898891358",
                            "title": "",
                            "sku": "sku-hosted-4",
                            "price": ""
                        }
                    ],
                    "title": "The 3p Fulfilled Snowboard",
                    "sku": "sku-hosted-1",
                    "raw_data": {
                        "update_time": "1747133824",
                        "skus": [
                            {
                                "id": "1731408756898694750",
                                "inventory": [
                                    {
                                        "quantity": "1",
                                        "warehouse_id": "7486467134296966918"
                                    }
                                ],
                                "price": {
                                    "currency": "SGD",
                                    "tax_exclusive_price": "1"
                                },
                                "seller_sku": "sku-hosted-1"
                            },
                            {
                                "id": "1731408756898760286",
                                "inventory": [
                                    {
                                        "quantity": "1",
                                        "warehouse_id": "7486467134296966918"
                                    }
                                ],
                                "price": {
                                    "currency": "SGD",
                                    "tax_exclusive_price": "1"
                                },
                                "seller_sku": "sku-hosted-2"
                            },
                            {
                                "id": "1731408756898825822",
                                "inventory": [
                                    {
                                        "quantity": "1",
                                        "warehouse_id": "7486467134296966918"
                                    }
                                ],
                                "price": {
                                    "currency": "SGD",
                                    "tax_exclusive_price": "1"
                                },
                                "seller_sku": "sku-hosted-3"
                            },
                            {
                                "id": "1731408756898891358",
                                "inventory": [
                                    {
                                        "quantity": "1",
                                        "warehouse_id": "7486467134296966918"
                                    }
                                ],
                                "price": {
                                    "currency": "SGD",
                                    "tax_exclusive_price": "1"
                                },
                                "seller_sku": "sku-hosted-4"
                            }
                        ],
                        "create_time": "1745865882",
                        "sales_regions": [
                            "SG"
                        ],
                        "id": "1731292238864221790",
                        "title": "The 3p Fulfilled Snowboard",
                        "recommended_categories": [],
                        "status": "ACTIVATE"
                    }
                }
            },
            "transformed_at": "2025-05-13T07:29:27.861510+00:00",
            "id": "b686e98e-788d-4050-a9bd-a644121a15b2/get_product/product/product_8920696520937.json",
            "raw_record_version": "HhXLZOPsi.bWXrtz9ILnqIofX5KKV9rz",
            "published_at": "2025-05-13T07:30:15.786404+00:00",
            "created_at": "2025-04-28T08:00:59.908615+00:00",
            "standard_source_data": {
                "images": [
                    "https://cdn.shopify.com/s/files/1/0743/0678/1417/files/Main_b9e0da7f-db89-4d41-83f0-7f417b02831d.jpg?v=1745466340"
                ],
                "updated_at": "2025-04-24T18:38:53+00:00",
                "created_at": "2025-04-24T03:45:39+00:00",
                "id": "8920696520937",
                "fetch_event_id": "fb853499-72c0-433f-8601-905a71683ff3",
                "variants": [
                    {
                        "image": None,
                        "id": "48353990476009",
                        "title": "The 3p Fulfilled Snowboard - Default Title",
                        "sku": "sku-hosted-1",
                        "price": "2630"
                    }
                ],
                "title": "The 3p Fulfilled Snowboard",
                "sku": "sku-hosted-1",
                "raw_data": {
                    "images": {
                        "nodes": [
                            {
                                "width": "1600",
                                "id": "gid://shopify/ProductImage/45024219169001",
                                "altText": "Top and bottom view of a snowboard. The top view shows 7 stacked hexagons and the bottom view\n          shows a small, centred hexagonal logo for Hydrogen.",
                                "url": "https://cdn.shopify.com/s/files/1/0743/0678/1417/files/Main_b9e0da7f-db89-4d41-83f0-7f417b02831d.jpg?v=1745466340",
                                "height": "1600"
                            }
                        ]
                    },
                    "publishedAt": "2025-04-24T03:45:39Z",
                    "isGiftCard": False,
                    "description": "",
                    "handle": "the-3p-fulfilled-snowboard",
                    "totalInventory": "20",
                    "tracksInventory": True,
                    "variants": {
                        "nodes": [
                            {
                                "availableForSale": True,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - Default Title",
                                "inventoryQuantity": "20",
                                "title": "Default Title",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"
                                },
                                "createdAt": "2025-04-24T03:45:39Z",
                                "legacyResourceId": "48353990476009",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Title",
                                        "value": "Default Title"
                                    }
                                ],
                                "id": "gid://shopify/ProductVariant/48353990476009",
                                "position": "1",
                                "sku": "sku-hosted-1",
                                "barcode": None,
                                "updatedAt": "2025-04-24T03:45:41Z",
                                "compareAtPrice": None
                            }
                        ]
                    },
                    "title": "The 3p Fulfilled Snowboard",
                    "tags": [
                        "Accessory",
                        "Sport",
                        "Winter"
                    ],
                    "createdAt": "2025-04-24T03:45:39Z",
                    "metafields": {
                        "edges": []
                    },
                    "legacyResourceId": "8920696520937",
                    "vendor": "sonnv24041",
                    "options": [
                        {
                            "name": "Title",
                            "id": "gid://shopify/ProductOption/11227716944105",
                            "position": "1",
                            "values": [
                                "Default Title"
                            ]
                        }
                    ],
                    "descriptionHtml": "",
                    "id": "gid://shopify/Product/8920696520937",
                    "category": None,
                    "hasVariantsThatRequiresComponents": False,
                    "productType": "snowboard",
                    "status": "ACTIVE",
                    "updatedAt": "2025-04-24T18:38:53Z"
                }
            },
            "finished_at": "2025-05-13T07:30:15.786404+00:00",
            "fetch_event_id": "fb853499-72c0-433f-8601-905a71683ff3",
            "is_source": False,
            "response_message": "Publish successfully",
            "updated_at": "2025-05-14T03:17:57.293972+00:00",
            "remote_record_id": "1731292238864221790",
            "fetched_at": "2025-05-08T03:59:32.211000+00:00",
            "response": {
                "data": {
                    "images": [],
                    "updated_at": "2025-05-13T07:30:15.786297+00:00",
                    "created_at": "2025-05-13T07:30:15.786297+00:00",
                    "id": "1731292238864221790",
                    "fetch_event_id": None,
                    "variants": [
                        {
                            "image": "",
                            "id": "1731406529837368926",
                            "title": "",
                            "sku": "sku-hosted-1",
                            "price": ""
                        }
                    ],
                    "title": None,
                    "sku": "sku-hosted-1",
                    "raw_data": {
                        "skus": [
                            {
                                "id": "1731406529837368926",
                                "external_sku_id": "sku-hosted-1",
                                "sales_attributes": [
                                    {
                                        "id": "7500338913765918510",
                                        "value_id": "6977632259545483013"
                                    }
                                ],
                                "seller_sku": "sku-hosted-1"
                            }
                        ],
                        "audit": {
                            "status": "AUDITING"
                        },
                        "product_id": "1731292238864221790",
                        "warnings": [
                            {
                                "message": "The [product_attributes]:100107 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100107, attribute name Warranty Type.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100336 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100336, attribute name Country of Origin.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100347 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100347, attribute name Quantity Per Pack.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100495 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100495, attribute name Warranty Duration.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100667 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100667, attribute name Sports Equipment Type.]. You can edit it later."
                            }
                        ]
                    }
                },
                "message": "",
                "success": True,
                "meta": {
                    "updated_at": "2025-05-13 07:30:15.786355"
                }
            },
            "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
            "record_type": "product"
        }
        new_sync_record = {"company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d", "status": "PUBLISHED",
                           "user": None, "transformed_record_version": "nFKTFW2pSGlvOSjhdJ72PkR.2t.2IgtJ",
                           "channel": "tiktok_shop", "other_mappings": {
                "mapping_data": {"destination_key": "seller_sku", "mapping_values": {"sku-hosted-1": "sku-hosted-2"}},
                "is_extra_destination_mapping": False}, "mapping_status": "MAPPED",
                           "transformed_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314735848.tiktok_shop",
                           "standard_destination_data": {"external_updated_at": "2025-05-13T07:47:22+00:00",
                                                         "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d",
                                                         "updated_at": "2025-05-13T07:49:35.406806+00:00",
                                                         "external_created_at": "2025-04-28T18:44:42+00:00",
                                                         "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
                                                         "extra": "sku-hosted-1",
                                                         "name": "OneX-The Boto4 Fulfilled Snowboard - M / Rubber",
                                                         "created_at": "2025-04-28T19:27:51.253161+00:00",
                                                         "id": "1731292238864221790", "type": "product",
                                                         "user": None, "standard_data": {"images": [],
                                                                                         "updated_at": "2025-05-13T07:47:22+00:00",
                                                                                         "created_at": "2025-04-28T18:44:42+00:00",
                                                                                         "id": "1731292238864221790",
                                                                                         "fetch_event_id": "eb26f2ad-e757-4f7f-b487-89fc02b9d91d",
                                                                                         "variants": [
                                                                                             {"image": "",
                                                                                              "id": "1731406773450016350",
                                                                                              "title": "",
                                                                                              "sku": "sku-hosted-1",
                                                                                              "price": ""},
                                                                                             {"image": "",
                                                                                              "id": "1731406773450081886",
                                                                                              "title": "",
                                                                                              "sku": "sku-hosted-2",
                                                                                              "price": ""},
                                                                                             {"image": "",
                                                                                              "id": "1731406773450147422",
                                                                                              "title": "",
                                                                                              "sku": "sku-hosted-3",
                                                                                              "price": ""},
                                                                                             {"image": "",
                                                                                              "id": "1731406773450212958",
                                                                                              "title": "",
                                                                                              "sku": "sku-hosted-4",
                                                                                              "price": ""}],
                                                                                         "title": "OneX-The Boto4 Fulfilled Snowboard - M / Rubber",
                                                                                         "sku": "sku-hosted-1",
                                                                                         "raw_data": {
                                                                                             "update_time": "1747122442",
                                                                                             "skus": [{
                                                                                                 "id": "1731406773450016350",
                                                                                                 "inventory": [
                                                                                                     {
                                                                                                         "quantity": "1",
                                                                                                         "warehouse_id": "7486467134296966918"}],
                                                                                                 "price": {
                                                                                                     "currency": "SGD",
                                                                                                     "tax_exclusive_price": "1"},
                                                                                                 "seller_sku": "sku-hosted-1"},
                                                                                                 {
                                                                                                     "id": "1731406773450081886",
                                                                                                     "inventory": [
                                                                                                         {
                                                                                                             "quantity": "1",
                                                                                                             "warehouse_id": "7486467134296966918"}],
                                                                                                     "price": {
                                                                                                         "currency": "SGD",
                                                                                                         "tax_exclusive_price": "1"},
                                                                                                     "seller_sku": "sku-hosted-2"},
                                                                                                 {
                                                                                                     "id": "1731406773450147422",
                                                                                                     "inventory": [
                                                                                                         {
                                                                                                             "quantity": "1",
                                                                                                             "warehouse_id": "7486467134296966918"}],
                                                                                                     "price": {
                                                                                                         "currency": "SGD",
                                                                                                         "tax_exclusive_price": "1"},
                                                                                                     "seller_sku": "sku-hosted-3"},
                                                                                                 {
                                                                                                     "id": "1731406773450212958",
                                                                                                     "inventory": [
                                                                                                         {
                                                                                                             "quantity": "1",
                                                                                                             "warehouse_id": "7486467134296966918"}],
                                                                                                     "price": {
                                                                                                         "currency": "SGD",
                                                                                                         "tax_exclusive_price": "1"},
                                                                                                     "seller_sku": "sku-hosted-4"}],
                                                                                             "create_time": "1745865882",
                                                                                             "sales_regions": [
                                                                                                 "SG"],
                                                                                             "id": "1731292238864221790",
                                                                                             "title": "OneX-The Boto4 Fulfilled Snowboard - M / Rubber",
                                                                                             "recommended_categories": [],
                                                                                             "status": "ACTIVATE"}}},
                           "transformed_at": "2025-05-13T18:07:23.641323+07:00",
                           "id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314735848.json",
                           "raw_record_version": ".QZWaDeM8Ml3gsKqkV9Qr.dCURcTt.hc",
                           "published_at": "2025-05-13T11:07:27.486414+00:00",
                           "created_at": "2025-05-09T04:08:06.301085+00:00", "standard_source_data": {"images": [
                "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/Main_b9e0da7f-db89-4d41-83f0-7f417b02831d.jpg?v=1743500399"],
                "updated_at": "2025-05-13T09:59:05+00:00",
                "created_at": "2025-04-01T09:39:58+00:00",
                "id": "9114314735848",
                "fetch_event_id": "3c453cca-1d8e-495f-a7c4-3a44616f0d9f",
                "variants": [
                    {
                        "image": None,
                        "id": "47336508784872",
                        "title": "The 3p Fulfilled Snowboard - M / Rubber",
                        "sku": "sku-hosted-1",
                        "price": "2630"},
                    {
                        "image": None,
                        "id": "47336508817640",
                        "title": "The 3p Fulfilled Snowboard - M / Uranium",
                        "sku": "sku-hosted-2",
                        "price": "2630"},
                    {
                        "image": None,
                        "id": "47336508850408",
                        "title": "The 3p Fulfilled Snowboard - L / Rubber",
                        "sku": "sku-hosted-3",
                        "price": "2630"},
                    {
                        "image": None,
                        "id": "47336508883176",
                        "title": "The 3p Fulfilled Snowboard - L / Uranium",
                        "sku": "sku-hosted-4",
                        "price": "2630"}],
                "title": "The 3p Fulfilled Snowboard",
                "sku": "sku-hosted-1",
                "raw_data": {
                    "images": {
                        "nodes": [
                            {
                                "width": "1600",
                                "id": "gid://shopify/ProductImage/45872069247208",
                                "altText": "Top and bottom view of a snowboard. The top view shows 7 stacked hexagons and the bottom view\n          shows a small, centred hexagonal logo for Hydrogen.",
                                "url": "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/Main_b9e0da7f-db89-4d41-83f0-7f417b02831d.jpg?v=1743500399",
                                "height": "1600"}]},
                    "publishedAt": "2025-04-01T09:39:59Z",
                    "isGiftCard": False,
                    "description": "",
                    "handle": "the-3p-fulfilled-snowboard",
                    "totalInventory": "20",
                    "tracksInventory": True,
                    "variants": {
                        "nodes": [
                            {
                                "availableForSale": True,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - M / Rubber",
                                "inventoryQuantity": "20",
                                "title": "M / Rubber",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"},
                                "createdAt": "2025-05-12T10:16:26Z",
                                "legacyResourceId": "47336508784872",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Size",
                                        "value": "M"},
                                    {
                                        "name": "Material",
                                        "value": "Rubber"}],
                                "id": "gid://shopify/ProductVariant/47336508784872",
                                "position": "1",
                                "sku": "sku-hosted-1",
                                "barcode": "",
                                "updatedAt": "2025-05-12T10:16:26Z",
                                "compareAtPrice": None},
                            {
                                "availableForSale": False,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - M / Uranium",
                                "inventoryQuantity": "0",
                                "title": "M / Uranium",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"},
                                "createdAt": "2025-05-12T10:16:26Z",
                                "legacyResourceId": "47336508817640",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Size",
                                        "value": "M"},
                                    {
                                        "name": "Material",
                                        "value": "Uranium"}],
                                "id": "gid://shopify/ProductVariant/47336508817640",
                                "position": "2",
                                "sku": "sku-hosted-2",
                                "barcode": "",
                                "updatedAt": "2025-05-12T10:16:26Z",
                                "compareAtPrice": None},
                            {
                                "availableForSale": False,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - L / Rubber",
                                "inventoryQuantity": "0",
                                "title": "L / Rubber",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"},
                                "createdAt": "2025-05-12T10:16:26Z",
                                "legacyResourceId": "47336508850408",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Size",
                                        "value": "L"},
                                    {
                                        "name": "Material",
                                        "value": "Rubber"}],
                                "id": "gid://shopify/ProductVariant/47336508850408",
                                "position": "3",
                                "sku": "sku-hosted-3",
                                "barcode": "",
                                "updatedAt": "2025-05-12T10:16:26Z",
                                "compareAtPrice": None},
                            {
                                "availableForSale": False,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - L / Uranium",
                                "inventoryQuantity": "0",
                                "title": "L / Uranium",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"},
                                "createdAt": "2025-05-12T10:16:26Z",
                                "legacyResourceId": "47336508883176",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Size",
                                        "value": "L"},
                                    {
                                        "name": "Material",
                                        "value": "Uranium"}],
                                "id": "gid://shopify/ProductVariant/47336508883176",
                                "position": "4",
                                "sku": "sku-hosted-4",
                                "barcode": "",
                                "updatedAt": "2025-05-12T10:16:26Z",
                                "compareAtPrice": None}]},
                    "title": "The 3p Fulfilled Snowboard",
                    "tags": [
                        "Accessory",
                        "Sport",
                        "Winter"],
                    "createdAt": "2025-04-01T09:39:58Z",
                    "metafields": {
                        "edges": []},
                    "legacyResourceId": "9114314735848",
                    "vendor": "trannhon-partner-2",
                    "options": [
                        {
                            "name": "Size",
                            "id": "gid://shopify/ProductOption/11726982381800",
                            "position": "1",
                            "values": [
                                "M",
                                "L"]},
                        {
                            "name": "Material",
                            "id": "gid://shopify/ProductOption/11726982414568",
                            "position": "2",
                            "values": [
                                "Rubber",
                                "Uranium"]}],
                    "descriptionHtml": "",
                    "id": "gid://shopify/Product/9114314735848",
                    "category": {
                        "isRoot": True,
                        "level": "1",
                        "ancestorIds": [],
                        "isArchived": False,
                        "name": "Uncategorized",
                        "fullName": "Uncategorized",
                        "id": "gid://shopify/TaxonomyCategory/na",
                        "isLeaf": True,
                        "parentId": None},
                    "hasVariantsThatRequiresComponents": False,
                    "productType": "snowboard",
                    "status": "ACTIVE",
                    "updatedAt": "2025-05-13T09:59:05Z"}},
                           "finished_at": "2025-05-13T10:57:01.849334+00:00",
                           "fetch_event_id": "3c453cca-1d8e-495f-a7c4-3a44616f0d9f", "is_source": False,
                           "response_message": "Publish successfully",
                           "updated_at": "2025-05-13T11:07:27.486521+00:00",
                           "remote_record_id": "1731292238864221790",
                           "fetched_at": "2025-05-13T10:56:38.552000+00:00", "response": {
                "data": {"images": [], "updated_at": "2025-05-13T10:57:01.849221+00:00",
                         "created_at": "2025-05-13T10:57:01.849221+00:00", "id": "1731292238864221790",
                         "fetch_event_id": None, "variants": [
                        {"image": "", "id": "1731408756898694750", "title": "", "sku": "sku-hosted-1", "price": ""},
                        {"image": "", "id": "1731408756898760286", "title": "", "sku": "sku-hosted-2", "price": ""},
                        {"image": "", "id": "1731408756898825822", "title": "", "sku": "sku-hosted-3", "price": ""},
                        {"image": "", "id": "1731408756898891358", "title": "", "sku": "sku-hosted-4", "price": ""}],
                         "title": None, "sku": "sku-hosted-1", "raw_data": {"skus": [
                        {"id": "1731408756898694750", "external_sku_id": "sku-hosted-1",
                         "sales_attributes": [{"id": "7503881984972769029", "value_id": "7503882273503315718"},
                                              {"id": "7502336857703810836", "value_id": "7502253461032109829"}],
                         "seller_sku": "sku-hosted-1"}, {"id": "1731408756898760286", "external_sku_id": "sku-hosted-2",
                                                         "sales_attributes": [{"id": "7503881984972769029",
                                                                               "value_id": "7503882273503315718"},
                                                                              {"id": "7502336857703810836",
                                                                               "value_id": "7118051000312760069"}],
                                                         "seller_sku": "sku-hosted-2"},
                        {"id": "1731408756898825822", "external_sku_id": "sku-hosted-3",
                         "sales_attributes": [{"id": "7503881984972769029", "value_id": "7503864595561400086"},
                                              {"id": "7502336857703810836", "value_id": "7502253461032109829"}],
                         "seller_sku": "sku-hosted-3"}, {"id": "1731408756898891358", "external_sku_id": "sku-hosted-4",
                                                         "sales_attributes": [{"id": "7503881984972769029",
                                                                               "value_id": "7503864595561400086"},
                                                                              {"id": "7502336857703810836",
                                                                               "value_id": "7118051000312760069"}],
                                                         "seller_sku": "sku-hosted-4"}],
                        "audit": {"status": "AUDITING"},
                        "product_id": "1731292238864221790",
                        "warnings": [{
                            "message": "The [product_attributes]:100336 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100336, attribute name Country of Origin.]. You can edit it later."},
                            {
                                "message": "The [product_attributes]:100347 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100347, attribute name Quantity Per Pack.]. You can edit it later."},
                            {
                                "message": "The [product_attributes]:100495 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100495, attribute name Warranty Duration.]. You can edit it later."},
                            {
                                "message": "The [product_attributes]:100667 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100667, attribute name Sports Equipment Type.]. You can edit it later."},
                            {
                                "message": "The [product_attributes]:100107 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100107, attribute name Warranty Type.]. You can edit it later."}]}},
                "message": "", "success": True, "meta": {"updated_at": "2025-05-13 10:57:01.849290"}},
                           "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b", "record_type": "product"}
        sync_record_44 = {
            "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d",
            "status": "COMPLETED",
            "user": None,
            "transformed_record_version": "Y5bzjXVkh5Q5FQkdV5RnZb.RD.uiM.Y4",
            "channel": "tiktok_shop",
            "other_mappings": None,
            "mapping_status": "UNMAPPED",
            "transformed_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314637544.tiktok_shop",
            "standard_destination_data": {
                "images": [],
                "updated_at": "2025-05-12T10:25:32.245950+00:00",
                "created_at": "2025-05-12T10:25:32.245950+00:00",
                "id": "1731372846132528734",
                "fetch_event_id": None,
                "variants": [
                    {
                        "image": "",
                        "id": "1731399704120690270",
                        "title": "",
                        "sku": "UOW",
                        "price": ""
                    }
                ],
                "title": None,
                "sku": "UOW",
                "raw_data": {
                    "skus": [
                        {
                            "id": "1731399704120690270",
                            "external_sku_id": "UOW",
                            "sales_attributes": [
                                {
                                    "id": "7500338913765918510",
                                    "value_id": "6977632259545483013"
                                }
                            ],
                            "seller_sku": "UOW"
                        }
                    ],
                    "audit": {
                        "status": "AUDITING"
                    },
                    "product_id": "1731372846132528734",
                    "warnings": [
                        {
                            "message": "The [product_attributes]:100107 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100107, attribute name Warranty Type.]. You can edit it later."
                        },
                        {
                            "message": "The [product_attributes]:100336 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100336, attribute name Country of Origin.]. You can edit it later."
                        },
                        {
                            "message": "The [product_attributes]:100347 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100347, attribute name Quantity Per Pack.]. You can edit it later."
                        },
                        {
                            "message": "The [product_attributes]:100495 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100495, attribute name Warranty Duration.]. You can edit it later."
                        },
                        {
                            "message": "The [product_attributes]:100667 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100667, attribute name Sports Equipment Type.]. You can edit it later."
                        }
                    ]
                }
            },
            "transformed_at": "2025-05-12T10:25:02.276572+00:00",
            "id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314637544.json",
            "raw_record_version": "OtOycrvY4uGQ70IQRcFrNdMpUsWNiZni",
            "published_at": "2025-05-12T10:25:32.246061+00:00",
            "created_at": "2025-05-09T04:08:06.228036+00:00",
            "standard_source_data": {
                "images": [
                    "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/Main_0a4e9096-021a-4c1e-8750-24b233166a12.jpg?v=1743500399"
                ],
                "updated_at": "2025-04-01T10:08:05+00:00",
                "created_at": "2025-04-01T09:39:58+00:00",
                "id": "9114314637544",
                "fetch_event_id": "e1796434-d645-4324-88af-23d9dc8c2fd7",
                "variants": [
                    {
                        "image": None,
                        "id": "47203979460840",
                        "title": "The Multi-location Snowboard - Default Title",
                        "sku": "UOW",
                        "price": "730"
                    }
                ],
                "title": "The Multi-location Snowboard",
                "sku": "UOW",
                "raw_data": {
                    "images": {
                        "nodes": [
                            {
                                "width": "1600",
                                "id": "gid://shopify/ProductImage/45872069050600",
                                "altText": "Top and bottom view of a snowboard. The top view shows a pixelated Shopify bag logo and a\n        pixelated character reviewing a clipboard with a questioning expression with a bright green-blue background.\n        The bottom view is a pattern of many pixel characters with a bright green-blue background.",
                                "url": "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/Main_0a4e9096-021a-4c1e-8750-24b233166a12.jpg?v=1743500399",
                                "height": "1600"
                            }
                        ]
                    },
                    "publishedAt": "2025-04-01T09:39:58Z",
                    "isGiftCard": False,
                    "description": "",
                    "handle": "the-multi-location-snowboard",
                    "totalInventory": "100",
                    "tracksInventory": True,
                    "variants": {
                        "nodes": [
                            {
                                "availableForSale": True,
                                "image": None,
                                "taxable": True,
                                "displayName": "The Multi-location Snowboard - Default Title",
                                "inventoryQuantity": "100",
                                "title": "Default Title",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"
                                },
                                "createdAt": "2025-04-01T09:39:58Z",
                                "legacyResourceId": "47203979460840",
                                "price": "730",
                                "selectedOptions": [
                                    {
                                        "name": "Title",
                                        "value": "Default Title"
                                    }
                                ],
                                "id": "gid://shopify/ProductVariant/47203979460840",
                                "position": "1",
                                "sku": "UOW",
                                "barcode": None,
                                "updatedAt": "2025-04-01T10:08:04Z",
                                "compareAtPrice": None
                            }
                        ]
                    },
                    "title": "The Multi-location Snowboard",
                    "tags": [
                        "Premium",
                        "Snow",
                        "Snowboard",
                        "Sport",
                        "Winter"
                    ],
                    "createdAt": "2025-04-01T09:39:58Z",
                    "metafields": {
                        "edges": []
                    },
                    "legacyResourceId": "9114314637544",
                    "vendor": "trannhon-partner-2",
                    "options": [
                        {
                            "name": "Title",
                            "id": "gid://shopify/ProductOption/11696219980008",
                            "position": "1",
                            "values": [
                                "Default Title"
                            ]
                        }
                    ],
                    "descriptionHtml": "",
                    "id": "gid://shopify/Product/9114314637544",
                    "category": {
                        "isRoot": True,
                        "level": "1",
                        "ancestorIds": [],
                        "isArchived": False,
                        "name": "Uncategorized",
                        "fullName": "Uncategorized",
                        "id": "gid://shopify/TaxonomyCategory/na",
                        "isLeaf": True,
                        "parentId": None
                    },
                    "hasVariantsThatRequiresComponents": False,
                    "productType": "snowboard",
                    "status": "ACTIVE",
                    "updatedAt": "2025-04-01T10:08:05Z"
                }
            },
            "finished_at": "2025-05-12T10:25:32.246061+00:00",
            "fetch_event_id": "e1796434-d645-4324-88af-23d9dc8c2fd7",
            "is_source": False,
            "response_message": "Publish successfully",
            "updated_at": "2025-05-12T10:25:32.247480+00:00",
            "remote_record_id": "1731372846132528734",
            "fetched_at": "2025-05-12T10:24:58.045000+00:00",
            "response": {
                "data": {
                    "images": [],
                    "updated_at": "2025-05-12T10:25:32.245950+00:00",
                    "created_at": "2025-05-12T10:25:32.245950+00:00",
                    "id": "1731372846132528734",
                    "fetch_event_id": None,
                    "variants": [
                        {
                            "image": "",
                            "id": "1731399704120690270",
                            "title": "",
                            "sku": "UOW",
                            "price": ""
                        }
                    ],
                    "title": None,
                    "sku": "UOW",
                    "raw_data": {
                        "skus": [
                            {
                                "id": "1731399704120690270",
                                "external_sku_id": "UOW",
                                "sales_attributes": [
                                    {
                                        "id": "7500338913765918510",
                                        "value_id": "6977632259545483013"
                                    }
                                ],
                                "seller_sku": "UOW"
                            }
                        ],
                        "audit": {
                            "status": "AUDITING"
                        },
                        "product_id": "1731372846132528734",
                        "warnings": [
                            {
                                "message": "The [product_attributes]:100107 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100107, attribute name Warranty Type.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100336 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100336, attribute name Country of Origin.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100347 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100347, attribute name Quantity Per Pack.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100495 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100495, attribute name Warranty Duration.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100667 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100667, attribute name Sports Equipment Type.]. You can edit it later."
                            }
                        ]
                    }
                },
                "message": "",
                "success": True,
                "meta": {
                    "updated_at": "2025-05-12 10:25:32.246016"
                }
            },
            "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
            "record_type": "product"
        }
        sync_record_48 = {
            "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d",
            "status": "COMPLETED",
            "user": None,
            "transformed_record_version": "97y.N2GjhFKhNC0hy4mGyEYmRDgsXhAI",
            "channel": "tiktok_shop",
            "other_mappings": {
                "mapping_data": {
                    "destination_key": "seller_sku",
                    "mapping_values": {
                        "sku-hosted-1": "sku-hosted-2"
                    }
                },
                "is_extra_destination_mapping": True
            },
            "mapping_status": "SYNCED",
            "transformed_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314735848.tiktok_shop",
            "standard_destination_data": {
                "images": [],
                "updated_at": "2025-05-14T06:52:19.208131+00:00",
                "created_at": "2025-05-14T06:52:19.208131+00:00",
                "id": "1731292238864221790",
                "fetch_event_id": None,
                "variants": [
                    {
                        "image": "",
                        "id": "1731414919776470622",
                        "title": "",
                        "sku": "sku-hosted-1",
                        "price": ""
                    },
                    {
                        "image": "",
                        "id": "1731414919776536158",
                        "title": "",
                        "sku": "sku-hosted-2",
                        "price": ""
                    },
                    {
                        "image": "",
                        "id": "1731414919776601694",
                        "title": "",
                        "sku": "sku-hosted-3",
                        "price": ""
                    },
                    {
                        "image": "",
                        "id": "1731414919776667230",
                        "title": "",
                        "sku": "sku-hosted-4",
                        "price": ""
                    }
                ],
                "title": None,
                "sku": "sku-hosted-1",
                "raw_data": {
                    "skus": [
                        {
                            "id": "1731414919776470622",
                            "external_sku_id": "sku-hosted-1",
                            "sales_attributes": [
                                {
                                    "id": "7504151175172966166",
                                    "value_id": "7504153494648047382"
                                },
                                {
                                    "id": "7502336857703810836",
                                    "value_id": "7502253461032109829"
                                }
                            ],
                            "seller_sku": "sku-hosted-1"
                        },
                        {
                            "id": "1731414919776536158",
                            "external_sku_id": "sku-hosted-2",
                            "sales_attributes": [
                                {
                                    "id": "7504151175172966166",
                                    "value_id": "7504153494648047382"
                                },
                                {
                                    "id": "7502336857703810836",
                                    "value_id": "7118051000312760069"
                                }
                            ],
                            "seller_sku": "sku-hosted-2"
                        },
                        {
                            "id": "1731414919776601694",
                            "external_sku_id": "sku-hosted-3",
                            "sales_attributes": [
                                {
                                    "id": "7504151175172966166",
                                    "value_id": "7504170109130295062"
                                },
                                {
                                    "id": "7502336857703810836",
                                    "value_id": "7502253461032109829"
                                }
                            ],
                            "seller_sku": "sku-hosted-3"
                        },
                        {
                            "id": "1731414919776667230",
                            "external_sku_id": "sku-hosted-4",
                            "sales_attributes": [
                                {
                                    "id": "7504151175172966166",
                                    "value_id": "7504170109130295062"
                                },
                                {
                                    "id": "7502336857703810836",
                                    "value_id": "7118051000312760069"
                                }
                            ],
                            "seller_sku": "sku-hosted-4"
                        }
                    ],
                    "audit": {
                        "status": "AUDITING"
                    },
                    "product_id": "1731292238864221790",
                    "warnings": [
                        {
                            "message": "The [product_attributes]:100347 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100347, attribute name Quantity Per Pack.]. You can edit it later."
                        },
                        {
                            "message": "The [product_attributes]:100495 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100495, attribute name Warranty Duration.]. You can edit it later."
                        },
                        {
                            "message": "The [product_attributes]:100667 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100667, attribute name Sports Equipment Type.]. You can edit it later."
                        },
                        {
                            "message": "The [product_attributes]:100107 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100107, attribute name Warranty Type.]. You can edit it later."
                        },
                        {
                            "message": "The [product_attributes]:100336 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100336, attribute name Country of Origin.]. You can edit it later."
                        }
                    ]
                }
            },
            "transformed_at": "2025-05-14T06:51:46.852567+00:00",
            "id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314735848.json",
            "raw_record_version": "MviSizGi80sQgC9ocQjU.zFxWGt3imfF",
            "published_at": "2025-05-14T06:52:19.208234+00:00",
            "created_at": "2025-05-09T04:08:06.301085+00:00",
            "standard_source_data": {
                "images": [
                    "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/Main_b9e0da7f-db89-4d41-83f0-7f417b02831d.jpg?v=1743500399"
                ],
                "updated_at": "2025-05-13T09:59:05+00:00",
                "created_at": "2025-04-01T09:39:58+00:00",
                "id": "9114314735848",
                "fetch_event_id": "ba3e0ca7-d6ae-42d7-9da8-dbbe43a04b9f",
                "variants": [
                    {
                        "image": None,
                        "id": "47336508784872",
                        "title": "The 3p Fulfilled Snowboard - M / Rubber",
                        "sku": "sku-hosted-1",
                        "price": "2630"
                    },
                    {
                        "image": None,
                        "id": "47336508817640",
                        "title": "The 3p Fulfilled Snowboard - M / Uranium",
                        "sku": "sku-hosted-2",
                        "price": "2630"
                    },
                    {
                        "image": None,
                        "id": "47336508850408",
                        "title": "The 3p Fulfilled Snowboard - L / Rubber",
                        "sku": "sku-hosted-3",
                        "price": "2630"
                    },
                    {
                        "image": None,
                        "id": "47336508883176",
                        "title": "The 3p Fulfilled Snowboard - L / Uranium",
                        "sku": "sku-hosted-4",
                        "price": "2630"
                    }
                ],
                "title": "The 3p Fulfilled Snowboard",
                "sku": "sku-hosted-1",
                "raw_data": {
                    "images": {
                        "nodes": [
                            {
                                "width": "1600",
                                "id": "gid://shopify/ProductImage/45872069247208",
                                "altText": "Top and bottom view of a snowboard. The top view shows 7 stacked hexagons and the bottom view\n          shows a small, centred hexagonal logo for Hydrogen.",
                                "url": "https://cdn.shopify.com/s/files/1/0730/8866/3784/files/Main_b9e0da7f-db89-4d41-83f0-7f417b02831d.jpg?v=1743500399",
                                "height": "1600"
                            }
                        ]
                    },
                    "publishedAt": "2025-04-01T09:39:59Z",
                    "isGiftCard": False,
                    "description": "",
                    "handle": "the-3p-fulfilled-snowboard",
                    "totalInventory": "20",
                    "tracksInventory": True,
                    "variants": {
                        "nodes": [
                            {
                                "availableForSale": True,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - M / Rubber",
                                "inventoryQuantity": "20",
                                "title": "M / Rubber",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"
                                },
                                "createdAt": "2025-05-12T10:16:26Z",
                                "legacyResourceId": "47336508784872",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Size",
                                        "value": "M"
                                    },
                                    {
                                        "name": "Material",
                                        "value": "Rubber"
                                    }
                                ],
                                "id": "gid://shopify/ProductVariant/47336508784872",
                                "position": "1",
                                "sku": "sku-hosted-1",
                                "barcode": "",
                                "updatedAt": "2025-05-12T10:16:26Z",
                                "compareAtPrice": None
                            },
                            {
                                "availableForSale": False,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - M / Uranium",
                                "inventoryQuantity": "0",
                                "title": "M / Uranium",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"
                                },
                                "createdAt": "2025-05-12T10:16:26Z",
                                "legacyResourceId": "47336508817640",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Size",
                                        "value": "M"
                                    },
                                    {
                                        "name": "Material",
                                        "value": "Uranium"
                                    }
                                ],
                                "id": "gid://shopify/ProductVariant/47336508817640",
                                "position": "2",
                                "sku": "sku-hosted-2",
                                "barcode": "",
                                "updatedAt": "2025-05-12T10:16:26Z",
                                "compareAtPrice": None
                            },
                            {
                                "availableForSale": False,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - L / Rubber",
                                "inventoryQuantity": "0",
                                "title": "L / Rubber",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"
                                },
                                "createdAt": "2025-05-12T10:16:26Z",
                                "legacyResourceId": "47336508850408",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Size",
                                        "value": "L"
                                    },
                                    {
                                        "name": "Material",
                                        "value": "Rubber"
                                    }
                                ],
                                "id": "gid://shopify/ProductVariant/47336508850408",
                                "position": "3",
                                "sku": "sku-hosted-3",
                                "barcode": "",
                                "updatedAt": "2025-05-12T10:16:26Z",
                                "compareAtPrice": None
                            },
                            {
                                "availableForSale": False,
                                "image": None,
                                "taxable": True,
                                "displayName": "The 3p Fulfilled Snowboard - L / Uranium",
                                "inventoryQuantity": "0",
                                "title": "L / Uranium",
                                "taxCode": "",
                                "unitPriceMeasurement": {
                                    "measuredType": None,
                                    "quantityUnit": None,
                                    "referenceUnit": None,
                                    "quantityValue": "0",
                                    "referenceValue": "0"
                                },
                                "createdAt": "2025-05-12T10:16:26Z",
                                "legacyResourceId": "47336508883176",
                                "price": "2630",
                                "selectedOptions": [
                                    {
                                        "name": "Size",
                                        "value": "L"
                                    },
                                    {
                                        "name": "Material",
                                        "value": "Uranium"
                                    }
                                ],
                                "id": "gid://shopify/ProductVariant/47336508883176",
                                "position": "4",
                                "sku": "sku-hosted-4",
                                "barcode": "",
                                "updatedAt": "2025-05-12T10:16:26Z",
                                "compareAtPrice": None
                            }
                        ]
                    },
                    "title": "The 3p Fulfilled Snowboard",
                    "tags": [
                        "Accessory",
                        "Sport",
                        "Winter"
                    ],
                    "createdAt": "2025-04-01T09:39:58Z",
                    "metafields": {
                        "edges": []
                    },
                    "legacyResourceId": "9114314735848",
                    "vendor": "trannhon-partner-2",
                    "options": [
                        {
                            "name": "Size",
                            "id": "gid://shopify/ProductOption/11726982381800",
                            "position": "1",
                            "values": [
                                "M",
                                "L"
                            ]
                        },
                        {
                            "name": "Material",
                            "id": "gid://shopify/ProductOption/11726982414568",
                            "position": "2",
                            "values": [
                                "Rubber",
                                "Uranium"
                            ]
                        }
                    ],
                    "descriptionHtml": "",
                    "id": "gid://shopify/Product/9114314735848",
                    "category": {
                        "isRoot": True,
                        "level": "1",
                        "ancestorIds": [],
                        "isArchived": False,
                        "name": "Uncategorized",
                        "fullName": "Uncategorized",
                        "id": "gid://shopify/TaxonomyCategory/na",
                        "isLeaf": True,
                        "parentId": None
                    },
                    "hasVariantsThatRequiresComponents": False,
                    "productType": "snowboard",
                    "status": "ACTIVE",
                    "updatedAt": "2025-05-13T09:59:05Z"
                }
            },
            "finished_at": "2025-05-14T06:52:19.208234+00:00",
            "fetch_event_id": "ba3e0ca7-d6ae-42d7-9da8-dbbe43a04b9f",
            "is_source": False,
            "response_message": "Publish successfully",
            "updated_at": "2025-05-14T06:52:19.210274+00:00",
            "remote_record_id": "1731292238864221790",
            "fetched_at": "2025-05-14T06:51:37.631000+00:00",
            "response": {
                "data": {
                    "images": [],
                    "updated_at": "2025-05-14T06:52:19.208131+00:00",
                    "created_at": "2025-05-14T06:52:19.208131+00:00",
                    "id": "1731292238864221790",
                    "fetch_event_id": None,
                    "variants": [
                        {
                            "image": "",
                            "id": "1731414919776470622",
                            "title": "",
                            "sku": "sku-hosted-1",
                            "price": ""
                        },
                        {
                            "image": "",
                            "id": "1731414919776536158",
                            "title": "",
                            "sku": "sku-hosted-2",
                            "price": ""
                        },
                        {
                            "image": "",
                            "id": "1731414919776601694",
                            "title": "",
                            "sku": "sku-hosted-3",
                            "price": ""
                        },
                        {
                            "image": "",
                            "id": "1731414919776667230",
                            "title": "",
                            "sku": "sku-hosted-4",
                            "price": ""
                        }
                    ],
                    "title": None,
                    "sku": "sku-hosted-1",
                    "raw_data": {
                        "skus": [
                            {
                                "id": "1731414919776470622",
                                "external_sku_id": "sku-hosted-1",
                                "sales_attributes": [
                                    {
                                        "id": "7504151175172966166",
                                        "value_id": "7504153494648047382"
                                    },
                                    {
                                        "id": "7502336857703810836",
                                        "value_id": "7502253461032109829"
                                    }
                                ],
                                "seller_sku": "sku-hosted-1"
                            },
                            {
                                "id": "1731414919776536158",
                                "external_sku_id": "sku-hosted-2",
                                "sales_attributes": [
                                    {
                                        "id": "7504151175172966166",
                                        "value_id": "7504153494648047382"
                                    },
                                    {
                                        "id": "7502336857703810836",
                                        "value_id": "7118051000312760069"
                                    }
                                ],
                                "seller_sku": "sku-hosted-2"
                            },
                            {
                                "id": "1731414919776601694",
                                "external_sku_id": "sku-hosted-3",
                                "sales_attributes": [
                                    {
                                        "id": "7504151175172966166",
                                        "value_id": "7504170109130295062"
                                    },
                                    {
                                        "id": "7502336857703810836",
                                        "value_id": "7502253461032109829"
                                    }
                                ],
                                "seller_sku": "sku-hosted-3"
                            },
                            {
                                "id": "1731414919776667230",
                                "external_sku_id": "sku-hosted-4",
                                "sales_attributes": [
                                    {
                                        "id": "7504151175172966166",
                                        "value_id": "7504170109130295062"
                                    },
                                    {
                                        "id": "7502336857703810836",
                                        "value_id": "7118051000312760069"
                                    }
                                ],
                                "seller_sku": "sku-hosted-4"
                            }
                        ],
                        "audit": {
                            "status": "AUDITING"
                        },
                        "product_id": "1731292238864221790",
                        "warnings": [
                            {
                                "message": "The [product_attributes]:100347 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100347, attribute name Quantity Per Pack.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100495 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100495, attribute name Warranty Duration.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100667 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100667, attribute name Sports Equipment Type.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100107 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100107, attribute name Warranty Type.]. You can edit it later."
                            },
                            {
                                "message": "The [product_attributes]:100336 field is incorrect and has been automatically cleared by the system. Reason: [The product attribute does not support multiple selection, check and modify for attribute ID 100336, attribute name Country of Origin.]. You can edit it later."
                            }
                        ]
                    }
                },
                "message": "",
                "success": True,
                "meta": {
                    "updated_at": "2025-05-14 06:52:19.208191"
                }
            },
            "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
            "record_type": "product"
        }
        test_input_sync_record = sync_record_48

        # Arrange
        event_body = {"sync_record": test_input_sync_record
                      }
        event = self.create_lambda_event_invoke(event_body)
        context = self.create_lambda_context()

        response = mapping_handler(event, context)
