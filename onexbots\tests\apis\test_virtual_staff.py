import unittest
from tests.base import BaseTestCase
from nolicore.utils.exceptions import NotFoundRequest, BadRequest

from models.bots.virtual_staff import VirtualStaffModel, Role, ResponseLength
from onexbots.src.apis.virtual_staff import (
    create_virtual_staff,
    get_virtual_staff,
    list_virtual_staffs,
    update_virtual_staff,
    delete_virtual_staff
)


class TestVirtualStaff(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        cls.period = 'custom'
        cls.date_from = '2024-01-01'
        cls.date_to = '2025-01-01'
        if not cls.period:
            raise ValueError("Invalid period")
        if cls.period == 'custom':
            if not all([cls.period, cls.date_from, cls.date_to]):
                raise ValueError("period/date_from/date_to not found in environment variables")

    def test_create_virtual_staff(self):
        """Test creating a new virtual staff"""
        # Prepare test data
        test_data = {
            "name": "<PERSON><PERSON><PERSON>'s Virtual Staff",
            "department_id": "test-department-id",
            "role": Role.VIRTUAL_PERSONAL_ASSISTANT.value,
            "image": "test_image",
        }

        event = self.create_lambda_event(body=test_data, is_authorized=True)
        context = self.create_lambda_context()

        # Test successful creation
        response = create_virtual_staff(event, context)
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Virtual staff created successfully')
        self.assertIsNotNone(response['data'])

        # Test missing required fields
        invalid_data = test_data.copy()
        del invalid_data['name']
        event = self.create_lambda_event(body=invalid_data)

        with self.assertRaises(BadRequest) as context:
            create_virtual_staff(event, self.create_lambda_context())
        self.assertEqual(str(context.exception), "Missing required field: name")

    def test_get_virtual_staff(self):
        staff_id = "48a03ff4-bdb4-42be-9ab8-780b79711503"

        event = self.create_lambda_event(
            path_params={'id': staff_id},
            is_authorized=True
        )
        response = get_virtual_staff(event, self.create_lambda_context())

    def test_list_virtual_staffs(self):
        # Test listing all virtual staffs\
        event = self.create_lambda_event(
            query_params={"page": "0", "limit": "20"},
            is_authorized=True)
        response = list_virtual_staffs(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Virtual staffs retrieved successfully')
        self.assertIsInstance(response['data'], dict)
        self.assertIn('items', response['data'])

    def test_update_virtual_staff(self):
        """Test updating a virtual staff"""
        # First create a virtual staff

        update_data = {
            "name": "Updated Virtual Staff456",
        }
        event = self.create_lambda_event(
            path_params={'id': "5dd285fb-fe7f-4140-bf64-6e849217b754"},
            body={"data": update_data, "type_update": "INFORMATION"},
            is_authorized=True
        )
        response = update_virtual_staff(event, self.create_lambda_context())

    def test_delete_virtual_staff(self):

        event = self.create_lambda_event(
            path_params={'id': "6805a7a3-19b2-4773-8f38-3eaed6ad4f89"},
            is_authorized=True
        )
        response = delete_virtual_staff(event, self.create_lambda_context())

    def test_update_virtual_staff_styles(self):
        """Test updating virtual staff styles"""
        update_data = {
            "interaction_style": {
                "language": "test_lang"
            }
        }
        event = self.create_lambda_event(
            path_params={'id': "5dd285fb-fe7f-4140-bf64-6e849217b754"},
            body={"data": update_data, "type_update": "STYLES"},
            is_authorized=True
        )
        response = update_virtual_staff(event, self.create_lambda_context())

    def test_update_virtual_staff_skills(self):
        """Test updating virtual staff styles"""
        update_data = {
            "skills": ["skill1", "skill2"]
        }
        event = self.create_lambda_event(
            path_params={'id': "5dd285fb-fe7f-4140-bf64-6e849217b754"},
            body={"data": update_data, "type_update": "SKILL"},
            is_authorized=True
        )
        response = update_virtual_staff(event, self.create_lambda_context())

    def test_update_virtual_staff_knowledge(self):
        """Test updating virtual staff knowledge"""
        update_data = {
            "name": "Updated Virtual Staff Knowledge",
            "KNOWLEDGE": {
                "configuration": {
                    "knowledge_base": {
                        "knowledge_ids": ["b1c846bf-4a63-41ff-bbbd-e65495c4ff10", "318fb164-eb4b-4221-aecf-fa91db483dca",
                          "150abc47-446a-4fd6-b665-d8eaabd041fd"],
                    }
                }
            },
        }
        event = self.create_lambda_event(
            path_params={'id': '48a03ff4-bdb4-42be-9ab8-780b79711503'},
            body=update_data,
            is_authorized=True
        )
        response = update_virtual_staff(event, self.create_lambda_context())

    def test_update_virtual_staff_task(self):
        """Test updating virtual staff tasks"""
        update_data = {
            "name": "Updated Virtual Staff Task",
            "task_ids": ["9b19254f-9fb0-4e3c-94e6-32b78e32dc73"]
        }
        event = self.create_lambda_event(
            path_params={'id': "5dd285fb-fe7f-4140-bf64-6e849217b754"},
            body={"data": update_data, "type_update": "TASK"},
            is_authorized=True
        )
        response = update_virtual_staff(event, self.create_lambda_context())

    def test_update_virtual_staff_invalid_type(self):
        """Test updating virtual staff with invalid type"""
        update_data = {
            "name": "Updated Virtual Staff Invalid",
        }
        event = self.create_lambda_event(
            path_params={'id': "5dd285fb-fe7f-4140-bf64-6e849217b754"},
            body={"data": update_data, "type_update": "invalid_type"},
            is_authorized=True
        )
        response = update_virtual_staff(event, self.create_lambda_context())


if __name__ == '__main__':
    unittest.main()
