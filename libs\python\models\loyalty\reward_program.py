from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import List

from elasticsearch import NotFoundError
from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.default_obj import DefaultObject, DefaultObjectSchema
from models.common.duration import DurationType
from models.media.image import Image, ImageSchema


class RewardType(Enum):
    POINT_REDEEM = "POINT_REDEEM"


@dataclass
class RewardProgram(Attributes):
    id: str = None
    name: str = None
    money: Decimal = None
    type: RewardType = None
    start_date: str = None
    expiry_date: str = None
    number_used: int = 0
    image: Image = None
    customer_groups: List[DefaultObject] = None
    accumulate_limit: bool = False
    duration_limit: DurationType = None
    number_limit: int = None


class RewardProgramSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    money = fields.Decimal(required=True)
    type = EnumField(RewardType, required=True)
    start_date = fields.Str(required=True)
    expiry_date = fields.Str(allow_none=True)
    number_used = fields.Int(allow_none=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    customer_groups = fields.List(fields.Nested(DefaultObjectSchema(DefaultObject)), allow_none=True)
    accumulate_limit = fields.Bool(allow_none=True)
    duration_limit = EnumField(DurationType, allow_none=True)
    number_limit = fields.Int(allow_none=True)


@dataclass
class RewardProgramAttributes(RewardProgram, BasicAttributes):
    pass


class RewardProgramAttributesSchema(RewardProgramSchema, BasicAttributesSchema):
    pass


class RewardProgramModel(BasicModel):
    table_name = 'rewardProgram'
    attributes: RewardProgramAttributes
    attributes_class = RewardProgramAttributes
    attributes_schema = RewardProgramAttributesSchema

    query_mapping = {
        'query': ['name', 'id'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'money': 'number',
            'type': 'query',
            'number_used': 'number',
            'company_id': 'query',
            'start_date': 'range',
            'expiry_date': 'range',
            'updated_at': 'range',
            'created_at': 'range'
        },
        'mapping': {
            'image': 'image',
            'user': 'user',
        }
    }

    @classmethod
    def increase_used_number(cls, company_id, reward_program_id):
        reward_program_obj = cls.by_key({cls.key__id: reward_program_id, "company_id": company_id})
        reward_program_obj.update({"number_used": reward_program_obj.attributes.number_used + 1})

    @classmethod
    def get_effective_program(cls, company_id, reward_program_id=None):
        try:
            search_params = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "term": {
                                    "company_id.keyword": company_id
                                }
                            },
                            {
                                "range": {
                                    "start_date": {
                                        "lte": "now",
                                    }
                                }
                            }
                        ],
                        "should": [
                            {
                                "range": {
                                    "expiry_date": {
                                        "gte": "now",
                                    }
                                }
                            },
                            {
                                "bool": {
                                    "must_not": {
                                        "exists": {
                                            "field": "expiry_date"
                                        }
                                    }
                                }
                            }
                        ],
                        "minimum_should_match": 1
                    }
                }
            }
            if reward_program_id is not None:
                search_params['query']['bool']['must'].append({
                    "term": {
                        "id.keyword": reward_program_id
                    }
                }, )
            response = cls.report(search_params, service=RESOURCE_SERVICE).body['hits']['hits']
        except NotFoundError as ex:
            response = []
        result = [item['_source'] for item in response]
        if len(result) == 0:
            return None
        return cls(result[0])
