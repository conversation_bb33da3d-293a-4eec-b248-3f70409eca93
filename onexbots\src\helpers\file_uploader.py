import os
from typing import Dict, Any

from botocore.exceptions import Client<PERSON><PERSON>r
from nolicore.utils.aws.boto_helper import get_s3_client
from nolicore.utils.utils import logger

KNOWLEDGE_BUCKET_NAME = os.getenv('KNOWLEDGE_BUCKET_NAME')


class FileService:
    # Supported file types and their MIME types
    SUPPORTED_FILE_TYPES = {
        "text": ["text/plain", "text/markdown", "text/csv"],
        "document": [
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ],
        "web": ["text/html"]
    }

    # Maximum file size (15MB)
    MAX_FILE_SIZE = 15 * 1024 * 1024

    def __init__(self):
        self.s3_client = get_s3_client()
        self.bucket_name = KNOWLEDGE_BUCKET_NAME

    def check_s3_file_exists(self, file_key: str) -> bool:
        """
        Check if a file exists in S3.

        Args:
            file_key: S3 key of the file to check

        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=file_key
            )
            return True
        except Exception as e:
            logger.error(f"Failed to check file existence {file_key}: {str(e)}")
            return False

    def generate_upload_url(
            self,
            file: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate presigned URL for file to upload to S3.

        Args:
            file: File metadata (name, type, size, s3_key)

        Returns:
            Dict containing upload URL and file metadata

        Raises:
            ValueError: If file validation fails or URL generation fails
        """
        # Validate file type
        if not self._validate_file_type(file["type"]):
            raise ValueError(f"Unsupported file type: {file['type']}")

        # Validate file size
        if not self._validate_file_size(file["size"]):
            raise ValueError(
                f"File size exceeds limit of {self.MAX_FILE_SIZE} bytes"
            )

        # Generate S3 key
        s3_key = file['s3_key']

        try:
            # Generate presigned URL
            url = self.s3_client.generate_presigned_url(
                'put_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': s3_key,
                    'ContentType': file["type"],
                },
                ExpiresIn=900,
            )

            return url

        except ClientError as e:
            logger.error(f"Failed to generate upload URL: {str(e)}")
            raise ValueError(f"Failed to generate upload URL: {str(e)}")

    def upload_file(self, file: Dict[str, Any]) -> Dict[str, Any]:
        """
        Upload file to S3.

        Args:
            file: File data (key, body)

        Returns:
            Dict containing upload result

        Raises:
            ValueError: If upload fails
        """
        try:
            response = self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=file['key'],
                Body=file['body']
            )
            return response
        except ClientError as e:
            logger.error(f"Failed to upload file {file['key']}: {str(e)}")
            raise ValueError(f"Failed to upload file {file['key']}: {str(e)}")

    def delete_file(self, file_key: str) -> bool:
        """
        Delete file from S3.

        Args:
            file_key: S3 key of the file to delete

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=file_key
            )
            return True
        except ClientError as e:
            logger.error(f"Failed to delete file {file_key}: {str(e)}")
            return False

    def _validate_file_type(self, file_type: str) -> bool:
        """Validate if file type is supported."""
        mime_types = []
        for types in self.SUPPORTED_FILE_TYPES.values():
            mime_types.extend(types)
        return file_type in mime_types

    def _validate_file_size(self, file_size: int) -> bool:
        """Validate if file size is within limits."""
        return file_size <= self.MAX_FILE_SIZE
