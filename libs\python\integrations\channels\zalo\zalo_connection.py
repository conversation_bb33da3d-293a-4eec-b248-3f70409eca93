from dataclasses import dataclass, field

from marshmallow import fields

from ..connection_base import OAuth2Connection, OAuth2ConnectionAttributes, OAuth2ConnectionSchema, OAuth2Settings, \
    OAuth2SettingsSchema


class ZaloSettingsSchema(OAuth2SettingsSchema):
    app_id = fields.Str(required=True)
    app_secret = fields.Str(required=True)
    api_key = fields.Str(required=True)


@dataclass
class ZaloSettings(OAuth2Settings):
    app_id: str = None
    app_secret: str = None
    api_key: str = None


@dataclass
class ZaloConnectionAttributes(OAuth2ConnectionAttributes):
    channel_name: str = field(default='zalo')
    settings: ZaloSettings = field(default_factory=ZaloSettings)


class ZaloConnectionSchema(OAuth2ConnectionSchema):
    settings = fields.Nested(ZaloSettingsSchema(ZaloSettings))


class ZaloConnection(OAuth2Connection):
    attributes: ZaloConnectionAttributes = None
    attributes_schema = ZaloConnectionSchema
    attributes_class = ZaloConnectionAttributes
