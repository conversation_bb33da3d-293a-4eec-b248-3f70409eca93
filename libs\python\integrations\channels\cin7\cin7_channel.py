from typing import Dict, Any, Tu<PERSON>, Optional, List

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import AbstractChannel
from integrations.channels.cin7.cin7_connection import Cin7Settings
from integrations.channels.cin7.cin7_library import Cin7<PERSON>ibrary
from integrations.channels.connection_types import ChannelType
from integrations.common.event import FetchEvent


class Cin7Channel(AbstractChannel):
    channel_name = "cin7"
    library_class = Cin7Library

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        pass

    def _create_library(self, connection):
        settings: Cin7Settings = connection.attributes.settings
        return Cin7Library(
            account_id=settings.account_id,
            api_key=settings.api_key,
            base_url=settings.base_url
        )

    def test_connection(self) -> bool:
        return self.library.test_connection()

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            "name": "Cin7",
            "channel_type": ChannelType.ERP,
            "description": "Connect your Cin7 (Dear Inventory) account to sync products and other data.",
            "logo": cls._load_image_as_base64(__file__, "cin7.png")
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_product:
            return {
                "Page": 1,
                "Limit": 50,
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        return {}

    def get_product__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        if fetch_event.object_id:
            return self.library.get_product(product_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        else:
            params = fetch_event.to_dict()['meta'].get('current_params') if fetch_event.to_dict()['meta'] else None
            return self.library.get_products(params=params, settings=settings, fetch_event_id=fetch_event.id,
                                             next_page=fetch_event.next_page)

    def get_product__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_product_params()

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        return self.library.extract_object_details(payload, headers, event_type)
