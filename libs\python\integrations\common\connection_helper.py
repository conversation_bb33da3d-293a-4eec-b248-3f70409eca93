import os
from typing import Dict, Any

from cachetools.func import ttl_cache
from nolicore.utils.aws.request import ApiGatewayResponse
from nolicore.utils.exceptions import NotFoundRequest, BadRequest
from nolicore.utils.utils import logger

from integrations.channels.action_types import ActionGroup, ActionType, FetchActionSettings, PublishActionSettings
from integrations.channels.channel_factory import ChannelFactory
from integrations.channels.channel_library import OAuth2Channel
from integrations.channels.connection_base import OAuth2Connection, OAuth2ConnectionAttributes
from integrations.channels.connection_registry import connection_registry
from integrations.channels.connection_types import AuthType, ConnectionStatus
from integrations.common.base_api_library import Base<PERSON><PERSON><PERSON><PERSON>
from integrations.integration_exception import ConnectionTestFailedException
from models.integration.connection import ConnectionModel, ConnectionByCompany, ConnectionAttributes

APP_URL = os.getenv('APP_URL')


def get_connection_instance(connection_data: Dict[str, Any], enrich_setting=False,
                            redact_data=False) -> ConnectionModel:
    """
    Returns the correct connection instance based on the channel name and AuthType.

    :param connection_data: Raw connection data from the database
    :param enrich_setting: Enrich the connection instance with dynamic settings
    :param redact_data: If True, will remove sensitive data like OAuth2 tokens
    :return: An instance of the appropriate ConnectionModel subclass
    """
    if not connection_data:
        return None

    # Remove OAuth2 data if redact_data=True
    if redact_data:
        # Get list of fields from OAuth2ConnectionAttributes
        oauth2_fields = set(OAuth2ConnectionAttributes.__dataclass_fields__.keys())
        # Get list of fields from ConnectionAttributes
        base_fields = set(ConnectionAttributes.__dataclass_fields__.keys())
        # Remove fields that are in OAuth2ConnectionAttributes but not in ConnectionAttributes
        fields_to_remove = oauth2_fields - base_fields
        for field in fields_to_remove:
            connection_data.pop(field, None)

    channel_name = connection_data.get('channel_name')
    auth_type = connection_data.get('auth_type')
    default_settings = ChannelFactory.create_default_connection_settings(channel_name)

    # Merge action groups
    connection_data['action_groups'] = {**default_settings['action_groups'], **connection_data.get('action_groups', {})}

    # Update destinations for each action group
    for object_type, action_group_data in connection_data['action_groups'].items():
        for action_type, action_type_data in action_group_data.get('fetch_actions', {}).items():
            destinations = get_destinations(connection_data['company_id'], object_type)
            old_destinations = action_type_data.get('destinations', {}) or {}
            action_type_data['destinations'] = {**destinations, **old_destinations}
            if str(connection_data['id']) in action_type_data['destinations']:
                action_type_data['destinations'].pop(connection_data['id'], None)

    # Merge webhook settings
    connection_data['webhook_settings'] = {**default_settings['webhook_settings'],
                                           **connection_data.get('webhook_settings', {})}

    # Merge all settings
    enrich_connection_data = {**default_settings, **connection_data}

    # Get the specific connection class from the registry
    connection_class = connection_registry.get(channel_name)

    if connection_class:
        # If a specific connection class is found, use it
        connection_instance = connection_class(enrich_connection_data)
        channel = connection_instance.get_channel()
        if enrich_setting:
            dynamic_settings = channel.get_dynamic_settings()
            connection_instance.attributes.dynamic_settings = dynamic_settings
    elif auth_type == AuthType.OAUTH.value:
        # If no specific class is found but it's OAuth, use OAuth2Connection
        connection_instance = OAuth2Connection(enrich_connection_data)
    else:
        # Default to ConnectionModel for other cases
        connection_instance = ConnectionModel(enrich_connection_data)

    return connection_instance


def get_connection_by_id(_id: str, company_id=None, enrich_setting=False, redact_data=False) -> ConnectionModel:
    """
    Retrieves a connection by ID and returns the appropriate connection instance.

    :param _id: The ID of the connection
    :param company_id: The ID of the connection
    :param enrich_setting: Enrich the connection instance with dynamic settings
    :param redact_data: If True, will remove sensitive data like OAuth2 tokens
    :return: An instance of ConnectionModel or OAuth2Connection
    """
    connection_data = ConnectionModel.get_connection_data(_id, company_id)
    return get_connection_instance(connection_data, enrich_setting=enrich_setting, redact_data=redact_data)


@ttl_cache(ttl=300, maxsize=128)
def get_destinations(company_id, object_type):
    connections = ConnectionByCompany.get_active_publish_actions_by_company(str(company_id))
    active_destinations = {}

    for connection in connections:
        action_group = connection['action_groups'].get(object_type, {})
        publish_actions = action_group.get('publish_actions', {})

        for action_name, action_data in publish_actions.items():
            active_destinations[connection['id']] = {
                'type': connection['channel_name'],
                'enable': False
            }

    return active_destinations


def process_connection(connection, is_new=False, base_url: str = None, redirect_url: str = None):
    connection_class = connection_registry.get(connection.attributes.channel_name)
    if not connection_class:
        raise NotFoundRequest("Connection type not found")

    try:
        if issubclass(connection_class, OAuth2Connection):
            oauth2_connection = connection_class(connection.attributes_dict)
            auth_channel: OAuth2Channel = oauth2_connection.get_channel()
            if not redirect_url:
                redirect_url = connection.attributes_dict.get('settings', {}).get('redirect_url')
            auth_url = auth_channel.initiate_oauth_flow(redirect_url, base_url)
            # For OAuth2, we save immediately to store the state
            connection.save()
            return connection, auth_url
        else:
            try:
                test_and_setup_connection(connection)
                # Only save if the test and setup are successful
                connection.save()
                return connection, None
            except ConnectionTestFailedException as e:
                # No need to refresh, as we haven't saved any changes yet
                raise BadRequest(f"{'Connection setup' if is_new else 'Connection update'} failed: {str(e)}")

    except Exception as e:
        logger.error(f"Error {'setting up' if is_new else 'updating'} connection: {str(e)}")
        raise BadRequest(str(e))


def test_and_setup_connection(connection):
    channel = connection.get_channel()
    if hasattr(channel, 'setup_webhooks'):
        channel.setup_webhooks()
    test_result = channel.test_connection()
    if not test_result:
        raise ConnectionTestFailedException("Connection test failed")
    connection.activate_connection()


def generate_response(connection, auth_url, is_new):
    action = "initiated" if is_new else "re-initiated"
    message = "setup" if is_new else "updated"

    if auth_url:
        return {
            "message": f"{connection.attributes.channel_name} OAuth2 flow {action}",
            "authorization_url": auth_url,
            "connection_id": str(connection.attributes.id)
        }
    else:
        return {
            "message": f"{connection.attributes.channel_name} connection {message} successfully",
            "connection_id": str(connection.attributes.id)
        }


def process_oauth2_callback(state, query_params, company_id=None):
    """
    Process OAuth2 callback and complete the authorization flow.
    
    Args:
        state: The state parameter from the OAuth2 callback
        query_params: Query parameters from the OAuth2 callback
        company_id: Optional company ID for the connection
        
    Returns:
        ApiGatewayResponse: Response with redirect location
        
    Raises:
        BadRequest: If required parameters are missing
        NotFoundRequest: If connection is not found
    """
    if not state:
        raise BadRequest("Missing state parameter")
    if not query_params:
        raise BadRequest("Missing query parameters")

    try:
        connection_id, location = BaseAPILibrary.get_redirect_url(state, query_params)

        if not connection_id:
            raise BadRequest("Invalid state parameter: missing connection_id")

        # Get connection
        connection = get_connection_by_id(connection_id)
        if not connection:
            raise NotFoundRequest("Connection not found")

        # Validate connection type
        connection_class = connection_registry.get(connection.attributes.channel_name)
        if not connection_class or not issubclass(connection_class, OAuth2Connection):
            raise BadRequest("Invalid connection type for OAuth2 callback")

        # Complete OAuth flow
        auth_channel: OAuth2Channel = connection.get_channel()
        auth_channel.complete_oauth_flow(query_params)

        return ApiGatewayResponse(
            http_code=302,
            headers={'Location': location},
            body=None
        )

    except Exception as e:
        logger.error(f"Error processing OAuth2 callback: {str(e)}")
        raise BadRequest(f"Error processing OAuth2 callback: {str(e)}")


def _create_connection(source_channel, company_id, credentials, base_url: str = None, redirect_url: str = None):
    connection_class = connection_registry.get(source_channel)
    if not connection_class:
        raise NotFoundRequest("Connection type not found")

    connection_data = {
        'company_id': company_id,
        'settings': credentials
    }

    connection = connection_class.create_connection(connection_data)
    return process_connection(connection, is_new=True, base_url=base_url, redirect_url=redirect_url)


def validate_connections(source, destination, company_id):
    source_connection = get_connection_by_id(source, company_id)
    destination_connection = get_connection_by_id(destination, company_id)

    if not source_connection or not destination_connection:
        raise NotFoundRequest("Connection not found")
    if source == destination:
        raise BadRequest("Source and destination cannot be the same")
    if source_connection.attributes.status != ConnectionStatus.ACTIVE:
        raise BadRequest("Source connection is not active")

    return source_connection, destination_connection


def update_fetch_settings(connection, action_group, enabled, destination_id, destination_type):
    group = ActionGroup(action_group)
    if not group or action_group not in ['order', 'product', 'inventory']:
        return False, False

    action_groups = connection.attributes.action_groups
    if group.value not in action_groups:
        return group.value, None

    # Update fetch actions
    get_action_type = ActionType(f"get_{action_group}")
    fetch_actions = action_groups[group.value]['fetch_actions']
    if get_action_type.value in fetch_actions:
        settings = fetch_actions[get_action_type.value]
        new_destinations = {destination_id: {"enabled": enabled, "type": destination_type}}
        fetch_settings = FetchActionSettings(
            enabled=enabled,
            response_mapping=settings.response_mapping,
            rate_limit=settings.rate_limit,
            retry_settings=settings.retry_settings,
            custom_settings=settings.custom_settings,
            schedule=settings.schedule if settings.schedule else None,
            status=settings.status,
            destinations={**settings.destinations, **new_destinations}
        )
        connection.set_fetch_action_settings(group, get_action_type, fetch_settings)

    # Update publish actions
    sync_action_type = ActionType(f"sync_{action_group}")
    publish_actions = action_groups[group.value]['publish_actions']
    if sync_action_type.value in publish_actions:
        settings = publish_actions[sync_action_type.value]
        publish_settings = PublishActionSettings(
            enabled=enabled,
            payload_template=settings.payload_template,
            rate_limit=settings.rate_limit,
            retry_settings=settings.retry_settings,
            custom_settings=settings.custom_settings
        )
        connection.set_publish_action_settings(group, sync_action_type, publish_settings)
        return None, None
    return None, sync_action_type.value
