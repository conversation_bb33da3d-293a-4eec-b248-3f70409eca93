# onexapis-sample
INSTALL:
- npm install
- sls plugin install -n serverless-prune-plugin
- sls plugin install -n serverless-dynamodb-autoscaling


Staging deploy:
serverless deploy --stage dev


Production deploy:
serverless deploy --stage prod

####################################################
.ENV
from dotenv import load_dotenv

load_dotenv()
os.getenv('PUBLIC_BUCKET_NAME')
####################################################

COMMON ERROR:

ISSUE:
Unable to import module XXXX: No module named 'rpds.rpds'
RESOLVE:
some module broken -> debug to find it
