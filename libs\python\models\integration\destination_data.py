from dataclasses import dataclass
from typing import Dict, Any
from marshmallow import fields
from marshmallow_enum import EnumField

from integrations.channels.action_types import ActionGroup
from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


@dataclass
class DestinationDataAttributes(BasicAttributes):
    name: str = None
    extra: str = None
    type: ActionGroup = None
    connection_id: str = None
    standard_data: Dict[str, Any] = None
    external_updated_at: str = None
    external_created_at: str = None
    
    
class DestinationDataAttributesSchema(BasicAttributesSchema):
    name = fields.Str(allow_none=True)
    extra = fields.Str(allow_none=True)
    type = EnumField(ActionGroup, required=True)
    connection_id = fields.Str(required=True)
    standard_data = fields.Dict(required=True)
    external_updated_at = fields.DateTime(required=True)
    external_created_at = fields.DateTime(required=True)
    
    
class DestinationDataModel(BasicModel):
    key__id = 'id'
    key__ranges = ['connection_id', 'type']
    table_name = 'destinationData'
    attributes: DestinationDataAttributes
    attributes_schema = DestinationDataAttributesSchema
    attributes_class = DestinationDataAttributes

    query_mapping = {
        'query': ['id', 'name', 'extra'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'extra': 'query',
            'connection_id': 'query',
            'type': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'external_updated_at': 'range',
            'created_at': 'range',
            'external_created_at': 'range',
        },
    }
    
    @classmethod
    def put(cls, company_id, connection_id, group, data):
        if 'id' in data and data['id'] is not None:
            destination_data = cls.list({
                'id': data['id'],
                'connection_id': connection_id,
                'type': group,
            }, limit=10).get('Items', [])
            if destination_data:
                destination_data_obj = cls(destination_data[0])
                destination_data_obj.update(data)
                return destination_data_obj
            else:
                destination_data = cls.create(company_id, {
                    'connection_id': connection_id,
                    'type': group,
                    **data
                })
                return destination_data
        else:
            destination_data = cls.create(company_id, {
                'connection_id': connection_id,
                'type': group,
                **data
            })
            return destination_data
        
    @classmethod
    def get_detail(cls, destination_data_id, connection_id, group):
        destination_data = cls.list({
            'id': destination_data_id,
            'connection_id': connection_id,
            'type': group,
        }, limit=10).get('Items', [])
        if destination_data:
            return cls(destination_data[0])
        else:
            return None
