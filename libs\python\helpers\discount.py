import re
from decimal import Decimal

import pendulum
from nolicore.utils.exceptions import BadRequest

from models.promotion.discount import DiscountType, DiscountModel, DiscountCodeIndex


def validate_discount(discount, company_id):
    # validate discount id
    discount_id = discount.get('id')
    if discount_id is not None:
        key = {
            DiscountModel.key__id: discount_id,
            'company_id': company_id
        }
        discount_obj = DiscountModel.by_key(key)
        if discount_obj is not None:
            raise BadRequest('Discount ID is available')

    # validate discount code
    discount_code = discount.get('discount_code')
    if discount_code is None:
        raise BadRequest('Require discount code')
    check_discount = DiscountCodeIndex.get_discount_by_code(code=discount_code, company_id=company_id)
    if check_discount is not None:
        raise BadRequest('Discount code available')

    # validate expire_at
    discount_start_at = discount.get('start_at')
    discount_expire_at = discount.get('expire_at')
    if discount_start_at is not None and discount_expire_at is not None and pendulum.parse(
            discount_start_at) >= pendulum.parse(discount_expire_at):
        raise BadRequest(f'Start_at is not greater than expire_at')

    discount_effective_time_range = discount.get('effective_time_range')
    if discount_effective_time_range is not None:
        time_range_pattern = re.compile(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]$')
        if time_range_pattern.match(discount_effective_time_range) is None:
            raise BadRequest('Time range require HH:MM-HH:MM format')

    discount_effective_days_of_week = discount.get('effective_days_of_week')
    if discount_effective_days_of_week is not None:
        for day_of_week in discount_effective_days_of_week:
            try:
                if int(day_of_week) > 6 or int(day_of_week) < 0:
                    raise BadRequest('Invalid days of week')
            except ValueError:
                raise BadRequest('Invalid days of week')

    discount_effective_days_of_month = discount.get('effective_days_of_month')
    if discount_effective_days_of_month is not None:
        for day_of_month in discount_effective_days_of_month:
            try:
                if int(day_of_month) > 31 or int(day_of_month) < 1:
                    raise BadRequest('Invalid days of month')
            except ValueError:
                raise BadRequest('Invalid days of month')

    discount_effective_months = discount.get('effective_months')
    if discount_effective_months is not None:
        for month in discount_effective_months:
            try:
                if int(month) > 12 or int(month) < 1:
                    raise BadRequest('Invalid months')
            except ValueError:
                raise BadRequest('Invalid months')

    # validate discount condition
    validation_functions = {
        DiscountType.BY_ORDER_TOTAL.value: validate_discount_by_order_total,
        DiscountType.BY_PRODUCT.value: validate_discount_by_product,
        DiscountType.PRODUCT_BY_ORDER_TOTAL.value: validate_discount_product_by_order_total,
        DiscountType.BY_QUANTITY.value: validate_discount_by_quantity,
        DiscountType.BY_PURCHASE_PRODUCT.value: validate_discount_by_purchase_product,
        DiscountType.GIFT_BY_ORDER_TOTAL.value: validate_gift_by_order_total,
        DiscountType.GIFT_BY_PURCHASE_PRODUCT.value: validate_gift_by_purchase_product,
    }
    discount_type = discount.get('type')
    validation_function = validation_functions.get(discount_type)
    if validation_function is None:
        raise BadRequest('Invalid type')
    discount_info_key = discount_type.lower() if discount_type.startswith(
        "GIFT_") else f'discount_{discount_type.lower()}'
    discount_info = discount[discount_info_key]
    if discount_info is None:
        raise BadRequest(f'required {discount_info_key}')
    validation_function(discount_info)


def validate_discount_by_order_total(discount_info):
    last_amount_to = 0
    condition_length = len(discount_info)
    for i, item in enumerate(discount_info):
        amount_from = item.get('amount_from')
        amount_to = item.get('amount_to')
        if amount_from is None:
            raise BadRequest(f'required discount_by_order_total[{i}].amount_from')
        if Decimal(amount_from) < 0:
            raise BadRequest(f'invalid discount_by_order_total[{i}].amount_from')
        if amount_to is None:
            if condition_length > i + 1:
                raise BadRequest(f'invalid discount_by_order_total[{i}].amount_to')
        else:
            if Decimal(amount_to) < 0:
                raise BadRequest(f'invalid discount_by_order_total[{i}].amount_to')
            if Decimal(amount_from) > Decimal(amount_to):
                raise BadRequest('amount_from must greater than amount_to')
            if last_amount_to > Decimal(amount_from) and i > 0:
                raise BadRequest(
                    f'discount_by_order_total[{i}].amount_from must be greater than discount_by_order_total[{i - 1}].amount_to')
            last_amount_to = Decimal(amount_to)


def validate_discount_by_product(discount_info):
    discount_items = []
    for i, item in enumerate(discount_info):
        min_quantity = item.get('min_quantity')
        discount_limit = item.get('discount_limit')
        if discount_limit is not None and Decimal(min_quantity) > Decimal(discount_limit):
            raise BadRequest(
                f'discount_by_product[{i}].min_quantity must be less than discount_by_order_total[{i}].discount_limit')
        if item['for_all_items'] is False:
            new_item_id = validate_item_line(item, i, discount_items, "discount_by_product")
            discount_items.append(new_item_id)


def validate_discount_product_by_order_total(discount_info):
    last_amount_to = 0
    for i, item in enumerate(discount_info):
        amount_from = item.get('amount_from')
        if amount_from is None:
            raise BadRequest(f'required discount_product_by_order_total[{i}].amount_from')
        if Decimal(amount_from) < 0:
            raise BadRequest(f'invalid discount_product_by_order_total[{i}].amount_from')
        if last_amount_to > Decimal(amount_from):
            raise BadRequest(
                f'discount_product_by_order_total[{i}].amount_from must be greater than discount_product_by_order_total[{i - 1}].amount_from')
        last_amount_to = Decimal(amount_from)


def validate_discount_by_quantity(discount_info):
    discount_items = []
    for i, item in enumerate(discount_info):
        if item['for_all_items'] is False:
            new_item_id = validate_item_line(item, i, discount_items, "discount_by_quantity")
            discount_items.append(new_item_id)
        if item['conditions'] is None or len(item['conditions']) == 0:
            raise BadRequest(f'required discount_by_quantity[{i}].conditions')
        last_quantity_to = 0
        if type(item.get('conditions')) is not list:
            raise BadRequest(f'Invalid conditions')
        condition_length = len(item['conditions'])
        for sub_index, sub_item in enumerate(item['conditions']):
            quantity_from = sub_item.get('quantity_from')
            quantity_to = sub_item.get('quantity_to')
            if quantity_from is None:
                raise BadRequest(f'required discount_by_quantity[{i}].conditions[{sub_index}].quantity_from')
            if Decimal(quantity_from) < 0:
                raise BadRequest(f'invalid discount_by_quantity[{i}].conditions[{sub_index}].quantity_from')
            if quantity_to is None:
                if condition_length > i + 1:
                    raise BadRequest(f'invalid discount_by_quantity[{i}].conditions[{sub_index}].quantity_to')
            else:
                if Decimal(quantity_to) < 0:
                    raise BadRequest(f'invalid discount_by_quantity[{i}].conditions[{sub_index}].quantity_to')
                if Decimal(quantity_from) > Decimal(quantity_to):
                    raise BadRequest('quantity_from must greater than quantity_to')
                if last_quantity_to > Decimal(quantity_from) and sub_index > 0:
                    raise BadRequest(
                        f'discount_by_quantity[{i}].conditions[{sub_index}].quantity_from must be greater than discount_by_quantity[{i - 1}].conditions[{sub_index}].quantity_to')
                last_quantity_to = Decimal(quantity_to)


def validate_discount_by_purchase_product(discount_info):
    discount_items = []
    for i, item in enumerate(discount_info):
        if item['for_all_purchase_product'] is False:
            new_item_id = validate_item_line(item, i, discount_items, "discount_by_purchase_product", True)
            discount_items.append(new_item_id)


def validate_gift_by_order_total(discount_info):
    last_amount_to = 0
    for i, item in enumerate(discount_info):
        amount_from = item.get('amount_from')
        if amount_from is None:
            raise BadRequest(f'required gift_by_order_total[{i}].amount_from')
        if Decimal(amount_from) < 0:
            raise BadRequest(f'invalid gift_by_order_total[{i}].amount_from')
        if last_amount_to > Decimal(amount_from):
            raise BadRequest(
                f'gift_by_order_total[{i}].amount_from must be greater than gift_by_order_total[{i - 1}].amount_from')
        last_amount_to = Decimal(amount_from)


def validate_gift_by_purchase_product(discount_info):
    discount_items = []
    for i, item in enumerate(discount_info):
        if item['for_all_purchase_product'] is False:
            new_item_id = validate_item_line(item, i, discount_items, "gift_by_purchase_product", True)
            discount_items.append(new_item_id)


def validate_item_line(item, i, discount_items, validation_function, is_purchase_product=False):
    item_type = item.get('purchase_product_type') if is_purchase_product else item.get('item_type')
    item_data = item.get(f'purchase_{item_type.lower()}') if is_purchase_product else item.get(f'{item_type.lower()}')
    if item_data is None or len(item_data) == 0:
        raise BadRequest(f'invalid {validation_function}[{i}].{item_type.lower()}')
    else:
        for sub_index, sub_item in enumerate(item_data):
            item_id = sub_item['id']
            if item_id in discount_items:
                raise BadRequest(f'{validation_function}[{i}].{item_type.lower()}[{sub_index}] is duplicated')
            else:
                return item_id
