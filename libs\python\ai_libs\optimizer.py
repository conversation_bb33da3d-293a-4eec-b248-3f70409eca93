import os
import json
import requests
from typing import Dict, Any

SUPPORTED_MARKETPLACES = {
    "amazon": {
        "name": "Amazon",
        "max_title_length": 200,
        "max_description_length": 2000,
        "features": ["keyword_optimization", "competitive_analysis", "listing_compliance"]
    },
    "ebay": {
        "name": "eBay",
        "max_title_length": 80,
        "max_description_length": 4000,
        "features": ["keyword_optimization", "listing_compliance"]
    },
    "general": {
        "name": "General E-commerce",
        "max_title_length": 100,
        "max_description_length": 1000,
        "features": ["keyword_optimization"]
    }
}


class ProductOptimizer:
    def __init__(self):
        """Initialize the ProductOptimizer."""
        self.api_key = os.environ.get('OPEN_AI_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key not found in environment variables")
        self.api_base = "https://api.openai.com/v1/chat/completions"

    def _make_openai_request(self, messages):
        """Make a request to OpenAI API."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "gpt-4o-mini",
            "messages": messages,
            "response_format": {"type": "json_object"}
        }

        response = requests.post(self.api_base, headers=headers, json=data)
        response.raise_for_status()
        return response.json()

    def optimize_product_listing(self, original_title: str, original_description: str,
                                 target_marketplace: str = "general",
                                 max_title_length: int = 100) -> Dict[str, str]:
        """Optimize a product listing."""
        try:
            messages = [
                {
                    "role": "system",
                    "content": f"You are an expert in e-commerce optimization for {target_marketplace}."
                },
                {
                    "role": "user",
                    "content": f"""Optimize this product listing for {target_marketplace}:
                    Title: {original_title}
                    Description: {original_description}
                    Max title length: {max_title_length}

                    Return a JSON with 'title' and 'description' fields."""
                }
            ]

            response = self._make_openai_request(messages)
            result = json.loads(response['choices'][0]['message']['content'])

            return {
                "title": result.get("title", original_title),
                "description": result.get("description", original_description)
            }

        except Exception as e:
            return {
                "error": str(e),
                "title": original_title,
                "description": original_description
            }

    def analyze_optimization(self, original: Dict[str, str],
                             optimized: Dict[str, str]) -> Dict[str, Any]:
        """Analyze optimization results."""
        try:
            messages = [
                {
                    "role": "system",
                    "content": "You are an expert in analyzing e-commerce listings."
                },
                {
                    "role": "user",
                    "content": f"""Compare these listings:
                    Original: {json.dumps(original)}
                    Optimized: {json.dumps(optimized)}

                    Return a JSON with 'key_changes', 'improvements', 'seo_impact', and 'readability_score' fields."""
                }
            ]

            response = self._make_openai_request(messages)
            return json.loads(response['choices'][0]['message']['content'])

        except Exception as e:
            return {"error": str(e)}


def create_response(status_code: int, body: Any) -> Dict[str, Any]:
    """Create API Gateway response."""
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        },
        'body': json.dumps(body)
    }


def validate_request(body: Dict[str, Any]) -> tuple[Dict[str, str], str, int]:
    """Validate request parameters."""
    title = body.get('title')
    description = body.get('description')

    if not title or not isinstance(title, str):
        raise ValueError("Title is required and must be a string")
    if not description or not isinstance(description, str):
        raise ValueError("Description is required and must be a string")

    marketplace = body.get('target_marketplace', 'general').lower()
    if marketplace not in SUPPORTED_MARKETPLACES:
        raise ValueError(f"Unsupported marketplace: {marketplace}")

    max_length = body.get('max_title_length',
                          SUPPORTED_MARKETPLACES[marketplace]['max_title_length'])
    try:
        max_length = int(max_length)
        if max_length <= 0:
            raise ValueError
    except (ValueError, TypeError):
        raise ValueError("max_title_length must be a positive integer")

    return (
        {'title': title.strip(), 'description': description.strip()},
        marketplace,
        max_length
    )


def optimize_product(event, context):
    """Lambda handler for product optimization."""
    try:
        body = json.loads(event.get('body', '{}'))
        if not body:
            return create_response(400, {"error": "Missing request body"})

        product_data, marketplace, max_length = validate_request(body)

        optimizer = ProductOptimizer()
        optimized = optimizer.optimize_product_listing(
            original_title=product_data['title'],
            original_description=product_data['description'],
            target_marketplace=marketplace,
            max_title_length=max_length
        )

        if "error" in optimized:
            return create_response(400, {"error": optimized["error"]})

        analysis = optimizer.analyze_optimization(product_data, optimized)

        return create_response(200, {
            "message": "Product listing optimized successfully",
            "result": {
                "optimized": optimized,
                "analysis": analysis
            }
        })

    except ValueError as e:
        return create_response(400, {"error": str(e)})
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return create_response(500, {"error": "An unexpected error occurred"})


def get_supported_marketplaces(event, context):
    """Lambda handler for supported marketplaces."""
    try:
        return create_response(200, {
            "marketplaces": [
                {"key": key, **config}
                for key, config in SUPPORTED_MARKETPLACES.items()
            ]
        })
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return create_response(500, {"error": "An unexpected error occurred"})