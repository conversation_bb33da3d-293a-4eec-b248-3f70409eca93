import os
import re

from botocore.exceptions import ClientError
from nolicore.adapters.db.model import Model
from nolicore.utils.aws.boto_helper import get_resource
from nolicore.utils.utils import logger

from helpers.errors import ShortcutNameInvalid
from models.common.shortcut_name import ShortcutName

RESOURCE_SERVICE = os.getenv('RESOURCE_SERVICE')
COUNTER_TABLE = 'counter'
BASE_URL = os.getenv('BASE_URL')
APP_URL = os.getenv('APP_URL')

# Integration credentials
ONEXBOTS_API = os.environ.get('ONEXBOTS_API')
FACEBOOK_APP_ID = os.environ.get('FACEBOOK_APP_ID')
FACEBOOK_APP_SECRET = os.environ.get('FACEBOOK_APP_SECRET')
ZALO_OA_APP_ID = os.environ.get('ZALO_OA_APP_ID')
ZALO_OA_APP_SECRET = os.environ.get('ZALO_OA_APP_SECRET')
ZALO_OA_SECRET = os.environ.get('ZALO_OA_SECRET')
SHOPIFY_CLIENT_ID = os.environ.get('SHOPIFY_CLIENT_ID')
SHOPIFY_CLIENT_SECRET = os.environ.get('SHOPIFY_CLIENT_SECRET')
TIKTOK_APP_KEY = os.environ.get('TIKTOK_APP_KEY')
TIKTOK_APP_SECRET = os.environ.get('TIKTOK_APP_SECRET')
TIKTOK_SERVICE_ID = os.environ.get('TIKTOK_SERVICE_ID')


def convert_shortcut_name(s, code):
    shortcut_name_mapping = {
        'order': ShortcutName.SOO.value,
        'purchaseOrder': ShortcutName.POO.value,
        'returnOrder': ShortcutName.ROO.value,
        'package': ShortcutName.PO.value,
        'stockAdjustment': ShortcutName.SAO.value,
        'stockRelocate': ShortcutName.SRO.value,
    }
    result = shortcut_name_mapping.get(s)
    if result is None:
        raise ShortcutNameInvalid()
    return result + code


def get_increment_id(company_id, model: Model.__class__):
    dynamodb = get_resource('dynamodb')
    table = dynamodb.Table(COUNTER_TABLE)

    try:
        # Use DynamoDB's atomic counter to increment the ID
        response = table.update_item(
            Key={'id': model.table_name, 'company_id': company_id},
            UpdateExpression='SET current_id = if_not_exists(current_id, :start) + :inc',
            ExpressionAttributeValues={':inc': 1, ':start': 0},
            ReturnValues='UPDATED_NEW'
        )
        new_id = str(response['Attributes']['current_id']).zfill(8)  # Pad with 8 zeros
        shortcut_name = convert_shortcut_name(model.table_name, new_id)
        return shortcut_name
    except ClientError as e:
        logger.exception(e)
        return None
