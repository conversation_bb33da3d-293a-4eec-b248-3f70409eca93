import base64
import imghdr
import inspect
import os
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Any, Callable, Optional, Tuple

from integrations.channels.action_types import ActionType, ActionKind, ActionGroup
from integrations.common.base_api_library import BaseAPILibrary
from integrations.common.event import FetchEvent
from models.integration.webhook import WebhookModel
from models.inventory.location import LocationCompanyIdIndex


class ChannelCapability:
    def __init__(self, action: ActionType, kind: ActionKind, method_name: str):
        self.action = action
        self.kind = kind
        self.method_name = method_name


class AbstractChannel(ABC):
    channel_name: str
    library_class: BaseAPILibrary

    def __init__(self, connection):
        self.connection = connection
        self.library = self._create_library(connection)

    def get_master_locations(self):
        return LocationCompanyIdIndex.get_all_locations(str(self.connection.attributes.company_id))

    @abstractmethod
    def _create_library(self, connection):
        """
        Create and return the appropriate library instance for this channel.
        """
        pass

    @classmethod
    def webhook_response_message(cls, success: bool, message: str = 'Success!', error_code: str = None) -> Dict[
        str, Any]:
        return {'success': success, 'message': message, 'error_code': error_code}

    @classmethod
    @abstractmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        pass

    @classmethod
    @abstractmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        pass

    def has_batch_publish_action(self, action_type: ActionType) -> bool:
        try:
            method = self.get_method_for_action(action_type, ActionKind.BATCH_PUBLISH)
            return method is not None
        except ValueError:
            return False

    def execute_action(self, action_type: ActionType, kind: ActionKind, settings: Dict[str, Any],
                       fetch_event: Optional[FetchEvent] = None, publish_data: dict = None) -> Any:
        method = self.get_method_for_action(action_type, kind)
        if kind in [ActionKind.FETCH, ActionKind.PARAMS]:
            return method(self, settings, fetch_event)
        elif kind == ActionKind.PUBLISH:
            return method(self, settings, publish_data)
        elif kind == ActionKind.BATCH_PUBLISH:
            return method(self, settings, publish_data)
        else:
            raise ValueError(f"Unsupported action kind: {kind}")

    @classmethod
    @abstractmethod
    def get_info(cls) -> Dict[str, str]:
        """
        Returns a dictionary containing channel information.
        Must include 'name', 'description', and 'image' keys.
        """
        pass

    @classmethod
    def get_integration_info(cls) -> Dict[str, str]:
        """
        Returns a dictionary containing channel information.
        Must include 'name', 'description', and 'image' keys.
        """
        return {
            "title": "Seamless Integration",
            "description": "Connect your instance to sync products, orders, and customers.",
            "integration": {
                "Product API": {
                    "description":
                        "Manage your product listings, including creating, updating, and deleting products.",
                    "apis": [
                        "POST /api/v1/product/add",
                        "PUT /api/v1/product/update",
                        "DELETE /api/v1/product/delete",
                    ],
                },
                "Order API": {
                    "description":
                        "Handle order management, from retrieving order details to updating order statuses.",
                    "apis": [
                        "GET /api/v1/orders",
                        "POST /api/v1/orders/update",
                        "GET /api/v1/orders/details",
                    ],
                },
                "Inventory API": {
                    "description":
                        "Keep your inventory levels synchronized with real-time updates.",
                    "apis": ["GET /api/v1/inventory", "POST /api/v1/inventory/update"],
                },
                "Logistics API": {
                    "description":
                        "Manage shipping and logistics, including tracking shipments and updating statuses.",
                    "apis": ["GET /api/v1/logistics/track", "POST /api/v1/logistics/update"],
                },
                "Payment API": {
                    "description":
                        "Handle payment processing and transaction details securely.",
                    "apis": ["GET /api/v1/payments", "POST /api/v1/payments/verify"],
                },
            }
        }

    @classmethod
    def get_features(cls) -> List[Dict[str, str]]:
        return [
            {
                "image": cls._load_image_as_base64(__file__, "images/simplifying.png"),
                "name": "Simplifying E-Commerce for Small Businesses",
                "description":
                    "Discover how a small retail business effortlessly integrated with an e-commerce platform using our API, streamlining their operations and boosting sales.",
            },
            {
                "image": cls._load_image_as_base64(__file__, "images/channel.png"),
                "name": "Unified Management for Multi-Channel Sellers",
                "description":
                    "See how a multi-channel seller centralized their operations, managing all sales channels from one platform.",
            },
            {
                "image": cls._load_image_as_base64(__file__, "images/logistics.png"),
                "name": "Efficient Logistics Management",
                "description":
                    "Learn how a 3PL provider synchronized inventory and orders with an e-commerce platform, improving their warehouse efficiency.",
            },
            {
                "image": cls._load_image_as_base64(__file__, "images/analytics.png"),
                "name": "Enhanced Analytics for Better Insights",
                "description":
                    "Find out how an analytics company provided comprehensive insights to sellers using our standardized an e-commerce platform data.",
            },
            {
                "image": cls._load_image_as_base64(__file__, "images/automatic.png"),
                "name": "Automating E-Commerce Operations",
                "description":
                    "Explore how an automation tool helped businesses reduce manual workload and improve customer experience.",
            }
        ]

    def test_connection(self) -> bool:
        return self.library.test_connection()

    def setup_webhooks(self):
        if self.connection.attributes.external_id:
            company_id = self.connection.attributes.company_id
            WebhookModel.create(company_id, {
                'id': f'{self.channel_name}-{self.connection.attributes.external_id}',
                'connection_id': str(self.connection.attributes.id),
            })

    @classmethod
    def get_supported_actions(cls, kind: ActionKind) -> List[ActionType]:
        supported_actions = []
        for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
            if name.endswith(f"__{kind.value}"):
                action_type = ActionType(name.split("__")[0])
                supported_actions.append(action_type)
        return supported_actions

    @classmethod
    def is_action_supported(cls, action: ActionType, kind: ActionKind) -> bool:
        method_name = f"{action.value}__{kind.value}"
        return hasattr(cls, method_name)

    @classmethod
    def get_method_for_action(cls, action: ActionType, kind: ActionKind) -> Callable:
        method_name = f"{action.value}__{kind.value}"
        if hasattr(cls, method_name):
            return getattr(cls, method_name)
        raise ValueError(f"Unsupported action: {action} for kind: {kind}")

    @classmethod
    def get_all_supported_actions(cls) -> Dict[ActionType, Dict[str, Any]]:
        supported_actions = {}
        for kind in ActionKind:
            for action in cls.get_supported_actions(kind):
                if kind == ActionKind.FETCH:
                    default_settings = cls.get_default_fetch_settings(action)
                elif kind == ActionKind.PUBLISH:
                    default_settings = cls.get_default_publish_settings(action)
                elif kind == ActionKind.BATCH_PUBLISH:
                    continue
                elif kind == ActionKind.WEBHOOK:
                    default_settings = {}
                elif kind == ActionKind.PARAMS:
                    continue
                else:
                    raise ValueError(f"Unsupported action kind: {kind}")

                supported_actions[action] = {
                    "kind": kind,
                    "default_settings": default_settings
                }
        return supported_actions

    @classmethod
    def _load_image_as_base64(cls, _file, image_filename: str) -> Optional[str]:
        """
        Load an image file and return it as a base64-encoded string.

        :param image_filename: The filename of the image in the same directory as the class.
        :return: A base64-encoded string of the image content, or None if the file couldn't be loaded.
        """
        image_path = os.path.join(os.path.dirname(os.path.abspath(_file)), image_filename)
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                image_type = imghdr.what(None, h=image_data)

                if image_type is None:
                    print(f"Warning: Unable to determine image type for {image_filename}")
                    return None

                encoded_image = base64.b64encode(image_data).decode('utf-8')
                return f"data:image/{image_type};base64,{encoded_image}"
        except FileNotFoundError:
            print(f"Warning: Image file {image_filename} not found.")
            return None
        except Exception as e:
            print(f"Error loading image {image_filename}: {str(e)}")
            return None

    @classmethod
    def verify_webhook_signature(cls, payload: str, headers: dict, settings) -> bool:
        pass

    @classmethod
    def extract_merchant_id(cls, payload):
        pass

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        pass

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        pass

    @abstractmethod
    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> FetchEvent:
        """
        Extract object details from the webhook payload.
        
        :param payload: The webhook payload
        :param headers: The webhook headers
        :param event_type: The webhook topic
        :return: A tuple containing:
            - is_batch: Boolean indicating if it's a batch event
            - object_id: The ID of the object (None for batch events)
            - action_group: The type of the object (ActionGroup enum)
            - object_data: The data of the object
            - object_ids: List of object IDs for batch events (None for single object events)
        """
        pass

    def process_webhook_event(self, event_type: str, payload: dict) -> bool:
        """
        Process the webhook event and return a boolean indicating if the event should be fetched.
        :param event_type: The type of the event
        :param payload: The payload of the event
        :return: A boolean indicating if the event should be fetched
        """
        pass

    def get_dynamic_settings(self) -> Dict[str, Any]:
        return self.connection.get_dynamic_settings()


class OAuth2Channel(AbstractChannel, ABC):
    @abstractmethod
    def initiate_oauth_flow(self, redirect_url: str = None, base_url: str = None) -> str:
        pass

    @abstractmethod
    def refresh_oauth_token(self) -> Dict[str, Any]:
        pass

    @abstractmethod
    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        pass

    def complete_oauth_flow(self, query_params: Dict[str, Any]) -> bool:
        external_id, access_token, expires_at, refresh_token, token_data = self.get_token(query_params)

        self.connection.update_token(
            external_id=external_id,
            access_token=access_token,
            expires_at=expires_at,
            refresh_token=refresh_token,
            token_data=token_data
        )
        channel = self.connection.get_channel()
        if hasattr(channel, 'setup_webhooks'):
            channel.setup_webhooks()

    def get_current_token(self) -> Dict[str, Any]:
        token = self.connection.get_token()
        if self.is_token_expired(token):
            token = self.refresh_oauth_token()
        return token

    @staticmethod
    def is_token_expired(token: Dict[str, Any]) -> bool:
        expires_at = datetime.fromisoformat(token['expires_at'])
        return expires_at - timedelta(minutes=5) <= datetime.now()
