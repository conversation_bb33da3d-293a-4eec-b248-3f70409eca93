# optiwarehouse-connection-service
INSTALL:
- npm install
- serverless plugin install -n serverless-python-requirements
- serverless plugin install -n serverless-prune-plugin
- serverless plugin install -n serverless-dotenv-plugin
- serverless plugin install -n serverless-dynamodb-autoscaling


Staging deploy:
serverless deploy --stage dev


Production deploy:
serverless deploy --stage prod

####################################################
.ENV
from dotenv import load_dotenv

load_dotenv()
os.getenv('PUBLIC_BUCKET_NAME')
####################################################

COMMON ERRORS:

ISSUE:
Unable to import module XXXX: No module named 'rpds.rpds'
RESOLVE:
some module broken -> debug to find it
