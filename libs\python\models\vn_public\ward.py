from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema, Model

from models.vn_public.district import DistrictSchemaAttributes, DistrictAttributes
from models.vn_public.province import ProvinceAttributes, ProvinceSchemaAttributes


@dataclass
class WardAttributes(Attributes):
    id: str
    name: str
    slug: str
    name_with_type: str
    path: str
    path_with_type: str
    code: str
    province: ProvinceAttributes
    district: DistrictAttributes


class WardSchemaAttributes(AttributesSchema):
    id = fields.UUID(required=True)
    name = fields.Str(required=True)
    slug = fields.Str(required=True)
    name_with_type = fields.Str(required=True)
    path = fields.Str(required=True)
    path_with_type = fields.Str(required=True)
    code = fields.Str(required=True)
    province = fields.Nested(ProvinceSchemaAttributes(ProvinceAttributes), required=True)
    district = fields.Nested(DistrictSchemaAttributes(DistrictAttributes), required=True)


class WardModel(Model):
    table_name = 'ward'
    attributes: WardAttributes
    attributes_schema = WardSchemaAttributes
    attributes_class = WardAttributes

    query_mapping = {
        'query': ['id', 'name', 'slug', 'name_with_type', 'code', 'path', 'path_with_type'],
        'filter': {
            'district.code': 'query',
            'district.slug': 'query',
            'district.id': 'query',
            'province.code': 'query',
            'province.slug': 'query',
            'province.id': 'query',
        },
        'mapping': {
            'province': 'province',
            'district': 'district'
        }
    }
