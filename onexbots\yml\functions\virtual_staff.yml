create_virtual_staff:
  handler: src.apis.virtual_staff.create_virtual_staff
  events:
    - httpApi:
        path: /virtual-staff
        method: post
        authorizer:
          name: optiAuthorizer

get_virtual_staff:
  handler: src.apis.virtual_staff.get_virtual_staff
  events:
    - httpApi:
        path: /virtual-staff/{id}
        method: get
        authorizer:
          name: optiAuthorizer
    - httpApi:
        path: /services/virtual-staff/{id}
        method: get
        authorizer:
          name: onexbotAuthorizer

get_public_virtual_staff:
  handler: src.apis.virtual_staff.get_public_virtual_staff
  events:
    - httpApi:
        path: /public/virtual-staff/{id}
        method: get

list_virtual_staffs:
  handler: src.apis.virtual_staff.list_virtual_staffs
  events:
    - httpApi:
        path: /virtual-staff
        method: get
        authorizer:
          name: optiAuthorizer

update_virtual_staff:
  handler: src.apis.virtual_staff.update_virtual_staff
  events:
    - httpApi:
        path: /virtual-staff/{id}
        method: put
        authorizer:
          name: optiAuthorizer

delete_virtual_staff:
  handler: src.apis.virtual_staff.delete_virtual_staff
  events:
    - httpApi:
        path: /virtual-staff/{id}
        method: delete
        authorizer:
          name: opti<PERSON>uth<PERSON>zer

removeKnowledgeFromStaff:
  handler: src.apis.virtual_staff.remove_knowledge_from_staff_handler
  timeout: 900
  memorySize: 256