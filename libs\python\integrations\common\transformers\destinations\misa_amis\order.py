import uuid
from typing import Dict, Any

from helpers.common import format_date_from_iso8601_string, convert_to_kebab_case
from integrations.channels.action_types import ActionGroup, ActionType
from models.common.status import Status, OrderType
from models.order.order import OrderAttributesSchema
from ...base import DestinationTransformer


class MisaAmisOrderDestinationTransformer(DestinationTransformer):
    channel_name = 'misa_amis'
    object_type = ActionGroup.order.value

    def _map_object_type_to_action_type(self) -> str:
        return ActionType.sync_order.value

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.order

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        branch_id = self.dynamic_settings.get('branch_id', None)
        stock_mapping = self.dynamic_settings.get('stock_mapping', {})
        account_object_mapping = self.dynamic_settings.get('account_object_mapping', {})

        # Handle customer information
        shipping_address = data['billing_address']['address1']

        depot_id = data['source'].get('origin_id', '')
        account_object = account_object_mapping.get(depot_id, {})

        def map_status(data_status: str) -> str:
            """Map status to the corresponding status in the enum."""
            status_map = {
                Status.DRAFT.value: 0,  # Đơn mới
                Status.PENDING.value: 0,  # Đang xác nhận
                Status.PACKING.value: 1,  # Đang đóng gói
                Status.READY.value: 1,  # Đã đóng gói
                Status.BLOCKING.value: 1,  # Đổi kho xuất hàng
                Status.AWAIT_PACKING.value: 1,  # Chờ thu gom
                Status.SHIPPING.value: 1,  # Đang chuyển
                Status.COMPLETED.value: 2,  # Thành công
                Status.CANCELLED.value: 3,  # Thất bại
                Status.RETURNING.value: 1,  # Đang chuyển hoàn
                Status.RETURNED.value: 3  # Đã chuyển hoàn
            }
            return status_map.get(data_status, 0)

        def get_vat_rate(vat_value):
            """
            Map VAT rate to MISA's format
            0%: 0
            5%: 5
            8%: 8
            10%: 10
            KCT: -1 (Không chịu thuế)
            KKKNT: -2 (Không kê khai nộp thuế)
            KHAC: -3 (Khác)
            """
            vat_map = {
                0: 0,  # 0%
                5: 5,  # 5%
                8: 8,  # 8%
                10: 10,  # 10%
                -1: -1,  # KCT
                -2: -2,  # KKKNT
                -3: -3  # KHAC
            }
            return vat_map.get(vat_value, -3)

        def calculate_vat_amount(price: float, quantity: float, vat_rate: int) -> float:
            """Calculate VAT amount based on price, quantity and VAT rate."""
            if vat_rate < 0:  # For special cases (KCT, KKKNT, KHAC)
                return 0
            return (price * quantity * vat_rate) / 100

        def convert_product_detail(product: Dict[str, Any], index: int, order: Dict[str, Any],
                                   allocation_discount: float) -> Dict[str, Any]:
            """Convert a single product from Nhanh format to MISA detail format."""
            price = float(product['unit_price'])
            quantity = float(product['quantity'])
            discount = float(product.get('discount', 0))
            amount = price * quantity
            discount_amount = discount * quantity + allocation_discount
            vat_rate = get_vat_rate(product.get('vat', 0))
            vat_amount = calculate_vat_amount(price, quantity, product.get('vat', 0))
            stock_object = stock_mapping.get(depot_id, {})
            new_product = {
                "sort_order": index + 1,
                "is_promotion": False,
                "quantity": quantity,
                "unit_price": price,
                "amount_oc": amount,
                "amount": amount,
                "discount_rate": 0.0,
                "main_convert_rate": 1.0,
                "main_quantity": quantity,
                "discount_amount_oc": discount_amount,
                "discount_amount": discount_amount,
                "vat_rate": vat_rate,
                "vat_amount_oc": vat_amount,
                "vat_amount": vat_amount,
                "main_unit_price": price,
                "description": product['name'],
                "inventory_item_name": product['variant_name'],
                "exchange_rate_operator": "*",
                "inventory_item_code": product['sku'],
                "main_unit_name": "Cái",
                "unit_name": "Cái",
                "currency_id": "VND",
                "quantity_remain": quantity,
                "main_quantity_remain": 0.0,
                "status": 0,
                "discount_type": 1,
                "discount_rate_voucher": 0,
                "inventory_item_type": 0,
                "account_object_address": shipping_address,
                **account_object,
                **stock_object,
            }

            if vat_rate == -3:
                new_product['other_vat_rate'] = product.get('vat')
            return new_product

        created_at = format_date_from_iso8601_string(data['created_at'])
        updated_at = format_date_from_iso8601_string(data['updated_at'])
        posted_date = None
        status = map_status(data['status'])

        delivered_status = 2 if status == 2 else 0 if status == 0 else 1

        detail = []
        discount_type = 0

        total_quantity = sum(float(product['quantity']) for product in data['order_line_items'])
        redeem_money = float(data['redeem_money'])
        discount_per_unit = redeem_money / total_quantity
        remaining_discount = redeem_money

        for idx, product in enumerate(data['order_line_items']):
            if float(product.get('discount', 0)) > 0:
                discount_type = 1
            if redeem_money > 0:
                if idx != len(data['order_line_items']) - 1:
                    remaining_discount -= round(discount_per_unit * float(product['quantity']))
            detail.append(convert_product_detail(product, idx, data, remaining_discount))
        if discount_type == 0 and data['discount'] > 0:
            discount_type = 3
        elif discount_type == 1 and redeem_money > 0:
            discount_type = 4

        employee_name = data['staff']['name'] if data['staff'] is not None else ""

        # Create base MISA order
        misa_order = {
            "org_refid": data['external_id'],
            "voucher_type": 13,
            "org_reftype": 3530,
            "reftype": 3530,
            "refdate": updated_at,
            "branch_id": None,
            "delivered_status": delivered_status,
            "delivery_date": data.get('deliveryDate'),
            **account_object,
            "account_object_address": shipping_address,
            "shipping_address": shipping_address,
            "status": status,
            "exchange_rate": 1.0,
            "receiver": data['customer']['first_name'],
            "currency_id": "VND",
            "discount_type": discount_type,
            "created_date": created_at,
            "modified_date": updated_at,
            "posted_date": posted_date,
            "created_by": "OneXAPIs",
            "modified_by": "OneXAPIs",
            "employee_name": employee_name,
            "detail": detail,
            "total_amount": data['total'],
            "total_amount_oc": data['total'],
            "inv_date": updated_at,
            "is_paid": False,
            "is_posted": False,
            "invoice_status": 1,
            # "inv_link_code": data['external_id'],
            # "inv_link": f"https://app.nhanh.vn/order/view/id/{data['external_id']}",
            "inv_no": data['external_id'],
            "include_invoice": 1,
            "journal_memo": data['note'],
            "paid_type": 0,
            "is_created_invoice": 1,
            "sa_invoice": {
                "branch_id": None,
                **account_object,
                "account_object_address": shipping_address,
                "inv_date": updated_at,
                "inv_no": data['external_id'],
                "is_paid": False,
                "is_posted": True,
                "include_invoice": 1,
                "exchange_rate": 1.0,
                "employee_name": employee_name,
                # "payment_method": "/".join(methods),
                "payment_method": "TM/CK",
                "currency_id": "VND",
                "refno_finance": data['external_id'],
                "invoice_status": 1,
                "invoice_type": 0,
                "reftype": 3560
            },
            "is_sale_with_outward": True,
            "outward_exported_status": 1,
            "in_outward": {
                "branch_id": None,
                **account_object,
                "account_object_address": shipping_address,
                "in_reforder": updated_at,
                "refdate": updated_at,
                "posted_date": updated_at,
                "reftype": 2020,
                "reforder": data['external_id'],
                "refno_finance": data['external_id'],
            },
        }

        if data.get('order_type') == OrderType.POS.value:
            org_refno = f"{convert_to_kebab_case(data['source']['name'])}-{data['external_id']}"
            methods = []
            payments = data['payments'] or []
            for item in payments:
                methods.append(item['payment_method']['name'])

            misa_order = {
                **misa_order,
                "org_refno": org_refno,
                "refno_finance": org_refno,
                "refno": org_refno,
                "org_reftype_name": 'Đơn offline',
            }
        else:
            org_refno = data['external_id']
            org_reftype_name = f"Đơn {data['source']['name']}"
            note = f"{org_reftype_name}\n Ngày tạo đơn: {created_at}\n Trạng thái: {data['status']}"

            if status == 2:
                posted_date = format_date_from_iso8601_string(data['updated_at'])
                note = f"{org_reftype_name}\n Trạng thái: {data['status']}\n Ngày thành công: {posted_date}"

            misa_order = {
                **misa_order,
                "org_refno": org_refno,
                "refno_finance": org_refno,
                "refno": org_refno,
                "journal_memo": note,
                "org_reftype_name": org_reftype_name,
            }
        if data['status'] != Status.COMPLETED.value:
            misa_order['opti_published'] = False
        # Transform master format to MisaAmis order data
        seen_codes = set()
        dictionary = []
        if branch_id:
            for item in misa_order.get('detail', []):
                item_code = item.get('inventory_item_code')
                if item_code in seen_codes:
                    continue
                seen_codes.add(item_code)
                dictionary.append({
                    "dictionary_type": 3,
                    "inventory_item_name": item["inventory_item_name"],
                    "purchase_description": item["inventory_item_name"],
                    "sale_description": item["inventory_item_name"],
                    "inventory_item_code": item_code,
                    "inventory_item_id": str(uuid.uuid4()),
                    "inventory_item_type": 0,
                    "branch_id": branch_id,  ## thay branch_id để danh mục đc đẩy lên
                    "inactive": False
                })
        misa_order['dictionary'] = dictionary
        return misa_order

    @property
    def input_schema(self):
        return OrderAttributesSchema().dump(OrderAttributesSchema())

    @property
    def output_schema(self):
        # This should reflect the MisaAmis API order schema
        return {
            "type": "object",
            "properties": {
                "detail": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "sort_order": {"type": "integer"},
                            "is_promotion": {"type": "boolean"},
                            "quantity": {"type": "number"},
                            "unit_price": {"type": "number"},
                            "amount_oc": {"type": "number"},
                            "amount": {"type": "number"},
                            "discount_rate": {"type": "number"},
                            "main_convert_rate": {"type": "number"},
                            "main_quantity": {"type": "number"},
                            "discount_amount_oc": {"type": "number"},
                            "discount_amount": {"type": "number"},
                            "vat_rate": {"type": "number"},
                            "vat_amount_oc": {"type": "number"},
                            "vat_amount": {"type": "number"},
                            "main_unit_price": {"type": "number"},
                            "description": {"type": "string"},
                            "inventory_item_name": {"type": "string"},
                            "exchange_rate_operator": {"type": "string"},
                            "inventory_item_code": {"type": "string"},
                            "account_object_name": {"type": "string"},
                            "currency_id": {"type": "string"},
                            "quantity_remain": {"type": "number"},
                            "main_quantity_remain": {"type": "number"},
                            "status": {"type": "integer"},
                            "is_description": {"type": "boolean"},
                            "discount_type": {"type": "integer"},
                            "discount_rate_voucher": {"type": "number"},
                            "inventory_item_type": {"type": "integer"}
                        }
                    }
                },
                "refdate": {"type": "string"},
                "voucher_type": {"type": "integer"},
                "org_refid": {"type": "string"},
                "org_refno": {"type": "string"},
                "org_reftype": {"type": "integer"},
                "org_reftype_name": {"type": "string"},
                "branch_id": {"type": ["null", "string"]},
                "refno": {"type": "string"},
                "delivered_status": {"type": "integer"},
                "account_object_name": {"type": "string"},
                "account_object_address": {"type": "string"},
                "account_object_code": {"type": "string"},
                "account_object_tax_code": {"type": "string"},
                "receiver": {"type": "string"},
                "journal_memo": {"type": "string"},
                "currency_id": {"type": "string"},
                "employee_code": {"type": "string"},
                "employee_name": {"type": "string"},
                "reftype": {"type": "integer"},
                "created_date": {"type": "string"},
                "created_by": {"type": "string"},
                "modified_date": {"type": "string"},
                "modified_by": {"type": "string"}
            }
        }
