from dataclasses import dataclass

from marshmallow import fields, EXCLUDE, pre_load
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.text import get_slug_by_name
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.meta_data import MetaData, MetaDataSchema
from models.media.image import Image, ImageSchema


@dataclass
class BlogCategory(Attributes):
    id: str = None
    name: str = None
    image: Image = None
    parent_category_id: str = None
    has_children: bool = False
    meta_data: MetaData = None
    slug: str = None
    locale: str = None
    description: str = None
    content_group_id: str = None


class BlogCategorySchema(AttributesSchema):
    class Meta:
        unknown = EXCLUDE

    id = fields.Str(required=True)
    parent_category_id = fields.Str(allow_none=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    name = fields.Str(required=True)
    has_children = fields.Bool(allow_none=True)
    meta_data = fields.Nested(MetaDataSchema(MetaData), allow_none=True)
    slug = fields.Str(allow_none=True)
    locale = fields.Str(required=True)
    description = fields.Str(allow_none=True)
    content_group_id = fields.Str(required=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['slug'] = data['slug'] if 'slug' in data else get_slug_by_name(data['name'])
        data['content_group_id'] = data['content_group_id'] if 'content_group_id' in data else data['id']
        data['locale'] = data['locale'] if 'locale' in data else 'en'
        return data


@dataclass
class BlogCategoryAttributes(BlogCategory, BasicAttributes):
    pass


class BlogCategoryAttributesSchema(BlogCategorySchema, BasicAttributesSchema):
    pass


class BlogCategoryModel(BasicModel):
    table_name = 'blogCategory'
    attributes: BlogCategoryAttributes
    attributes_class = BlogCategoryAttributes
    attributes_schema = BlogCategoryAttributesSchema

    query_mapping = {
        'query': ['name', 'id', 'slug', 'description'],
        'filter': {
            'id': 'query',
            'slug': 'query',
            'name': 'query',
            'has_children': 'boolean',
            'parent_category_id': 'query',
            'locale': 'query',
            'content_group_id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range'
        },
        'mapping': {
            'image': 'image',
        }
    }


class BlogCategorySlugIndex(BlogCategoryModel):
    key__id = 'slug'
    index_name = 'blogCategorySlugIndex'

    @classmethod
    def get_blog_category_by_slug(cls, slug, company_id):
        try:
            return cls(
                BlogCategorySlugIndex.list({'slug': slug, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return


class ContentGroupIdIndex(BlogCategoryModel):
    key__id = 'content_group_id'
    index_name = 'contentGroupIdIndex'

    @classmethod
    def list_by_content_group_id(cls, content_group_id, company_id):
        try:
            blog_categories = cls.list({'content_group_id': content_group_id, 'company_id': company_id}, limit=200).get(
                'Items', [])
            return blog_categories
        except IndexError:
            return []

    @classmethod
    def get_by_locale(cls, content_group_id, company_id, locale):
        try:
            return cls(cls.list({'content_group_id': content_group_id, 'company_id': company_id, 'locale': locale},
                                limit=200).get(
                'Items', [])[0])
        except IndexError:
            return None
