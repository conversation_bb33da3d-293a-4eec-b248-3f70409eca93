{"create_conversation": {"POST": [{"name": "Create a new conversation", "body": {"name": "Order Support", "customer_name": "<PERSON>", "customer_phone_number": "+1234567890", "image": "https://example.com/image.jpg", "assignee_id": "user-123", "role": "VIRTUAL_STAFF"}, "response": {"success": true, "message": "Conversation created successfully", "data": {"id": "conversation-id-123", "name": "Order Support", "customer_id": "customer-123", "image": "https://example.com/image.jpg", "members": ["user-123", "customer-123"], "assignee_id": "user-123", "role": "VIRTUAL_STAFF", "created_at": "2024-01-01T00:00:00Z"}}}]}, "create_public_conversation": {"POST": [{"name": "Create a new conversation", "body": {"customer_name": "<PERSON>", "customer_phone_number": "+1234567890", "assignee_id": "user-123"}, "response": {"success": true, "message": "Conversation created successfully", "data": {"id": "conversation-id-123", "name": "Order Support", "customer_id": "customer-123", "image": "https://example.com/image.jpg", "members": ["user-123", "customer-123"], "assignee_id": "user-123", "role": "VIRTUAL_STAFF", "created_at": "2024-01-01T00:00:00Z"}}}]}, "get_conversation": {"GET": [{"name": "Get conversation by ID", "path_params": {"id": "conversation-id-123"}, "response": {"success": true, "message": "Conversation retrieved successfully", "data": {"id": "conversation-id-123", "name": "Order Support", "customer_id": "customer-123", "image": "https://example.com/image.jpg", "members": ["user-123", "customer-123"], "assignee_id": "user-123", "role": "VIRTUAL_STAFF", "created_at": "2024-01-01T00:00:00Z"}}}]}, "list_conversations": {"GET": [{"name": "List conversations with filters", "query_params": {"name": "Order Support", "assignee_id": "user-123", "role": "VIRTUAL_STAFF", "customer_id": "customer-123", "created_at_start": "2024-01-01T00:00:00Z", "created_at_end": "2024-12-31T23:59:59Z"}, "response": {"success": true, "message": "Conversations retrieved successfully", "data": {"items": [{"id": "conversation-id-123", "name": "Order Support", "customer_id": "customer-123", "image": "https://example.com/image.jpg", "members": ["user-123", "customer-123"], "assignee_id": "user-123", "role": "VIRTUAL_STAFF", "created_at": "2024-01-01T00:00:00Z"}], "total": 1}}}]}, "delete_conversation": {"DELETE": [{"name": "Delete conversation", "path_params": {"id": "conversation-id-123"}, "response": {"success": true, "message": "Conversation deleted successfully"}}]}}