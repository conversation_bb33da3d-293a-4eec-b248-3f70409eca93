import json
import os

import pendulum
from nolicore.utils.aws.boto_helper import get_client
from nolicore.utils.aws.decorator import invoke
from nolicore.utils.aws.request import LambdaRequest
from nolicore.utils.exceptions import NotFoundRequest, BadRequest, ServerExp
from nolicore.utils.utils import logger

from integrations.channels.action_types import ActionKind
from integrations.common.base_api_library import Sync<PERSON><PERSON><PERSON>ponse, SyncAPIResponseList
from integrations.common.bucket_helper import extract_file_id, get_transformed_data
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.queue_helper import enqueue_publish_message
from models.basic import BasicAttributes
from models.integration.sync_record import SyncRecordModel, SyncRecordStatus, MappingStatus

s3 = get_client('s3')
sqs = get_client('sqs')

TRANSFORMED_DATA_BUCKET = os.environ['TRANSFORMED_DATA_BUCKET']
MAX_RETRIES = 3


def process_single_publish(message):
    connection_id = message['connection_id']
    destination_id = message['destination_id']
    source_key = message['source_key']
    destination_key = message['destination_key']
    destination_version_id = message['destination_version_id']
    source_version_id = message['source_version_id']
    retry_count = message.get('retry_count', 0)
    object_type = message['object_type']
    fetch_event_id = message['fetch_event_id']
    sync_record_obj = SyncRecordModel.by_key({
        'id': source_key,
        'connection_id': destination_id
    })
    if sync_record_obj is None:
        raise NotFoundRequest('Sync record not found')
    sync_record_obj.update({
        'published_at': pendulum.now().to_iso8601_string(),
        'status': SyncRecordStatus.PUBLISHED.value,
    })
    try:
        destination_connection = get_connection_by_id(destination_id)
        transformed_data = get_transformed_data(TRANSFORMED_DATA_BUCKET, destination_key, destination_version_id)
        channel = destination_connection.get_channel()
        action_type = destination_connection.get_publish_action_type(object_type)
        publish_settings = destination_connection.get_publish_action_settings(object_type, action_type)
        transformed_data['remote_record_id'] = SyncRecordModel.get_record(source_key, destination_id).attributes.remote_record_id
        result: SyncAPIResponse = channel.execute_action(action_type, ActionKind.PUBLISH, publish_settings, publish_data=transformed_data)
        result.update_sync_record(sync_record_obj)
        logger.info(f"Successfully published data for connection {connection_id}, destination {destination_id}")
    except ServerExp as e:
        handle_publish_error(
            connection_id,
            destination_id,
            source_key,
            str(source_version_id),
            destination_key,
            str(e),
            destination_version_id,
            retry_count,
            object_type,
            sync_record_obj,
            fetch_event_id
        )
        logger.exception(
            f"Retry publishing data for connection {connection_id}, destination {destination_id}: {str(e)}")
    except Exception as e:
        logger.exception(
            f"Error publishing data for connection {connection_id}, destination {destination_id}: {str(e)}")


@invoke()
def publish_lambda_handler(lambda_request: LambdaRequest):
    records = lambda_request.event['Records']
    grouped: dict[tuple[str, str, str], dict[str, dict]] = {}
    for record in records:
        message = json.loads(record['body'])
        connection_id = message['connection_id']
        object_type = message['object_type']
        destination_id = message['destination_id']
        file_id = extract_file_id(message['source_key'])
        destination_connection = get_connection_by_id(destination_id)
        action_type = destination_connection.get_publish_action_type(object_type)
        group_key = (connection_id, destination_id, action_type)
        sent_timestamp = int(record.get('attributes', {}).get('SentTimestamp', 0))
        group = grouped.setdefault(group_key, {})
        # Always keep the message with the latest sent_timestamp
        if file_id not in group or sent_timestamp > group[file_id]['sent_timestamp']:
            group[file_id] = {'message': message, 'sent_timestamp': sent_timestamp}

    for group_key, file_id_to_message in grouped.items():
        messages = [item['message'] for item in file_id_to_message.values()]
        # Get channel from first connection in group
        connection_id, destination_id, action_type = group_key
        destination_connection = get_connection_by_id(destination_id)
        channel = destination_connection.get_channel()
        # If channel supports batch
        if channel.has_batch_publish_action(action_type):
            # Prepare batch data
            publish_settings = destination_connection.get_publish_action_settings(messages[0]['object_type'], action_type)
            batch_data = []
            sync_record_objs = []
            for message in messages:
                source_key = message['source_key']
                destination_key = message['destination_key']
                destination_version_id = message['destination_version_id']
                sync_record_obj = SyncRecordModel.by_key({
                    'id': source_key,
                    'connection_id': destination_id
                })
                if sync_record_obj is not None:
                    sync_record_objs.append(sync_record_obj)
                    transformed_data = get_transformed_data(TRANSFORMED_DATA_BUCKET, destination_key,
                                                            destination_version_id)
                    transformed_data['remote_record_id'] = sync_record_obj.attributes.remote_record_id
                    batch_data.append(transformed_data)
            try:
                updated_data = {
                    'published_at': pendulum.now().to_iso8601_string(),
                    'status': SyncRecordStatus.PUBLISHED.value,
                }
                for i in range(0, len(sync_record_objs), 25):
                    items = [BasicAttributes.add_basic_attributes({**item.attributes_dict, **updated_data}, item.attributes_dict.get('company_id')) for item in sync_record_objs[i:i+25]]
                    SyncRecordModel.batch_add(items)
                if not batch_data:
                    logger.error(f"No data to publish for connection {connection_id}, action_type {action_type}")
                    continue
                results: SyncAPIResponseList = channel.execute_action(action_type, ActionKind.BATCH_PUBLISH,
                                                                        publish_settings, publish_data=batch_data)
                results.update_sync_records(sync_record_objs)
            except BadRequest as e:
                logger.exception(f"Error in batch publish for connection {connection_id}, action_type {action_type}: {str(e)}")
                # fallback: process each record as before if batch fails
                for message in messages:
                    process_single_publish(message)
        else:
            # Does not support batch, process each record as before
            for message in messages:
                process_single_publish(message)


def handle_publish_error(connection_id, destination_id, source_key, source_version_id, destination_key,
                         error_message, destination_version_id, retry_count, object_type,
                         sync_record_obj: SyncRecordModel, fetch_event_id):
    sync_record_obj.update({
        'response': {
            'status': 'failed',
            'error_message': error_message
        },
        'status': SyncRecordStatus.ERROR.value,
        'mapping_status': MappingStatus.ERROR.value,
        'response_message': f"Publish failed: {error_message}",
        'finished_at': pendulum.now().to_iso8601_string()
    })

    # You might want to implement retry logic here
    if retry_count < MAX_RETRIES:
        enqueue_publish_message(connection_id, destination_id, source_key, destination_key,
                                destination_version_id, source_version_id, object_type, fetch_event_id,
                                retry_count + 1)
    else:
        logger.error(f"Max retries reached for publishing {destination_key}")

