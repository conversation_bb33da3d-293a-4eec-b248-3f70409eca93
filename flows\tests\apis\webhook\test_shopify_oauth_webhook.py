import unittest

from tests.base import BaseTestCase
from flows.src.apis.webhook import webhook_handler_by_connection_id
from flows.tests.apis.webhook.data_test import shopify_oauth_webhook_event


class TestShopifyWebhook(BaseTestCase):
    def setUp(self):
        super().setUp()

    def test_webhook_handler_by_connection_id(self):
        webhook_handler_by_connection_id(shopify_oauth_webhook_event, {})

if __name__ == '__main__':
    unittest.main()