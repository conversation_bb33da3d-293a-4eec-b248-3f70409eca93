from dataclasses import dataclass, field

from marshmallow import fields
from ..connection_base import OAuth2ConnectionAttributes, OAuth2ConnectionSchema, OAuth2Connection, \
    OAuth2SettingsSchema, OAuth2Settings


class ZaloOASettingsSchema(OAuth2SettingsSchema):
    app_id = fields.Str(required=True)
    secret_key = fields.Str(required=True)
    oa_secret_key = fields.Str(required=True)
    code_challenge = fields.Str(required=False, allow_none=True)


@dataclass
class ZaloOASettings(OAuth2Settings):
    app_id: str = None
    secret_key: str = None
    oa_secret_key: str = None
    code_challenge: str = None


@dataclass
class ZaloOAConnectionAttributes(OAuth2ConnectionAttributes):
    channel_name: str = field(default='zalo_oa')
    settings: ZaloOASettings = field(default_factory=ZaloOASettings)


class ZaloOAConnectionSchema(OAuth2ConnectionSchema):
    channel_name = fields.Str(required=True)
    settings = fields.Nested(ZaloOASettingsSchema(ZaloOASettings))


class ZaloOAConnection(OAuth2Connection):
    attributes: ZaloOAConnectionAttributes = None
    attributes_schema = ZaloOAConnectionSchema
    attributes_class = ZaloOAConnectionAttributes
