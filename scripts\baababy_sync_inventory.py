import time

from nolicore.utils.utils import decompress

from integrations.channels.haravan.haravan_library import Haravan<PERSON><PERSON>rary
from integrations.channels.nhanh.nhanh_library import Nhanh<PERSON>ibrary
from scripts.load_env_test import load_env_test
load_env_test("prod")

from integrations.common.connection_helper import get_connection_by_id
from integrations.common.transformers import registry

nhanh_library = NhanhLibrary(
    app_id="74307",
    business_id="58863",
    secret_key="GzqIl8r6iiEcvd35QEgodW3huwvkagwehORW2lDLIBBVLi8OO76K9MH40eLzzLa7pr9I8KTYxN7dNnfD4WqCy5yFInw5GHHbEa7xH6EuPlstlpX9YJ5F6cTH9RV92k3D",
    auth_url="https://nhanh.vn/oauth",
    base_url="https://open.nhanh.vn",
    access_token="pAFAI4RemQPMlCHLAMj75oUOMrMyRQpEEMy4y6wq6Qh2hPLGuAlGwQThViHTIFOlNbcrvuDm2WnUKrhgrAMNfrjJEAhViVEwIH0c3gX8n8IbNl2Wk2BizZlUiQiJglyx33FXmBkU0Fqu"
)
haravan_library = HaravanLibrary(
    client_id="06c110f8a1ca7206a16c87acbcf4eb47",
    org_id="200000867385",
    client_secret="09d491296e9743a5c9e256bfcc7a999b71a8ac29637f5a1c00619c09d16c8f94",
    access_token="8051C6B518A51A5CC9FC387CC5F85052D6C56E95C8D128A43C80C28B986F663F"
)
continuation_token = None
while True:
    line_items = []
    standard_product = nhanh_library.get_products({}, None, next_page=continuation_token)
    product = standard_product.data[0].raw_data
    for variant_id, variant in product.items():
        if variant['parentId'] in [-1, -2, None, ""]:
            continue
        if variant.get('inventory', {}):
            depots = variant.get('inventory', {}).get('depots', {})
            if isinstance(depots, dict):
                default_depot = variant.get('inventory', {}).get('depots', {}).get("78673")
            elif isinstance(depots, list):
                default_depot = next((d for d in depots if d.get("78673")), None)
            else:
                continue
            if default_depot is None:
                continue
            available = default_depot.get('available', 0)
            line_item = {"sku": variant['code'], "quantity": available if available > 0 else 0}
            line_items.append(line_item)
    if line_items:
        # Chia line_items thành các nhóm, mỗi nhóm tối đa 100 dòng
        chunk_size = 100
        result = []
        for i in range(0, len(line_items), chunk_size):
            chunk = line_items[i:i + chunk_size]
            haravan_inventory = {
                "inventory": {
                    "type": "set",
                    "reason": "productionofgoods",
                    "note": "update from onexapis",
                    "line_items": chunk
                }
            }
            time.sleep(1)
            result.append(haravan_library.sync_inventory(haravan_inventory))
        print(f"sync complete for {standard_product.meta.page}/{standard_product.meta.total_page}")
    else:
        print(f"no line items for {standard_product.meta.page}/{standard_product.meta.total_page}")
    continuation_token = standard_product.meta.continuation_token
    if continuation_token is None:
        break
    # response = self.nhanh_library.restock_baababy(file)
    # self.assertTrue(response.success)