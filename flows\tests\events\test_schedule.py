import json
import unittest

from tests.base import BaseTestCase
from flows.src.events.step_1_schedule import schedule_handler


class TestScheduleLambdaHandler(BaseTestCase):

    def test_schedule_lambda_handler(self):
        result = schedule_handler({}, {})
        self.assertEqual(result['statusCode'], 200)
        self.assertEqual(json.loads(result['body']), 'Fetch Lambda execution completed')

        # Add more specific assertions here based on expected behavior
        # For example, you might want to check if the product was properly processed and stored

        # Add more specific assertions here based on expected behavior
        # For example, you might want to check if the order was properly processed and stored


if __name__ == '__main__':
    unittest.main()
