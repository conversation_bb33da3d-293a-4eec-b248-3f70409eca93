import json
from datetime import datetime
from typing import Dict, Any, Union

import aiohttp

from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, SyncAPIResponse,
    MetaData, PublishMetaData, StandardOrder, StandardProduct, StandardReturnOrder, StandardPurchaseOrder
)
from integrations.common.event import FetchEvent


class BusinessCentralEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.ORDERS = EndpointType(type="rest", method="GET", path=f"{self.base_url}/salesOrders")
        self.PRODUCTS = EndpointType(type="rest", method="GET", path=f"{self.base_url}/items")
        self.SYNC_ORDER = EndpointType(type="rest", method="POST", path=f"{self.base_url}/salesOrders")
        self.SYNC_PRODUCT = EndpointType(type="rest", method="POST", path=f"{self.base_url}/items")
        self.ORDER_DETAIL = EndpointType(type="rest", method="GET", path=f"{self.base_url}/salesOrders")
        self.PRODUCT_DETAIL = EndpointType(type="rest", method="GET", path=f"{self.base_url}/items")
        self.CREATE_WEBHOOK = EndpointType(type="rest", method="POST", path=f"{self.base_url}/webhookSubscriptions")


class BusinessCentralLibrary(BaseAPILibrary):

    AVAILABLE_WEBHOOKS = {
        "sales_order_created": "/business_central/order/created",
        "sales_order_updated": "/business_central/order/updated",
        "item_created": "/business_central/product/created",
        "item_updated": "/business_central/product/updated",
        "inventory_updated": "/business_central/inventory/updated",
    }

    def __init__(self, tenant_id: str, environment: str, company_id: str, access_token: str, api_version: str = "v2.0"):
        base_url = f"https://api.businesscentral.dynamics.com/{api_version}/{tenant_id}/{environment}/api/v2.0/companies({company_id})"
        super().__init__(base_url)
        self.access_token = access_token
        self.endpoints = BusinessCentralEndpoints(self.endpoints.base_url)

    async def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None) -> \
            Dict[str, Any]:
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        async with aiohttp.ClientSession() as session:
            if endpoint.method == "GET":
                async with session.get(endpoint.path, headers=headers, params=params) as response:
                    response.raise_for_status()
                    return await response.json()
            elif endpoint.method in ["POST", "PUT"]:
                async with session.request(endpoint.method, endpoint.path, headers=headers, params=params,
                                           json=data) as response:
                    response.raise_for_status()
                    return await response.json()

    async def test_connection(self) -> bool:
        try:
            # Use a simple API call to test the connection
            await self._make_request(self.endpoints.PRODUCTS, params={"$top": 1})
            return True
        except Exception:
            return False

    async def get_orders(self, params: Dict[str, Any] = None, fetch_event_id: str = None) -> FetchAPIResponse[
        StandardOrder]:
        default_params = {
            "$top": 50,
            "$filter": "",
            "$orderby": "number desc"
        }
        params = {**default_params, **(params or {})}

        response = await self._make_request(self.endpoints.ORDERS, params=params)

        orders = [
            self.standardize_order(order, fetch_event_id)
            for order in response.get("value", [])
        ]

        return FetchAPIResponse(
            success=True,
            data=orders,
            meta=MetaData(
                total_count=len(orders),
                page=1,
                limit=params["$top"]
            )
        )

    async def get_products(self, params: Dict[str, Any] = None, fetch_event_id: str = None) -> FetchAPIResponse[
        StandardProduct]:
        default_params = {
            "$top": 50,
            "$filter": "",
            "$orderby": "number asc"
        }
        params = {**default_params, **(params or {})}

        response = await self._make_request(self.endpoints.PRODUCTS, params=params)

        products = [
            self.standardize_product(product, fetch_event_id)
            for product in response.get("value", [])
        ]

        return FetchAPIResponse(
            success=True,
            data=products,
            meta=MetaData(
                total_count=len(products),
                page=1,
                limit=params["$top"]
            )
        )

    async def sync_order(self, order_data: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        bc_order_data = self._transform_order_data(order_data)

        try:
            response = await self._make_request(self.endpoints.SYNC_ORDER, data=bc_order_data)
            synced_order = self.standardize_order(response)
            return SyncAPIResponse(
                success=True,
                data=synced_order,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Order {synced_order.order_number} synced successfully"
            )
        except Exception as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync order: {str(e)}"
            )

    async def sync_product(self, product_data: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        bc_product_data = self._transform_product_data(product_data)

        try:
            response = await self._make_request(self.endpoints.SYNC_PRODUCT, data=bc_product_data)
            synced_product = self.standardize_product(response)
            return SyncAPIResponse(
                success=True,
                data=synced_product,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Product {synced_product.title} synced successfully"
            )
        except Exception as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync product: {str(e)}"
            )

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        return StandardOrder(
            id=str(raw_order["id"]),
            fetch_event_id=fetch_event_id,
            order_number=raw_order["number"],
            total_price=float(raw_order["totalAmountIncludingTax"]),
            currency=raw_order["currencyCode"],
            status=raw_order.get("status", ""),
            created_at=datetime.fromisoformat(raw_order["orderDate"].rstrip('Z')),
            raw_data=raw_order
        )

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        return StandardProduct(
            id=str(raw_product["id"]),
            fetch_event_id=fetch_event_id,
            title=raw_product["displayName"],
            price=float(raw_product["unitPrice"]),
            currency=raw_product.get("priceIncludesTax", ""),
            inventory_quantity=int(raw_product.get("inventory", 0)),
            status=raw_product.get("blocked", False),
            created_at=datetime.now(),  # Business Central doesn't provide creation date for items
            raw_data=raw_product
        )

    async def process_webhook(self, event_type: str, payload: Dict[str, Any]) -> SyncAPIResponse[
        Union[StandardOrder, StandardProduct, Dict[str, Any]]]:
        event_processors = {
            'sale_order_created': self._process_order_created,
            'sale_order_updated': self._process_order_updated,
            'item_created': self._process_product_created,
            'item_updated': self._process_product_updated,
        }

        processor = event_processors.get(event_type)
        if not processor:
            raise ValueError(f"Unsupported event type: {event_type}")

        return await processor(payload)

    async def _process_order_created(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        order = self.standardize_order(payload)

        return SyncAPIResponse(
            success=True,
            data=order,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed new order: {order.order_number}"
        )

    async def _process_order_updated(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        order = self.standardize_order(payload)
        return SyncAPIResponse(
            success=True,
            data=order,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed updated order: {order.order_number}"
        )

    async def _process_product_created(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        product = self.standardize_product(payload)
        return SyncAPIResponse(
            success=True,
            data=product,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed new product: {product.title}"
        )

    async def _process_product_updated(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        product = self.standardize_product(payload)
        return SyncAPIResponse(
            success=True,
            data=product,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed updated product: {product.title}"
        )

    def _transform_order_data(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "customerNumber": order_data.get("customer_number"),
            "orderDate": order_data.get("order_date"),
            "currencyCode": order_data.get("currency_code"),
            "lines": [
                {
                    "itemNumber": line["item_number"],
                    "quantity": line["quantity"],
                    "unitPrice": line["unit_price"]
                }
                for line in order_data.get("lines", [])
            ]
        }

    def _transform_product_data(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "number": product_data.get("number"),
            "displayName": product_data.get("display_name"),
            "type": "Inventory",
            "unitPrice": product_data.get("unit_price"),
            "inventory": product_data.get("inventory", 0)
        }

    @staticmethod
    def verify_webhook_signature(payload: str, signature: str, secret: str) -> bool:
        # Business Central uses a different webhook verification method
        # This is a placeholder - you'll need to implement the actual verification logic
        # based on Business Central's documentation
        return True  # Replace with actual verification logic

    async def handle_webhook(self, event_type: str, payload: str, signature: str) -> SyncAPIResponse[
        Union[StandardOrder, StandardProduct, Dict[str, Any]]]:
        if not self.verify_webhook_signature(payload, signature, "your_secret_here"):
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid webhook signature"
            )

        try:
            payload_dict = json.loads(payload)
            return await self.process_webhook(event_type, payload_dict)
        except json.JSONDecodeError:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid JSON payload"
            )
        except ValueError as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=str(e)
            )

    async def setup_webhooks(self, connection):
        webhooks_to_create = [
            {"topic": topic, "address": connection.get_webhook_url(self.AVAILABLE_WEBHOOKS[topic])}
            for topic in self.AVAILABLE_WEBHOOKS
            if connection.attributes.webhook_settings.get(topic, False)
        ]

        results = []
        for webhook in webhooks_to_create:
            result = await self._create_webhook(webhook)
            results.append((webhook['topic'], result['success']))

        for topic, success in results:
            if success:
                print(f"Successfully created webhook: {topic}")
            else:
                print(f"Failed to create webhook: {topic}")

    async def _create_webhook(self, webhook_data: Dict[str, str]) -> Dict[str, Any]:
        try:
            payload = {
                "notificationUrl": webhook_data['address'],
                "eventType": webhook_data['topic'],
            }
            response = await self._make_request(
                self.endpoints.CREATE_WEBHOOK,
                data=payload
            )
            return {
                "success": True,
                "data": response,
                "message": f"Webhook {webhook_data['topic']} created successfully"
            }
        except Exception as e:
            return {
                "success": False,
                "data": None,
                "message": f"Error creating webhook {webhook_data['topic']}: {str(e)}"
            }

    @staticmethod
    def extract_merchant_id(payload):
        return payload.get('company_id')

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return payload.get('eventType') or headers.get('BC-Event-Type')