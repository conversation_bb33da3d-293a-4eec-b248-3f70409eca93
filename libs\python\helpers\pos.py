import uuid
from decimal import Decimal

import pendulum
from elasticsearch import NotFoundError
from nolicore.utils.exceptions import BadRequest

from helpers.errors import AmountInvalid
from helpers.utils import RESOURCE_SERVICE
from models.finance.account import Type
from models.order.order import OrderModel
from models.pos.shift import ShiftModel
from models.pos.terminal import TerminalModel


def list_opened_shift(user_id):
    search_params = {
        "query": {
            "bool": {
                "must": [
                    {"term": {"user.id.keyword": user_id}},
                    {"bool": {
                        "should": [
                            {"bool": {"must_not": {"exists": {"field": "close_time"}}}}
                        ],
                        "minimum_should_match": 1
                    }
                    }
                ]
            }
        }
    }
    try:
        response = ShiftModel.report(search_params, service=RESOURCE_SERVICE)
        list_item = response.body['hits']['hits']
        list_obj = [item['_source'] for item in list_item]
        return list_obj
    except NotFoundError:
        return []


def validate_shift(shift_id, company_id):
    key = {
        ShiftModel.key__id: shift_id,
        'company_id': company_id
    }
    shift_obj = ShiftModel.by_key(key)
    if shift_obj is None:
        raise BadRequest("Shift not found!")
    if shift_obj.attributes_dict.get('is_closed'):
        raise BadRequest("Shift is closed. Can not modify!")
    return shift_obj


def create_shift_pay_line_item(list_obj):
    new_list = []
    current_time = pendulum.now().to_iso8601_string()
    for item in list_obj:
        if 'id' not in item:
            item['id'] = str(uuid.uuid4())
        if 'created_at' not in item:
            item['created_at'] = current_time
        new_list.append(item)
    return new_list


def update_shift_paid(shift_item, shift_id, company_id, user, is_delete=False):
    key = {
        ShiftModel.key__id: shift_id,
        'company_id': company_id
    }
    shift_obj = ShiftModel.by_key(key)
    if shift_obj is None:
        raise BadRequest("Shift not found!")
    if shift_obj.attributes_dict.get('is_closed'):
        raise BadRequest("Shift is closed. Can not modify!")

    paid_in = shift_obj.attributes.paid_in
    paid_out = shift_obj.attributes.paid_out
    expected_fund = shift_obj.attributes.expected_fund
    item_type = shift_item.get("type")
    item_amount = Decimal(shift_item.get("amount", 0))
    if item_type == 'PAID_IN':
        if is_delete:
            paid_in -= item_amount
            expected_fund -= item_amount
        else:
            paid_in += item_amount
            expected_fund += item_amount
    elif item_type == 'PAID_OUT':
        if is_delete:
            paid_out -= item_amount
            expected_fund += paid_out
        else:
            paid_out += item_amount
            expected_fund -= paid_out
    shift_obj.update({'paid_in': paid_in, 'paid_out': paid_out, 'expected_fund': expected_fund}, user)
    return shift_obj.attributes_dict


def create_order_update_shift(order: OrderModel, shift_id, company_id, user):
    key = {
        ShiftModel.key__id: shift_id,
        'company_id': company_id
    }
    shift_obj = ShiftModel.by_key(key)
    if shift_obj is None:
        raise BadRequest("Shift not found!")
    if shift_obj.attributes_dict.get('is_closed'):
        raise BadRequest("Shift is closed. Can not modify!")

    expected_cash = shift_obj.attributes.expected_cash
    gross_sale = shift_obj.attributes.gross_sale
    net_sales = shift_obj.attributes.net_sales
    banking = shift_obj.attributes.banking
    cash = shift_obj.attributes.cash

    order_sub_total = order.attributes.sub_total
    order_total = order.attributes.total
    order_discount = order.attributes.discount
    order_payments = order.attributes.payments
    order_cash = sum([item.amount for item in order_payments if item.payment_method.account.type == Type.CASH])
    order_banking = sum([item.amount for item in order_payments if item.payment_method.account.type == Type.BANK])

    expected_cash = expected_cash + order_cash
    gross_sale = gross_sale + order_sub_total
    net_sales = net_sales + order_total
    banking += order_banking
    cash += order_cash
    shift_obj.update(
        {'discounts': order_discount, 'cash': cash, 'banking': banking, 'expected_cash': expected_cash,
         'gross_sale': gross_sale, 'net_sales': net_sales},
        user)
    return shift_obj.attributes_dict


def update_terminal_status(terminal_id, company_id, status):
    terminal_obj = TerminalModel.by_key({
        TerminalModel.key__id: terminal_id,
        'company_id': company_id
    })
    terminal_obj.update({"is_activate": status})


def validate_shift_pay_line(shift_pay_line_item):
    amount = shift_pay_line_item.get("amount", 0)
    note = shift_pay_line_item.get("note", "")
    if amount == 0:
        raise AmountInvalid()
    if not note:
        raise BadRequest("Note should not be empty!")
