from decimal import Decimal
from typing import List

import pendulum
from nolicore.utils.exceptions import NotFoundRequest, BadRequest

from models.basic import BasicAttributes
from models.finance.account import AccountModel, Account, AccountSchema
from models.finance.payment_method import PaymentMethodModel, PaymentMethodSchema, PaymentMethod
from models.finance.transaction import TransactionModel, Transaction
from models.finance.voucher import Type, ReservedVoucher, TransactionVoucherModel, TransactionVoucherSchema, \
    TransactionVoucher


def validate(company_id, voucher_id, from_account, to_account, by_payment_method):
    if from_account:
        from_account_obj = AccountModel.by_key({
            'id': str(from_account),
            'company_id': company_id
        })
        if from_account_obj is None:
            raise NotFoundRequest('Account could not found')
    else:
        from_account_obj = None

    if to_account:
        to_account_obj = AccountModel.by_key({
            'id': str(to_account),
            'company_id': company_id
        })
        if to_account_obj is None:
            raise NotFoundRequest('Account could not found')
    else:
        to_account_obj = None

    payment_method_obj = PaymentMethodModel.by_key({
        'id': str(by_payment_method),
        'company_id': company_id
    })
    if payment_method_obj is None:
        raise NotFoundRequest('Payment method could not found')

    voucher_obj = TransactionVoucherModel.get_transaction_voucher(str(voucher_id), company_id)
    if voucher_obj is None:
        raise NotFoundRequest('Voucher could not found')

    return from_account_obj, to_account_obj, payment_method_obj, voucher_obj


def need_update_source_balance(voucher_obj: TransactionVoucherModel, from_account: AccountModel):
    if from_account is None:
        return False

    if voucher_obj.attributes.id in [ReservedVoucher.ORDER_PAYMENT_VOUCHER.value]:
        return False
    return True


def need_update_destination_balance(voucher_obj: TransactionVoucherModel, to_account: AccountModel):
    if to_account is None:
        return False

    if voucher_obj.attributes.id in [ReservedVoucher.PURCHASE_ORDER_VOUCHER.value,
                                     ReservedVoucher.RETURN_ORDER_VOUCHER.value]:
        return False

    return True


def add_transaction(amount, from_account, to_account, voucher_id, company_id, by_payment_method, user,
                    reference=None, effective_date=None, note=None):
    from_account_obj, to_account_obj, payment_method_obj, voucher_obj = validate(company_id, voucher_id, from_account,
                                                                                 to_account, by_payment_method)
    transaction = BasicAttributes.add_basic_attributes({
        'amount': Decimal(str(amount)),
        'payment_method': PaymentMethodSchema(PaymentMethod).dump(payment_method_obj.attributes),
        'voucher': TransactionVoucherSchema(TransactionVoucher).dump(voucher_obj.attributes),
        'user': user,
        'effective_date': effective_date or pendulum.now().to_iso8601_string(),
        'note': note,
        'reference': str(reference),
        'from_account': AccountSchema(Account).dump(from_account_obj.attributes) if from_account else None,
        'to_account': AccountSchema(Account).dump(to_account_obj.attributes) if to_account else None
    }, company_id)

    db_transactions = [TransactionModel(transaction).save(prepared_request=True)]
    if need_update_source_balance(voucher_obj, from_account_obj):
        db_transactions.append(
            from_account_obj.update(
                {
                    'balance': from_account_obj.attributes.balance - Decimal(str(amount)),
                    'last_transaction': transaction['id']
                }, prepared_request=True, replace=False
            )
        )
    if need_update_destination_balance(voucher_obj, to_account_obj):
        db_transactions.append(
            to_account_obj.update(
                {
                    'balance': to_account_obj.attributes.balance + Decimal(str(amount)),
                    'last_transaction': transaction['id']
                }, prepared_request=True, replace=False
            )
        )

    AccountModel.transact_write_items(db_transactions)
    return transaction


def sync_payment(company_id, user, total, current_payments: List[Transaction], payments: List[dict],
                 voucher_id, reference=None,
                 note=None, effective_date=None, is_destination_account=False):
    current_payments_dict = {
        f'{payment.id}-{float(payment.amount)}': payment for payment in current_payments
    } if current_payments else {}
    payments_dict = {
        f'{payment["payment_method_id"]}-{float(payment["amount"])}': payment for payment in payments
    }

    payments_to_add = payments_dict.keys() - current_payments_dict.keys()

    # validate amount
    paid_amount = sum([Decimal(str(payment.amount)) for payment in current_payments]) if current_payments else 0
    to_add_amount = sum([Decimal(str(payments_dict[payment]['amount'])) for payment in payments_to_add])
    if paid_amount + to_add_amount > total:
        raise BadRequest('Invalid amount.')

    transactions = []
    for payment_key in payments_to_add:
        _payment: dict = payments_dict[payment_key]
        payment_method_id = _payment['payment_method_id']
        amount = _payment['amount']

        payment_method_obj = PaymentMethodModel.by_key({
            'id': str(payment_method_id),
            'company_id': str(company_id)
        })

        if is_destination_account:
            from_account = None
            destination_account = payment_method_obj.attributes.account.id
        else:
            from_account = payment_method_obj.attributes.account.id
            destination_account = None
        by_payment_method = payment_method_id
        transactions.append(
            add_transaction(amount, from_account, destination_account, voucher_id, company_id, by_payment_method,
                            user,
                            reference, effective_date, note))
    return transactions


def revert_transaction(transaction: Transaction):
    return
