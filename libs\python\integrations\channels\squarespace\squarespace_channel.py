from typing import Dict, Any, <PERSON><PERSON>, Optional, List

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import AbstractChannel
from integrations.channels.connection_types import ChannelType
from integrations.channels.squarespace.squarespace_connection import SquarespaceSettings
from integrations.channels.squarespace.squarespace_library import Square<PERSON>Library
from integrations.common.event import FetchEvent


class SquarespaceChannel(AbstractChannel):
    channel_name = "squarespace"
    library_class = SquarespaceLibrary

    def _create_library(self, connection):
        settings: SquarespaceSettings = connection.attributes.settings
        return SquarespaceLibrary(
            api_key=settings.api_key,
            base_url=settings.base_url
        )

    def test_connection(self) -> bool:
        return self.library.test_connection()

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            "name": "Squarespace",
            "channel_type": ChannelType.ECOMMERCE,
            "description": "Connect your Squarespace store to sync products and other data.",
            "logo": cls._load_image_as_base64(__file__, "squarespace.png")
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_product:
            return {
                "cursor": None,
                "limit": 50,
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_product:
            return {}
        else:
            return {}

    def get_product__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        merged_settings = {**settings, **fetch_event.to_dict()}

        if fetch_event.object_id:
            return self.library.get_product(product_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        else:
            return self.library.get_products(params=merged_settings, fetch_event_id=fetch_event.id)

    def sync_product__publish(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        return self.library.sync_product(product_data)

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        return self.library.extract_object_details(payload, headers, event_type)

    def get_dynamic_settings(self) -> Dict[str, Any]:
        stored_settings = super().get_dynamic_settings()
        stored_settings['store_page_id'] = stored_settings.get('store_page_id', None)
        return stored_settings
