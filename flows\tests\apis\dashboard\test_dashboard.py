import json
import unittest
from dotenv import load_dotenv

from tests.base import BaseTestCase
from flows.src.apis.dashboard import connection_summary, fetch_event_summary, sync_record_summary


class DashboardTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.period = 'custom'
        cls.date_from = '2024-01-01'
        cls.date_to = '2025-01-01'
        if not cls.period:
            raise ValueError("Invalid period")
        if cls.period == 'custom':
            if not all([cls.period, cls.date_from, cls.date_to]):
                raise ValueError("period/date_from/date_to not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        self.company_id = "4c8e6154-fba3-4837-909b-8a50dff16a2b"
        self.user_id = "56c8ca59-2214-4d3e-95f8-0fa3178d0031"
        self.username = "baababy_admin"

    def create_lambda_event(self, body):
        return {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_connection_summary_success(self):
        # Arrange
        event_body = {}
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = connection_summary(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertIn('num_connections', response_body)
        self.assertIn('num_active', response_body)
        self.assertIn('num_inactive', response_body)
        self.assertIn('num_pending', response_body)

    def test_fetch_event_summary_success(self):
        # Arrange
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to}
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = fetch_event_summary(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertIn('num_fetch_events', response_body)
        self.assertIn('period', response_body)
        self.assertIn('status_counts', response_body)
        self.assertIn('event_source_count', response_body)

        status_counts = response_body['status_counts']
        event_source_count = response_body['event_source_count']
        self.assertIn('num_pending', status_counts)
        self.assertIn('num_processing', status_counts)
        self.assertIn('num_completed', status_counts)
        self.assertIn('num_failed', status_counts)

        self.assertIn('num_webhook', event_source_count)
        self.assertIn('num_scheduler', event_source_count)
        self.assertIn('num_sync_record_api', event_source_count)
        self.assertIn('num_sync_filter_api', event_source_count)

    def test_sync_record_summary_success(self):
        # Arrange
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to}
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = sync_record_summary(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertIn('period', response_body)
        self.assertIn('by_connection', response_body)


if __name__ == '__main__':
    unittest.main()
