from nolicore.utils.exceptions import BadRequest
from typing import List, Any

from helpers.transformation.factory import TransformationHandlerFactory
from helpers.transformation.transformation import Transformation, TransformationType


def _apply_transformation(value: Any, transformation: Transformation) -> Any:
    """Apply a single transformation to a value"""
    handler = TransformationHandlerFactory.create(transformation)
    return handler.handle_transform(value)


def process_transformations(input_value: Any, transformations: List[dict], source_field: str) -> List[Any]:
    """Process a list of transformations on a value"""
    outputs = []
    current_value = input_value

    for idx, transform_config in enumerate(transformations):
        transformation_type = transform_config.get('type')
        if not transformation_type:
            raise BadRequest("Missing required field: transformation_type")

        try:
            transformation = Transformation(
                id=f"transform_{idx}",
                source_field=[source_field],
                type=TransformationType(transformation_type),
                config=transform_config.get('config', {})
            )
            current_value = _apply_transformation(current_value, transformation)
            outputs.append(current_value)
        except (ValueError, KeyError) as e:
            raise BadRequest(f"Invalid transformation configuration: {str(e)}")

    return outputs