from typing import Dict, Any, List
from integrations.common.transformers.base import SourceTransformer
# from models.inventory.inventory import InventoryAttributes, InventoryAttributesSchema
from integrations.channels.action_types import ActionGroup


class Cin7InventoryTransformer(SourceTransformer):
    channel_name = 'cin7'
    object_type = 'inventory'

    def _map_object_type_to_action_type(self) -> str:
        return 'get_inventory'

    def _transform(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        try:
            self.validate_input(data)
            location_mapping = self.connection_settings.get('dynamic_settings', {}).get('location_mapping', {}).get(
                'mapping', {})

            inventory_data = {
                'sku': data['SKU'],
                'inventories': []
            }
            for inventory_item in data['ProductAvailabilityList']:
                inventory = {
                    'location_id': str(location_mapping.get(inventory_item['Location'], inventory_item['Location'])),
                    'available': int(inventory_item['Available'])
                }
                inventory_data['inventories'].append(inventory)

            self.validate_output(inventory_data)
            return inventory_data
        except Exception as e:
            raise ValueError(f"Error transforming Cin7 inventory data: {str(e)}")

    def validate_input(self, data: Dict[str, Any]):
        # Implement input validation logic using self.input_schema
        pass

    def validate_output(self, data: List[Dict[str, Any]]):
        # Implement output validation logic using self.output_schema
        pass

    @property
    def input_schema(self):
        return {
            'type': 'object',
            'properties': {
                'inventory': {
                    'type': 'object',
                    'properties': {
                        'depots': {
                            'type': 'object',
                            'patternProperties': {
                                '^\d+$': {'type': 'integer'}
                            }
                        }
                    }
                }
            }
        }

    @property
    def output_schema(self):
        return {
            'type': 'array',
            # 'items': InventoryAttributesSchema().dump(InventoryAttributesSchema())
        }

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.inventory
