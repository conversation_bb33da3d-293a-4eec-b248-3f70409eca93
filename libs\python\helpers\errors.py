from nolicore.utils.exceptions import BadRequest


class WMSBadRequest(BadRequest):

    def __init__(self, messages=None, request_id=None):
        messages = messages if messages else self.messages
        super().__init__(messages, request_id)


class NotFound(WMSBadRequest):
    error_code = 'error_not_found'
    messages = 'Object not found.'


class ProductNotFound(WMSBadRequest):
    error_code = 'error_product_not_found'
    messages = 'Product not found.'


class OutOfStock(WMSBadRequest):
    error_code = 'error_out_of_stock'
    messages = 'Out of stock'


class SKUExisted(WMSBadRequest):
    error_code = 'error_sku_existed'
    messages = 'Sku already existed'


class LocationNotFound(WMSBadRequest):
    error_code = 'error_location_not_found'

    def __init__(self, location_id, request_id=None):
        super().__init__(f'Location {location_id} not found', request_id)


class PriceInvalid(WMSBadRequest):
    error_code = 'error_price_invalid'
    messages = 'Invalid price'


class PaymentMissing(WMSBadRequest):
    error_code = 'error_price_invalid'
    messages = 'Payment method'


class PaymentNotFound(WMSBadRequest):
    error_code = 'error_price_invalid'
    messages = 'Payment method not found'


class PurchaseOrderNotFound(WMSBadRequest):
    error_code = 'error_purchase_order_not_found'
    messages = 'Purchase Order not found'


class PurchaseOrderStatusInvalid(WMSBadRequest):
    error_code = 'error_status_invalid'
    messages = 'Purchase Order status invalid'


class OrderStatusInvalid(WMSBadRequest):
    error_code = 'error_status_invalid'
    messages = 'Order status invalid'


class ReturnOrderStatusInvalid(WMSBadRequest):
    error_code = 'error_return_order_status_invalid'
    messages = 'Return order status invalid'


class StockAdjustmentStatusInvalid(WMSBadRequest):
    error_code = 'error_status_invalid'
    messages = 'Stock adjustment status invalid'


class StockRelocateStatusInvalid(WMSBadRequest):
    error_code = 'error_status_invalid'
    messages = 'Stock relocate status invalid'


class InventoryUpdateMultiProcessing(WMSBadRequest):
    error_code = 'error_inventory_update_multi_processing'
    messages = 'Inventory update multiprocessing fail'


class ShortcutNameInvalid(WMSBadRequest):
    error_code = 'error_shortcut_name_invalid'
    messages = 'Shortcut name invalid! Please add it in shortcut name enum.'


class SlugExisted(WMSBadRequest):
    error_code = 'error_slug_existed'
    messages = 'Slug already existed'


class AmountInvalid(WMSBadRequest):
    error_code = 'error_amount_invalid'
    messages = 'Amount invalid'


class UnitNameExisted(WMSBadRequest):
    error_code = 'error_unit_name_existed'
    messages = 'Unit name already existed'


class UnitRatioExisted(WMSBadRequest):
    error_code = 'error_unit_ratio_existed'
    messages = 'Unit ratio already existed'


class UnitDataInvalid(WMSBadRequest):
    error_code = 'error_unit_data_invalid'
    messages = 'Unit data invalid'


class CustomerExisted(WMSBadRequest):
    error_code = 'error_customer_phone_existed'
    messages = 'Customer phone already existed'


class LoyaltyProgramDateInvalid(WMSBadRequest):
    error_code = 'error_loyalty_program_date_invalid'
    messages = 'Loyalty program date invalid'


class RewardProgramDateInvalid(WMSBadRequest):
    error_code = 'error_reward_program_date_invalid'
    messages = 'Reward program date invalid'


class ImportStatusInvalid(WMSBadRequest):
    error_code = 'error_status_invalid'
    messages = 'Import status invalid'


class LocaleExisted(WMSBadRequest):
    error_code = 'error_locale_existed'
    messages = 'Locale already existed'
