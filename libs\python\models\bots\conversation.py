from dataclasses import dataclass
from enum import Enum
from typing import List

from marshmallow import fields
from marshmallow_enum import EnumField

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


class Role(str, Enum):
    USER = "USER"
    VIRTUAL_STAFF = "VIRTUAL_STAFF"

@dataclass
class ConversationAttributes(BasicAttributes):
    name: str = None
    customer_id: str = None
    image: str = None
    members: List[str] = None
    files: List[str] = None
    links: List[str] = None
    assignee_id: str = None  # this can be user or virtual_staff_id
    staff_id: str = None
    role: Role = Role.VIRTUAL_STAFF
    connection_id: str = None
    channel:str = "chat_box"

class ConversationAttributesSchema(BasicAttributesSchema):
    name = fields.Str(required=False, allow_none=True)
    customer_id = fields.Str(required=True)
    image = fields.Str(allow_none=True)
    members = fields.List(fields.Str(), required=True)
    files = fields.List(fields.Str(), allow_none=True)
    links = fields.List(fields.Str(), allow_none=True)
    assignee_id = fields.Str(required=True)
    staff_id = fields.Str(required=True)
    role = EnumField(Role, required=False, default=Role.VIRTUAL_STAFF)
    connection_id = fields.Str(required=False, allow_none=True)
    channel = fields.Str(required=False, default="chat_box")

class ConversationModel(BasicModel):
    table_name = 'conversation'
    attributes: ConversationAttributes
    attributes_schema = ConversationAttributesSchema
    attributes_class = ConversationAttributes

    query_mapping = {
        'query': ['id', 'name', 'assignee_id', 'staff_id', 'role'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'staff_id': 'query',
            'assignee_id': 'query',
            'channel':'query',
            'role': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
    }

class ConversationCustomerAndStaffIdIndex(ConversationModel):
    key__id = 'customer_id'
    key__ranges = ['staff_id']
    index_name = 'conversationByCustomerAndStaffIdIndex'