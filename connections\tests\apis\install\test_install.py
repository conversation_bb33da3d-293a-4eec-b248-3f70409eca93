import json
import unittest

from connections.tests.apis.install.data_test import subscribe_event
from tests.base import BaseTestCase
from dotenv import load_dotenv

from connections.src.apis.install import install, subscribe_plan


class InstallTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.hmac = '713f892045bd384b60acb2fc79e9ed0b485301ef92b8caeb10f32a9437ef6d49'
        cls.destination_channel = 'tiktok_shop'
        cls.source_channel = 'shopify_oauth'
        cls.host = 'YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvc29ubnYyNjAzMQ'
        cls.shop = 'sonnv26031.myshopify.com'
        cls.timestamp = 1742980874
        cls.connection_id = '4ec2391d-b4cd-438e-b381-2c45125df74b'
        cls.plan_id = "242d5c04-fb44-4352-9b60-1a5e00f1f451"

        if not all([cls.hmac, cls.shop]):
            raise ValueError("Credentials not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        # self.company_id = str(uuid.uuid4())
        # self.user_id = str(uuid.uuid4())
        # self.username = f"user_{self.user_id}"

        self.company_id = "14af040a-069f-46e0-bbe2-fef294a0a715"
        self.user_id = "0697fd7f-1c9d-4dae-9d93-51858da0c0af"
        self.username = f"user_{self.user_id}"
        self.id_token = "eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Lfh2b7Z5y-ptvzbD5WhlUBdUqhcWt1Rfz_fXntqlUkjqXvgTpoSHuP_6IEWLC4MU0tBjzOslmllWy_5bEH16UixP8OBLuDsBe73KA-5UmzkibamgwHq36o24lK5t1fs4xAGH8ylVJ1CfA6RbhkeavD-9ejrr_dLOqrPDoTSFFA9aqowDFNvar6bslhNwIQYf-wMr3FP3gY54tdntn62mxGrvBxEbYmU9XNcgKOO04wMzksMG8WiQShefdbtm0kQiLTKaL7dg5QKgQgKJVY-QI98rwR13bZjEY6CI3HW5IPNAKsdzFq7AkD1qw2_QdH0fqQ4KIm7MeeeJjN_nMEKZyw"
        self.access_token = "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_setup_install_success(self):
        # Arrange
        event = self.create_lambda_event(
            path_params={"channel_name": self.source_channel},
            body={"source_channel": self.source_channel, "destination_channel": self.destination_channel,
                  "hmac": self.hmac, "host": self.host, "shop": self.shop, "timestamp": self.timestamp})
        context = self.create_lambda_context()
        # Act
        response = install(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'nhanh OAuth2 flow initiated')
        self.assertIn('authorization_url', response_body)
        self.assertIn('connection_id', response_body)

    def test_subscribe_plan_success(self):
        # Arrange
        data = {
            "plan_id": "242d5c04-fb44-4352-9b60-1a5e00f1f451",
            "connection_id": "06736eb4-33f6-45fe-a453-11237036a05c",
            "duration": "MONTHLY",
            "updated_at": "2025-03-05T10:43:46.020295+00:00",
            "service_id": "5c4bf15b-29fc-492c-a338-b15f3ce4b5be",
            "code": "STARTER",
            "sale_price": "79000",
            "created_at": "2025-03-05T10:43:46.020295+00:00",
            "price": "100000",
            "description": "",
            "id": "242d5c04-fb44-4352-9b60-1a5e00f1f451",
            "name": "Starter",
            "features": {
                "integration": {
                    "_list": True,
                    "add": True,
                    "update": True,
                    "delete": True
                }
            }
        }
        event = self.create_lambda_event(
            company_id="f22c352d-0c01-420d-935c-7574e6e62114",
            header={
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.id_token}'
            },
            body=data,
            query_params={"access_token": self.access_token}

        )
        context = self.create_lambda_context()
        # Act
        response = subscribe_plan(subscribe_event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])


if __name__ == '__main__':
    unittest.main()
