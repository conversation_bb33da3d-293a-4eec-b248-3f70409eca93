import json
from pathlib import <PERSON>
from typing import List, Dict

from models.bots.virtual_staff import Role


# Load roles and expertises from JSON file
def load_roles_and_expertises() -> Dict[str, List[str]]:
    current_dir = Path(__file__).parent
    json_path = current_dir / "virtual_staff_roles.json"
    
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Create a mapping of role to expertises
    role_expertises = {}
    for role_data in data['roles']:
        role_name = role_data['role'].upper().replace(' ', '_')
        role_expertises[role_name] = role_data['expertize']
    
    return role_expertises

# Get valid expertises for a role
def get_valid_expertises(role: Role) -> List[str]:
    role_expertises = load_roles_and_expertises()
    return role_expertises.get(role.value, [])

# Validate expertises for a role
def validate_expertises(role: Role, expertises: List[str]) -> bool:
    if not expertises:
        return True
    
    valid_expertises = get_valid_expertises(role)
    return all(expertise in valid_expertises for expertise in expertises)

# Validate role
def validate_role(role: str) -> bool:
    try:
        Role(role)
        return True
    except ValueError:
        return False 