import copy
import os
from collections import defaultdict

import pendulum
from nolicore.utils.utils import logger

from models.order.order import OrderModel
from models.product.product import ProductModel
from models.purchase_order.purchase_order import PurchaseOrderModel

RESOURCE_SERVICE = os.getenv('RESOURCE_SERVICE')


def get_variants_details(product_id):
    # Fetch product details including variants
    query = {"query": {"match": {"id": product_id}}}
    response = ProductModel.report(query, service=RESOURCE_SERVICE)
    product = response['hits']['hits'][0]['_source'] if response['hits']['hits'] else None
    if product:
        return {
            "id": product["id"],
            "name": product.get("name", ""),
            "images": product.get("images", ""),
            "variants": product.get("variants", [])
        }
    return None


def get_months_with_order(variant_id):
    # Update your query to include both the nested filter and Date Histogram aggregation
    query = {
        "size": 0,  # No hits needed, only aggregations
        "query": {
            "bool": {
                "filter": [
                    {
                        "nested": {
                            "path": "order_line_items",
                            "query": {
                                "bool": {
                                    "filter": [
                                        {"term": {"order_line_items.variant_id.keyword": variant_id}}
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        },
        "aggs": {
            "orders_over_time": {
                "date_histogram": {
                    "field": "created_at",
                    "calendar_interval": "month",
                    "format": "yyyy-MM",  # Formats the date as Year-Month
                    "min_doc_count": 1  # Only include buckets that have at least one document
                }
            }
        }
    }

    # Execute the search query with aggregation
    response = OrderModel.report(query, service=RESOURCE_SERVICE)

    # Processing the response to print out the months with orders
    buckets = response['aggregations']['orders_over_time']['buckets']
    return [bucket['key_as_string'] for bucket in buckets]


def calculate_sales_metrics(variant_id, initial_date):
    metrics_by_month = {}

    months = get_months_with_order(variant_id)

    for month in months:
        start_date = initial_date.to_iso8601_string()
        end_date = pendulum.parse(month).end_of('month').to_iso8601_string()

        # Query to aggregate sales data for the variant within the monthly interval
        query = {
            'size': 0,
            'query': {
                'bool': {
                    'filter': [
                        {
                            'nested': {
                                'path': 'order_line_items',
                                'query': {
                                    'bool': {
                                        'filter': [
                                            {
                                                'term': {
                                                    'order_line_items.variant_id.keyword': variant_id
                                                }
                                            }
                                        ]
                                    }
                                }
                            }
                        },
                        {
                            'range': {
                                'created_at': {
                                    'gte': start_date,
                                    'lt': end_date
                                }
                            }
                        }
                    ]
                }
            },
            'aggs': {
                'nested_agg': {  # Aggregate within the nested path
                    'nested': {
                        'path': 'order_line_items'
                    },
                    'aggs': {
                        'filter_variant': {  # Further filter down to the specific variant
                            'filter': {
                                'term': {
                                    'order_line_items.variant_id.keyword': variant_id
                                }
                            },
                            'aggs': {
                                'total_units_sold': {
                                    'sum': {
                                        'field': 'order_line_items.quantity'
                                    }
                                },
                                'total_revenue': {
                                    'sum': {
                                        'field': 'order_line_items.price'
                                    }
                                },
                                'average_unit_price': {
                                    'avg': {
                                        'field': 'order_line_items.unit_price'
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        response = OrderModel.report(query, service=RESOURCE_SERVICE)
        aggs = response['aggregations']

        total_units_sold = aggs['nested_agg']['filter_variant']['total_units_sold']['value']
        total_revenue = aggs['nested_agg']['filter_variant']['total_revenue']['value']
        average_sale_price = total_revenue / total_units_sold if total_units_sold > 0 else 0
        average_unit_price = aggs['nested_agg']['filter_variant']['average_unit_price']['value'] or average_sale_price

        # Populate metrics for the month
        metrics_by_month[month] = {
            "sold_units": total_units_sold,
            "average_sale_price": average_sale_price,
            "revenue": average_sale_price * total_units_sold,
            "average_unit_price": average_unit_price,
            "average_discount": average_unit_price - average_sale_price
        }

    return metrics_by_month


def get_aggregated_purchase_info(variant_id):
    query = {
        "size": 0,  # No hits needed, only aggregations
        "query": {
            "nested": {
                "path": "order_line_items",
                "query": {
                    "bool": {
                        "must": [
                            {"match": {"order_line_items.variant_id.keyword": variant_id}}
                        ]
                    }
                }
            }
        },
        "aggs": {
            "order_line_items_nested": {
                "nested": {
                    "path": "order_line_items"
                },
                "aggs": {
                    "filtered_by_variant": {
                        "filter": {
                            "term": {"order_line_items.variant_id.keyword": variant_id}
                        },
                        "aggs": {
                            "total_quantity": {"sum": {"field": "order_line_items.quantity"}},
                            "total_cost": {"sum": {"script": {
                                "source": "doc['order_line_items.quantity'].value * doc['order_line_items.sale_price'].value"
                            }}}
                        }
                    }
                }
            },
            "purchase_ids": {"terms": {"field": "id.keyword", "size": 10000}},
            # Assuming a high enough size to capture all IDs
            "earliest_purchase_date": {"min": {"field": "created_at"}}
        }
    }

    response = PurchaseOrderModel.report(query, service=RESOURCE_SERVICE)
    logger.info(response)
    total_quantity = response['aggregations']['order_line_items_nested']['filtered_by_variant']['total_quantity'][
        'value']
    total_cost = response['aggregations']['order_line_items_nested']['filtered_by_variant']['total_cost']['value']
    purchase_ids = [bucket['key'] for bucket in response['aggregations']['purchase_ids']['buckets']]
    earliest_purchase_date = response['aggregations']['earliest_purchase_date']['value_as_string']

    return {
        "total_quantity": total_quantity,
        "total_cost": total_cost,
        "purchase_ids": purchase_ids,
        "earliest_purchase_date": pendulum.parse(earliest_purchase_date)
    }


def get_latest_purchase_info(variant_id):
    query = {
        "size": 1,
        "sort": [{"created_at": {"order": "desc"}}],  # Sort by created_at in descending order
        "query": {
            "nested": {
                "path": "order_line_items",
                "query": {
                    "bool": {
                        "must": [
                            {"match": {"order_line_items.variant_id.keyword": variant_id}}
                        ]
                    }
                }
            }
        },
        "_source": ["id", "created_at", "order_line_items.variant_id", "order_line_items.quantity",
                    "order_line_items.unit_price"]
    }
    response = PurchaseOrderModel.report(query, service=RESOURCE_SERVICE)

    if response['hits']['hits']:
        latest_purchase = response['hits']['hits'][0]['_source']
        # Extract the required details from the nested order_line_items
        for item in latest_purchase.get('order_line_items', []):
            if item['variant_id'] == variant_id:
                latest_date = pendulum.parse(latest_purchase['created_at'])
                stock_at_latest_purchase = item['quantity']
                cost_at_latest_purchase = item['unit_price'] * stock_at_latest_purchase  # Cost calculation
                return latest_purchase['id'], latest_date, stock_at_latest_purchase, cost_at_latest_purchase

    return None, None, 0, 0  # Default values if no matching purchase order found


def fill_missing_months(variant_metrics, sorted_months):
    filled_metrics = {}
    last_metrics = None
    for month in sorted_months:
        if month in variant_metrics:
            filled_metrics[month] = variant_metrics[month]
            last_metrics = variant_metrics[month]
        elif last_metrics:
            filled_metrics[month] = copy.deepcopy(last_metrics)  # Use deep copy to avoid modifying original data
        else:  # For the case where the first month(s) have no data
            filled_metrics[month] = {'sold_units': 0, 'average_sale_price': 0,
                                     'average_discount': 0, 'average_unit_price': 0}
    return filled_metrics


def calculate_monthly_product_metrics(report):
    all_months = set()
    for variant in report['variants']:
        all_months.update(variant['metrics_by_month'].keys())
    sorted_months = sorted(all_months)

    # Work with a deep copy of the report's variants to ensure the original data is not modified
    updated_variants = copy.deepcopy(report['variants'])

    # Ensure each variant has metrics for each month, filling missing months
    for variant in updated_variants:
        variant['metrics_by_month'] = fill_missing_months(variant['metrics_by_month'], sorted_months)

    temp_product_metrics = defaultdict(
        lambda: {'total_sold_units': 0, 'total_revenue': 0, 'total_discount_value': 0, 'count': 0,
                 'total_retails_cost_rate': 0})

    for variant in updated_variants:
        for month, metrics in variant['metrics_by_month'].items():
            temp_product_metrics[month]['total_sold_units'] += metrics['sold_units']
            temp_product_metrics[month]['total_revenue'] += metrics['sold_units'] * metrics['average_sale_price']
            temp_product_metrics[month]['total_retails_cost_rate'] += variant['initial_stock'] * metrics[
                'average_unit_price']
            temp_product_metrics[month]['total_discount_value'] += metrics['sold_units'] * metrics['average_discount']
            temp_product_metrics[month]['count'] += 1

    monthly_aggregated_metrics = {}
    for month in sorted_months:
        data = temp_product_metrics[month]
        monthly_aggregated_metrics[month] = {
            "sold_units": data['total_sold_units'],
            "revenue_retails_cost_rate": data['total_revenue'] / data['total_retails_cost_rate'] if data['total_retails_cost_rate'] else 0,
            "average_sale_price": data['total_revenue'] / data['total_sold_units'] if data['total_sold_units'] else 0,
            "average_discount": data['total_discount_value'] / data['total_sold_units'] if data[
                'total_sold_units'] else 0,
        }

    return monthly_aggregated_metrics


def generate_report(company_id, product_id):
    product_details = get_variants_details(product_id)

    product_report = {
        "id": product_details["id"],
        "name": product_details["name"],
        "images": product_details["images"],
        "initial_stock": 0,  # Initialize product-level stock
        "variants": [],
    }

    for variant in product_details["variants"]:
        purchase_info = get_aggregated_purchase_info(variant["id"])
        initial_date = purchase_info['earliest_purchase_date']
        initial_stock = purchase_info['total_quantity']
        cost = purchase_info['total_cost']
        purchase_ids = purchase_info['purchase_ids']

        variant_metrics = calculate_sales_metrics(variant["id"], initial_date) if initial_date else {}

        variant_report = {
            "id": variant["id"],
            "name": variant.get("name"),
            "images": variant.get("images"),
            "initial_stock": initial_stock,  # Include initial stock per variant
            "initial_date": initial_date,
            "cost": cost,
            "purchase_ids": purchase_ids,
            "metrics_by_month": variant_metrics,
        }

        product_report["initial_stock"] += initial_stock  # Accumulate stock at the product level
        product_report["variants"].append(variant_report)

    # Include metrics_by_month calculation for the product level as before
    # (Keep the previous logic for aggregating sales metrics and calculating averages)
    monthly_product_metrics = calculate_monthly_product_metrics(product_report)
    product_report['metrics_by_month'] = monthly_product_metrics
    return product_report
