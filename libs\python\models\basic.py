import os
import uuid
from dataclasses import dataclass
import pendulum
from marshmallow import fields, pre_load
from nolicore.adapters.db.model import AttributesSchema, Attributes, Model
from nolicore.utils.utils import logger

from helpers.errors import NotFound


@dataclass
class User(Attributes):
    id: str
    name_staff: str


class UserSchema(AttributesSchema):
    id = fields.UUID(required=True)
    name_staff = fields.Str(required=True)


@dataclass
class BasicAttributes(Attributes):
    id: str
    created_at: str = None
    updated_at: str = None
    company_id: str = None
    user: User = None

    @classmethod
    def add_basic_attributes(cls, data, company_id, user=None):
        current_time = pendulum.now().to_iso8601_string()
        data['updated_at'] = current_time
        data['company_id'] = company_id
        if 'id' not in data:
            data['id'] = str(uuid.uuid4())
        if 'created_at' not in data:
            data['created_at'] = current_time
        if 'user' not in data:
            data['user'] = user
        return data


class BasicAttributesSchema(AttributesSchema):
    id = fields.Str(required=True)
    company_id = fields.UUID(allow_none=True)
    created_at = fields.DateTime(required=True)
    updated_at = fields.DateTime(required=True)
    user = fields.Nested(UserSchema(User), allow_none=True, default=None)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['user'] = data['user'] if 'user' in data else None
        data['company_id'] = data['company_id'] if 'company_id' in data else None
        return data


class BasicModel(Model):
    key__id = 'id'
    key__ranges = ['company_id']
    read_only_fields = ['id', 'company_id', 'created_at']
    attributes: BasicAttributes = None

    @property
    def id(self):
        return str(self.attributes.id)

    @classmethod
    def get(cls, company_id, _id, _except=False):
        basic_obj = cls.by_key({
            'company_id': str(company_id),
            'id': str(_id)
        })
        if _except and basic_obj is None:
            raise NotFound()
        return basic_obj

    @classmethod
    def get_by_id(cls, _id, _except=False):
        try:
            return cls(cls.list({
                'id': str(_id),
            }, limit=1000)['Items'][0])
        except IndexError:
            if _except:
                raise NotFound()
            return None

    @classmethod
    def get_connection_data(cls, _id, company_id=None, _except=False):
        filters = {
            'id': str(_id),
        }
        if company_id:
            filters['company_id'] = company_id
        try:
            return cls.list(filters, limit=1000)['Items'][0]
        except IndexError:
            if _except:
                raise NotFound()
            return None

    @classmethod
    def create(cls, company_id, data, user=None):
        model_data = BasicAttributes.add_basic_attributes(data, company_id, user=user)
        logger.info(model_data)
        model = cls(model_data)
        model.save()
        return model

    def update(self, data: dict, user=None, prepared_request=False, save=True, condition: dict = None, replace=True):
        data['updated_at'] = pendulum.now().to_iso8601_string()
        data['user'] = user
        return super().update(data, prepared_request, save, condition, replace)

    @classmethod
    def create_if_not_exist(cls, company_id, data):
        if data and not data.get('id'):
            return cls.create(company_id, data)
        return data

    @classmethod
    def search(cls, params, raw_query=False, service=None, company_id=None):
        if company_id:
            params['company_id'] = company_id
        return super().search(params, raw_query, service)

    def copy(self):
        """
        Create a copy of the current BasicModel instance using attributes_dict.
        """
        return self.__class__(self.attributes_dict)
