from uuid import uuid4

from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import BadRequest, NotFoundRequest

from helpers.utils import RESOURCE_SERVICE
from models.bots.knowledge import KnowledgeModel, Status, Source
from ..helpers.common import deep_update
from ..helpers.knowledge import (
    create_knowledge,
    file_service,
    generate_s3_key
)
from ..helpers.virtual_staff import invoke_remove_knowledge_from_staff


@as_api()
def create_knowledge_from_urls_api(api_gateway_request: ApiGatewayRequest):
    """Create knowledge from a URL"""
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id

    staff_id = body.get('staff_id') if isinstance(body, dict) else None
    urls = body if isinstance(body, list) else body.get('urls') if isinstance(body, dict) else None

    if not isinstance(urls, list):
        raise BadRequest('Payload must be a list')

    if next((item for item in urls if not isinstance(item, str)), None):
        raise BadRequest('Payload must be a list of strings')
    try:
        knowledge_list = []
        for url in urls:
            meta_data = {
                "url": url
            }
            knowledge = create_knowledge(company_id, content=url, source=Source.URL.value, meta_data=meta_data, staff_id=staff_id)
            knowledge_list.append(knowledge)
        return {
            'success': True,
            'message': 'Knowledge created successfully',
            'data': knowledge_list
        }
    except Exception as e:
        raise BadRequest(str(e))


@as_api()
def create_knowledge_from_text_api(api_gateway_request: ApiGatewayRequest):
    """Create knowledge from direct text"""
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id

    staff_id = body.get('staff_id') if isinstance(body, dict) else None
    texts = body if isinstance(body, list) else body.get('texts') if isinstance(body, dict) else None

    if not isinstance(texts, list):
        raise BadRequest('Payload must be a list')

    # Validate required fields
    required_fields = ['content', 'knowledge_name']
    result = []
    try:
        for idx, text_payload in enumerate(texts):
            for field in required_fields:
                if field not in text_payload:
                    raise BadRequest(f"Missing required field in : [{idx}].{field}")
            meta_data = {
                "content": text_payload['content']
            }
            knowledge = create_knowledge(
                company_id,
                content=text_payload['content'],
                knowledge_name=text_payload['knowledge_name'],
                source=Source.DIRECT_TEXT.value,
                meta_data=meta_data,
                staff_id=staff_id
            )
            result.append(knowledge)
        return {
            'success': True,
            'message': 'Knowledge created successfully',
            'data': result
        }
    except Exception as e:
        return {
            'success': False,
            'message': str(e),
            'data': result
        }


@as_api()
def create_knowledge_from_files_api(api_gateway_request: ApiGatewayRequest):
    """Create knowledge from files"""
    body = api_gateway_request.body
    company_id = api_gateway_request.company_id
    staff_id = body.get('staff_id')
    files = body.get('files')
    required_file_fields = ["file_name", "size", "type", "s3_key"]
    knowledge_list = []

    if not files:
        raise BadRequest("No files provided")

    for file in files:
        # Validate required fields
        for field in required_file_fields:
            if field not in file:
                raise BadRequest(f"Missing required field: {field}")

        # Check if file exists in S3
        if not file_service.check_s3_file_exists(file["s3_key"]):
            raise BadRequest(f"File not found in S3: {file['s3_key']}")

        try:
            # Extract company_id and knowledge_id from key
            # Expected format: knowledge/{company_id}/{knowledge_id}.txt
            try:
                s3_key = file.get("s3_key")
                _, company_id, knowledge_file = s3_key.split('/')
                knowledge_id = knowledge_file.split('.')[0]
            except ValueError:
                continue

            meta_data = dict(file)
            knowledge = create_knowledge(company_id, content=file['file_name'], knowledge_name=file['file_name'],
                                         source=Source.FILE.value, meta_data=meta_data, s3_key=s3_key, staff_id=staff_id)
            knowledge_list.append(knowledge)
        except Exception as e:
            raise BadRequest(f"Failed to create knowledge: {str(e)}")

    return {
        'success': True,
        'message': 'Knowledge created successfully',
        'data': knowledge_list
    }


@as_api()
def generate_upload_url(api_gateway_request: ApiGatewayRequest):
    files = api_gateway_request.body['files']
    urls = []
    for file in files:
        required_fields = ['file_name', 'type', 'size']
        for field in required_fields:
            if field not in file:
                raise BadRequest(f"Missing required field: {field}")
        extension = file.get('file_name').split('.')[-1]
        s3_key = generate_s3_key(company_id=api_gateway_request.company_id,
                                 file_name=f"{str(uuid4())}.{extension}")
        file['s3_key'] = s3_key
        url = file_service.generate_upload_url(file=file)
        urls.append({
            "url": url,
            "s3_key": s3_key
        })
    return urls


@as_api()
def get_knowledge(api_gateway_request: ApiGatewayRequest):
    """Get knowledge by ID"""
    knowledge_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id
    if not knowledge_id:
        raise BadRequest("Missing required field: id")

    # Get knowledge
    knowledge = KnowledgeModel.by_key(key={'company_id': company_id, 'id': knowledge_id})
    if not knowledge:
        raise NotFoundRequest("Knowledge not found")

    return knowledge.attributes_dict


@as_api()
def list_knowledge(api_gateway_request: ApiGatewayRequest):
    """List knowledge with optional filters"""
    params = api_gateway_request.query_string_parameters or {}
    company_id = api_gateway_request.company_id

    # Search using company index
    knowledge_list = KnowledgeModel.search(
        params,
        service=RESOURCE_SERVICE,
        company_id=company_id
    )

    return knowledge_list


@as_api()
def check_knowledge_status(api_gateway_request: ApiGatewayRequest):
    """Check status of a knowledge item"""
    knowledge_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id

    if not knowledge_id:
        raise BadRequest("Missing required field: id")

    # Get knowledge
    knowledge = KnowledgeModel.by_key(key={'company_id': company_id, 'id': knowledge_id})
    if not knowledge:
        raise NotFoundRequest("Knowledge not found")

    return knowledge.attributes_dict.get('status')


@as_api()
def update_knowledge_status(api_gateway_request: ApiGatewayRequest):
    """Update status of a knowledge item"""
    knowledge_id = api_gateway_request.path_parameters.get('id')
    body = api_gateway_request.body

    # Validate required fields
    if not knowledge_id:
        raise BadRequest("Missing required field: id")
    if 'status' not in body:
        raise BadRequest("Missing required field: status")

    # Get valid status values
    valid_statuses = [status.value for status in Status]
    if body['status'] not in valid_statuses:
        raise BadRequest(f"Invalid status. Must be one of: {valid_statuses}")

    # Get knowledge
    knowledge = KnowledgeModel.get_by_id(_id=knowledge_id)
    if not knowledge:
        raise NotFoundRequest("Knowledge not found")

    # Update status
    try:
        knowledge.update({'status': body['status']})
        return {
            'success': True,
            'message': 'Knowledge status updated successfully',
            'data': knowledge.attributes_dict
        }
    except Exception as e:
        raise BadRequest(f"Failed to update knowledge status: {str(e)}")


@as_api()
def update_knowledge(api_gateway_request: ApiGatewayRequest):
    """Update knowledge details"""
    knowledge_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id
    body = api_gateway_request.body

    if not knowledge_id:
        raise BadRequest("Missing required field: id")

    # Get knowledge
    knowledge = KnowledgeModel.by_key(key={'company_id': company_id, 'id': knowledge_id})
    if not knowledge:
        raise NotFoundRequest("Knowledge not found")

    # Only allow updating specific fields
    allowed_fields = ['name', 'description', 'summary_content']
    update_data = {
        k: v for k, v in body.items()
        if k in allowed_fields and v is not None
    }

    if not update_data:
        raise BadRequest("No valid fields to update")

    try:
        # Get current knowledge data
        current_data = knowledge.attributes_dict

        # Deep update only the fields that were passed
        updated_data = deep_update(current_data, update_data)

        # Update knowledge with merged data
        knowledge.update(updated_data)

        return {
            'success': True,
            'message': 'Knowledge updated successfully',
            'data': knowledge.attributes_dict
        }
    except Exception as e:
        raise BadRequest(f"Failed to update knowledge: {str(e)}")


@as_api()
def delete_knowledge(api_gateway_request: ApiGatewayRequest):
    """Delete knowledge and its associated S3 file"""
    knowledge_id = api_gateway_request.path_parameters.get('id')
    company_id = api_gateway_request.company_id

    if not knowledge_id:
        raise BadRequest("Missing required field: id")

    # Get knowledge
    knowledge = KnowledgeModel.by_key(key={'company_id': company_id, 'id': knowledge_id})
    if not knowledge:
        raise NotFoundRequest("Knowledge not found")

    try:
        knowledge_dict = knowledge.attributes_dict
        # Delete S3 file if it exists
        if knowledge_dict.get('s3_key'):
            file_service.delete_file(knowledge_dict.get('s3_key'))

        # Remove knowledge from virtual staff's knowledge list
        staff_ids = knowledge_dict.get('virtual_staff_ids', [])
        invoke_remove_knowledge_from_staff(company_id, knowledge_id, staff_ids)

        # Delete knowledge
        key = {
            KnowledgeModel.key__id: knowledge_id,
            'company_id': company_id
        }
        KnowledgeModel.delete(key=key)
        return {
            'success': True,
            'message': 'Knowledge deleted successfully'
        }
    except Exception as e:
        raise BadRequest(f"Failed to delete knowledge: {str(e)}")


def remove_staff_from_knowledge_handler(event, context) -> None:
    """
    Remove virtual staff ID from knowledge records' virtual_staff_ids.

    Args:
        event
        context
    """
    company_id = event['company_id']
    knowledge_ids = event['knowledge_ids']
    staff_id = event['staff_id']

    if not knowledge_ids:
        return

    for knowledge_id in knowledge_ids:
        knowledge = KnowledgeModel.by_key({
            'company_id': company_id,
            'id': knowledge_id
        })
        if knowledge:
            staff_ids = knowledge.attributes_dict.get(
                'virtual_staff_ids',
                []
            )
            if staff_id in staff_ids:
                staff_ids.remove(staff_id)
                knowledge.update({'virtual_staff_ids': staff_ids})
