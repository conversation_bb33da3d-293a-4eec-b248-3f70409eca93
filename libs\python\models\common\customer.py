from dataclasses import dataclass
from decimal import Decimal

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema


@dataclass
class SaleOrder(Attributes):
    last_order_on: str = None
    net_quantity: Decimal = 0
    purchased_order: int = 0
    returned_item_quantity: Decimal = 0
    total_spent: Decimal = 0


class SaleOrderSchema(AttributesSchema):
    last_order_on = fields.Str(allow_none=True)
    net_quantity = fields.Decimal(allow_none=True, default=0)
    purchased_order = fields.Int(allow_none=True, default=0)
    returned_item_quantity = fields.Decimal(allow_none=True, default=0)
    total_spent = fields.Decimal(allow_none=True, default=0)


@dataclass
class LoyalCustomerPoint(Attributes):
    point: Decimal = 0
    used_point: Decimal = 0


class LoyalCustomerPointSchema(AttributesSchema):
    point = fields.Decimal(allow_none=True, default=0)
    used_point = fields.Decimal(allow_none=True, default=0)

