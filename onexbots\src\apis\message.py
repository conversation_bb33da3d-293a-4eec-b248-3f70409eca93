from nolicore.adapters.db.aws import Dynamo
from nolicore.utils.aws.decorator import as_api, invoke
from nolicore.utils.aws.request import ApiGatewayRequest, LambdaRequest
from nolicore.utils.exceptions import BadRequest

from helpers.utils import RESOURCE_SERVICE
from models.bots.conversation import ConversationModel
from models.bots.message import MessageModel, Role


@as_api()
def create_message(api_gateway_request: ApiGatewayRequest):
    """Create a new message in a conversation.
    
    Path parameters:
    - id: ID of the conversation to create message in
    
    Request body:
    {
        "content": "string",
        "role": "user" or "virtual_staff" or "external_user",
        "user_id": "string",  # required if role is user
        "external_user_id": "string",  # required if role is external_user
        "connection_id": "string"  # required
    }
    """
    body = api_gateway_request.body
    conversation_id = api_gateway_request.path_parameters.get('id')

    if not conversation_id:
        raise BadRequest("Missing required path parameter: id")

    # Validate required fields
    required_fields = ['content', 'role']
    for field in required_fields:
        if field not in body:
            raise BadRequest(f"Missing required field: {field}")

    # Validate role
    try:
        role = Role(body['role'])
    except ValueError:
        raise BadRequest(
            f"Invalid role. Must be one of: {[r.value for r in Role]}"
        )

    # Validate user-specific fields based on role
    if role == Role.USER and 'user_id' not in body:
        raise BadRequest("Missing required field: user_id")
    if role == Role.EXTERNAL_USER and 'external_user_id' not in body:
        raise BadRequest("Missing required field: external_user_id")

    try:
        # Verify conversation exists
        conversation = ConversationModel.get_by_id(
            _id=conversation_id
        )
        if not conversation:
            raise BadRequest("Conversation not found")

        # Prepare message data
        message_data = {
            'conversation_id': conversation_id,
            'content': body['content'],
            'role': role.value,
            'connection_id': body.get('connection_id', None)
        }

        # Add user-specific fields
        if role == Role.USER:
            message_data['user_id'] = body['user_id']
        elif role == Role.EXTERNAL_USER:
            message_data['external_user_id'] = body['external_user_id']

        company_id = str(conversation.attributes.company_id)
        # Create message
        message = MessageModel.create(company_id, message_data)

        return {
            'success': True,
            'message': 'Message created successfully',
            'data': message.attributes_dict
        }
    except Exception as e:
        raise BadRequest(f"Failed to create message: {str(e)}")


@as_api()
def list_messages(api_gateway_request: ApiGatewayRequest):
    """List messages for a conversation with optional filters.
    
    Path parameters:
    - id: ID of the conversation to list messages for
    
    Query parameters:
    - role: Filter by role (user, virtual_staff, external_user)
    - created_at_start: Start date for created_at range
    - created_at_end: End date for created_at range
    - updated_at_start: Start date for updated_at range
    - updated_at_end: End date for updated_at range
    """
    conversation_id = api_gateway_request.path_parameters.get('id')
    query_params = api_gateway_request.query_string_parameters or {}

    if not conversation_id:
        raise BadRequest("Missing required path parameter: id")

    try:
        # Verify conversation exists
        conversation = ConversationModel.get_by_id(
            _id=conversation_id
        )
        if not conversation:
            raise BadRequest("Conversation not found")
        company_id = str(conversation.attributes.company_id)

        # Add conversation_id to query params
        query_params['conversation_id'] = conversation_id
        return MessageModel.search(
            params=query_params,
            service=RESOURCE_SERVICE,
            company_id=company_id
        )
    except Exception as e:
        raise BadRequest(f"Failed to list messages: {str(e)}")
    

@as_api()
def list_public_messages(api_gateway_request: ApiGatewayRequest):
    """List messages for a conversation with optional filters.
    
    Path parameters:
    - id: ID of the conversation to list messages for
    
    Query parameters:
    - role: Filter by role (user, virtual_staff, external_user)
    - created_at_start: Start date for created_at range
    - created_at_end: End date for created_at range
    - updated_at_start: Start date for updated_at range
    - updated_at_end: End date for updated_at range
    """
    conversation_id = api_gateway_request.path_parameters.get('id')
    query_params = api_gateway_request.query_string_parameters or {}

    if not conversation_id:
        raise BadRequest("Missing required path parameter: id")

    try:
        # Verify conversation exists
        conversation = ConversationModel.get_by_id(
            _id=conversation_id
        )
        if not conversation:
            raise BadRequest("Conversation not found")
        company_id = str(conversation.attributes.company_id)

        # Add conversation_id to query params
        query_params['conversation_id'] = conversation_id
        return MessageModel.search(
            params=query_params,
            service=RESOURCE_SERVICE,
            company_id=company_id
        )
    except Exception as e:
        raise BadRequest(f"Failed to list messages: {str(e)}")

@invoke()
def delete_messages_handler(lambda_request: LambdaRequest):
    event = lambda_request.event
    conversation_id = event['conversation_id']
    try:
        # Get all messages for the conversation
        messages = MessageModel.get_conversation_messages(conversation_id)
        table_data = Dynamo(MessageModel.table_name)
        with table_data.table.batch_writer() as batch:
            for message in messages:
                key = {
                    MessageModel.key__id: message['id'],
                    'company_id': message.get('company_id')
                }
                batch.delete_item(Key=key)
    except Exception as e:
        raise BadRequest(f"Failed to delete messages: {str(e)}")
