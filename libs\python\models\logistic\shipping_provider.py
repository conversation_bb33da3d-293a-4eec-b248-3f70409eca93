from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema
from models.media.image import Image, ImageSchema


@dataclass
class ShippingProvider(Attributes):
    name: str = None
    image: Image = None


class ShippingProviderSchema(AttributesSchema):
    name = fields.Str(str=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)


@dataclass
class ShippingProviderAttributes(ShippingProvider, BasicAttributes):
    pass


class ShippingProviderAttributesSchema(BasicAttributesSchema, ShippingProviderSchema):
    pass


class ShippingProviderModel(BasicModel):
    table_name = 'shippingProvider'
    attributes: ShippingProviderAttributes
    attributes_schema = ShippingProviderAttributesSchema
    attributes_class = ShippingProviderAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'image.id': 'image.id',
            'image.url': 'image.url',
            'image.name': 'image.name',
        }
    }
