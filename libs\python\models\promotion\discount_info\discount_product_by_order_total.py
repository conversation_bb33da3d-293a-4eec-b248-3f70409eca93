from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import List

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import AttributesSchema, Attributes

from models.product.brand import Brand, BrandSchema
from models.product.category import Category, CategorySchema
from models.product.product import Product, ProductSchema
from models.product.variant import Variant, VariantSchema
from models.promotion.discount_info.discount_amount import DiscountAmountAttributes, DiscountAmountAttributesSchema


class ItemType(Enum):
    PRODUCTS = 'PRODUCTS'
    VARIANTS = 'VARIANTS'
    CATEGORIES = 'CATEGORIES'
    BRANDS = 'BRANDS'


@dataclass
class GiftOrderTotalAttributes(Attributes):
    item_type: ItemType
    max_quantity: Decimal
    amount_from: Decimal = 0
    for_all_items: bool = None
    products: List[Product] = None
    variants: List[Variant] = None
    categories: List[Category] = None
    brands: List[Brand] = None


class GiftOrderTotalAttributesSchema(AttributesSchema):
    item_type = EnumField(ItemType, required=True)
    max_quantity = fields.Decimal(required=True, default=1)
    amount_from = fields.Decimal(required=True, default=0)
    for_all_items = fields.Bool(allow_none=True)
    products = fields.List(fields.Nested(ProductSchema(Product)), allow_none=True)
    variants = fields.List(fields.Nested(VariantSchema(Variant)), allow_none=True)
    categories = fields.List(fields.Nested(CategorySchema(Category)), allow_none=True)
    brands = fields.List(fields.Nested(BrandSchema(Brand)), allow_none=True)


@dataclass
class DiscountExtraProductAttributes(GiftOrderTotalAttributes, DiscountAmountAttributes):
    pass


class DiscountExtraProductAttributesSchema(GiftOrderTotalAttributesSchema, DiscountAmountAttributesSchema):
    pass
