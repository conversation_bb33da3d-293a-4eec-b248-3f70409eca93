create_conversation:
  handler: src.apis.conversation.create_conversation
  events:
    - httpApi:
        path: /conversations
        method: post
        authorizer:
          name: opti<PERSON><PERSON><PERSON>zer
    - httpApi:
        path: /services/conversations
        method: post
        authorizer:
          name: onexbot<PERSON>uthorizer

create_public_conversation:
  handler: src.apis.conversation.create_public_conversation
  events:
    - httpApi:
        path: /public/conversations
        method: post

get_conversation:
  handler: src.apis.conversation.get_conversation
  events:
    - httpApi:
        path: /conversations/{id}
        method: get
        authorizer:
          name: opti<PERSON><PERSON><PERSON>zer
    - httpApi:
        path: /services/conversations/{id}
        method: get
        authorizer:
          name: onexbotAuthorizer

list_conversations:
  handler: src.apis.conversation.list_conversations
  events:
    - httpApi:
        path: /conversations
        method: get
        authorizer:
          name: optiAuthorizer
    - httpApi:
        path: /services/conversations
        method: get
        authorizer:
          name: onexbotAuthorizer

delete_conversation:
  handler: src.apis.conversation.delete_conversation
  events:
    - httpApi:
        path: /conversations/{id}
        method: delete
        authorizer:
          name: opti<PERSON>uth<PERSON>zer
    - httpApi:
        path: /services/conversations/{id}
        method: delete
        authorizer:
          name: onex<PERSON><PERSON><PERSON><PERSON><PERSON>

