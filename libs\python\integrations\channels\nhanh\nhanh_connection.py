from dataclasses import dataclass, field

from marshmallow import fields

from ..connection_base import OAuth2Connection, OAuth2ConnectionAttributes, OAuth2ConnectionSchema, OAuth2Settings, \
    OAuth2SettingsSchema


class NhanhSettingsSchema(OAuth2SettingsSchema):
    app_id = fields.Str(required=True)
    secret = fields.Str(required=True)
    api_version = fields.Str(default="2.0")
    base_url = fields.Url(default="https://open.nhanh.vn")
    auth_url = fields.Url(default="https://nhanh.vn/oauth")


@dataclass
class NhanhSettings(OAuth2Settings):
    app_id: str = None
    secret: str = None
    api_version: str = "2.0"
    base_url: str = "https://open.nhanh.vn"
    auth_url: str = "https://nhanh.vn/oauth"


@dataclass
class NhanhConnectionAttributes(OAuth2ConnectionAttributes):
    channel_name: str = field(default='nhanh')
    settings: NhanhSettings = field(default_factory=NhanhSettings)
    auth_url = 'https://nhanh.vn/oauth'


class NhanhConnectionSchema(OAuth2ConnectionSchema):
    settings = fields.Nested(NhanhSettingsSchema(NhanhSettings))


class NhanhConnection(OAuth2Connection):
    attributes: NhanhConnectionAttributes = None
    attributes_schema = NhanhConnectionSchema
    attributes_class = NhanhConnectionAttributes
