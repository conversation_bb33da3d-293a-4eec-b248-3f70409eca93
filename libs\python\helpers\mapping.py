from typing import List, Any

from nolicore.utils.exceptions import BadRequest, NotFoundRequest

from models.integration.sync_record import SyncRecordCompanyIdIndex


def format_sync_record(sync_record):
    return {
        "id": sync_record.get("id"),
        "connection_id": sync_record.get("connection_id"),
        "record_type": sync_record.get("record_type"),
        "channel": sync_record.get("channel"),
        "last_sync": sync_record.get("updated_at"),
        "mapping_status": sync_record.get("mapping_status"),
        "standard_source_data": sync_record.get("standard_source_data"),
        "standard_destination_data": sync_record.get("standard_destination_data"),
        "other_mappings": sync_record.get("other_mappings")
    }


def validate_transform_request(body: dict) -> None:
    """Validate the transformation request body"""
    required_fields = ['sync_record_id', 'connection_id', 'source_field', 'transformations']
    for field in required_fields:
        if not body.get(field):
            raise BadRequest(f"Missing required field: {field}")


def get_source_sync_record(sync_record_id: str, connection_id: str, company_id: str) -> dict:
    """Get the source sync record"""
    params = {
        "id": sync_record_id,
        "connection_id": connection_id,
        "company_id": company_id,
    }
    try:
        sync_records = SyncRecordCompanyIdIndex.list(params, limit=1000)['Items']
        if not sync_records:
            raise NotFoundRequest(f"Source sync record not found for id: {sync_record_id}")
        return sync_records[0]
    except Exception as e:
        raise NotFoundRequest(f"Error retrieving sync record: {str(e)}")


def map_variants(is_extra_dest_mapping: bool, extra_mapping_data: dict, source_variants: List[dict]) -> dict:
    if not is_extra_dest_mapping:
        return extra_mapping_data

    source_variant_skus = {variant.get('sku') for variant in source_variants if variant.get('sku')}
    mapped_variant_skus = set(extra_mapping_data.keys())
    unmapped_variant_skus = source_variant_skus - mapped_variant_skus

    for sku in unmapped_variant_skus:
        if sku not in extra_mapping_data:
            extra_mapping_data[sku] = sku

    return extra_mapping_data
