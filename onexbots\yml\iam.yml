role:
    statements:
        - Effect: Allow
          Action:
              - lambda:InvokeFunction
          Resource: "*"
        - Effect: Allow
          Action:
              - cognito-idp:AdminListGroupsForUser
              - cognito-idp:ListUsers
              - cognito-idp:AdminCreateUser
              - cognito-idp:AdminAddUserToGroup
              - cognito-idp:ListUsersInGroup
              - cognito-idp:CreateGroup
              - cognito-idp:AdminSetUserPassword
              - cognito-idp:AdminUpdateUserAttributes
          Resource:
              - "arn:aws:cognito-idp:${opt:region, self:provider.region}:*:userpool/ap-southeast-1_MfCAeQm09"
        - Effect: Allow
          Action:
              - "s3:PutObject"
              - "s3:GetObject"
              - "s3:DeleteObject"
              - "s3:ListBucket"
              - "s3:ListObjectsV2"
              - "s3:PutObjectAcl"
              - "s3:CreateMultipartUpload"
              - "s3:CompleteMultipartUpload"
          Resource:
              - "arn:aws:s3:::${param:bucket_name}/*"
              - "arn:aws:s3:::${param:bucket_name}"
              - "arn:aws:s3:::${self:service}-${self:provider.stage}-knowledge-bucket/*"
              - "arn:aws:s3:::${self:service}-${self:provider.stage}-knowledge-bucket"
        - Effect: Allow
          Action:
              - dynamodb:Get*
              - dynamodb:PutItem
              - dynamodb:UpdateItem
              - dynamodb:DeleteItem
              - dynamodb:Scan
              - dynamodb:Query
              - dynamodb:BatchWriteItem
          Resource:
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/department"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/virtualStaff"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/virtualStaff/index/virtualStaffByDepartmentIndex"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/task"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/taskExecution"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/taskExecution/index/taskExecutionByVirtualStaffIndex"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/conversation"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/conversation/index/conversationByCustomerAndStaffIdIndex"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/message"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/knowledge"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/feedback"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/customer"
              - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/image"
