from typing import Any, List
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class SplitTransformationHandler(BaseTransformationHandler):
    """Handler for split transformation"""
    
    config_definition = TransformationConfig(
        label="Split",
        description="Split text by delimiter",
        direction=TransformationDirection(
            title="Split Text",
            content="Split the value into multiple parts using a delimiter",
            example='"Hello,World" → ["Hello", "World"] (delimiter: ",")',
            value_type="Multiple Values"
        ),
        config_fields=[
            {
                "key": "delimiter",
                "label": "Delimiter",
                "type": "text",
                "placeholder": "Enter delimiter",
                "description": "The character or text to split by"
            }
        ]
    )
    
    def handle_transform(self, value: Any) -> List[str]:
        """Splits the value by delimiter if it's not None"""
        if value is None:
            return []
            
        if not self.transformation.config or 'delimiter' not in self.transformation.config:
            return [str(value)]
            
        delimiter = self.transformation.config['delimiter']
        return str(value).split(delimiter) 