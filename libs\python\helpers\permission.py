from copy import copy

permissions = {
    'order': {
        '_list': {
            'description': '<PERSON>em danh sách đơn hàng',
            'active': False
        },
        '_list_by_customer_id': {
            'description': 'Xem danh sách đơn hàng theo mã khách hàng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết đơn hàng',
            'active': False
        },
        'add': {
            'description': 'Thêm đơn hàng',
            'active': False
        },
        'update': {
            'description': 'Sửa đơn hàng',
            'active': False
        },
        'delete': {
            'description': '<PERSON><PERSON>a đơn đơn hàng',
            'active': False
        },
        'confirm_api': {
            'description': '<PERSON>ác nhận trạng thái đơn hàng',
            'active': False
        },
        'pay_api': {
            'description': '<PERSON>ác nhận trạng thái thanh toán đơn hàng',
            'active': False
        },
        'pay_pos_api': {
            'description': '<PERSON>h toán và hoàn thành đơn hàng',
            'active': False
        },
        'request_cancel_api': {
            'description': 'Xác nhận trạng thái hủy một phần đơn hàng',
            'active': False
        },
        'pack_api': {
            'description': 'Xác nhận trạng thái đóng gói đơn hàng và tạo gói hàng',
            'active': False
        },
        'order_process': {
            'description': 'Quản lý trạng thái đơn hàng',
            'active': False
        },
    },
    'return_order': {
        '_list': {
            'description': 'Xem danh sách đơn trả hàng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết đơn trả hàng',
            'active': False
        },
        'add': {
            'description': 'Thêm đơn trả hàng',
            'active': False
        },
        'pay_api': {
            'description': 'Thanh toán đơn trả hàng',
            'active': False
        },
        'receive_api': {
            'description': 'Nhận đơn trả hàng',
            'active': False
        },
        'cancel_api': {
            'description': 'Huỷ đơn trả hàng',
            'active': False
        },
    },
    'stock_adjustment': {
        '_list': {
            'description': 'Xem danh sách phiếu điều chỉnh kho',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết phiếu điều chỉnh kho',
            'active': False
        },
        'add': {
            'description': 'Thêm phiếu điều chỉnh kho',
            'active': False
        },
        'update': {
            'description': 'Sửa phiếu điều chỉnh kho',
            'active': False
        },
        'balance': {
            'description': 'Xác nhận trạng thái đã cân bằng cho phiếu điều chỉnh kho',
            'active': False
        },
        'delete': {
            'description': 'Xác nhận trạng thái đã xoá cho phiếu điều chỉnh kho',
            'active': False
        },
    },
    'stock_relocate': {
        '_list': {
            'description': 'Xem danh sách phiếu chuyển kho',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết phiếu chuyển kho',
            'active': False
        },
        'add': {
            'description': 'Thêm phiếu chuyển kho',
            'active': False
        },
        'update': {
            'description': 'Sửa phiếu chuyển kho',
            'active': False
        },
        'delivery': {
            'description': 'Xác nhận trạng thái đang vận chuyển cho phiếu chuyển kho',
            'active': False
        },
        'receive': {
            'description': 'Xác nhận trạng thái đã nhận cho phiếu chuyển kho',
            'active': False
        },
        'cancel': {
            'description': 'Xác nhận trạng thái đã huỷ cho phiếu chuyển kho',
            'active': False
        },
    },
    "package": {
        '_list': {
            'description': 'Xem danh sách gói hàng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết gói hàng',
            'active': False
        },
        'update': {
            'description': 'Chỉnh sửa gói hàng',
            'active': False
        },
        'sync': {
            'description': 'Đồng bộ gói hàng',
            'active': False
        },
        'cancel_api': {
            'description': 'Xác nhận trạng thái hủy gói hàng',
            'active': False
        },
        'await_packing_api': {
            'description': 'Xác nhận trạng thái chờ đóng gói hàng',
            'active': False
        },
        'pack_api': {
            'description': 'Xác nhận trạng thái đóng gói hàng',
            'active': False
        },
        'ready_api': {
            'description': 'Xác nhận trạng thái đã gói hàng',
            'active': False
        },
        'ship_api': {
            'description': 'Xác nhận trạng thái đang giao gói hàng',
            'active': False
        },
        'delivered_api': {
            'description': 'Xác nhận trạng thái đã gói hàng',
            'active': False
        },
        'return_api': {
            'description': 'Xác nhận trạng thái trả gói hàng',
            'active': False
        },
        'delivered_return_api': {
            'description': 'Xác nhận trạng thái đã trả gói hàng',
            'active': False
        },
        'blocking_api': {
            'description': 'Xác nhận trạng thái chờ giải quyết gói hàng',
            'active': False
        },
        'system_receive_package': {
            'description': 'Xác nhận trạng thái đã nhận hàng gói hàng',
            'active': False
        },
        'assign_staff_package': {
            'description': 'Gán nhân viên cho gói hàng',
            'active': False
        }
    },
    "product": {
        '_list': {
            'description': 'Xem danh sách sản phẩm',
            'active': False
        },
        'list_tags': {
            'description': 'Xem danh sách thẻ của sản phẩm',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết sản phẩm',
            'active': False
        },
        'get_by_slug': {
            'description': 'Xem chi tiết sản phẩm theo slug',
            'active': False
        },
        'add': {
            'description': 'Thêm sản phẩm',
            'active': False
        },
        'update': {
            'description': 'Sửa sản phẩm',
            'active': False
        },
        'delete': {
            'description': 'Xóa sản phẩm',
            'active': False
        }
    },
    "variant": {
        '_list': {
            'description': 'Xem danh sách biến thể sản phẩm',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết biến thể sản phẩm',
            'active': False
        },
        'get_by_slug': {
            'description': 'Xem chi tiết biến thể sản phẩm theo slug',
            'active': False
        },
        'add': {
            'description': 'Thêm biến thể sản phẩm',
            'active': False
        },
        'update': {
            'description': 'Sửa biến thể sản phẩm',
            'active': False
        },
        'delete': {
            'description': 'Xóa biến thể sản phẩm',
            'active': False
        }
    },
    "brand": {
        '_list': {
            'description': 'Xem danh sách thương hiệu',
            'active': False
        },
        'get': {
            'description': 'Xem chi thương hiệu',
            'active': False
        },
        'add': {
            'description': 'Thêm thương hiệu',
            'active': False
        },
        'update': {
            'description': 'Sửa thương hiệu',
            'active': False
        },
        'delete': {
            'description': 'Xóa thương hiệu',
            'active': False
        }
    },
    "category": {
        '_list': {
            'description': 'Xem danh sách danh mục',
            'active': False
        },
        'get': {
            'description': 'Xem chi chi tiết danh mục',
            'active': False
        },
        'get_by_slug': {
            'description': 'Xem chi chi tiết danh mục theo slug',
            'active': False
        },
        'add': {
            'description': 'Thêm danh mục',
            'active': False
        },
        'update': {
            'description': 'Sửa danh mục',
            'active': False
        },
        'delete': {
            'description': 'Xóa danh mục',
            'active': False
        }
    },
    "integration": {
        '_list': {
            'description': 'Xem danh sách kênh bán hàng',
            'active': False
        },
        'add': {
            'description': 'Thêm kênh bán hàng',
            'active': False
        },
        'update': {
            'description': 'Cài đặt kênh bán hàng',
            'active': False
        },
        'delete': {
            'description': 'Xóa kênh bán hàng',
            'active': False
        }
    },
    "shipping_provider": {
        '_list': {
            'description': 'Xem danh sách nhà vận chuyển',
            'active': False
        },
        'add': {
            'description': 'Thêm nhà vận chuyển',
            'active': False
        },
        'update': {
            'description': 'Sửa nhà vận chuyển',
            'active': False
        },
        'delete': {
            'description': 'Xóa nhà vận chuyển',
            'active': False
        }
    },
    "location": {
        '_list': {
            'description': 'Xem danh sách cửa hàng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết danh mục',
            'active': False
        },
        'add': {
            'description': 'Thêm cửa hàng',
            'active': False
        },
        'update': {
            'description': 'Sửa thông tin cửa hàng',
            'active': False
        },
        'delete': {
            'description': 'Xóa thông tin cửa hàng',
            'active': False
        }
    },
    "discount": {
        '_list': {
            'description': 'Xem danh sách chiết khấu',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết chiết khấu',
            'active': False
        },
        'add': {
            'description': 'Thêm chiết khấu',
            'active': False
        },
        'update': {
            'description': 'Sửa thông tin chiết khấu',
            'active': False
        },
        'delete': {
            'description': 'Xóa chiết khấu',
            'active': False
        }
    },
    "voucher": {
        '_list': {
            'description': 'Xem danh sách đợt phát hành mã',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết đợt phát hành mã',
            'active': False
        },
        'add': {
            'description': 'Thêm đợt phát hành mã',
            'active': False
        },
        'update': {
            'description': 'Sửa thông tin đợt phát hành mã',
            'active': False
        },
        'update_status': {
            'description': 'Thay đổi trạng thái đợt phát hành mã',
            'active': False
        }
    },
    "voucher_code": {
        '_list': {
            'description': 'Xem danh sách mã giảm giá',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết mã giảm giá',
            'active': False
        },
        'add': {
            'description': 'Thêm mã giảm giá',
            'active': False
        },
    },
    "settings": {
        'shop_info': {
            'description': 'Quản lý thông tin cửa hàng',
            'active': False
        },
        'price_groups': {
            'description': 'Quản lý giá của hàng',
            'active': False
        },
        'prints': {
            'description': 'Quản lý mẫu in',
            'active': False
        },
        'accounts': {
            'description': 'Quản lý nhân viên và phân quyền',
            'active': False
        },
        'theme_color': {
            'description': 'Quản lý màu chủ đề',
            'active': False
        },
        "activities": {
            'description': 'Quản lý lịch sử',
            'active': False
        }
    },
    "purchase_orders": {
        '_list': {
            'description': 'Xem danh sách đơn đặt hàng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết đơn đặt hàng',
            'active': False
        },
        'add': {
            'description': 'Thêm đơn đặt hàng',
            'active': False
        },
        'pay': {
            'description': 'Thanh toán đơn đặt hàng',
            'active': False
        },
        '_pay': {
            'description': 'Thanh toán một phần đơn đặt hàng',
            'active': False
        },
        'cancel': {
            'description': 'Huỷ đơn đặt hàng',
            'active': False
        },
        'return_products': {
            'description': 'Trả đơn đặt hàng',
            'active': False
        },
        'complete': {
            'description': 'Hoàn thành đơn đặt hàng',
            'active': False
        },
    },
    "supplier": {
        '_list': {
            'description': 'Xem danh sách nhà cung cấp',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết nhà cung cấp',
            'active': False
        },
        'add': {
            'description': 'Thêm nhà cung cấp',
            'active': False
        },
        'update': {
            'description': 'Sửa nhà cung cấp',
            'active': False
        },
    },
    "customer": {
        "dashboard": {
            'description': 'Xem tổng quan người dùng',
            'active': False
        },
        '_list': {
            'description': 'Xem danh sách người dùng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết người dùng',
            'active': False
        },
        'add': {
            'description': 'Thêm người dùng',
            'active': False
        },
        'update': {
            'description': 'Sửa người dùng',
            'active': False
        },
        'delete': {
            'description': 'Xóa người dùng',
            'active': False
        },
    },
    "customer_group": {
        '_list': {
            'description': 'Xem danh sách nhóm người dùng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết nhóm người dùng',
            'active': False
        },
        'add': {
            'description': 'Thêm nhóm người dùng',
            'active': False
        },
        'update': {
            'description': 'Sửa nhóm người dùng',
            'active': False
        },
        'delete': {
            'description': 'Xóa nhóm người dùng',
            'active': False
        },
    },
    "import": {
        "import_list": {
            'description': 'Quản lý nhập',
            'active': False
        },

    },
    "record": {
        "list_import_record": {
            'description': 'Quản lý nhập ghi',
            'active': False
        },
        "record_details": {
            'description': 'Chi tiết nhập ghi',
            'active': False
        }
    },
    "payment_methods": {
        '_list': {
            'description': 'Xem danh sách thanh toán',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết thanh toán',
            'active': False
        },
        'add': {
            'description': 'Thêm thanh toán',
            'active': False
        },
        'update': {
            'description': 'Sửa thanh toán',
            'active': False
        },
        'delete': {
            'description': 'Xóa thanh toán',
            'active': False
        },
    },
    "transaction": {
        '_list': {
            'description': 'Xem danh sách giao dịch',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết giao dịch',
            'active': False
        },
        'add': {
            'description': 'Thêm giao dịch',
            'active': False
        },
        'update': {
            'description': 'Sửa giao dịch',
            'active': False
        },
        'delete': {
            'description': 'Xóa giao dịch',
            'active': False
        },
    },
    "account": {
        '_list': {
            'description': 'Xem danh sách tài khoản',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết tài khoản',
            'active': False
        },
        'add': {
            'description': 'Thêm tài khoản',
            'active': False
        },
        'update': {
            'description': 'Sửa tài khoản',
            'active': False
        },
        'delete': {
            'description': 'Xóa thanh toán',
            'active': False
        },
    },
    "report": {
        'product_report_list': {
            'description': 'Xem báo cáo sản phẩm',
            'active': False
        },
    },
    "inventory_item": {
        '_list': {
            'description': 'Xem danh sách kho',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết kho',
            'active': False
        },
        'add': {
            'description': 'Thêm kho',
            'active': False
        },
        'update': {
            'description': 'Sửa kho',
            'active': False
        },
    },
    "blog": {
        '_list': {
            'description': 'Xem danh sách bài viết',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết bài viết',
            'active': False
        },
        'add': {
            'description': 'Thêm bài viết',
            'active': False
        },
        'update': {
            'description': 'Sửa bài viết',
            'active': False
        },
        'delete': {
            'description': 'Xóa bài viết',
            'active': False
        },
    },
    "locale_blog": {
        '_list': {
            'description': 'Xem danh sách bài viết',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết bài viết',
            'active': False
        },
        'add': {
            'description': 'Thêm bài viết',
            'active': False
        },
        'update': {
            'description': 'Sửa bài viết',
            'active': False
        },
        'delete': {
            'description': 'Xóa bài viết',
            'active': False
        },
    },
    "blog_category": {
        '_list': {
            'description': 'Xem danh sách danh mục bài viết',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết danh mục bài viết',
            'active': False
        },
        'add': {
            'description': 'Thêm danh mục bài viết',
            'active': False
        },
        'update': {
            'description': 'Sửa danh mục bài viết',
            'active': False
        },
        'delete': {
            'description': 'Xóa danh mục bài viết',
            'active': False
        },
    },
    "terminal": {
        '_list': {
            'description': 'Xem danh sách terminal',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết terminal',
            'active': False
        },
        'add': {
            'description': 'Thêm terminal',
            'active': False
        },
        'update': {
            'description': 'Sửa terminal',
            'active': False
        },
        'delete': {
            'description': 'Xóa terminal',
            'active': False
        },
    },
    "shift": {
        '_list': {
            'description': 'Xem danh sách ca làm việc',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết ca làm việc',
            'active': False
        },
        'add': {
            'description': 'Thêm ca làm việc',
            'active': False
        },
        'update': {
            'description': 'Sửa ca làm việc',
            'active': False
        },
        'close_shift': {
            'description': 'Đóng ca làm việc',
            'active': False
        },
        'check_opened_shift': {
            'description': 'Kiểm tra ca làm việc đang hoạt động',
            'active': False
        },
    },
    "reward": {
        '_list': {
            'description': 'Xem danh sách phần thưởng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết phần thưởng',
            'active': False
        },
        'add': {
            'description': 'Thêm phần thưởng',
            'active': False
        },
        'update': {
            'description': 'Sửa phần thưởng',
            'active': False
        },
        'delete': {
            'description': 'Xóa phần thưởng',
            'active': False
        },
    },
    "redeem": {
        '_list': {
            'description': 'Xem danh sách đổi thưởng',
            'active': False
        },
        'get': {
            'description': 'Xem chi tiết đổi thưởng',
            'active': False
        },
        'add': {
            'description': 'Thêm đổi thưởng',
            'active': False
        },
        'update': {
            'description': 'Sửa đổi thưởng',
            'active': False
        },
        'delete': {
            'description': 'Xóa đổi thưởng',
            'active': False
        },
    },
    "pos": {
        'pos': {
            'description': 'Giao diện POS',
            'active': False
        },
    },
    "loyalty_app": {
        'settings': {
            'description': 'Cấu hình cho loyalty app',
            'active': False
        },
    },
}

PLANS = {
    "POS": ['order', 'stock_adjustment', 'stock_relocate', 'product', 'variant', 'brand', 'category', 'location',
            'discount', 'voucher', 'voucher_code', 'settings', 'purchase_orders', 'supplier', 'customer',
            'customer_group', 'import', 'record', 'payment_methods', 'transaction', 'account', 'report',
            'inventory_item', 'terminal', 'shift', 'reward', 'redeem', 'pos'],
    "ECOMMERCE": ['order', 'return_order', 'stock_adjustment', 'stock_relocate', 'package', 'product', 'variant',
                  'brand', 'category', 'channels', 'sync_records', 'shipping_provider', 'location', 'discount',
                  'voucher', 'voucher_code', 'settings', 'purchase_orders', 'supplier', 'customer', 'customer_group',
                  'import', 'record', 'payment_methods', 'transaction', 'account', 'report', 'inventory_item', 'blog',
                  'blog_category', 'pos'],
    "OMNICHANNEL": ['order', 'return_order', 'stock_adjustment', 'stock_relocate', 'package', 'product', 'variant',
                    'brand', 'category', 'channels', 'sync_records', 'shipping_provider', 'location', 'discount',
                    'voucher', 'voucher_code', 'settings', 'purchase_orders', 'supplier', 'customer', 'customer_group',
                    'import', 'record', 'payment_methods', 'transaction', 'account', 'report', 'inventory_item', 'blog',
                    'blog_category', 'terminal', 'shift', 'reward', 'redeem', 'pos', 'loyalty_app', 'locale_blog',
                    'notification']
}


def activate_permissions(plan_keys):
    permission_obj = copy(permissions)
    for key in plan_keys:
        if key in permission_obj:
            for action in permission_obj[key].values():
                action['active'] = True
    return permission_obj
