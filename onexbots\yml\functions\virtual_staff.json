{"create_virtual_staff": {"POST": [{"name": "Create virtual staff", "body": {"name": "Test Virtual Staff", "department_id": "test-department-id", "role": "VIRTUAL_STAFF", "image": "test_image", "skills": ["customer service", "sales"], "configuration": {"mcp": {"enabled": false}, "knowledge_base": {"enabled": false}, "interaction_style": {"tone": "professional", "language": "en", "response_length": "MEDIUM", "trait": "helpful", "ethical_constraints": true}}}}]}, "get_virtual_staff": {"GET": [{"name": "Get virtual staff by ID", "path_params": {"id": "staff-id-123"}}]}, "list_virtual_staffs": {"GET": [{"name": "List all virtual staffs", "query_params": {"page": "0", "limit": "20"}}]}, "update_virtual_staff": {"PUT": [{"name": "Update virtual staff", "path_params": {"id": "staff-id-123"}, "body": {"STYLES": {"configuration": {"personality": {"tone": "professional but friendly", "language": "en-US", "personal_trait": {"formality": 70, "detailed": 60, "creativity": 40}}}}, "KNOWLEDGE": {"configuration": {"knowledge_base": {"knowledge_ids": ["product-guide", "faq"], "domain_expertise_ids": ["customer-service"]}}}, "TASK": {"task_ids": ["task1", "task2"]}, "SKILL": {"name": "Updated Virtual Staff", "skills": ["customer service", "sales", "support"]}}}]}, "delete_virtual_staff": {"DELETE": [{"name": "Delete virtual staff", "path_params": {"id": "staff-id-123"}}]}}