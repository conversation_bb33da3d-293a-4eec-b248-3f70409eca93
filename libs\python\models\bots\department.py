from dataclasses import dataclass
from marshmallow import fields

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


@dataclass
class DepartmentAttributes(BasicAttributes):
    name: str = None
    description: str = None


class DepartmentAttributesSchema(BasicAttributesSchema):
    name = fields.Str(required=True)
    description = fields.Str(required=False, allow_none=True)

class DepartmentModel(BasicModel):
    table_name = 'department'
    attributes: DepartmentAttributes
    attributes_schema = DepartmentAttributesSchema
    attributes_class = DepartmentAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'description': 'description',
        }
    }
