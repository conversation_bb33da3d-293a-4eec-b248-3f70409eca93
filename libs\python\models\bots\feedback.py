from dataclasses import dataclass
from typing import Dict, Any

from marshmallow import fields

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


@dataclass
class FeedbackAttributes(BasicAttributes):
    message_id: str = None
    score: float = None
    meta_data: Dict[str, Any] = None


class FeedbackAttributesSchema(BasicAttributesSchema):
    message_id = fields.Str(required=True)
    score = fields.Float(required=True)
    meta_data = fields.Dict(keys=fields.Str(), values=fields.Raw(), allow_none=True)


class FeedbackModel(BasicModel):
    key__id = 'id'
    table_name = 'feedback'
    attributes: FeedbackAttributes
    attributes_schema = FeedbackAttributesSchema
    attributes_class = FeedbackAttributes

    query_mapping = {
        'query': ['id', 'message_id'],
        'filter': {
            'id': 'query',
            'message_id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
    }
