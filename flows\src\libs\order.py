import os

from models.order.order import OrderModel
from .helper import get_time_period

RESOURCE_SERVICE = os.getenv('RESOURCE_SERVICE')


def sale_report(company_id, start_date, end_date, period, location_ids=None, category_ids=None):
    filters = [
        {
            "term": {
                "company_id.keyword": company_id
            }
        },
        {"range": {"created_at": {"gte": start_date, "lte": end_date}}}
    ]
    # Apply nested query for location_ids if they are specified
    if location_ids:
        location_filter = {
            "nested": {
                "path": "order_line_items",
                "query": {
                    "terms": {
                        "order_line_items.location.id.keyword": location_ids
                    }
                }
            }
        }
        filters.append(location_filter)

    # Apply nested query for category_ids if they are specified
    if category_ids:
        category_filter = {
            "nested": {
                "path": "order_line_items",
                "query": {
                    "terms": {
                        "order_line_items.category.id.keyword": category_ids
                    }
                }
            }
        }
        filters.append(category_filter)
    # Construct the Elasticsearch query
    query = {
        "size": 0,
        "query": {
            "bool": {
                "filter": filters
            }
        },
        "aggs": {
            "sales_over_time": {
                "date_histogram": {
                    "field": "created_at",
                    "calendar_interval": period,
                    "format": "yyyy-MM-dd"
                },
                "aggs": {
                    "order_line_items_nested": {
                        "nested": {
                            "path": "order_line_items"
                        },
                        "aggs": {
                            "total_quantity_sold": {
                                "sum": {
                                    "field": "order_line_items.quantity"
                                }
                            },
                            "average_sale_price": {
                                "avg": {
                                    "field": "order_line_items.sale_price"
                                }
                            },
                            "average_discount": {
                                "avg": {
                                    "field": "order_line_items.discount"
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    # Execute the query
    response = OrderModel.report(query, service=RESOURCE_SERVICE)

    sale_histogram = []
    # Placeholder for cumulative quantity calculation
    cumulative_quantity = 0
    # Print the results
    for bucket in response['aggregations']['sales_over_time']['buckets']:
        # Extract total quantity sold, average sale price, and average discount for the current bucket
        total_quantity_sold = bucket['order_line_items_nested']['total_quantity_sold']['value']
        average_sale_price = bucket['order_line_items_nested']['average_sale_price']['value']
        average_discount = bucket['order_line_items_nested']['average_discount']['value']

        # Update cumulative quantity
        cumulative_quantity += total_quantity_sold
        sale_histogram.append({
            'period': bucket['key_as_string'],
            'sold_units': total_quantity_sold,
            'cumulative_quantity': cumulative_quantity,
            'average_sale_price': average_sale_price,
            'average_discount': average_discount
        })
    return sale_histogram


def report_order_numbers_and_revenue(company_id, period, locations=None, source=None, staff_id=None, from_date=None, to_date=None):
    # Get the time period for the query
    time_from, time_to, _, _ = get_time_period(period, from_date, to_date)

    filters = [
        {
            "term": {
                "company_id.keyword": company_id
            }
        },
        {"range": {"created_at": {"gte": time_from, "lte": time_to}}}
    ]
    if staff_id:
        filters.append({"match": {"staff_id.keyword": staff_id}})
    if source:
        filters.append({"match": {"source.keyword": source}})
    if locations:
        filters.append({"nested": {
            "path": "order_line_items",
            "query": {
                "bool": {
                    "must": [
                        {"match": {"order_line_items.location.name.keyword": locations}}
                    ]
                }
            }
        }})

    # Construct the search query
    query = {
        "size": 0,
        "query": {
            "bool": {
                "must": filters
            }
        },
        "aggs": {
            "status_group": {
                "terms": {
                    "field": "status.keyword"
                },
                "aggs": {
                    "total_revenue": {
                        "sum": {
                            "field": "total"
                        }
                    },
                    "order_count": {
                        "value_count": {
                            "field": "status.keyword"
                        }
                    }
                }
            }
        }
    }

    # Perform the search
    response = OrderModel.report(query, service=RESOURCE_SERVICE)  # Confirm correct index name

    # Extract the results
    status_aggregation = response['aggregations']['status_group']['buckets']
    results = {}
    for status_bucket in status_aggregation:
        status = status_bucket['key']
        total_revenue = status_bucket['total_revenue']['value']
        order_count = status_bucket['order_count']['value']
        results[status] = {
            'total_revenue': total_revenue,
            'order_count': order_count
        }

    return results
