import base64
import json
import re
from urllib.parse import quote_plus, unquote_plus
import uuid
from decimal import Decimal
import zlib

import pendulum
import unicodedata
from nolicore.utils.exceptions import BadRequest
from nolicore.utils.utils import json_dumps

DEFAULT_MIN_DATE = pendulum.from_timestamp(0).isoformat()


def handle_pagination_value(items, limit, page):
    try:
        new_limit = Decimal(limit)
        if new_limit != int(new_limit):
            raise BadRequest("Invalid limit")
    except (TypeError, ValueError):
        raise BadRequest("Invalid limit")

    try:
        new_page = Decimal(page)
        if new_page != int(new_page):
            raise BadRequest("Invalid page")
    except (TypeError, ValueError):
        raise BadRequest("Invalid page")

    total_results = len(items)
    if total_results == 0:
        return {
            'total': 0,
            'page': 0,
            'limit': new_limit,
            'items': []
        }

    total_pages = total_results // new_limit
    if total_results % new_limit == 0:
        total_pages -= 1
    if new_page > total_pages or total_pages < 0:
        new_page = 0
    start_index = int(new_page * new_limit)
    end_index = int(start_index + new_limit)
    return {
        'total': total_results,
        'page': new_page,
        'limit': new_limit,
        'items': items[start_index: end_index]
    }


def is_valid_uuid(val, version):
    if not isinstance(val, str):
        return False
    try:
        # Convert string to UUID object
        uuid_obj = uuid.UUID(val)
        # Check if the UUID version matches the required version
        return uuid_obj.version == version
    except (ValueError, AttributeError, TypeError):
        # If conversion fails, it's not a valid UUID
        return False


def convert_to_kebab_case(text):
    # Remove diacritics (Vietnamese accents)
    text = unicodedata.normalize('NFKD', text).encode('ASCII', 'ignore').decode('ASCII')

    # Convert to uppercase and replace non-alphanumeric with space
    text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text.upper())

    # Replace multiple spaces with single hyphen
    text = re.sub(r'\s+', '-', text.strip())

    return text


def format_date_from_iso8601_string(iso8601_string, format_type="YYYY-MM-DD"):
    dt = pendulum.parse(iso8601_string)
    # Format to "YYYY-MM-DD"
    formatted_date = dt.format(format_type)
    return formatted_date


def get_default_source():
    return {
        'channel_name': 'optiwarehouse',
        'id': "OPTIWAREHOUSE"
    }


def safe_compress(data):
    # Convert to JSON and compress
    state_json = json_dumps(data)
    compressed = zlib.compress(state_json.encode())

    # Base64 encode and make URL safe
    state_encoded = base64.urlsafe_b64encode(compressed).decode()
    return quote_plus(state_encoded)


def safe_decompress(data):
    # Decode from URL safe base64
    state_encoded = unquote_plus(data)
    compressed = base64.urlsafe_b64decode(state_encoded.encode())
    return json.loads(zlib.decompress(compressed).decode())
