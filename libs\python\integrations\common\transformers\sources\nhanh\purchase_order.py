import logging
import uuid
from typing import Dict, Any

import pendulum

from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.transformers.base import SourceTransformer
from models.common.status import Status, PurchaseOrderType
from models.purchase_order.purchase_order import PurchaseOrderSchema

logger = logging.getLogger(__name__)


class NhanhPurchaseOrderTransformer(SourceTransformer):
    channel_name = 'nhanh'
    object_type = ActionGroup.purchase_order.value

    def _map_object_type_to_action_type(self) -> str:
        return ActionType.get_purchase_order.value

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.purchase_order

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)

            location_mapping = self.connection_settings.get('dynamic_settings', {}).get('location_mapping', {}).get(
                'mapping', {})
            location_external = self.connection_settings.get('dynamic_settings', {}).get('location_mapping', {}).get(
                'external', [])
            location_id = location_mapping.get(str(data['depotId']))
            staff_id = self.dynamic_settings.get('staff_mapping', {}).get("staff_id")

            location = next((item for item in location_external if str(item['id']) == str(data['depotId'])), {})

            origin_id = str(data['depotId'])
            origin_name = location.get('name') or 'Supplier'

            supplier_name = data['supplierName']

            address = {
                "address2": None,
                "province_code": None,
                "country": "",
                "province": "",
                "name": supplier_name,
                "last_name": None,
                "country_code": "",
                "address1": "",
                "first_name": supplier_name,
                "default_billing": False,
                "default_shipping": False,
                "phone": "",
                "district": "",
                "city": "",
                "zip": None,
                "ward": ""
            }

            created_at = pendulum.from_format(data['createdDateTime'], 'YYYY-MM-DD HH:mm:ss',
                                              tz="Asia/Bangkok").to_iso8601_string()
            updated_at = created_at
            order_line_items = [self._transform_order_line_items(p, location_id) for p in
                                data['products'].values()]
            sub_total = sum(
                [float(item['quantity']) * float(item['sale_price']) for item in
                 order_line_items])
            external_id = str(data['id'])
            user_id = staff_id or self.connection_settings.get('company_id') or str(uuid.uuid4())
            # Transform Nhanh order data to master format
            transformed_data = {
                "external_id": external_id,
                "supplier": {
                    "addresses": [address],
                    "company_name": "",
                    "billing_address": address,
                    "last_name": None,
                    "first_name": supplier_name,
                    "name": supplier_name,
                    "email": None,
                    "phone": None,
                    "tags": None
                },
                # "status": map_status(data.get('statusCode')),
                "status": Status.COMPLETED.value,
                "purchase_type": PurchaseOrderType.IMPORTED.value,
                "location_id": location_id,
                "staff_id": staff_id,
                "source": {
                    "id": origin_id,
                    "name": origin_name,
                    "origin_id": origin_id,
                    "origin": origin_name
                },
                "order_line_items": order_line_items,
                "note": data['description'],
                "tags": data['tags'].join() if data['tags'] else None,
                "total": data['money'],
                "sub_total": sub_total,
                "discount": data['discount'],
                "payments": None,
                "created_at": created_at,
                "updated_at": updated_at,
                "user": {
                    "id": user_id,
                    "name_staff": "ONEXAPIS"
                }
            }

            # Output validation
            self.validate_output(transformed_data)
            return transformed_data
        except KeyError as e:
            logger.error(f"Missing required field in Nhanh order data: {str(e)}")
            raise ValueError(f"Missing required field in Nhanh order data: {str(e)}")
        except Exception as e:
            logger.error(f"Error transforming Nhanh order data: {str(e)}")
            raise

    def _transform_order_line_items(self, data: Dict[str, Any], location_id) -> Dict[str, Any]:
        """Convert product format"""
        sku = data['code']
        name = data['name']
        image_url = None
        discount = float(data['discount']) / float(data['quantity'])
        return {
            "unit_price": data['price'],
            "sale_price": float(data['price']) - discount,
            "discount": discount,
            "sku": sku,
            "name": name,
            "variant_name": name,
            "image_url": image_url,
            "quantity": data['quantity'],
            "product_id": "",  # Not available in Nhanh data
            "variant_id": "",  # Not available in Nhanh data
            "location": {
                "id": location_id,
            }
        }

    def validate_input(self, data: Dict[str, Any]):
        # Implement input validation logic using self.input_schema
        pass

    def validate_output(self, data: Dict[str, Any]):
        # Implement output validation logic using self.output_schema
        pass

    @property
    def input_schema(self, transform_type='bill'):
        # This should reflect the Nhanh API order schema
        return {
            'type': 'object',
            'properties': {
                'id': {'type': 'integer'},
                'privateId': {'type': 'string'},
                'shopOrderId': {'type': 'string'},
                'channel': {'type': 'integer'},
                'ecomShopId': {'type': 'string'},
                'saleChannel': {'type': 'integer'},
                'merchantTrackingNumber': {'type': ['string', 'null']},
                'depotId': {'type': 'integer'},
                'handoverId': {'type': ['string', 'null']},
                'depotName': {'type': 'string'},
                'type': {'type': 'string'},
                'typeId': {'type': 'integer'},
                'moneyDiscount': {'type': 'number'},
                'moneyDeposit': {'type': 'number'},
                'moneyTransfer': {'type': 'number'},
                'depositAccount': {'type': 'string'},
                'transferAccount': {'type': 'string'},
                'usedPoints': {'type': 'integer'},
                'moneyUsedPoints': {'type': 'number'},
                'carrierId': {'type': 'integer'},
                'carrierName': {'type': 'string'},
                'serviceId': {'type': ['integer', 'null']},
                'serviceName': {'type': 'string'},
                'carrierCode': {'type': ['string', 'null']},
                'shipFee': {'type': 'number'},
                'codFee': {'type': 'number'},
                'customerShipFee': {'type': 'number'},
                'returnFee': {'type': 'number'},
                'overWeightShipFee': {'type': 'number'},
                'declaredFee': {'type': 'number'},
                'description': {'type': 'string'},
                'privateDescription': {'type': 'string'},
                'customerId': {'type': ['integer', 'null']},
                'customerName': {'type': 'string'},
                'customerEmail': {'type': ['string', 'null']},
                'customerMobile': {'type': ['string', 'null']},
                'customerAddress': {'type': 'string'},
                'shipToCityLocationId': {'type': 'integer'},
                'shipToDistrictLocationId': {'type': 'string'},
                'customerCityId': {'type': 'integer'},
                'customerCity': {'type': 'string'},
                'customerDistrictId': {'type': 'integer'},
                'customerDistrict': {'type': 'string'},
                'shipToWardLocationId': {'type': ['string', 'null']},
                'customerWard': {'type': ['string', 'null']},
                'createdById': {'type': ['integer', 'null']},
                'createdByUserName': {'type': 'string'},
                'createdByName': {'type': 'string'},
                'saleId': {'type': ['integer', 'null']},
                'saleName': {'type': 'string'},
                'createdDateTime': {'type': 'string'},
                'deliveryDate': {'type': ['string', 'null']},
                'sendCarrierDate': {'type': ['string', 'null']},
                'statusName': {'type': 'string'},
                'statusCode': {'type': 'string'},
                'calcTotalMoney': {'type': 'number'},
                'trafficSourceId': {'type': ['integer', 'null']},
                'trafficSourceName': {'type': 'string'},
                'affiliateCode': {'type': 'string'},
                'affiliateBonusCash': {'type': 'number'},
                'affiliateBonusPercent': {'type': 'number'},
                'products': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'productId': {'type': 'string'},
                            'productName': {'type': 'string'},
                            'productCode': {'type': 'string'},
                            'productBarcode': {'type': ['string', 'null']},
                            'productImage': {'type': 'string'},
                            'price': {'type': 'string'},
                            'quantity': {'type': 'string'},
                            'weight': {'type': ['string', 'null']},
                            'vat': {'type': 'number'},
                            'discount': {'type': 'string'},
                            'description': {'type': 'string'},
                            'imei': {'type': 'string'},
                            'giftProducts': {'type': 'array'},
                            'batch': {'type': 'array'}
                        }
                    }
                },
                'tags': {'type': 'array', 'items': {'type': 'string'}},
                'couponCode': {'type': ['string', 'null']},
                'returnFromOrderId': {'type': ['string', 'null']},
                'utmSource': {'type': 'string'},
                'utmMedium': {'type': 'string'},
                'utmCampaign': {'type': 'string'},
                'facebook': {
                    'type': 'object',
                    'properties': {
                        'fanpageId': {'type': ['string', 'null']},
                        'adId': {'type': ['string', 'null']}
                    }
                }
            }
        }

    @property
    def output_schema(self):
        return PurchaseOrderSchema().dump(PurchaseOrderSchema())


def map_status(nhanh_status: str) -> str:
    """Map Nhanh status to the corresponding status in the enum."""
    status_map = {
        "New": Status.DRAFT,  # Đơn mới
        "Confirming": Status.PENDING,  # Đang xác nhận
        "CustomerConfirming": Status.PENDING,  # Chờ khách xác nhận
        "Confirmed": Status.READY,  # Đã xác nhận
        "Packing": Status.PACKING,  # Đang đóng gói
        "Packed": Status.READY,  # Đã đóng gói
        "ChangeDepot": Status.BLOCKING,  # Đổi kho xuất hàng
        "Pickup": Status.AWAIT_PACKING,  # Chờ thu gom
        "Shipping": Status.SHIPPING,  # Đang chuyển
        "Success": Status.COMPLETED,  # Thành công
        "Failed": Status.CANCELLED,  # Thất bại
        "Canceled": Status.CANCELLED,  # Khách hủy
        "Aborted": Status.CANCELLED,  # Hệ thống hủy
        "CarrierCanceled": Status.CANCELLED,  # Hãng vận chuyển hủy đơn
        "SoldOut": Status.CANCELLED,  # Hết hàng
        "Returning": Status.RETURNING,  # Đang chuyển hoàn
        "Returned": Status.RETURNED  # Đã chuyển hoàn
    }
    return status_map.get(nhanh_status, Status.DRAFT).value
