import os

from helpers.lambda_client import lambda_client_invoke_async
from nolicore.utils.utils import logger

from models.integration.destination_data import DestinationDataModel

ENV = os.getenv('ENV')


def invoke_import_destination_data(request_id, company_id, connection_id, group, start=0):
    payload = {
        'request_id': request_id,
        'company_id': str(company_id),
        'start': start,
        'connection_id': connection_id,
        'group': group
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-flow-service-{ENV}-destination_data_handle')


def sync_destination_data(company_id, standard_data, connection_id, group):
    logger.info({
        'company_id': str(company_id),
        'standard_data': standard_data,
        'connection_id': connection_id,
        'group': group
    })
    extra = None
    if group in ['order', 'purchase_order', 'return_order']:
        extra = standard_data.get('order_number', None)
    elif group == 'product':
        extra = standard_data.get('sku', None)
    new_destination_data = {
        'id': standard_data['id'],
        'name': standard_data.get('title'),
        'extra': extra,
        'standard_data': standard_data,
        'external_updated_at': standard_data['updated_at'],
        'external_created_at': standard_data['created_at']
    }
    destination_data_obj = DestinationDataModel.put(company_id, connection_id, group, new_destination_data)
    return destination_data_obj.attributes_dict
