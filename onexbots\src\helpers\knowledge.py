import os
from typing import Dict, Any, List
from uuid import uuid4

from helpers.lambda_client import lambda_client_invoke_async
from models.bots.knowledge import KnowledgeModel, Status, Source
from ..helpers.file_uploader import FileService
from models.bots.virtual_staff import VirtualStaffModel
from ..helpers.common import deep_update
ENV = os.getenv('ENV')

file_service = FileService()


def generate_s3_key(file_name: str, company_id) -> str:
    return f"knowledge/{company_id}/{file_name}"


def create_knowledge(company_id: str, content: str, knowledge_name: str = None,
                     source: Source = Source.DIRECT_TEXT.value, meta_data: Dict[str, Any] = None, s3_key: str = None, staff_id: str = None) -> Dict[str, Any]:
    # Generate unique filename
    if not s3_key:
        knowledge_id = str(uuid4())
        s3_file_name = f"{knowledge_id}.txt" if source == Source.DIRECT_TEXT.value else f"{knowledge_id}.url"
        s3_key = generate_s3_key(s3_file_name, company_id)
        # Upload file
        file_service.upload_file({
            "key": s3_key,
            "body": content
        })
    else:
        _, company_id, knowledge_file = s3_key.split('/')
        knowledge_id = knowledge_file.split('.')[0]
    virtual_staff_ids = []
    if staff_id:
        virtual_staff_ids.append(staff_id)
    knowledge_data = {
        "id": knowledge_id,
        "s3_key": s3_key,
        "name": knowledge_name if knowledge_name else content,
        "virtual_staff_ids": virtual_staff_ids,
        "status": Status.PENDING,
        "source": source,
        "meta_data": meta_data
    }
    if meta_data is not None and meta_data.get('size'):
        knowledge_data['size'] = meta_data.get('size')

    try:
        knowledge = KnowledgeModel.create(company_id, knowledge_data)
        # Update VirtualStaffModel to add this knowledge_id
        if staff_id:
            staff = VirtualStaffModel.by_key({'company_id': company_id, 'id': staff_id})
            if staff:
                staff_attrs = staff.attributes_dict
                config = staff_attrs.get('configuration', {})
                kb = config.get('knowledge_base', {})
                knowledge_ids = kb.get('knowledge_ids', []) or []
                if knowledge_id not in knowledge_ids:
                    knowledge_ids.append(knowledge_id)
                # Rebuild the nested structure
                updated_data = {
                    'configuration': {
                        **config,
                        'knowledge_base': {
                            **kb,
                            'knowledge_ids': knowledge_ids
                        }
                    }
                }
                # Use deep_update to merge
                updated_data = deep_update(staff_attrs, updated_data)
                staff.update(updated_data)
    except Exception as e:
        file_service.delete_file(s3_key)
        raise e

    return knowledge.attributes_dict


def invoke_remove_staff_from_knowledge(
        company_id: str,
        staff_id: str,
        knowledge_ids: List[str]
) -> None:
    """
    Remove virtual staff ID from knowledge records' virtual_staff_ids.

    Args:
        company_id (str): Company ID
        staff_id (str): Staff ID to remove
        knowledge_ids (List[str]): List of knowledge IDs to update
    """
    payload = {
        'staff_id': staff_id,
        'company_id': str(company_id),
        'knowledge_ids': knowledge_ids

    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-onexbots-service-{ENV}-removeStaffFromKnowledge')
