import unittest
from tests.base import BaseTestCase
from nolicore.utils.exceptions import NotFoundRequest, BadRequest

from models.bots.task import TaskModel
from onexbots.src.apis.task import (
    create_task,
    get_task,
    list_tasks,
    update_task,
    delete_task
)


class TestTask(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    def test_create_task(self):
        """Test creating a new task"""
        # Prepare test data
        test_data = {
            "name": "Test Task",
            "description": "Test task description",
            "prompt_content": "Test prompt content with [language]",
            "variables": {
                "language": "string",
            }
        }

        event = self.create_lambda_event(body=test_data, is_authorized=True)
        context = self.create_lambda_context()

        # Test successful creation
        response = create_task(event, context)

    def test_get_task(self):
        """Test getting a task by ID"""
        # First create a task
        test_data = {
            "name": "Test Task for Get",
            "description": "Test description",
            "prompt_content": "Test prompt",
            "variables": {"test": "value"}
        }
        create_event = self.create_lambda_event(body=test_data, is_authorized=True)
        created_task = create_task(create_event, self.create_lambda_context())
        task_id = created_task['data']['id']

        # Test getting the created task
        event = self.create_lambda_event(
            path_params={'id': task_id}
        )
        response = get_task(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Task retrieved successfully')
        self.assertEqual(response['data']['id'], task_id)

        # Test getting non-existent task
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'}
        )
        with self.assertRaises(NotFoundRequest):
            get_task(event, self.create_lambda_context())

    def test_list_tasks(self):
        """Test listing tasks"""
        # Create test tasks
        test_data = {
            "name": "Test Task for List",
            "description": "Test description",
            "prompt_content": "Test prompt",
            "variables": {"test": "value"}
        }
        create_event = self.create_lambda_event(body=test_data, is_authorized=True)
        create_task(create_event, self.create_lambda_context())

        # Test listing all tasks
        event = self.create_lambda_event(
            query_params={"page": "0", "limit": "20"},
            is_authorized=True
        )
        response = list_tasks(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Tasks retrieved successfully')
        self.assertIsInstance(response['data'], dict)
        self.assertIn('items', response['data'])

    def test_update_task(self):
        """Test updating a task"""
        # First create a task
        test_data = {
            "name": "Test Task for Update",
            "description": "Test description",
            "prompt_content": "Test prompt",
            "variables": {"test": "value"}
        }
        create_event = self.create_lambda_event(body=test_data, is_authorized=True)
        created_task = create_task(create_event, self.create_lambda_context())
        task_id = created_task['data']['id']

        # Test updating the task
        update_data = {
            "name": "Updated Task Name",
            "description": "Updated description"
        }
        event = self.create_lambda_event(
            path_params={'id': task_id},
            body={"data": update_data},
            is_authorized=True
        )
        response = update_task(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Task updated successfully')
        self.assertEqual(response['data']['name'], "Updated Task Name")
        self.assertEqual(response['data']['description'], "Updated description")

        # Test updating non-existent task
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'},
            body={"data": update_data}
        )
        with self.assertRaises(NotFoundRequest):
            update_task(event, self.create_lambda_context())

    def test_delete_task(self):
        """Test deleting a task"""
        # First create a task
        test_data = {
            "name": "Test Task for Delete",
            "description": "Test description",
            "prompt_content": "Test prompt",
            "variables": {"test": "value"}
        }
        create_event = self.create_lambda_event(body=test_data, is_authorized=True)
        created_task = create_task(create_event, self.create_lambda_context())
        task_id = created_task['data']['id']

        # Test deleting the task
        event = self.create_lambda_event(
            path_params={'id': task_id},
            is_authorized=True
        )
        response = delete_task(event, self.create_lambda_context())
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Task deleted successfully')

        # Verify the task is deleted
        with self.assertRaises(NotFoundRequest):
            get_task(event, self.create_lambda_context())

        # Test deleting non-existent task
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'}
        )
        with self.assertRaises(NotFoundRequest):
            delete_task(event, self.create_lambda_context())


if __name__ == '__main__':
    unittest.main()
