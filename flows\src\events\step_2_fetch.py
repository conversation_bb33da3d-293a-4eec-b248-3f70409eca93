import json
import os
import time

import pendulum
from nolicore.utils.aws.boto_helper import get_client
from nolicore.utils.aws.decorator import invoke
from nolicore.utils.aws.request import LambdaRequest
from nolicore.utils.utils import logger, json_dumps

from integrations.common.base_api_library import FetchAPIResponse, AbstractStandardObject
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.event import FetchEvent, EventSource, FetchEventStatus
from integrations.common.queue_helper import enqueue_fetch_event
from models.integration.connection import ActionKind, ConnectionModel
from models.integration.fetch_event import FetchEventModel

FETCH_QUEUE_URL = os.environ['FETCH_QUEUE_URL']
RAW_DATA_BUCKET = os.environ['RAW_DATA_BUCKET']
MAX_BATCH_SIZE = 50  # Maximum number of items to process in a single Lambda invocation
MAX_RETRIES = 3
RETRY_DELAY = 5

sqs = get_client('sqs')
s3 = get_client('s3')


@invoke()
def fetch_lambda_handler(lambda_request: LambdaRequest):
    for record in lambda_request.event['Records']:
        try:
            message = json.loads(record['body'])
            fetch_event = FetchEvent.from_dict(message)
            connection = get_connection_by_id(fetch_event.connection_id)
        except Exception as e:
            logger.exception(f"Error retrieving connection: {e}")
            continue

        channel = connection.get_channel()
        try:
            execute_channel_actions(channel, connection, fetch_event)
        except Exception as e:
            logger.exception(f"Error executing channel actions: {e}" + f" for event: {fetch_event.to_dict()}")
            # Handle error (e.g., move to dead-letter queue or implement other error handling)
            continue

    return {
        'statusCode': 200,
        'body': json_dumps('Fetch Lambda execution completed')
    }


def execute_channel_actions(channel, connection: ConnectionModel, fetch_event: FetchEvent):
    action_type = fetch_event.action_type
    fetch_settings = connection.get_fetch_action_settings(fetch_event.action_group, action_type)

    # Remove the early return if fetch_settings is None
    if not fetch_settings:
        print(f"Fetch settings not found for action: {action_type}")
        fetch_settings = {}  # Use an empty dict instead of returning

    # Convert fetch_settings to a dictionary if it's not already one
    if not isinstance(fetch_settings, dict):
        fetch_settings = fetch_settings.__dict__ if hasattr(fetch_settings, '__dict__') else {}

    new_continuation_token = fetch_and_process(
        channel, fetch_event, fetch_settings
    )

    # Update the connection's last fetch time and status for the event from scheduler only
    if fetch_event.event_source == EventSource.scheduler:
        connection.update_fetch_action_status(action_type, pendulum.now('UTC'))
        connection.save()

    # If there's more data to fetch, re-queue the event
    if new_continuation_token:
        re_queue_fetch_event(fetch_event, new_continuation_token, connection.attributes.company_id)


def fetch_and_process(channel, fetch_event: FetchEvent, fetch_settings):
    fetch_event_obj = FetchEventModel.by_key({
        FetchEventModel.key__id: str(fetch_event.id),
        "continuation_token": str(fetch_event.continuation_token)
    })
    items_processed = 0
    company_id = str(fetch_event.company_id)
    fetch_event.status = FetchEventStatus.PROCESSING
    fetch_event.retry_count = 0
    if fetch_event_obj is None:
        fetch_event_obj = FetchEventModel.create(company_id, fetch_event.to_dict())
    else:
        fetch_event_obj.update(fetch_event.to_dict())
    for attempt in range(MAX_RETRIES):
        try:
            # Use the meta from the fetch_event if available
            if fetch_event.meta:
                fetch_settings.update(fetch_event.meta.current_params)

            result: FetchAPIResponse = channel.execute_action(fetch_event.action_type, ActionKind.FETCH, fetch_settings,
                                                              fetch_event)
            if not result.success:
                logger.error(f"Fetch failed: {result.message}")
                raise Exception(f"Fetch failed: {result.message}")

            if result.data:
                for item in result.data:
                    store_raw_data(fetch_event.connection_id, fetch_event.action_type, item)
                    items_processed += 1
                    if items_processed > MAX_BATCH_SIZE:
                        logger.info(
                            f"Store raw data reach max batch size ({MAX_BATCH_SIZE}) for event: {fetch_event.to_dict()}")
                        break

                # Update fetch_event with the new meta information
                fetch_event.meta = result.meta
                fetch_event.status = FetchEventStatus.COMPLETED
                fetch_event.error_msg = None
            else:
                fetch_event.meta = result.meta
                fetch_event.status = FetchEventStatus.SKIPPED
                fetch_event.error_msg = "No Records"
            fetch_event_obj.update(fetch_event.to_dict())

            continuation_token = result.meta.continuation_token
            return continuation_token

        except Exception as e:
            logger.exception(f"Fetch attempt {attempt + 1} failed: {str(e)}" + f" for event: {fetch_event.to_dict()}")
            fetch_event.error_msg = f"Fetch attempt {attempt + 1} failed: {str(e)}"
            fetch_event.retry_count += 1
            if attempt == MAX_RETRIES - 1:
                fetch_event.status = FetchEventStatus.FAILED
            fetch_event_obj.update(fetch_event.to_dict())
            if attempt == MAX_RETRIES - 1:
                raise
            time.sleep(RETRY_DELAY * (2 ** attempt))  # Exponential backoff
    return None


def store_raw_data(connection_id: str, action_type: str, item: AbstractStandardObject):
    object_type = item.get_object_type()
    unique_identifier = item.get_unique_identifier()

    key = f"{connection_id}/{action_type}/{object_type}/{unique_identifier}.json"

    s3.put_object(
        Bucket=RAW_DATA_BUCKET,
        Key=key,
        Body=json_dumps(item.to_dict()),
        ContentType='application/json'
    )


def re_queue_fetch_event(fetch_event: FetchEvent, new_continuation_token: str, company_id: str):
    if fetch_event.continuation_token == new_continuation_token:
        logger.error(f"Invalid new continuation token")
        return
    fetch_event.continuation_token = new_continuation_token
    logger.info(f'requeue: {fetch_event}')
    enqueue_fetch_event(fetch_event, company_id)