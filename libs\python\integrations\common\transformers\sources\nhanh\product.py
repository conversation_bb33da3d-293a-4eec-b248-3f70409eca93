import logging
from typing import Dict, Any, List, Optional

from integrations.channels.action_types import ActionGroup
from integrations.common.transformers.base import SourceTransformer
from models.product.product import ProductAttributesSchema
from .inventory import NhanhInventoryTransformer

logger = logging.getLogger(__name__)


class NhanhProductTransformer(SourceTransformer):
    def __init__(self, connection_settings: Dict[str, Any]):
        super().__init__(connection_settings)
        self.inventory_transformer = NhanhInventoryTransformer(connection_settings)

    channel_name = 'nhanh'
    object_type = 'product'

    def _map_object_type_to_action_type(self) -> str:
        return 'get_product'

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)
            brand_mapping = self.connection_settings.get('dynamic_settings', {}).get('brand')

            # Find the parent product (with parentId is -2 or -1)
            parent_product = next((product for product in data.values() if product['parentId'] in [-1, -2, "", None]),
                                  None)
            if not parent_product:
                raise ValueError("No parent product found in the data")

            # Extract the first and second options from the parent product
            first_option = self._extract_option_value(parent_product, 0)
            first_option_title = self._extract_option_title(parent_product, 0)

            # Transform Nhanh product data to master format
            brand_name = parent_product['brandName'] or brand_mapping
            brand = {'name': brand_name} if brand_name else None
            parent_product_category = parent_product.get('category')
            category = {'name': parent_product_category.get('name', '')} if parent_product_category else None
            variants = self._transform_variants(data, first_option, first_option_title)
            transformed_data = {
                'name': parent_product['name'],
                'sku': parent_product['code'],
                'barcode': parent_product['barcode'],
                'publish': parent_product['status'] == 'Active',
                'description': parent_product['description'],
                'shortDescription': (parent_product['description'] or '')[:100] if parent_product[
                    'description'] else None,
                'tags': parent_product.get('tags', ''),
                'brand': brand,
                'category': category,
                'images': self._transform_images(parent_product['image'], parent_product.get('images', [])),
                'variants': variants,
                'options': self._transform_options(data),
                'measurements': {
                    'weight_value': float(parent_product.get('shippingWeight', 0)) if parent_product.get(
                        'shippingWeight') is not None else 0,
                    'weight_unit': 'g',
                    'width_value': float(parent_product.get('width', 0)) if parent_product.get(
                        'width') is not None else 0,
                    'width_unit': 'cm',
                    'length_value': float(parent_product.get('length', 0)) if parent_product.get(
                        'length') is not None else 0,
                    'length_unit': 'cm',
                    'height_value': float(parent_product.get('height', 0)) if parent_product.get(
                        'height') is not None else 0,
                    'height_unit': 'cm'
                } if len(variants) == 0 else None
            }

            # Output validation
            self.validate_output(transformed_data)

            return transformed_data
        except KeyError as e:
            logger.error(f"Missing required field in Nhanh product data: {str(e)}")
            raise ValueError(f"Missing required field in Nhanh product data: {str(e)}")
        except Exception as e:
            logger.error(f"Error transforming Nhanh product data: {str(e)}")
            raise

    def _transform_images(self, main_image: str, additional_images: List[str]) -> List[Dict[str, str]]:
        images = []
        if main_image:
            images.append(self._create_image_dict(main_image))
        for img in additional_images:
            if img:
                images.append(self._create_image_dict(img))
        return images

    def _create_image_dict(self, image_url: str) -> Dict[str, str]:
        image_name = image_url.split('/')[-1]
        return {
            'src': image_url,
            'name': image_name
        }

    def _transform_variants(self, data: Dict[str, Any], first_option: Optional[str],
                            first_option_title: Optional[str]) -> List[Dict[str, Any]]:
        variants = []
        for product in data.values():
            if product['parentId'] not in [-2, -1]:  # This is a variant
                if first_option:
                    variant_option1 = first_option
                    variant_option2 = self._extract_option_value(product, 0)
                    second_option_title = self._extract_option_title(product, 0)
                else:
                    variant_option1 = self._extract_option_value(product, 0)
                    first_option_title = self._extract_option_title(product, 0)
                    variant_option2 = None
                    second_option_title = None
                variant_name = str(variant_option1 + ' / ' + variant_option2) if variant_option2 else str(
                    variant_option1)
                variant = {
                    'name': variant_name,
                    'sku': product['code'],
                    'barcode': product['barcode'],
                    'prices': [
                        {
                            'price': int(product['price'] or 0),
                            'price_group': {'id': 'RETAILS'}
                        },
                        {
                            'price': int(product['importPrice'] or 0),
                            'price_group': {'id': 'COST'}
                        },
                    ],
                    'measurements': {
                        'weight_value': float(product.get('shippingWeight', 0)) if product.get(
                            'shippingWeight') is not None else 0,
                        'weight_unit': 'g',
                        'width_value': float(product.get('width', 0)) if product.get('width') is not None else 0,
                        'width_unit': 'cm',
                        'length_value': float(product.get('length', 0)) if product.get('length') is not None else 0,
                        'length_unit': 'cm',
                        'height_value': float(product.get('height', 0)) if product.get('height') is not None else 0,
                        'height_unit': 'cm'
                    },
                    'inventories': self.inventory_transformer.transform(product),
                    'option1': variant_option1,
                    'option2': variant_option2,
                    'option3': self._extract_option_value(product, 2),
                    'optionTitle1': first_option_title,
                    'optionTitle2': second_option_title,
                    'optionTitle3': self._extract_option_title(product, 2),
                    'images': self._transform_images(product['image'], product.get('images', [])),
                }
                variants.append(variant)
        return variants

    def _transform_options(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        options = {}
        option_value_mappings = self.dynamic_settings.get('optionValueMappings', {})
        for product in data.values():
            for attr in product.get('attributes', []):
                for attr_value in attr.values():
                    option_name = attr_value['attributeName']
                    option_value = option_value_mappings.get(attr_value['name'], attr_value['name'])
                    if option_name not in options:
                        options[option_name] = set()
                    options[option_name].add(option_value)

        return [{'name': name, 'values': list(values)} for name, values in options.items()]

    def _extract_option_value(self, product: Dict[str, Any], index: int) -> Optional[str]:
        option_value_mappings = self.dynamic_settings.get('optionValueMappings', {})
        if product.get('attributes') and len(product['attributes']) > index:
            attr_value = list(product['attributes'][index].values())[0]
            return option_value_mappings.get(attr_value['name'], attr_value['name'])
        return None

    def _extract_option_title(self, product: Dict[str, Any], index: int) -> Optional[str]:
        if product.get('attributes') and len(product['attributes']) > index:
            attr_value = list(product['attributes'][index].values())[0]
            return attr_value['attributeName']
        return None

    def validate_input(self, data: Dict[str, Any]):
        # Implement input validation logic using self.input_schema
        pass

    def validate_output(self, data: Dict[str, Any]):
        # Implement output validation logic using self.output_schema
        pass

    @property
    def input_schema(self):
        # This should reflect the Nhanh API product schema
        return {
            'type': 'object',
            'patternProperties': {
                '^\d+$': {
                    'type': 'object',
                    'properties': {
                        'idNhanh': {'type': 'integer'},
                        'parentId': {'type': 'integer'},
                        'name': {'type': 'string'},
                        'code': {'type': 'string'},
                        'barcode': {'type': ['string', 'null']},
                        'status': {'type': 'string'},
                        'description': {'type': ['string', 'null']},
                        'brandName': {'type': 'string'},
                        'categoryName': {'type': ['string', 'null']},
                        'images': {'type': 'array', 'items': {'type': 'string'}},
                        'price': {'type': 'string'},
                        'importPrice': {'type': 'string'},
                        'shippingWeight': {'type': ['string', 'null']},
                        'inventory': {
                            'type': 'object',
                            'properties': {
                                'available': {'type': 'integer'},
                                'depots': {'type': 'object'}
                            }
                        },
                        'attributes': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'patternProperties': {
                                    '^\d+$': {
                                        'type': 'object',
                                        'properties': {
                                            'attributeName': {'type': 'string'},
                                            'name': {'type': 'string'}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    @property
    def output_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.product
