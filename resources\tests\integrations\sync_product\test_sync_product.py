import unittest

from tests.base import BaseTestCase
from resources.src.integrations.sync_product import handle
from resources.tests.integrations.sync_product.sync_product_data import sync_product_event


class SyncProductTestCase(BaseTestCase):
    def test_sync_product(self):
        response = handle(sync_product_event, {})
        self.assertTrue(True)


if __name__ == '__main__':
    # begin the unittest.main()
    unittest.main()
