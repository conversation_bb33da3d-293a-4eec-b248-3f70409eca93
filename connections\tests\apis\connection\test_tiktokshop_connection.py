import json
import unittest

from tests.base import BaseTestCase

from connections.src.apis.setting import set_fetch_action_settings
from integrations.channels.action_types import ActionGroup, ActionType
from models.integration.connection import ConnectionModel

from connections.src.apis.connection import get_connection_types, oauth2_callback, test_connection
from connections.tests.apis.connection.data_test import call_back_post_event, \
    tiktokshop_query_params, tiktok_shop_test_connection
from integrations.channels.tiktokshop.tiktokshop_library import TiktokShopLibrary


class TestTiktokShopConnection(BaseTestCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.app_key = '6emv3rous6f2u'
        cls.app_secret = 'b2285d947e5f4e1795124406ab3e415393c5d855'
        cls.service_id = '7448925715311937285',
        cls.shop_id = '7496026029151128085'
        cls.region = 'non-us'
        cls.tiktokshop_library = TiktokShopLibrary(
            app_key=cls.app_key,
            app_secret=cls.app_secret,
            service_id=cls.service_id,
            shop_id=cls.shop_id,
            region=cls.region
        )
        if not all([cls.app_key, cls.app_secret, cls.service_id, cls.region]):
            raise ValueError("Tiktokshop credentials not found in environment variables")

    def setUp(self):
        # self.maxDiff = None
        # self.company_id = str(uuid.uuid4())
        # self.user_id = str(uuid.uuid4())
        # self.username = f"user_{self.user_id}"
        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"

    def create_lambda_event(self, method='GET', path='/', body=None, query_params=None, path_params=None, headers=None):
        event = {
            'httpMethod': method,
            'body': json.dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': headers,
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            },
            'isBase64Encoded': False
        }
        return event

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test_function'

        return LambdaContext()

    def test_get_connection_types(self):
        response = get_connection_types(call_back_post_event, {})
        self.assertTrue(True)

    def test_get_authorization_url(self):
        auth_url = self.tiktokshop_library.get_authorization_url(connection_id='test')

    def test_oauth2_callback(self):
        new_params = {'app_key': '6fmsibjotg0ie', 'code': 'ROW_STS_IAAAAADvXMz2_T5yEVJ0VTaPXemkQPBVanBZEw-tEzx6rX72dvoDg9ftEqLeZUr5bDpclT-2UQF3VC0hE3E31CYGz7DpfAikV4J0WRoPnfheZgslAupV4QuvE83CCLcM2zi4X-qcTwMf_Z969qGFu9zs-qoG', 'locale': 'en', 'shop_region': 'SG', 'state': '18d8a690-62a8-4d26-99ee-6a99f5aec94b'}

        event = self.create_lambda_event(query_params=new_params)
        context = self.create_lambda_context()
        response = oauth2_callback(event, context)
        self.assertTrue(True)

    def test_get_authorized_shops(self):
        event = self.create_lambda_event()
        context = self.create_lambda_context()
        response = self.tiktokshop_library.get_authorized_shops()
    def test_test_connection(self):
        # Create a mock connection record to test
        connection_data = {
            "connection_id": "6a4b23d4-e414-46d7-8093-c08b328ad852",
            "connection_type": "tiktokshop",
            "settings": {
                "app_key": self.app_key,
                "app_secret": self.app_secret,
                "service_id": self.service_id,
                "shop_id": self.shop_id,
                "region": self.region,
                # You might need a valid token here
                "shop_cipher": "ROW__0pGoQAAAABS6fE3IRyu5jd9whqokA24"  # You might need a valid cipher here
            }
        }
        response = test_connection(tiktok_shop_test_connection, {})

        # Assert the response
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertTrue(response_body['success'])
        self.assertTrue(response_body['is_connected'])

    def test_setup_webhooks(self):
        event = self.create_lambda_event(
            path_params={'connection_id': 'feebf543-a080-4469-b301-6e9025c43983'},
            body={'action_type': 'get_order', 'params': {'warehouse_ids': ["7447771753554855685"]}},
            query_params={
                'access_token': 'ROW_CRVuIQAAAAA35XTma9EH1vpnDSy3wBe_F6c9GaWcEFr86A7nPhXUxXXu1YwznM4qFvhNixeCg4qs_EQxBkhijjFe9uzPGxWcWx0VYWUTpSgcbvWyZxBFEBWts-Uwp-pRsDyE_oaR3hU'}
            , method='POST'
        )
        context = self.create_lambda_context()
        # connection= get_connection_by_id('feebf543-a080-4469-b301-6e9025c43983')
        response = self.tiktokshop_library.setup_webhooks(event)
        self.assertTrue(True)

    def test_set_fetch_action_settings_success(self):
        connection_id = 'feebf543-a080-4469-b301-6e9025c43983'
        # Arrange
        event_body = {
            "actions": {
                "get_order": {
                    "enabled": True,
                    "rate_limit": 60,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "custom_settings": {
                        # "limit": 50,
                        # "status": "active"
                    },
                    "schedule": {
                        "type": "interval",
                        "value": "300"
                    },
                    "status": {
                        "last_run": None,
                        "next_run": None
                    }
                }
            }
        }
        event = self.create_lambda_event(body=event_body,
                                         path_params={'connection_id': connection_id}, )
        context = self.create_lambda_context()

        response = set_fetch_action_settings(event, context)

        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Fetch action settings updated successfully')


if __name__ == '__main__':
    unittest.main()
