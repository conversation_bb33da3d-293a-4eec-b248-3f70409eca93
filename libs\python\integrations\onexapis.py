import json
import os
from functools import lru_cache

import requests
from nolicore.utils.exceptions import BadRequest
from nolicore.utils.utils import json_dumps

ONEXAPIS = os.environ['ONEXAPIS']
HARAVAN_API = os.environ['HARAVAN_API']


def optiwarehouse_to_haravan(product):
    options = product['options']
    if options is None:
        options_dict = {}
        for variant in product['variants']:
            for index in range(1, 4):
                option_title = variant.get(f'optionTitle{index}') or f'Attribute {index}'
                options_dict.setdefault(option_title, [])
                options_dict[option_title].append(variant[f'option{index}'])

        options = [{'name': option_title} for option_title, values in options_dict.items()]

    product_type = product['category']['name'] if product['category'] else None
    if product_type is None and len(product['variants']) > 0:
        first_variant = product['variants'][0]
        product_type = first_variant['category']['name'] if first_variant['category'] else None

    return {
        "product": {
            "title": product['name'],
            "body_html": product['description'],
            "vendor": None,
            "product_type": product_type,
            "images": product['images'],
            "variants": [
                {
                    "option1": variant['option1'],
                    "option2": variant['option2'],
                    "price": [v for v in variant['prices'] if
                              v['price_group']['id'] in ["de9a6e10-03b7-4669-a7b2-2e3f81929302",
                                                         "b51cd6d6-aa24-4be0-855a-9dcd8d868c2b"]
                              ][0]['price'],
                    "grams": variant['measurements']['weight_value'],
                    "requires_shipping": True,
                    "inventory_management": "haravan",
                    "sku": variant['sku'],
                    "image": variant['images'][0] if len(variant['images']) > 0 else None,
                } for variant in product['variants']
            ],
            "options": [
                {
                    "name": option['name'],
                    "position": index
                } for index, option in zip(range(1, 3), options)
            ]
        }
    }


@lru_cache(maxsize=1024)
def get_token():
    auth_url = f'{ONEXAPIS}/auth/login'
    # Get from setting
    credentials = {
        "username": "baababy_admin",
        "password": "Admin@123!"
    }
    try:
        response = requests.post(auth_url, json=credentials)
        response.raise_for_status()
        return response.json()['IdToken']
    except requests.RequestException as e:
        print('Error fetching token:', e)
        return None


def haravan_sync_product(product):
    # Get from setting
    url = f'{HARAVAN_API}/83607663-86d8-4931-931e-fc79f6ed801d/sync_product'
    haravan_product = optiwarehouse_to_haravan(product)
    headers = {'Authorization': f'Bearer {get_token()}'}
    response = requests.post(url, json=json.loads(json_dumps(haravan_product)), headers=headers)
    try:
        result = response.json()
        if result.get('error'):
            raise BadRequest(result)
        return result
    except TypeError:
        raise BadRequest(response.text)
