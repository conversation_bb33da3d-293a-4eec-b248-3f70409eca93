from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes
from models.media.image import ImageSchema, Image
from models.sale_channel.sale_channel import SaleChannel, SaleChannelSchema


@dataclass
class Store(Attributes):
    id: str = None
    name: str = None
    sale_channel: SaleChannel = None
    image: Image = None
    settings: dict = None


class StoreSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    sale_channel = fields.Nested(SaleChannelSchema(SaleChannel), required=True)
    settings = fields.Dict(allow_none=None)


@dataclass
class StoreAttributes(Store, BasicAttributes):
    pass


class StoreAttributesSchema(BasicAttributesSchema, StoreSchema):
    pass


class StoreModel(BasicModel):
    table_name = 'store'
    attributes: StoreAttributes
    attributes_schema = StoreAttributesSchema
    attributes_class = StoreAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }
