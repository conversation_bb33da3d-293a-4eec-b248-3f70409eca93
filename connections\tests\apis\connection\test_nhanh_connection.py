import unittest

from tests.base import BaseTestCase
from connections.src.apis.connection import (
    test_connection, get_connection
)
from connections.tests.apis.connection.data_test import test_nhanh_connection_event, get_connection_event


class TestConnectionApiTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

    def test_connection_testing(self):
        test_connection(test_nhanh_connection_event, {})

    def test_get_connection_details(self):
        get_connection(get_connection_event, {})


if __name__ == '__main__':
    unittest.main()
