import unittest

from tests.base import BaseTestCase
from connections.src.apis.connection import (
    test_connection, get_connection
)
from connections.tests.apis.connection.data_test import test_nhanh_connection_event, get_connection_event
from integrations.channels.shopify_oauth.shopify_oauth_library import ShopifyOAuthLibrary, ShopifyOAuthEndpoints


class TestShopifyOauthLibrary(unittest.TestCase):
    def setUp(self):
        self.shopify_oauth_library = ShopifyOAuthLibrary(
            api_key='32cb50899aec19dc93914858b4d757eb',
            access_token='shpua_f403c1a619b63869bed6cc58aa72b92a',
            secret_key='c9b53eca6e700dcd8d0e18d352a49e86',
            shop_url='https://trannhon-partner-2.myshopify.com',
        )

    def test_test_connection(self):
        self.assertTrue(self.shopify_oauth_library.test_connection())

    def test_get_products(self):
        result = self.shopify_oauth_library.get_products({"limit": 50})
        print('result', result)

    def test_get_order_by_name(self):
        result = self.shopify_oauth_library.get_order_by_name(order_name='580191364854548490')
        print('result', result)


if __name__ == '__main__':
    unittest.main()
