Resources:
  imports:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: imports
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1

  importRecord:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: importRecord
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: record_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: record_id
          KeyType: RANGE
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1

  customer:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: customer
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1

  supplier:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: supplier
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      BillingMode: PAY_PER_REQUEST

  package:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: package
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: order_id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: package_number
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      GlobalSecondaryIndexes:
        - IndexName: packageOrderIdIndex
          KeySchema:
            - AttributeName: order_id
              KeyType: HASH
            - AttributeName: id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
        - IndexName: packageNumberIndex
          KeySchema:
            - AttributeName: package_number
              KeyType: HASH
            - AttributeName: id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  payment:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: payment
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
  template:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: template
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1

  order:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: order
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: external_id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: orderExternalIdIndex
          KeySchema:
            - AttributeName: external_id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  returnOrder:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: returnOrder
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: external_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: returnExternalIdIndex
          KeySchema:
            - AttributeName: external_id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  purchaseOrder:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: purchaseOrder
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: external_id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: purchaseOrderExternalIdIndex
          KeySchema:
            - AttributeName: external_id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  product:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: product
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: sku
          AttributeType: S
        - AttributeName: slug
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: productSkuIndex
          KeySchema:
            - AttributeName: sku
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
        - IndexName: productSlugIndex
          KeySchema:
            - AttributeName: slug
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  variant:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: variant
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: sku
          AttributeType: S
        - AttributeName: original_sku
          AttributeType: S
        - AttributeName: product_id
          AttributeType: S
        - AttributeName: slug
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: variantSkuIndex
          KeySchema:
            - AttributeName: sku
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
        - IndexName: productIdIndex
          KeySchema:
            - AttributeName: product_id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
        - IndexName: variantSlugIndex
          KeySchema:
            - AttributeName: slug
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
        - IndexName: variantOriginalSkuIndex
          KeySchema:
            - AttributeName: original_sku
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  unit:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: unit
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  image:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: image
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1

  priceGroup:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: priceGroup
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  inventoryItem:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: inventoryItem
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_location
          AttributeType: S
        - AttributeName: product_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_location
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: inventoryItemProductIdIndex
          KeySchema:
            - AttributeName: product_id
              KeyType: HASH
            - AttributeName: id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
  inventoryTransaction:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: inventoryTransaction
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_location_item
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_location_item
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: itemLocationTransactionIndex
          KeySchema:
            - AttributeName: company_location_item
              KeyType: HASH
            - AttributeName: id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  brand:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: brand
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  category:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: category
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: slug
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: categorySlugIndex
          KeySchema:
            - AttributeName: slug
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  location:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: location
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: locationCompanyIdIndex
          KeySchema:
            - AttributeName: company_id
              KeyType: HASH
            - AttributeName: id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
  filter:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: filter
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_user_type
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_user_type
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  account:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: account
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: accountUserIdIndex
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  paymentMethod:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: paymentMethod
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  transaction:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: transaction
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  transactionVoucher:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: transactionVoucher
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  saleChannel:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: saleChannel
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  store:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: store
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  shippingProvider:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: shippingProvider
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  settings:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: settings
      AttributeDefinitions:
        - AttributeName: setting_name
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: company_id
          KeyType: HASH
        - AttributeName: setting_name
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  printTemplate:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: printTemplate
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: company_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  history:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: history
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: version
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: version
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  wishlist:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: wishlist
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  discount:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: discount
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: discount_code
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: discountCodeIndex
          KeySchema:
            - AttributeName: discount_code
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  voucherCode:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: voucherCode
      AttributeDefinitions:
        - AttributeName: code
          AttributeType: S
        - AttributeName: voucher_id
          AttributeType: S
      KeySchema:
        - AttributeName: code
          KeyType: HASH
        - AttributeName: voucher_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  usedVoucher:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: usedVoucher
      AttributeDefinitions:
        - AttributeName: customer_id
          AttributeType: S
        - AttributeName: voucher_code
          AttributeType: S
      KeySchema:
        - AttributeName: customer_id
          KeyType: HASH
        - AttributeName: voucher_code
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  voucher:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: voucher
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  customerGroup:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: customerGroup
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: company_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  role:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: role
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  permission:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: permission
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  comment:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: comment
      AttributeDefinitions:
        - AttributeName: blog_id
          AttributeType: S
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: blog_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  blog:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: blog
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: slug
          AttributeType: S
        - AttributeName: org_blog_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: blogSlugIndex
          KeySchema:
            - AttributeName: slug
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
        - IndexName: orgBlogIdIndex
          KeySchema:
            - AttributeName: org_blog_id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
  blogCategory:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: blogCategory
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: slug
          AttributeType: S
        - AttributeName: content_group_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: blogCategorySlugIndex
          KeySchema:
            - AttributeName: slug
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
        - IndexName: contentGroupIdIndex
          KeySchema:
            - AttributeName: content_group_id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
  province:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: province
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  district:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: district
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  ward:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ward
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  posSocket:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: posSocket
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  screenSocket:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: screenSocket
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: screen_socket_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
        - AttributeName: screen_socket_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  shift:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: shift
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  shiftPayLineItem:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: shiftPayLineItem
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  terminal:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: terminal
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  draftOrder:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: draftOrder
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: draftOrderUserIdIndex
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
  stockAdjustment:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: stockAdjustment
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  stockRelocate:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: stockRelocate
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  version:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: version
      AttributeDefinitions:
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: company_id
          KeyType: HASH
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  loyaltyProgram:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: loyaltyProgram
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  rewardProgram:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: rewardProgram
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  counter:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: counter
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1

  connection:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: connection
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 3
        WriteCapacityUnits: 1
      GlobalSecondaryIndexes:
        - IndexName: connectionByCompanyIndex
          KeySchema:
            - AttributeName: company_id
              KeyType: HASH
            - AttributeName: id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 3
            WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  syncRecord:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: syncRecord
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: connection_id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: connection_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: syncRecordByCompanyIndex
          KeySchema:
            - AttributeName: id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
        - IndexName: syncRecordConnectionIdIndex
          KeySchema:
            - AttributeName: connection_id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  fetchEvent:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: fetchEvent
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: continuation_token
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: continuation_token
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: fetchEventByCompanyIndex
          KeySchema:
            - AttributeName: id
              KeyType: HASH
            - AttributeName: company_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  notification:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: notification
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  webhook:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: webhook
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: connection_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: connection_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  destinationData:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: destinationData
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: connection_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: connection_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  mappingAttribute:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: mappingAttribute
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: type
          AttributeType: S
        - AttributeName: connection_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: mappingAttributeTypeIndex
          KeySchema:
            - AttributeName: connection_id
              KeyType: HASH
            - AttributeName: type
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  botChannelConnection:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: botChannelConnection
      AttributeDefinitions:
        - AttributeName: connection_id
          AttributeType: S
        - AttributeName: channel_id
          AttributeType: S
        - AttributeName: bot_id
          AttributeType: S
      KeySchema:
        - AttributeName: connection_id
          KeyType: HASH
        - AttributeName: channel_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      GlobalSecondaryIndexes:
        - IndexName: botChannelConnectionByBotIdAndConnectionIdIndex
          KeySchema:
            - AttributeName: bot_id
              KeyType: HASH
            - AttributeName: connection_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1

  virtualStaff:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: virtualStaff
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: department_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: virtualStaffByDepartmentIndex
          KeySchema:
            - AttributeName: id
              KeyType: HASH
            - AttributeName: department_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
  department:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: department
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  task:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: task
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  conversation:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: conversation
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
        - AttributeName: customer_id
          AttributeType: S
        - AttributeName: staff_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: conversationByCustomerAndStaffIdIndex
          KeySchema:
            - AttributeName: customer_id
              KeyType: HASH
            - AttributeName: staff_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
  feedback:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: feedback
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  message:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: message
      AttributeDefinitions:
        - AttributeName: conversation_id
          AttributeType: S
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: conversation_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  taskExecution:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: taskExecution
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: task_id
          AttributeType: S
        - AttributeName: virtual_staff_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: task_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      GlobalSecondaryIndexes:
        - IndexName: taskExecutionByVirtualStaffIndex
          KeySchema:
            - AttributeName: id
              KeyType: HASH
            - AttributeName: virtual_staff_id
              KeyType: RANGE
          Projection:
            ProjectionType: "ALL"
          ProvisionedThroughput:
            ReadCapacityUnits: 1
            WriteCapacityUnits: 1
  knowledge:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: knowledge
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: company_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: company_id
          KeyType: RANGE
      ProvisionedThroughput:
        ReadCapacityUnits: 1
        WriteCapacityUnits: 1
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  
  RetrieverQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-retriever-queue
      VisibilityTimeout: 120
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt: [RetrieverDeadLetterQueue, Arn]
        maxReceiveCount: 3
      MessageRetentionPeriod: 86400

  RetrieverDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:provider.stage}-retriever-dead-letter-queue
      VisibilityTimeout: 120

Outputs:
  importTable:
    Value: !Ref imports
  importRecordTable:
    Value: !Ref importRecord
  customerTable:
    Value: !Ref customer
  supplierTable:
    Value: !Ref supplier
  packageTable:
    Value: !Ref package
  paymentTable:
    Value: !Ref payment
  orderTable:
    Value: !Ref order
  purchaseOrderTable:
    Value: !Ref purchaseOrder
  productTable:
    Value: !Ref product
  variantTable:
    Value: !Ref variant
  imageTable:
    Value: !Ref image
  priceGroupTable:
    Value: !Ref priceGroup
  inventoryItemTable:
    Value: !Ref inventoryItem
  inventoryTransactionTable:
    Value: !Ref inventoryTransaction
  brandTable:
    Value: !Ref brand
  categoryTable:
    Value: !Ref category
  locationTable:
    Value: !Ref location
#    historyTable:
#      Value: !Ref history

  RetrieverQueueUrl:
    Value: !Ref RetrieverQueue
    Export:
      Name: ${self:service}-${self:provider.stage}-RetrieverQueueUrl

