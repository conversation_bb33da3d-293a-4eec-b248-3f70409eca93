from dataclasses import dataclass, field
from enum import Enum

from marshmallow import fields, pre_load
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import Attributes, AttributesSchema
from helpers.common import DEFAULT_MIN_DATE
from integrations.channels.action_types import ActionGroup
from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


class SyncRecordStatus(str, Enum):
    FETCHED = "FETCHED"
    TRANSFORMED = "TRANSFORMED"
    PUBLISHED = "PUBLISHED"
    ERROR = "ERROR"
    SKIPPED = "SKIPPED"
    COMPLETED = "COMPLETED"


class MappingStatus(str, Enum):
    MAPPED = "MAPPED"
    SYNCED = "SYNCED"
    UNMAPPED = "UNMAPPED"
    ERROR = "ERROR"


@dataclass
class OtherMappingsAttributes(Attributes):
    is_extra_destination_mapping: bool = False
    mapping_data: dict = None


class OtherMappingsAttributesSchema(AttributesSchema):
    is_extra_destination_mapping = fields.Bool(required=False, default=False)
    mapping_data = fields.Dict(allow_none=True)


@dataclass
class SyncRecordAttributes(BasicAttributes):
    connection_id: str = None
    record_type: ActionGroup = None
    channel: str = None
    raw_record_version: str = None
    transformed_record_id: str = None
    transformed_record_version: str = None
    remote_record_id: str = None
    response: dict = None
    response_message: str = None
    fetched_at: str = None
    fetch_event_id: str = None
    transformed_at: str = None
    published_at: str = None
    finished_at: str = None
    status: SyncRecordStatus = SyncRecordStatus.FETCHED
    is_source: bool = True
    mapping_status: MappingStatus = MappingStatus.UNMAPPED
    standard_source_data: dict = None
    standard_destination_data: dict = None
    other_mappings: OtherMappingsAttributes = None


class SyncRecordAttributesSchema(BasicAttributesSchema):
    id = fields.Str(required=True)
    connection_id = fields.Str(required=True)
    record_type = EnumField(ActionGroup, required=True)
    channel = fields.Str(required=True)
    raw_record_version = fields.Str(required=True)
    transformed_record_id = fields.Str(allow_none=True)
    transformed_record_version = fields.Str(allow_none=True)
    remote_record_id = fields.Str(allow_none=True)
    response = fields.Dict(allow_none=True)
    fetch_event_id = fields.Str(allow_none=True)
    response_message = fields.Str(allow_none=True)
    fetched_at = fields.DateTime(required=True)
    transformed_at = fields.DateTime(required=True)
    published_at = fields.DateTime(required=True)
    finished_at = fields.DateTime(required=True)
    status = EnumField(SyncRecordStatus, allow_none=True, default=SyncRecordStatus.FETCHED)
    is_source = fields.Bool(allow_none=True, default=True)
    mapping_status = EnumField(MappingStatus, allow_none=True, default=MappingStatus.UNMAPPED)
    standard_source_data = fields.Dict(allow_none=True)
    standard_destination_data = fields.Dict(allow_none=True)
    other_mappings = fields.Nested(OtherMappingsAttributesSchema(OtherMappingsAttributes), allow_none=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['fetched_at'] = data['fetched_at'] if 'fetched_at' in data else DEFAULT_MIN_DATE
        data['transformed_at'] = data['transformed_at'] if 'transformed_at' in data else DEFAULT_MIN_DATE
        data['published_at'] = data['published_at'] if 'published_at' in data else DEFAULT_MIN_DATE
        data['finished_at'] = data['finished_at'] if 'finished_at' in data else DEFAULT_MIN_DATE
        data['status'] = data['status'] if 'status' in data else SyncRecordStatus.COMPLETED.value
        data['is_source'] = data['is_source'] if 'is_source' in data else data['connection_id'] in data['id']
        data['mapping_status'] = data['mapping_status'] if 'mapping_status' in data else MappingStatus.UNMAPPED.value
        return data


class SyncRecordModel(BasicModel):
    key__id = 'id'
    key__ranges = ['connection_id']
    table_name = 'syncRecord'
    attributes: SyncRecordAttributes
    attributes_schema = SyncRecordAttributesSchema
    attributes_class = SyncRecordAttributes

    query_mapping = {
        'query': ['id', 'record_type', 'channel', 'raw_record_version', 'transformed_record_version',
                  'transformed_record_id'],
        'filter': {
            'id': 'query',
            'connection_id': 'query',
            'channel': 'query',
            'status': 'query',
            'mapping_status': 'query',
            'is_source': 'boolean',
            'fetch_event_id': 'query',
            'record_type': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
            'fetched_at': 'range',
            'transformed_at': 'range',
            'published_at': 'range',
            'finished_at': 'range',
        },
        'mapping': {
            'standard_source_data.id': 'standard_source_data.id',
            'standard_source_data.title': 'standard_source_data.title',
            'standard_source_data.name': 'standard_source_data.name',
            'standard_source_data.price': 'standard_source_data.price',
            'standard_destination_data.id': 'standard_destination_data.id',
            'standard_destination_data.title': 'standard_destination_data.title',
            'standard_destination_data.name': 'standard_destination_data.name',
            'standard_destination_data.price': 'standard_destination_data.price',
        }
    }

    @classmethod
    def put(cls, key, connection_id, data):
        company_id = str(data.get('company_id'))
        record = cls.by_key({
            'id': key,
            'connection_id': connection_id
        })

        if record:
            record.update(data)
            return record
        record = cls.create(company_id, {'id': key, 'connection_id': connection_id,
                                         'fetched_at': DEFAULT_MIN_DATE,
                                         'transformed_at': DEFAULT_MIN_DATE,
                                         'published_at': DEFAULT_MIN_DATE,
                                         'finished_at': DEFAULT_MIN_DATE,
                                         **data})
        return record

    @classmethod
    def get_record(cls, key, connection_id):
        return cls.by_key({
            'id': key,
            'connection_id': connection_id
        })


class SyncRecordCompanyIdIndex(SyncRecordModel):
    key__id = 'id'
    key__ranges = ['company_id']
    index_name = 'syncRecordByCompanyIndex'


class SyncRecordConnectionIdIndex(SyncRecordModel):
    key__id = 'connection_id'
    key__ranges = ['company_id']
    index_name = 'syncRecordConnectionIdIndex'
