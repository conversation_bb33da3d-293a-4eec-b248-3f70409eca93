import os
import re

import pendulum
from boto3.dynamodb.types import TypeDeserializer

from models.basic import BasicAttributes
from models.history.history import HistoryModel

deserializer = TypeDeserializer()

SERVICE = os.getenv('SERVICE')
ENV = os.getenv('ENV')


def handle(event, context):
    histories = {}
    for record in event['Records']:
        if record['eventName'] in ['MODIFY', 'INSERT', 'REMOVE']:
            record_data = {key: deserializer.deserialize(value) for key, value in
                           record['dynamodb']['NewImage'].items()}
            old_data = {}
            if 'OldImage' in record['dynamodb']:
                old_data = {key: deserializer.deserialize(value) for key, value in
                            record['dynamodb']['OldImage'].items()}
            version = pendulum.parse(record_data['updated_at']).int_timestamp
            arn = record['eventSourceARN']
            table_name = re.search(r'table\/(.*)\/stream', arn)[1]
            data_history = {
                'version': str(version),
                'data': record_data,
                'action': record['eventName'],
                'table': table_name,
                'id': record_data['id'],
                'note': "",
                'old_data': old_data
            }

            history_obj = HistoryModel(
                BasicAttributes.add_basic_attributes(data_history, record_data['company_id'], record_data.get('user')))
            histories[record_data['id']] = history_obj.attributes_dict
    HistoryModel.batch_add(list(histories.values()))
