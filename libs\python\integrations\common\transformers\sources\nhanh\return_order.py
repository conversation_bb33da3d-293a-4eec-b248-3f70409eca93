import logging
from typing import Dict, Any

import pendulum

from helpers.common import is_valid_uuid
from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.transformers.base import SourceTransformer
from models.common.status import ReturnStatus, ReturnType
from models.order.order_return import ReturnAttributesSchema

logger = logging.getLogger(__name__)


class NhanhOrderTransformer(SourceTransformer):
    channel_name = 'nhanh'
    object_type = ActionGroup.return_order.value

    def _map_object_type_to_action_type(self) -> str:
        return ActionType.get_return_order.value

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.return_order

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Input validation
            self.validate_input(data)

            location_mapping = self.connection_settings.get('dynamic_settings', {}).get('location_mapping', {}).get(
                'mapping', {})

            location_external = self.connection_settings.get('dynamic_settings', {}).get('location_mapping', {}).get(
                'external', [])

            staff_id = self.dynamic_settings.get('staff_mapping', {}).get("staff_id")

            origin_id = str(data['depotId'])
            location_id = location_mapping.get(origin_id)
            location = next((item for item in location_external if str(item['id']) == origin_id), {})
            origin_name = location.get('name') or 'POS'
            customer_code = ""
            customer_name = data['customerName']

            address = {
                "address2": None,
                "province_code": None,
                "country": "",
                "province": data.get('customerCity', ''),
                "name": customer_name,
                "last_name": None,
                "country_code": "",
                "address1": data.get('customerAddress', ''),
                "first_name": customer_name,
                "default_billing": False,
                "default_shipping": False,
                "phone": data['customerMobile'],
                "district": data.get('customerDistrict', ''),
                "city": data.get('customerCity', ''),
                "zip": None,
                "ward": data.get('customerWard', '')
            }

            created_at = pendulum.from_format(data['createdDateTime'], 'YYYY-MM-DD HH:mm:ss',
                                              tz="Asia/Bangkok").to_iso8601_string()
            updated_at = created_at

            return_order_line_items = [self._transform_return_order_line_items(p, location_id) for p in
                                       data['products'].values()]

            external_id = str(data['id'])
            staff = {
                "id": staff_id,
                "name": data['saleName']
            } if data.get('saleId') and is_valid_uuid(staff_id, 4) else None

            # Transform Nhanh order data to master format
            transformed_data = {
                "external_id": external_id,
                "type": ReturnType.IMPORTED.value,
                "customer": {
                    "addresses": [address],
                    "customer_code": customer_code,
                    "company_name": "",
                    "billing_address": address,
                    "last_name": None,
                    "first_name": customer_name,
                    "id": "",
                    "email": data.get('customerEmail'),
                    "phone": data['customerMobile'],
                    "tags": None
                },
                "status": ReturnStatus.RECEIVED.value,
                "billing_address": address,
                "shipping_address": address,
                "location": {
                    "id": location_id,  # Default location ID
                    "name": location.get("name")
                },
                "staff": staff,
                "source": {
                    "id": location_id,
                    "name": origin_name,
                    "origin_id": origin_id,
                    "origin": origin_name
                },
                "return_order_line_items": return_order_line_items,
                "note": data['description'],
                "tags": data['tags'].join() if data['tags'] else None,
                "total": data.get('money'),
                "created_at": created_at,
                "updated_at": updated_at
            }

            # Output validation
            self.validate_output(transformed_data)
            return transformed_data
        except KeyError as e:
            logger.error(f"Missing required field in Nhanh return order data: {str(e)}")
            raise ValueError(f"Missing required field in Nhanh return order data: {str(e)}")
        except Exception as e:
            logger.error(f"Error transforming Nhanh return order data: {str(e)}")
            raise

    def _transform_return_order_line_items(self, data: Dict[str, Any], location_id) -> Dict[str, Any]:
        """Convert product format"""
        sku = data['code']
        name = data['name']
        return {
            "unit_price": data['price'],
            "return_price": float(data['price']) - float(data['discount']),
            "sale_price": float(data['price']) - float(data['discount']),
            "discount": float(data['discount']),
            "sku": sku,
            "name": name,
            "variant_name": name,
            "image_url": None,
            "quantity": data['quantity'],
            "product_id": "",  # Not available in Nhanh data
            "variant_id": "",  # Not available in Nhanh data
            "location": {
                "id": location_id
            }
        }

    def validate_input(self, data: Dict[str, Any]):
        # Implement input validation logic using self.input_schema
        pass

    def validate_output(self, data: Dict[str, Any]):
        # Implement output validation logic using self.output_schema
        pass

    @property
    def input_schema(self):
        # This should reflect the Nhanh API order schema
        return {
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "relatedBillId": {"type": "string"},
                "relatedDepotId": {"type": "string"},
                "relatedUserName": {"type": "string"},
                "orderId": {"type": "string"},
                "requirementBillId": {"type": "string"},
                "inventoryCheckId": {"type": "string"},
                "warrantyBillId": {"type": "string"},
                "depotId": {"type": "string"},
                "date": {"type": "string"},
                "createdDateTime": {"type": "string"},
                "customerId": {"type": "string"},
                "customerName": {"type": "string"},
                "customerMobile": {"type": "string"},
                "saleId": {"type": "string"},
                "saleName": {"type": "string"},
                "type": {"type": "string"},
                "mode": {"type": "string"},
                "saleUserName": {"type": "string"},
                "createdById": {"type": "string"},
                "createdByName": {"type": "string"},
                "createdByUserName": {"type": "string"},
                "technicalId": {"type": ["string", "null"]},
                "technicalName": {"type": ["string", "null"]},
                "discount": {"type": "string"},
                "points": {"type": "string"},
                "usedPoints": {"type": "integer"},
                "money": {"type": "number"},
                "saleBonus": {"type": "integer"},
                "moneyTransfer": {"type": "string"},
                "cash": {"type": "number"},
                "installmentMoney": {"type": "number"},
                "creditMoney": {"type": "number"},
                "usedPointsMoney": {"type": "number"},
                "returnFee": {"type": "number"},
                "payment": {
                    "type": "object",
                    "properties": {}
                },
                "products": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "string"},
                            "code": {"type": "string"},
                            "name": {"type": "string"},
                            "quantity": {"type": "string"},
                            "price": {"type": "string"},
                            "discount": {"type": "string"},
                            "vat": {"type": ["number", "null"]},
                            "extendedWarrantyMoney": {"type": ["number", "null"]},
                            "money": {"type": "number"},
                            "imei": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                },
                "description": {"type": "string"},
                "supplierId": {"type": ["string", "null"]},
                "supplierName": {"type": ["string", "null"]},
                "couponCode": {"type": ["string", "null"]},
                "couponValue": {"type": ["string", "null"]},
                "customerMoney": {"type": "number"},
                "moneyReturn": {"type": "number"},
                "cashAccount": {"type": ["string", "null"]},
                "transferAccount": {"type": "string"},
                "creditAccount": {"type": ["string", "null"]},
                "creditCode": {"type": ["string", "null"]},
                "installmentAccount": {"type": ["string", "null"]},
                "installmentCode": {"type": ["string", "null"]},
                "tags": {
                    "type": "array",
                    "items": {"type": "string"}
                },
            }
        }

    @property
    def output_schema(self):
        return ReturnAttributesSchema().dump(ReturnAttributesSchema())
