import requests


def install_api(params: dict):
    url = f'https://admin.onexapis.com/admin/install'
    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        raise Exception(f'Failed to install: {e}')


def list_plans_api(service_code: str, token_id: str, access_token: str):
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token_id}'
    }
    url = f'https://admin.onexapis.com/admin/list_service_plan/{service_code}'
    try:
        response = requests.get(url, headers=headers, params={'access_token': access_token})
        response.raise_for_status()
        return response.json()
    except Exception as e:
        raise Exception(f'Failed to list plans: {e}')


def subscribe_api(body: dict, token_id: str, access_token: str):
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token_id}'
    }
    url = f'https://admin.onexapis.com/admin/subscriptions/subscribe'
    try:
        response = requests.post(url, json=body, headers=headers, params={'access_token': access_token})
        response.raise_for_status()
        return response.json()
    except Exception as e:
        raise Exception(f'Failed to subscribe: {e}')


def update_subscription_by_external_id(external_id: str, body: dict, headers: dict):
    url = f'https://admin.onexapis.com/admin/subscriptions/external_id/{external_id}'
    try:
        response = requests.post(url, json=body, headers=headers)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        raise Exception(f'Failed to update subscription: {e}')


def get_subscription_api(subscription_id: str, token_id: str, access_token: str):
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token_id}'
    }
    url = f'https://admin.onexapis.com/admin/subscriptions/{subscription_id}'
    try:
        response = requests.get(url, headers=headers, params={'access_token': access_token})
        response.raise_for_status()
        return response.json()
    except Exception as e:
        raise Exception(f'Failed to get subscription: {e}')
