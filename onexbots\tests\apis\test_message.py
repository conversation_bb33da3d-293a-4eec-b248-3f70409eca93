import unittest

from tests.base import BaseTestCase
from models.bots.message import Role

from onexbots.src.apis.message import create_message, list_messages
from nolicore.utils.exceptions import BadRequest


class TestMessage(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

    def test_create_message(self):
        """Test creating a new message"""
        # Test successful creation for user message
        test_data = {
            "content": "Hello from staff",
            "role": Role.VIRTUAL_STAFF.value,
        }
        path_params = {"id": "1ffcfdb8-7294-4f5d-b89d-c06887859686"}
        event = self.create_lambda_event(
            path_params=path_params,
            body=test_data,
            is_authorized=True
        )
        response = create_message(event, self.create_lambda_context())

        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Message created successfully')
        self.assertIsNotNone(response['data'])
        self.assertEqual(response['data']['content'], test_data['content'])
        self.assertEqual(response['data']['role'], test_data['role'])

        # Test successful creation for external user message
        test_data = {
            "content": "Hello from external user",
            "role": "external_user",
            "external_user_id": "ext_456",
            "connection_id": "conn_789"
        }
        path_params = {"id": "conv_123"}
        event = self.create_lambda_event(
            path_parameters=path_params,
            body=test_data
        )
        response = create_message(event, self.create_lambda_context())
        self.assertTrue(response['success'])

        # Test successful creation for virtual staff message
        test_data = {
            "content": "Hello from virtual staff",
            "role": "virtual_staff",
            "connection_id": "conn_789"
        }
        path_params = {"id": "conv_123"}
        event = self.create_lambda_event(
            body=test_data,
            path_parameters=path_params
        )
        response = create_message(event, self.create_lambda_context())
        self.assertTrue(response['success'])

        # Test missing required fields
        invalid_data = {
            # Missing content, role, connection_id
        }
        path_params = {"id": "conv_123"}
        event = self.create_lambda_event(
            path_params=path_params,
            body=invalid_data

        )
        with self.assertRaises(BadRequest) as context:
            create_message(event, self.create_lambda_context())
        self.assertIn("Missing required field", str(context.exception))

        # Test invalid role
        invalid_data = {
            "content": "Test message",
            "role": "invalid_role",
            "connection_id": "conn_789"
        }
        path_params = {"id": "conv_123"}
        event = self.create_lambda_event(
            body=invalid_data,
            path_parameters=path_params
        )
        with self.assertRaises(BadRequest) as context:
            create_message(event, self.create_lambda_context())
        self.assertIn("Invalid role", str(context.exception))

        # Test missing user-specific fields
        invalid_data = {
            "content": "Test message",
            "role": "user",
            "connection_id": "conn_789"
            # Missing user_id
        }
        path_params = {"id": "conv_123"}
        event = self.create_lambda_event(
            body=invalid_data,
            path_parameters=path_params
        )
        with self.assertRaises(BadRequest) as context:
            create_message(event, self.create_lambda_context())
        self.assertIn("Missing required field: user_id", str(context.exception))

        # Test non-existent conversation
        test_data = {
            "content": "Test message",
            "role": "user",
            "user_id": "user_456",
            "connection_id": "conn_789"
        }
        path_params = {"id": "non-existent-conv"}
        event = self.create_lambda_event(
            body=test_data,
            path_parameters=path_params
        )
        with self.assertRaises(BadRequest) as context:
            create_message(event, self.create_lambda_context())
        self.assertIn("Conversation not found", str(context.exception))

    def test_list_messages(self):
        """Test listing messages with various filters"""
        # Test listing messages for a conversation
        path_params = {"id": "1ffcfdb8-7294-4f5d-b89d-c06887859686"}
        event = self.create_lambda_event(path_parameters=path_params)
        response = list_messages(event, self.create_lambda_context())

        self.assertTrue(response['success'])
        self.assertIsNotNone(response['data'])
        self.assertIsInstance(response['data'], dict)
        self.assertIn('items', response['data'])
        self.assertIn('total', response['data'])

        # Test filtering by role
        path_params = {"id": "1ffcfdb8-7294-4f5d-b89d-c06887859686"}
        query_params = {"role": Role.VIRTUAL_STAFF.value}
        event = self.create_lambda_event(
            path_parameters=path_params,
            query_params=query_params
        )
        response = list_messages(event, self.create_lambda_context())

        self.assertTrue(response['success'])
        self.assertIsNotNone(response['data'])
        for message in response['data']['items']:
            self.assertEqual(message['role'], Role.VIRTUAL_STAFF.value)

        # Test date range filtering
        path_params = {"id": "1ffcfdb8-7294-4f5d-b89d-c06887859686"}
        query_params = {
            "created_at_start": "2024-01-01T00:00:00Z",
            "created_at_end": "2024-12-31T23:59:59Z"
        }
        event = self.create_lambda_event(
            path_parameters=path_params,
            query_params=query_params
        )
        response = list_messages(event, self.create_lambda_context())

        self.assertTrue(response['success'])
        self.assertIsNotNone(response['data'])

        # Test invalid date format
        path_params = {"id": "1ffcfdb8-7294-4f5d-b89d-c06887859686"}
        query_params = {"created_at_start": "invalid-date"}
        event = self.create_lambda_event(
            path_parameters=path_params,
            query_params=query_params
        )
        with self.assertRaises(BadRequest) as context:
            list_messages(event, self.create_lambda_context())
        self.assertIn("Failed to list messages", str(context.exception))


if __name__ == '__main__':
    unittest.main()
