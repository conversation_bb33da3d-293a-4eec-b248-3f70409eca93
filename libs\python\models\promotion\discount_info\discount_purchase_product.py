from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import List

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import AttributesSchema, Attributes

from models.product.brand import BrandSchema, Brand
from models.product.category import CategorySchema, Category
from models.product.product import ProductSchema, Product
from models.product.variant import VariantSchema, Variant
from models.promotion.discount_info.discount_amount import DiscountAmountAttributes, DiscountAmountAttributesSchema


class ItemType(Enum):
    PRODUCTS = 'PRODUCTS'
    VARIANTS = 'VARIANTS'
    CATEGORIES = 'CATEGORIES'
    BRANDS = 'BRANDS'


@dataclass
class GiftPurchaseProductAttributes(Attributes):
    purchase_product_quantity: Decimal
    sale_offs_product_max_quantity: Decimal
    purchase_product_type: ItemType
    sale_offs_product_type: ItemType
    for_all_purchase_product: bool = False
    for_all_sale_offs_product: bool = False
    purchase_products: List[Product] = None
    purchase_variants: List[Variant] = None
    purchase_categories: List[Category] = None
    purchase_brands: List[Brand] = None
    sale_offs_products: List[Product] = None
    sale_offs_variants: List[Variant] = None
    sale_offs_categories: List[Category] = None
    sale_offs_brands: List[Brand] = None


class GiftPurchaseProductAttributesSchema(AttributesSchema):
    purchase_product_quantity = fields.Decimal(required=True)
    sale_offs_product_max_quantity = fields.Decimal(required=True)
    purchase_product_type = EnumField(ItemType, required=True)
    sale_offs_product_type = EnumField(ItemType, required=True)
    for_all_sale_offs_product = fields.Bool(allow_none=True)
    for_all_purchase_product = fields.Bool(allow_none=True)
    purchase_products = fields.List(fields.Nested(ProductSchema(Product)), allow_none=True)
    purchase_variants = fields.List(fields.Nested(VariantSchema(Variant)), allow_none=True)
    purchase_categories = fields.List(fields.Nested(CategorySchema(Category)), allow_none=True)
    purchase_brands = fields.List(fields.Nested(BrandSchema(Brand)), allow_none=True)
    sale_offs_products = fields.List(fields.Nested(ProductSchema(Product)), allow_none=True)
    sale_offs_variants = fields.List(fields.Nested(VariantSchema(Variant)), allow_none=True)
    sale_offs_categories = fields.List(fields.Nested(CategorySchema(Category)), allow_none=True)
    sale_offs_brands = fields.List(fields.Nested(BrandSchema(Brand)), allow_none=True)


@dataclass
class DiscountPurchaseProductAttributes(GiftPurchaseProductAttributes, DiscountAmountAttributes):
    pass


class DiscountPurchaseProductAttributesSchema(GiftPurchaseProductAttributesSchema, DiscountAmountAttributesSchema):
    pass
