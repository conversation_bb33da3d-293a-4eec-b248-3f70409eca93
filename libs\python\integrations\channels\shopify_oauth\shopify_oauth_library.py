import base64
import hashlib
import hmac
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Union, Tuple
import re

import requests
from nolicore.utils.exceptions import UnauthorizedExp, BadRequest
from nolicore.utils.utils import logger, json_dumps
import pendulum

from helpers.install import update_subscription_by_external_id
from integrations.channels.action_types import ActionType, ActionGroup
from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, StandardVariant, SyncAPIResponse,
    PublishMetaData, StandardOrder, StandardProduct, StandardPurchaseOrder, StandardReturnOrder, StandardSubscription,
    MetaData
)
from integrations.common.event import FetchEvent
from helpers.custom_jsonpath import get_value_from_jsonpath


class ShopifyOAuthEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.SHOP = EndpointType(type="rest", method="GET", path=f"/shop.json")
        self.RECURRING_CHARGES = EndpointType(type="rest", method="POST", path="/recurring_application_charges.json")
        self.GET_RECURRING_CHARGE = EndpointType(type="rest", method="GET",
                                                 path="/recurring_application_charges/{charge_id}.json")
        self.GRAPHQL = EndpointType(type="graphql", method="POST", path="/graphql.json")
        self.CREATE_WEBHOOK = EndpointType(type="rest", method="POST", path="/webhooks.json")
        self.GET_WEBHOOKS = EndpointType(type="rest", method="GET", path="/webhooks.json")


class ShopifyOAuthLibrary(BaseAPILibrary):
    AVAILABLE_WEBHOOKS = {
        "app_subscriptions/update": "/shopify_oauth/app_subscriptions/update",
        "products/create": "/shopify_oauth/products/create",
        "products/update": "/shopify_oauth/products/update",
    }
    non_quoted_fields_graphql = {"sortKey", "savedSearchId"}

    def get_query_graphql(self, action, query="", variables: Union[str, dict] = ""):
        QUERY_GRAPHQL = {
            "GET_ORDERS": {
                "query": f"query {{ orders({query})"
                         "{ edges { node { id legacyResourceId displayFinancialStatus displayFulfillmentStatus "
                         "totalPriceSet { shopMoney { amount currencyCode } }"
                         "transactions { id } "
                         "fulfillmentOrders(first: 10) { nodes { id } }"
                         " } } } }",
                "variables": variables
            },
            "GET_ORDER": {
                "query": "query GetOrder($id: ID!) { order(id: $id) { id legacyResourceId name displayFinancialStatus displayFulfillmentStatus "
                         "totalPriceSet { shopMoney { amount currencyCode } }"
                         "transactions { id } fulfillmentOrders(first: 10) { nodes { id } } } }",
                "variables": variables
            },
            "CREATE_ORDER": {
                "query": "mutation M($order: OrderCreateOrderInput!, $options: OrderCreateOptionsInput) { orderCreate(order: $order, options: $options) { order { id legacyResourceId createdAt billingAddress { firstName lastName address1 phone city province country zip } currencyCode customer { id firstName lastName email } discountCodes displayFinancialStatus displayFulfillmentStatus email fulfillments(first: 50) { location { id } } lineItems(first: 50) { nodes { variant { id } title originalUnitPriceSet { shopMoney { amount currencyCode } } quantity taxLines { title rate priceSet { shopMoney { amount currencyCode } } } } } note phone shippingAddress { firstName lastName address1 phone city province country zip } tags taxLines { title rate priceSet { shopMoney { amount currencyCode } } } totalTaxSet { shopMoney { amount currencyCode } } totalPriceSet { shopMoney { amount currencyCode } } transactions { kind status amountSet { shopMoney { amount currencyCode } } } } userErrors { field message } } }",
                "variables": variables
            },
            "UPDATE_ORDER": {
                "query": "mutation OrderUpdate($input: OrderInput!) { orderUpdate(input: $input) { order { id legacyResourceId displayFulfillmentStatus createdAt totalPriceSet { shopMoney { amount currencyCode } } } userErrors { field message } } }",
                "variables": variables,
            },
            "CANCEL_ORDER": {
                "query": "mutation OrderCancel($orderId: ID!, $notifyCustomer: Boolean, $refund: Boolean!, $restock: Boolean!, $reason: OrderCancelReason!, $staffNote: String) { orderCancel(orderId: $orderId, notifyCustomer: $notifyCustomer, refund: $refund, restock: $restock, reason: $reason, staffNote: $staffNote) {  orderCancelUserErrors { field message code } } }",
                "variables": variables
            },
            "FULFILL_ORDER": {
                "query": "mutation fulfillmentCreate($fulfillment: FulfillmentInput!, $message: String) { fulfillmentCreate(fulfillment: $fulfillment, message: $message) { fulfillment { status } userErrors { field message } } }",
                "variables": variables
            },
            "REFUND_ORDER": {
                "query": "mutation M($input: RefundInput!) { refundCreate(input: $input) { userErrors { field message } refund { id note totalRefundedSet { presentmentMoney { amount } } } } }",
                "variables": variables
            },
            "GET_PRODUCT": {
                "query": "query GetProduct($id: ID!) { product(id: $id) { id legacyResourceId title publishedAt description descriptionHtml totalInventory tags createdAt updatedAt category { fullName } variants(first: 10) { nodes { id legacyResourceId sku displayName barcode inventoryQuantity "
                         "inventoryItem { inventoryLevels(first:10) { nodes { quantities(names:[\"available\"]) { quantity } location { id } } } measurement { weight { unit value } } } price image { id url altText width height } selectedOptions  { name value } } } options(first: 10) { name values } metafields(first: 5) { nodes { key value } } media(first: 5) { nodes { id id preview { image { altText url } } } } } }",
                "variables": variables
            },
            "GET_PRODUCTS": {
                "query": f"""
                query GetProducts {{
                  products({query}) {{
                    nodes {{
                      id
                      legacyResourceId
                      title
                      category {{
                          ancestorIds
                          fullName
                          id
                          isArchived
                          isLeaf
                          isRoot
                          level
                          name
                          parentId
                      }}
                      handle
                      description
                      descriptionHtml
                      vendor
                      productType
                      status
                      createdAt
                      updatedAt
                      publishedAt
                      tags
                      hasVariantsThatRequiresComponents
                      isGiftCard
                      totalInventory
                      tracksInventory
                      options(first: 10) {{
                        id
                        name
                        position
                        values
                      }}
                      variants(first: 10) {{
                        nodes {{
                            id
                            legacyResourceId
                            availableForSale
                            title
                            createdAt
                            updatedAt
                            displayName
                            price
                            compareAtPrice
                            sku
                            barcode
                            position
                            inventoryQuantity
                            inventoryItem {{ 
                                measurement {{ weight {{ unit value }} }} 
                                inventoryLevels(first:10) {{ nodes {{ 
                                    quantities(names:[\"available\"]) {{quantity}} 
                                    location {{ id }} 
                                }} }}
                            }}
                            availableForSale
                            taxable
                            taxCode
                            unitPriceMeasurement {{
                                measuredType
                                quantityUnit
                                quantityValue
                                referenceUnit
                                referenceValue
                            }}
                            selectedOptions {{
                              name
                              value
                            }}
                            image {{
                              id
                              url
                              altText
                              width
                              height
                            }}
                        }}
                      }}
                      media(first: 5) {{ nodes {{ id preview {{ image {{ altText url }} }} }} }}
                      metafields(first: 5) {{
                        edges {{
                          node {{
                            id
                            namespace
                            key
                            value
                            type
                            description
                          }}
                        }}
                      }}
                    }}
                    pageInfo {{
                      hasNextPage
                      endCursor
                    }}
                  }}
                }}
                """,
                "variables": variables
            }
        }
        return QUERY_GRAPHQL.get(action, None)

    def __init__(self,
                 api_key: str,
                 secret_key: str,
                 shop_url: str,
                 api_version: str = "2025-04",
                 access_token: Optional[str] = None):
        super().__init__(f"{shop_url}/admin/api/{api_version}")
        self.access_token = access_token
        self.api_key = api_key
        self.secret_key = secret_key
        self.shop_url = shop_url
        self.api_version = api_version
        self.endpoints = ShopifyOAuthEndpoints(self.endpoints.base_url)
        self.date_time_format = 'YYYY-MM-DD HH:mm:ss'
        self.verify = False

    def process_webhook(self, event_type: str, payload: Dict[str, Any]) -> SyncAPIResponse[
        Union[StandardOrder, StandardProduct, Dict[str, Any]]]:
        pass

    @staticmethod
    def verify_hmac(query_dict: Dict[str, Any], secret: str) -> bool:
        if 'hmac' not in query_dict:
            return False
        hmac_to_verify = query_dict.pop('hmac')
        sorted_params = '&'.join([
            f"{key}={value}"
            for key, value in sorted(query_dict.items())
        ])
        digest = hmac.new(
            secret.encode('utf-8'),
            sorted_params.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return hmac.compare_digest(digest, hmac_to_verify)

    @classmethod
    def verify_webhook_signature(cls, payload: str, hmac_header: str, secret: str) -> bool:
        # Tính HMAC và chuyển sang base64
        calculated_bytes = hmac.new(secret.encode('utf-8'), payload.encode('utf-8'), hashlib.sha256).digest()
        calculated_hmac = base64.b64encode(calculated_bytes).decode('utf-8')
        # So sánh với header (đã ở dạng base64)
        return hmac.compare_digest(calculated_hmac, hmac_header)

    def handle_webhook(self, event_type: str, payload: str, hmac_header: str, secret: str) -> SyncAPIResponse[
        Union[StandardOrder, StandardProduct, Dict[str, Any]]]:
        if not self.verify_webhook_signature(payload, hmac_header, secret):
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid webhook signature"
            )

        try:
            payload_dict = json.loads(payload)
            return self.process_webhook(event_type, payload_dict)
        except json.JSONDecodeError:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid JSON payload"
            )
        except ValueError as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=str(e)
            )

    def setup_webhooks(self, connection):
        webhooks_to_create = [
            {"topic": topic, "address": connection.get_webhook_url(), "format": "json"}
            for topic in self.AVAILABLE_WEBHOOKS
            if connection.attributes.webhook_settings.get(topic, False)
        ]

        results = []
        for webhook in webhooks_to_create:
            result = self._create_webhook(webhook)
            results.append((webhook['topic'], result[0]))

        for topic, success in results:
            if success:
                logger.info(f"Successfully created webhook: {topic}")
            else:
                logger.error(f"Failed to create webhook: {topic}")

    def _create_webhook(self, webhook_data: Dict[str, str]) -> Tuple[bool, str]:
        try:
            response = self._make_request(
                self.endpoints.CREATE_WEBHOOK,
                data={"webhook": webhook_data}
            )
            return True, ""
        except Exception as e:
            return False, str(e)

    @staticmethod
    def get_id_from_gid(gid):
        """
        Extracts the ID from a Shopify GID string.

        Args:
            gid (str): Shopify GID string (e.g. 'gid://shopify/AppSubscription/34499100986')

        Returns:
            str: The extracted ID
        """
        return gid.split('/')[-1]

    def process_webhook_event(self, event_type, payload: dict, channel_name: str) -> bool:
        if event_type == "app_subscriptions/update":
            try:
                webhook_data = payload['app_subscription']
                gid = webhook_data['admin_graphql_api_id']
                external_id = self.get_id_from_gid(gid)
                if webhook_data['status'] != 'ACTIVE':
                    return True
                subscription = {
                    'external_id': external_id,
                    'status': webhook_data['status'],
                    'charge_info': payload
                }
                update_subscription_by_external_id(external_id, subscription, headers={'secret_key': self.secret_key,
                                                                                       'channel_name': channel_name})
            except Exception as e:
                logger.error(f"Failed to update subscription: {e}")
            return True
        return False

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any], event_type: str) -> FetchEvent:
        action_type = ActionType.webhook
        action_group = ActionGroup.webhook
        channel_name = 'shopify_oauth'
        if event_type in ['products/create', 'products/update']:
            product_data = payload
            object_id = str(product_data.get('admin_graphql_api_id', ''))

            action_type = ActionType.get_product
            action_group = ActionGroup.product

            return FetchEvent.create_single_object_event(
                connection_id=str(uuid.uuid4()),
                channel=channel_name,
                action_type=action_type,
                action_group=action_group,
                event_time=pendulum.now().to_iso8601_string(),
                object_id=object_id,
                object_data=product_data
            )

        return FetchEvent(
            channel=channel_name,
            connection_id=str(uuid.uuid4()),
            action_type=action_type,
            action_group=action_group,
            event_time=pendulum.now().to_iso8601_string(),
            is_batch=False,
            object_data=payload
        )

    @staticmethod
    def extract_merchant_id(payload):
        return payload.get('shop_id')

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return headers.get('x-shopify-topic') or payload.get('event_type')

    @staticmethod
    def format_graphql_value(value, non_quoted_fields=None, field_name=""):
        """
        Format a value correctly for GraphQL queries.
        - By default, strings are enclosed in double quotes.
        - Fields listed in `non_quoted_fields` will not be quoted.

        non_quoted_fields: Set of field names that should NOT be quoted.
        field_name: The current field being processed.
        """
        if non_quoted_fields and field_name in non_quoted_fields:
            return str(value)  # No quotes for these fields
        elif isinstance(value, bool):
            return "true" if value else "false"
        elif value is None:
            return "null"
        return json.dumps(value)  # Strings and other quoted values

    @staticmethod
    def format_param(key, value, non_quoted_fields_graphql=None):
        return f"{key}: {ShopifyOAuthLibrary.format_graphql_value(value, non_quoted_fields_graphql, field_name=key)}"

    @staticmethod
    def update_params_query_updated_at(query: str, date_value: Any, operator: str, params: Dict[str, Any]) -> str:
        # Define opposite replacements
        operator_map = {
            '>': '>=',
            '>=': '>',
            '<': '<=',
            '<=': '<'
        }

        # Find the opposite operator
        opposite_operator = operator_map.get(operator)

        if not opposite_operator:
            return query  # Invalid operator, return unchanged query

        # Patterns to find both the correct and opposite operator
        correct_pattern = rf'updated_at:{re.escape(operator)}\'?[^\']*\'?'
        opposite_pattern = rf'updated_at:{re.escape(opposite_operator)}\'?[^\']*\'?'

        # New replacement string
        replacement = f"updated_at:{operator}'{date_value}'"

        if re.search(correct_pattern, query):
            # If the correct operator already exists, update its date value
            query = re.sub(correct_pattern, replacement, query, count=1)
        elif re.search(opposite_pattern, query):
            # If the opposite operator exists, replace it with the correct one
            query = re.sub(opposite_pattern, replacement, query, count=1)
        else:
            # If neither exist, prepend the new condition
            query = f"{replacement} " + query

        return query

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None,
                      path_params: Dict[str, Any] = None) -> Dict[str, Any]:

        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': self.access_token
        }

        if endpoint.type == "rest":
            response = self._make_request_sync(endpoint, headers=headers, params=params, json=data,
                                               path_params=path_params, verify=self.verify)

            return response
        elif endpoint.type == "graphql":
            return self._make_graphql_request(data["query"], data.get("variables"))

    def _make_graphql_request(self, query: str, variables: Dict[str, Any] = None) -> Dict[str, Any]:
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": self.access_token
        }
        payload = {
            "query": query,
            "variables": variables or {}
        }
        response = requests.post(f"{self.endpoints.base_url}{self.endpoints.GRAPHQL.path}", headers=headers,
                                 json=payload, verify=self.verify)
        response.raise_for_status()
        return response.json()

    def get_authorization_url(self, connection_id: str, redirect_url: str = None, base_url: str = None) -> str:
        app_callback_url = self.get_callback_url(base_url)
        state = self.generate_state(connection_id, redirect_url)

        scopes = [
            'read_product_listings',
            'write_products',
            'write_orders',
            'read_customers',
            'write_locations',
            'write_inventory',
            'write_fulfillments',
            'write_assigned_fulfillment_orders',
            'write_merchant_managed_fulfillment_orders'
        ]

        return (f"{self.shop_url}/admin/oauth/authorize?"
                f"&scope={', '.join(scopes)}&"
                f"&client_id={self.api_key}&"
                f"&redirect_uri={app_callback_url}"
                f"&state={state}")

    @staticmethod
    def get_shop_name(shop_url: str) -> str:
        parsed_url = shop_url.split('//')[-1].split('.')[0]
        return parsed_url

    def fetch_token(self, access_code: str, state) -> Dict[str, Any]:
        token_url = (f"{self.shop_url}/admin/oauth/access_token"
                     f"?client_id={self.api_key}"
                     f"&client_secret={self.secret_key}"
                     f"&code={access_code}")
        response = requests.post(token_url, verify=self.verify)

        if response.status_code == 400:
            raise BadRequest(response)

        token_data = response.json()
        if 'access_token' not in token_data:
            raise UnauthorizedExp(token_data)

        # Process the token data
        access_token = token_data['access_token']

        # Update the library's access token
        self.access_token = access_token

        return {
            'external_id': self.get_shop_name(self.shop_url),
            'access_token': access_token,
            'expires_at': None,
            'refresh_token': None,
            'token_data': token_data
        }

    def test_connection(self) -> bool:
        try:
            self._make_request(self.endpoints.GRAPHQL, data={
                "query": "query GetProducts { products(first: 10) { nodes { id title }, pageInfo { hasNextPage, endCursor }  } }",
                "variables": ""})
            return True
        except Exception:
            return False

    def get_products(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                     fetch_event_id: str = None) -> FetchAPIResponse[StandardProduct]:
        default_params = {
            "first": 50,
            "last": 50,
        }

        if 'last' in params:
            default_params.pop('first')
        elif 'first' in params:
            default_params.pop('last')
        else:
            default_params.pop('last')

        params = {**default_params, **(params or {})}

        # remove wrong params
        for key in ['limit', 'updated_after', 'page', 'updated_at_max', 'updated_at_min']:
            params.pop(key, None)

        # updated_at_from, updated_at_to in params
        if params.get('updated_at_from') or params.get('updated_at_to'):
            params['query'] = params.get('query', '')
            updated_at_from = pendulum.parse(params.get('updated_at_from')) if params.get('updated_at_from') else None
            updated_at_to = pendulum.parse(params.get('updated_at_to')) if params.get('updated_at_to') else None
            if (updated_at_from and updated_at_to) and updated_at_from > updated_at_to:
                raise BadRequest("updated_at_from must less than updated_at_to")
            if updated_at_from:
                params['query'] = self.update_params_query_updated_at(params['query'],
                                                                      updated_at_from.to_iso8601_string(), '>=', params)
            if updated_at_to:
                params['query'] = self.update_params_query_updated_at(params['query'],
                                                                      updated_at_to.to_iso8601_string(), '<=', params)

        updated_after = settings.get('updated_after') if settings is not None else None
        if updated_after:
            params['query'] = params.get('query', '')
            params['query'] = self.update_params_query_updated_at(params['query'],
                                                                  pendulum.parse(updated_after).to_iso8601_string(),
                                                                  '>=',
                                                                  params)

        # remove updated_at_from, updated_at_to
        for key in ['updated_at_from', 'updated_at_to']:
            params.pop(key, None)

        params_str = ", ".join(
            self.format_param(k, v, self.non_quoted_fields_graphql) for k, v in params.items())

        try:
            response = self._make_request(self.endpoints.GRAPHQL,
                                          data=self.get_query_graphql("GET_PRODUCTS", params_str))

            products = [self.standardize_product(product, fetch_event_id) for product in
                        response['data']["products"]['nodes']]
            pageInfo = response['data']["products"]['pageInfo']

            return FetchAPIResponse(
                success=True,
                data=products,
                meta=MetaData(
                    limit=params["first"] if "first" in params else params["last"],
                    continuation_token=str(pageInfo['endCursor']) if pageInfo['hasNextPage'] else None,
                ),
            )
        except Exception as e:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"{e}",
            )

    def get_product(self, product_id: str, fetch_event_id: str) -> FetchAPIResponse[StandardProduct]:
        try:
            response = self._make_request(self.endpoints.GRAPHQL, data=self.get_query_graphql("GET_PRODUCT", variables={
                "id": product_id}))

            product = self.standardize_product(response['data']["product"], fetch_event_id)

            return FetchAPIResponse(
                success=True,
                data=[product],
                meta=MetaData(
                    limit=1,
                    continuation_token=None,
                ),
            )
        except Exception as e:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"{e}",
            )

    def get_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                   fetch_event_id: str = None) -> \
            FetchAPIResponse[
                StandardOrder]:
        pass

    def get_order_by_name(self, order_name: str) -> Optional[Dict[str, Any]]:
        try:
            response = self._make_request(self.endpoints.GRAPHQL,
                                          data=self.get_query_graphql("GET_ORDERS",
                                                                      query=f"first: 10, query:\"name:{order_name}\"",
                                                                      variables=""))
            orders = response['data']["orders"]['edges']
            return orders
        except Exception:
            return None

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def sync_product(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardProduct]:
        pass

    def sync_order(self, order_data: Dict[str, Any], settings: Dict[str, Any] = None) -> \
            SyncAPIResponse[StandardOrder]:
        try:
            remote_order_list = self.get_order_by_name(order_data.get('name', ''))
            remote_order_id = remote_order_list[0].get('node', {}).get('id', '') if remote_order_list else ''

            order_data.pop('remote_record_id', None)

            user_errors = None
            synced_order = None
            if remote_order_id:
                response_remote_order_data = self._make_request(self.endpoints.GRAPHQL, None,
                                                                data=self.get_query_graphql("GET_ORDER",
                                                                                            variables={
                                                                                                "id": remote_order_id
                                                                                            }))
                current_order_data = response_remote_order_data['data']['order']

                # Update order status
                current_order_status = current_order_data['displayFulfillmentStatus']
                new_order_status = order_data.get("fulfillmentStatus", None)
                if new_order_status and new_order_status != current_order_status:
                    if new_order_status == 'RESTOCKED':
                        response = self._make_request(self.endpoints.GRAPHQL, None,
                                                      data=self.get_query_graphql("CANCEL_ORDER",
                                                                                  variables={
                                                                                      "orderId": remote_order_id,
                                                                                      "notifyCustomer": True,
                                                                                      "refund": False,
                                                                                      "restock": True,
                                                                                      "reason": "OTHER",
                                                                                      "staffNote": ""
                                                                                  }))
                    if new_order_status == 'FULFILLED':
                        fulfillmentOrderId = get_value_from_jsonpath(current_order_data,
                                                                     '$.fulfillmentOrders.nodes[0].id')
                        response = self._make_request(self.endpoints.GRAPHQL, None,
                                                      data=self.get_query_graphql("FULFILL_ORDER",
                                                                                  variables={
                                                                                      "fulfillment": {
                                                                                          "lineItemsByFulfillmentOrder": [
                                                                                              {
                                                                                                  "fulfillmentOrderId": fulfillmentOrderId
                                                                                              }
                                                                                          ],
                                                                                          "notifyCustomer": False
                                                                                      }
                                                                                  }))

                # Update payment status
                current_payment_status = get_value_from_jsonpath(current_order_data, '$.displayFinancialStatus')
                new_payment_status = get_value_from_jsonpath(order_data, '$.financialStatus') or None
                if new_payment_status and new_payment_status != current_payment_status:
                    if new_payment_status == 'REFUNDED':
                        parentId = get_value_from_jsonpath(current_order_data, '$.transactions[0].id')
                        amount = get_value_from_jsonpath(current_order_data, '$.totalPriceSet.shopMoney.amount')
                        response = self._make_request(self.endpoints.GRAPHQL, None,
                                                      data=self.get_query_graphql("REFUND_ORDER",
                                                                                  variables={
                                                                                      "input": {
                                                                                          "orderId": remote_order_id,
                                                                                          "note": "Want to exchange for a different item",
                                                                                          "transactions": [
                                                                                              {
                                                                                                  "orderId": remote_order_id,
                                                                                                  "gateway": "shopify_gateway",
                                                                                                  "kind": "REFUND",
                                                                                                  "amount": amount,
                                                                                                  "parentId": parentId
                                                                                              }
                                                                                          ]
                                                                                      }
                                                                                  }))

                # Update metafield
                response = self._make_request(self.endpoints.GRAPHQL, None,
                                              data=self.get_query_graphql("UPDATE_ORDER",
                                                                          variables={
                                                                              "input": {"id": remote_order_id,
                                                                                        "email": order_data.get('email',
                                                                                                                ''),
                                                                                        "metafields": order_data.get(
                                                                                            'metafields', None),
                                                                                        "note": order_data.get('note',
                                                                                                               ''),
                                                                                        "poNumber": order_data.get(
                                                                                            'po_number', ''),
                                                                                        "shippingAddress": order_data.get(
                                                                                            'shipping_address', None),
                                                                                        ## need check last_name must exist when update
                                                                                        "tags": order_data.get('tags',
                                                                                                               None),
                                                                                        }
                                                                          }))
                if response["data"]["orderUpdate"]["userErrors"]:
                    user_errors = json_dumps(response['data']['orderUpdate']['userErrors'])
                else:
                    ## TODO: check is necessary move this when update order status is done
                    synced_order = self.standardize_order(response["data"]["orderUpdate"]["order"])
            else:
                response = self._make_request(self.endpoints.GRAPHQL,
                                              data=self.get_query_graphql("CREATE_ORDER", variables={
                                                  "order": order_data
                                              }))
                if response["data"]["orderCreate"]["userErrors"]:
                    user_errors = json_dumps(response['data']['orderCreate']['userErrors'])
                else:
                    synced_order = self.standardize_order(response["data"]["orderCreate"]["order"])

            if not user_errors:
                return SyncAPIResponse(
                    success=True,
                    data=synced_order,
                    meta=PublishMetaData(updated_at=datetime.now()),
                    message=f"Order {synced_order.order_number} synced successfully"
                )
            else:
                return SyncAPIResponse(
                    success=False,
                    data=None,
                    meta=PublishMetaData(updated_at=datetime.now()),
                    message=f"Failed when calling orderCreate graphql api: {user_errors}"
                )
        except Exception as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync order: {str(e)}"
            )

    def standardize_subscription(self, raw_subscription: Dict[str, Any]) -> StandardSubscription:
        return StandardSubscription(
            id=str(raw_subscription["subscription_contract_id"]),
            name=raw_subscription["admin_graphql_api_order_id"],
            raw_data=raw_subscription
        )

    def standardize_variant(self, raw_variant: Dict[str, Any]) -> StandardVariant:
        return StandardVariant(
            id=raw_variant['legacyResourceId'],
            title=raw_variant.get('displayName', ''),
            sku=raw_variant['sku'],
            price=str(raw_variant.get('price', 0)),
            image=raw_variant['image'].get('url') if raw_variant['image'] else None
        )

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        variants = [self.standardize_variant(variant) for variant in raw_product['variants']['nodes']]
        images = []
        for image in raw_product['media']['nodes']:
            image_url = get_value_from_jsonpath(image, '$.preview.image.url')
            if image_url:
                images.append(image_url)
        return StandardProduct(
            id=str(raw_product["legacyResourceId"]),
            fetch_event_id=fetch_event_id,
            title=raw_product["title"],
            raw_data=raw_product,
            images=images,
            sku=raw_product['variants']['nodes'][0]['sku'],
            variants=variants,
            created_at=pendulum.parse(raw_product['createdAt']),
            updated_at=pendulum.parse(raw_product['updatedAt'])
        )

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        total_price = raw_order.get("totalPriceSet", {}).get("shopMoney", {}).get("total")
        return StandardOrder(
            fetch_event_id=fetch_event_id,
            id=str(raw_order.get("legacyResourceId")),
            order_number=str(raw_order.get("legacyResourceId")),
            total_price=float(total_price) if total_price else 0,
            currency="VND",
            status=raw_order.get("displayFulfillmentStatus", ""),
            created_at=pendulum.parse(raw_order['createdAt']),
            raw_data=raw_order
        )

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def get_product_params(self):
        return FetchAPIResponse(
            success=True,
            data={
                'after': 'String',
                'before': 'String',
                'first': 'Number',
                'last': 'Number',
                'query': 'String',
                'reverse': 'Boolean',
                'savedSearchId': 'String',
                'sortKey': 'String',
                'updated_at_from': 'DateTime|ISO',
                'updated_at_to': 'DateTime|ISO',
            },
            meta=None
        )
