from models.product.product import ProductAttributesSchema
from ...base import DestinationTransformer


class ShopifyProductDestinationTransformer(DestinationTransformer):
    channel_name = 'shopify'
    object_type = 'product'

    def transform(self, data):
        # Transform master format to Shopify product data
        return {
            'title': data.get('name'),
            'body_html': data.get('description'),
            'vendor': data.get('brand', {}).get('name'),
            'product_type': data.get('category', {}).get('name'),
            'status': 'active' if data.get('publish') else 'draft',
            'tags': data.get('tags', '').split(','),
            'variants': [
                {
                    'sku': variant.get('sku'),
                    'barcode': variant.get('barcode'),
                    'price': variant.get('price'),
                    'compare_at_price': variant.get('compare_at_price'),
                    'weight': variant.get('weight'),
                    'weight_unit': variant.get('weight_unit'),
                    'inventory_quantity': variant.get('inventory_quantity'),
                }
                for variant in data.get('variants', [])
            ],
            'options': [
                {
                    'name': option.get('name'),
                    'values': option.get('values', []),
                }
                for option in data.get('options', [])
            ],
            'images': [
                {'src': image.get('src')}
                for image in data.get('images', [])
            ],
        }

    @property
    def input_schema(self):
        return ProductAttributesSchema().dump(ProductAttributesSchema())

    @property
    def output_schema(self):
        # This should reflect the Shopify API product schema
        return {
            'type': 'object',
            'properties': {
                'title': {'type': 'string'},
                'body_html': {'type': 'string'},
                'vendor': {'type': 'string'},
                'product_type': {'type': 'string'},
                'status': {'type': 'string'},
                'tags': {'type': 'array', 'items': {'type': 'string'}},
                'variants': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'sku': {'type': 'string'},
                            'barcode': {'type': 'string'},
                            'price': {'type': 'string'},
                            'compare_at_price': {'type': 'string'},
                            'weight': {'type': 'number'},
                            'weight_unit': {'type': 'string'},
                            'inventory_quantity': {'type': 'integer'},
                        }
                    }
                },
                'options': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'name': {'type': 'string'},
                            'values': {'type': 'array', 'items': {'type': 'string'}}
                        }
                    }
                },
                'images': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'src': {'type': 'string'}
                        }
                    }
                }
            }
        }
