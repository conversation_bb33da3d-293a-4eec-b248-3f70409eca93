import json
import unittest
import uuid

from tests.base import BaseTestCase
from connections.tests.apis.connection.data_test import get_connection_types_expected_body, \
    connection_setup_fields_expected_body
from integrations.channels.shopify.shopify_connection import ShopifyConnection
from connections.src.apis.connection import (
    get_connection_types,
    get_connection_setup_fields,
    setup_connection,
    oauth2_callback
)


class ConnectionApiTestCaseNoMocks(BaseTestCase):
    def setUp(self):
        self.maxDiff = None

    def create_lambda_event(self, method='GET', path='/', body=None, query_params=None, path_params=None, user_id=None,
                            username=None, company_id=None):
        if user_id is None:
            user_id = str(uuid.uuid4())
        if username is None:
            username = f"user_{user_id}"
        if company_id is None:
            company_id = str(uuid.uuid4())

        event = {
            'httpMethod': method,
            'body': json.dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': {},
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': user_id,
                            'cognito:username': username,
                            'custom:company_id': company_id
                        }
                    }
                }
            },
            'isBase64Encoded': False
        }
        return event

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test_function'
                self.memory_limit_in_mb = 128
                self.aws_request_id = 'test_request_id'
                self.log_group_name = '/aws/lambda/test_function'
                self.log_stream_name = '2023/05/01/[$LATEST]abcdefghijklmnopqrstuvwxyz'
                self.identity = None
                self.client_context = None

        return LambdaContext()

    def assert_api_response(self, response, expected_status_code, expected_body):
        self.assertIsInstance(response, dict)
        self.assertIn('headers', response)
        self.assertIn('statusCode', response)
        self.assertIn('body', response)
        self.assertIn('isBase64Encoded', response)

        self.assertEqual(response['statusCode'], expected_status_code)
        self.assertEqual(response['isBase64Encoded'], False)

        self.assertDictEqual(response['headers'], {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
            'content-type': 'application/json'
        })

        actual_body = json.loads(response['body'])
        self.assertEqual(actual_body, expected_body)

    def test_get_connection_types(self):
        event = self.create_lambda_event()
        context = self.create_lambda_context()
        response = get_connection_types(event, context)

        self.assert_api_response(response, 200, get_connection_types_expected_body)

    def test_get_connection_setup_fields(self):
        event = self.create_lambda_event(path_params={'connection_type': 'shopify'})
        context = self.create_lambda_context()
        response = get_connection_setup_fields(event, context)

        self.assert_api_response(response, 200, connection_setup_fields_expected_body)

    def test_setup_shopify_connection(self):
        event = self.create_lambda_event(
            method='POST',
            body={
                "connection_type": "shopify",
                "setup_data": {
                    "shop_url": "https://test-shop.myshopify.com",
                    "api_key": "test_api_key",
                    "secret_key": "test_api_secret"
                }
            }
        )
        context = self.create_lambda_context()

        response = setup_connection(event, context)

        self.assertEqual(response['statusCode'], 200)

        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'shopify connection setup successful')
        self.assertIn('connection_id', response_body)

        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = ShopifyConnection.get(
            event['requestContext']['authorizer']['jwt']['claims']['custom:company_id'], connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.shop_url, 'https://test-shop.myshopify.com')
        self.assertEqual(created_connection.attributes.api_key, 'test_api_key')
        self.assertEqual(created_connection.attributes.secret_key, 'test_api_secret')
        self.assertEqual(created_connection.attributes.company_id,
                         event['requestContext']['authorizer']['jwt']['claims']['custom:company_id'])

        # Clean up: Delete the created connection
        created_connection.delete()

    def test_oauth2_callback(self):
        # This test assumes you have a way to create a valid state and code for testing
        # You might need to set up a test OAuth2 flow to get these values
        state = 'test_state'
        code = 'test_auth_code'

        event = self.create_lambda_event(
            method='GET',
            query_params={'state': state, 'code': code}
        )
        context = self.create_lambda_context()

        response = oauth2_callback(event, context)

        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Authorization successful')
        self.assertIn('connection_id', response_body)

        # Verify the authorized connection
        connection_id = response_body['connection_id']
        authorized_connection = ShopifyConnection.get(
            event['requestContext']['authorizer']['jwt']['claims']['custom:company_id'], connection_id)

        self.assertIsNotNone(authorized_connection)
        self.assertEqual(authorized_connection.attributes.status, 'ACTIVE')

        # Clean up: Delete the authorized connection
        authorized_connection.delete()

    def test_get_connection_setup_fields_not_found(self):
        event = self.create_lambda_event(path_params={'connection_type': 'non_existent'})
        context = self.create_lambda_context()
        response = get_connection_setup_fields(event, context)

        expected_body = {"request_id": "test_request_id", "error": "Not found", "message": "Connection type not found"}
        self.assert_api_response(response, 404, expected_body)

    def test_setup_connection_not_found(self):
        event = self.create_lambda_event(
            method='POST',
            body={"connection_type": "non_existent", "setup_data": {}}
        )
        context = self.create_lambda_context()
        response = setup_connection(event, context)

        expected_body = {"request_id": "test_request_id", "error": "Not found", "message": "Connection type not found"}
        self.assert_api_response(response, 404, expected_body)

    def test_oauth2_callback_missing_params(self):
        event = self.create_lambda_event(method='GET')
        context = self.create_lambda_context()
        response = oauth2_callback(event, context)

        expected_body = {"request_id": "test_request_id", "error": "Bad request",
                         "message": "Missing required parameters"}
        self.assert_api_response(response, 400, expected_body)


if __name__ == '__main__':
    unittest.main()
