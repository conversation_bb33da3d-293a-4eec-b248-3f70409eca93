from dataclasses import dataclass, field

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema

from integrations.channels.connection_types import ConnectionAttributes, ConnectionSchema
from models.integration.connection import ConnectionModel


class ShopifySettingsSchema(AttributesSchema):
    shop_url = fields.Url(required=True)
    api_key = fields.Str(required=True)
    secret_key = fields.Str(required=True)
    access_token = fields.Str(required=True)
    api_version = fields.Str(default="2023-04")


@dataclass
class ShopifySettings:
    shop_url: str
    api_key: str
    secret_key: str
    access_token: str
    api_version: str = "2023-04"


@dataclass
class ShopifyConnectionAttributes(ConnectionAttributes):
    channel_name: str = field(default='shopify')
    settings: ShopifySettings = field(default_factory=ShopifySettings)


class ShopifyConnectionSchema(ConnectionSchema):
    channel_name = fields.Str(required=True)
    settings = fields.Nested(ShopifySettingsSchema(ShopifySettings))


class ShopifyConnection(ConnectionModel):
    attributes: ShopifyConnectionAttributes = None
    attributes_schema = ShopifyConnectionSchema
    attributes_class = ShopifyConnectionAttributes
