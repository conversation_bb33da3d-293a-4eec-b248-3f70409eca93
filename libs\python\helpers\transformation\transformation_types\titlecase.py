from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class TitlecaseTransformationHandler(BaseTransformationHandler):
    """Handler for title case transformation"""
    
    config_definition = TransformationConfig(
        label="Title Case",
        description="Convert text to title case",
        direction=TransformationDirection(
            title="Title Case",
            content="Convert text to title case (capitalize first letter of each word)",
            example='"hello world" → "Hello World"',
            value_type="Single Value"
        )
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Converts the value to title case if it's not None"""
        return str(value).title() if value is not None else None 