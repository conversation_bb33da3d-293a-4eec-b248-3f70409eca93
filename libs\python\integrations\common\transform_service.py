import json
import os

import boto3
from botocore.exceptions import ClientError
from datetime import datetime

from nolicore.adapters.queue.aws.queue import sqs

from integrations.common.transformer_loader import TransformerLoader


s3 = boto3.client('s3')
dynamodb = boto3.resource('dynamodb')

PUBLISH_QUEUE_URL = os.environ['PUBLISH_QUEUE_URL']


class TransformationError(Exception):
    pass


def get_nested_value(data, path):
    keys = path.split('.')
    for key in keys:
        data = data.get(key, {})
    return data if data != {} else None


def set_nested_value(data, path, value):
    keys = path.split('.')
    for key in keys[:-1]:
        data = data.setdefault(key, {})
    data[keys[-1]] = value


def apply_operation(op_type, value, args):
    # Implementation of apply_operation remains the same
    pass


def generate_structure(structure_def, data):
    # Implementation of generate_structure remains the same
    return data


def get_connection(connection_id, table):
    try:
        response = table.get_item(Key={'id': connection_id})
        return response['Item']
    except ClientError as e:
        print(f"Error getting connection {connection_id}: {str(e)}")
        raise

