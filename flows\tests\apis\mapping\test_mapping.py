import unittest

from tests.base import BaseTestCase
from helpers.transformation.transformation import TransformationType

from nolicore.utils.exceptions import NotFoundRequest, BadRequest

from models.integration.sync_record import MappingStatus, SyncRecordModel
from flows.src.apis.mapping import (
    get_mapping_list, get_mapping, unmap, map_variants,
    map_sync_records, get_transformation_list, handle_transform
)


class TestMapping(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        cls.period = 'custom'
        cls.date_from = '2024-01-01'
        cls.date_to = '2025-01-01'
        if not cls.period:
            raise ValueError("Invalid period")
        if cls.period == 'custom':
            if not all([cls.period, cls.date_from, cls.date_to]):
                raise ValueError("period/date_from/date_to not found in environment variables")

    def test_get_mapping_list(self):
        query_params = {"page": "0",
                        "limit": "20",
                        "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
                        }

        event = self.create_lambda_event(
            path_params={'record_type': 'product'},
            query_params=query_params,
            is_authorized=True
        )
        context = self.create_lambda_context()

        response = get_mapping_list(event, context)

    def test_get_mapping_detail(self):
        event = {'version': '2.0', 'routeKey': 'POST /mappings/{record_type}/details',
                 'rawPath': '/mappings/product/details',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ws5VKFi-0COBg4B78SKR-FRDbETsOymF-GpepcUewlxr5aMYXPWaTxb6rGg8hnF3S8DFKcLwwkagSDmbLRe1Zsc7CA_WL0BbWdrawqHmofg49tQd3wpvy3p1hAdo2j3uadJTN4JyMIQRDJFnUtdZO6YLYaUqANL1pqIRJkiXXVRIlkFKu3so5d4-MyExgRNIqWPGQFt3dvrhLin6vbXuAwhwbE9u04tgBWFi6d-CNSL9Bd2YFQP_cksqaAqoDFTXrPay0sdJbYrlk4WUD62gzi6ZfUEXZ2TMoAR9URUaqaaydix7HeVcQWMKAmuG84PsIZHkjXf7KAVzs4OCzA0Zyw',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd',
                             'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,fr-FR;q=0.7,fr;q=0.6,en-US;q=0.5',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xct33yLEgqOS8JPejQkSBhk60VwYM_Vk0S0oU_o1_iHfmFsOm0BsRhLnR6F885v3N4UvHRuta1ptmymJVAFT_gxLffoXZgIqvjrIiIzHUOEpPFr0zpeLM0_wEXJXPVzylyXmVXLMpzMK4a37n4X9iUyVwmjJ449HpdfdZBD-E1JpvfowmxLUoUTJwz7EdtShtwfXvcxWH-vMxcAiCJgnDCHKkxYwa0dEeS5miKV1HCzZJz_T12Q6c5c9D1Ed6FKC5lvpJGCaICfQLP9BhoYo9wOQWSDcxBbCyPz30AtPL-IoV6SKsS3PLBwbcJmCq8opb9vA8GUzQUgBi8yOFMMwEQ',
                             'content-length': '104', 'content-type': 'application/json',
                             'host': 'api-staging.optiwarehouse.com', 'origin': 'http://localhost:3000',
                             'priority': 'u=1, i', 'referer': 'http://localhost:3000/',
                             'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Opera GX";v="117"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                             'sec-fetch-mode': 'cors', 'sec-fetch-site': 'cross-site',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
                             'x-amzn-trace-id': 'Root=1-6808b3b9-618be57b3c68138d048059b1',
                             'x-forwarded-for': '**************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ws5VKFi-0COBg4B78SKR-FRDbETsOymF-GpepcUewlxr5aMYXPWaTxb6rGg8hnF3S8DFKcLwwkagSDmbLRe1Zsc7CA_WL0BbWdrawqHmofg49tQd3wpvy3p1hAdo2j3uadJTN4JyMIQRDJFnUtdZO6YLYaUqANL1pqIRJkiXXVRIlkFKu3so5d4-MyExgRNIqWPGQFt3dvrhLin6vbXuAwhwbE9u04tgBWFi6d-CNSL9Bd2YFQP_cksqaAqoDFTXrPay0sdJbYrlk4WUD62gzi6ZfUEXZ2TMoAR9URUaqaaydix7HeVcQWMKAmuG84PsIZHkjXf7KAVzs4OCzA0Zyw'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': '62421a78-e344-4bef-81cd-4d5f71cdbb1e', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': '0e001ac4-df69-4995-90d2-b574e8db236c',
                                'origin_jti': 'c4b39b99-fa5e-4c47-b783-009d26662f69',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging',
                                    'http': {'method': 'POST', 'path': '/mappings/product/details',
                                             'protocol': 'HTTP/1.1', 'sourceIp': '**************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********'},
                                    'requestId': 'JeEFAgyiyQ0EJDg=', 'routeKey': 'POST /mappings/{record_type}/details',
                                    'stage': '$default', 'time': '23/Apr/2025:09:32:41 +0000',
                                    'timeEpoch': 1745400761254}, 'pathParameters': {'record_type': 'product'},
                 'body': '{"sync_record_id":"8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json"}',
                 'isBase64Encoded': False}

        context = self.create_lambda_context()

        response = get_mapping(event, context)

    def test_update_sync_record(self):
        key = "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json"
        connection_id = "b66c48f8-a826-4440-bfa9-ca4b1304f854"
        sync_record_data = {
            "mapping_status": MappingStatus.MAPPED.value,
            "standard_source_data": {
                "id": 1,
                "name": "SP1",
                "price": 199,
                "title": None
            },
            "standard_destination_data": {
                "id": 2,
                "name": "SP2",
                "price": 199,
                "title": None
            },
            "other_mappings": {
                "is_extra_destination_mapping": True,
                "mapping_data": {
                    "variant_id_1": "variant_id_2",
                    "variant_id_3": "variant_id_3"
                }
            }
        }
        updated_sync_record = SyncRecordModel.put(key, connection_id, sync_record_data)
        attribute = updated_sync_record.attributes_dict

    def test_unmap(self):
        event = self.create_lambda_event(
            body={
                "connection_id": "b66c48f8-a826-4440-bfa9-ca4b1304f854",
                "sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json"},
        )
        context = self.create_lambda_context()

        response = unmap(event, context)

    def test_map_variants(self):
        # Test case 1: is_extra_dest_mapping is False
        extra_mapping_data = {"variant_1": "dest_1", "variant_2": "dest_2"}
        source_variants = [
            {"id": "variant_1", "name": "Variant 1"},
            {"id": "variant_2", "name": "Variant 2"},
            {"id": "variant_3", "name": "Variant 3"}
        ]
        result = map_variants(False, extra_mapping_data, source_variants)
        self.assertEqual(result, extra_mapping_data)

        # Test case 2: is_extra_dest_mapping is True with new variants
        result = map_variants(True, extra_mapping_data, source_variants)
        expected = {
            "variant_1": "dest_1",
            "variant_2": "dest_2",
            "variant_3": "variant_3"  # New unmapped variant
        }
        self.assertEqual(result, expected)

        # Test case 3: is_extra_dest_mapping is True with empty extra_mapping_data
        result = map_variants(True, {}, source_variants)

        result = map_variants(False, {}, source_variants)
        # Test case 4: is_extra_dest_mapping is True with no new variants
        result = map_variants(True, extra_mapping_data, [
            {"id": "variant_1", "name": "Variant 1"},
            {"id": "variant_2", "name": "Variant 2"}
        ])
        self.assertEqual(result, extra_mapping_data)

    def test_map_sync_records(self):
        # Create a sync record to be mapped
        old_sync_record_id = 'b686e98e-788d-4050-a9bd-a644121a15b2/get_product/product/product_8920696520937.json'
        new_sync_record_id = "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314735848.json"
        sync_record_id = old_sync_record_id
        connection_id = "764b8784-c48f-4378-80ec-f0085c20f13b",
        record_type = "product"
        # Create the mapping request
        event = self.create_lambda_event(
            body={
                "sync_record_id": sync_record_id,
                "connection_id": connection_id,
                "record_type": record_type,
                "destination_data_id": "1731292238864221790",
                "is_extra_destination_mapping": False,
                "extra_mapping_data": {
                    "sku-hosted-1": "sku-hosted-2"
                },
            }
        )
        context = self.create_lambda_context()

        # Call the map_sync_records function
        response = map_sync_records(event, context)

    def test_get_transformation_list(self):
        """Test getting the list of available transformations"""
        event = self.create_lambda_event()
        context = self.create_lambda_context()

        # Get the list of transformations
        response = get_transformation_list(event, context)

    def test_handle_transform_2(self):
        body = {
            "sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314309864.json",
            "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
            "source_field": "name",
            "transformations": [
                {
                    "type": "LOWERCASE",
                    "config": {}
                }
            ]
        }
        event = self.create_lambda_event(
            body=body
        )
        response = handle_transform(event, self.create_lambda_context())

    def test_handle_transform(self):
        """Test handling transformations on source fields"""
        # Create a test sync record with source data
        sync_record_id = "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json"
        connection_id = "b66c48f8-a826-4440-bfa9-ca4b1304f854"

        def test_transformation(source_field, transformations, expected_outputs=None):
            """Helper function to test transformations"""
            event = self.create_lambda_event(
                body={
                    "sync_record_id": sync_record_id,
                    "connection_id": connection_id,
                    "source_field": source_field,
                    "transformations": transformations
                }
            )
            response = handle_transform(event, self.create_lambda_context())
            if expected_outputs is not None:
                self.assertEqual(response['data']['outputs'], expected_outputs)
            return response

        # Test 1: Basic text transformations

        # Test 2: Test all transformations in sequence
        response = test_transformation(
            "name",
            [
                {
                    "type": TransformationType.TRIM.value,
                    "config": {}
                },
                {
                    "type": TransformationType.UPPERCASE.value,
                    "config": {}
                },
                {
                    "type": TransformationType.SUBSTRING.value,
                    "config": {
                        "start": 0,
                        "end": 5
                    }
                },
                {
                    "type": TransformationType.LOWERCASE.value,
                    "config": {}
                },
                {
                    "type": TransformationType.PAD.value,
                    "config": {
                        "length": 10,
                        "char": " ",
                        "side": "both"
                    }
                },
                {
                    "type": TransformationType.REPLACE.value,
                    "config": {
                        "searchValue": "*",
                        "replaceValue": "-"
                    }
                },
                {
                    "type": TransformationType.CONCAT.value,
                    "config": {
                        "separator": " World"
                    }
                },
                {
                    "type": TransformationType.TITLECASE.value,
                    "config": {}
                },
                {
                    "type": TransformationType.SPLIT.value,
                    "config": {
                        "delimiter": " "
                    }
                },
                {
                    "type": TransformationType.PREFIX.value,
                    "config": {
                        "prefix": "Pre-"
                    }
                },
                {
                    "type": TransformationType.POSTFIX.value,
                    "config": {
                        "postfix": "-Post"
                    }
                }
            ],
            expected_outputs=[
                "  Hello World  ",  # After TRIM
                "HELLO WORLD",  # After UPPERCASE
                "HELLO",  # After SUBSTRING
                "hello",  # After LOWERCASE
                "**hello***",  # After PAD
                "--hello---",  # After REPLACE
                "--hello--- World",  # After CONCAT
                "--Hello--- World",  # After TITLECASE
                ["--Hello---", "World"],  # After SPLIT
                ["Pre---Hello---", "Pre-World"],  # After PREFIX
                ["Pre---Hello----Post", "Pre-World-Post"]  # After POSTFIX
            ]
        )
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Transformations applied successfully')

        # Test 3: Title case transformation
        response = test_transformation(
            "title",
            [
                {
                    "type": TransformationType.TITLECASE.value,
                    "config": {}
                }
            ],
            expected_outputs=["Test Product Title"]
        )
        self.assertTrue(response['success'])

        # Test error cases
        # Test 4: Missing source_field
        with self.assertRaises(BadRequest):
            test_transformation(None, [
                {
                    "type": TransformationType.UPPERCASE.value,
                    "config": {}
                }
            ])

        # Test 5: Missing transformations
        with self.assertRaises(BadRequest):
            test_transformation("name", [])

        # Test 6: Invalid transformation type
        with self.assertRaises(BadRequest):
            test_transformation(
                "name",
                [
                    {
                        "type": "invalid_type",
                        "config": {}
                    }
                ]
            )

        # Test 7: Invalid sync_record_id
        with self.assertRaises(NotFoundRequest):
            event = self.create_lambda_event(
                body={
                    "sync_record_id": "invalid_id",
                    "connection_id": connection_id,
                    "source_field": "name",
                    "transformations": [
                        {
                            "type": TransformationType.UPPERCASE.value,
                            "config": {}
                        }
                    ]
                }
            )
            handle_transform(event, self.create_lambda_context())

        # Test 8: Non-existent source field
        response = test_transformation(
            "non_existent_field",
            [
                {
                    "type": TransformationType.UPPERCASE.value,
                    "config": {}
                }
            ]
        )
        self.assertTrue(response['success'])
        self.assertEqual(response['data']['outputs'], [None])


if __name__ == '__main__':
    unittest.main()
