from dataclasses import dataclass
from enum import Enum

from marshmallow import fields
from marshmallow_enum import EnumField

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


class Role(str, Enum):
    USER = "USER"
    VIRTUAL_STAFF = "VIRTUAL_STAFF"
    EXTERNAL_USER = "EXTERNAL_USER"


@dataclass
class MessageAttributes(BasicAttributes):
    conversation_id: str = None
    user_id: str = None
    external_user_id: str = None
    connection_id: str = None
    role: Role = None
    content: str = None
    is_resolved: bool = True


class MessageAttributesSchema(BasicAttributesSchema):
    conversation_id = fields.Str(required=True)
    user_id = fields.Str(allow_none=True)
    external_user_id = fields.Str(allow_none=True)
    connection_id = fields.Str(required=False, allow_none=True)
    role = EnumField(Role, required=True)
    content = fields.Str(required=True)
    is_resolved = fields.Bool(required=False, allow_none=True, default=True)


class MessageModel(BasicModel):
    key__id = 'conversation_id'
    key__ranges = ['id']
    table_name = 'message'
    attributes: MessageAttributes
    attributes_schema = MessageAttributesSchema
    attributes_class = MessageAttributes

    query_mapping = {
        'query': ['id', 'conversation_id', 'user_id', 'role'],
        'filter': {
            'id': 'query',
            'conversation_id': 'query',
            'user_id': 'query',
            'external_user_id': 'query',
            'role': 'query',
            'is_resolved': 'boolean',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping':{
            'content': 'content'
        }
    }
    
    @classmethod
    def get_conversation_messages(cls, conversation_id: str):
        messages = []
        last_evaluated_key = None
        while True:
            response = cls.list({"conversation_id": conversation_id}, limit=300, last_evaluated_key=last_evaluated_key)
            messages.extend(response['Items'])
            last_evaluated_key = response.get('LastEvaluatedKey')
            if not last_evaluated_key:
                break
        return messages
