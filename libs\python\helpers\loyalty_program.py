import pendulum
from elasticsearch import NotFoundError
from nolicore.utils.exceptions import BadRequest

from helpers.errors import LoyaltyProgramDateInvalid
from helpers.utils import RESOURCE_SERVICE
from models.loyalty.loyalty_program import LoyaltyProgramModel


def validate_loyalty_program(company_id, start_date, expiry_date=None, loyalty_program_id=None):
    if not start_date:
        raise BadRequest("Required start_date")
    datetime_obj1 = pendulum.parse(start_date)
    if expiry_date is not None:
        datetime_obj2 = pendulum.parse(expiry_date)
        if datetime_obj1 < datetime_obj2:
            raise LoyaltyProgramDateInvalid()
    try:
        search_params = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "company_id.keyword": company_id
                            }
                        },
                    ],
                    "should": [
                        {
                            "range": {
                                "expiry_date": {
                                    "gt": start_date,
                                }
                            }
                        },
                        {
                            "bool": {
                                "must_not": {
                                    "exists": {
                                        "field": "expiry_date"
                                    }
                                }
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
        }
        # If loyalty_program_id is provided, add a must_not clause to exclude matching ids
        if loyalty_program_id is not None:
            search_params["query"]["bool"]["must_not"] = [
                {
                    "term": {
                        "id.keyword": loyalty_program_id
                    }
                }
            ]
        response = LoyaltyProgramModel.report(search_params, service=RESOURCE_SERVICE).body['hits']['hits']
    except NotFoundError as ex:
        response = []
    result = [item['_source'] for item in response]

    if len(result) > 0:
        raise BadRequest("Can not create a loyalty program in effective loyalty program date time!")
