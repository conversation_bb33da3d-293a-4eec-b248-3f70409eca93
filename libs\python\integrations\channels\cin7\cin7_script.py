import requests

# Replace with your Dear Inventory Account ID and API Key
ACCOUNT_ID = 'cdd0ccef-78e3-4d91-9d76-ed0e33ad148c'
API_KEY = '09296dff-7116-ff3a-4870-4e60c92509e0'

# Base URL for Dear Inventory API
BASE_URL = 'https://inventory.dearsystems.com/ExternalApi/v2/product'

# Headers for authorization
headers = {
    'Content-Type': 'application/json',
    'api-auth-accountid': ACCOUNT_ID,
    'api-auth-applicationkey': API_KEY
}


def get_products(page=1, page_size=100):
    # API endpoint with pagination
    url = f'{BASE_URL}?Page={page}&Limit={page_size}'

    try:
        # Make the API request
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise exception for HTTP errors

        # Parse the JSON response
        products = response.json()

        return products

    except requests.exceptions.RequestException as e:
        print(f'Error fetching products: {e}')
        return None


# Fetch and print products
products = get_products()

if products:
    print(f"Total products fetched: {len(products['Products'])}")
    for product in products['Products']:
        print(f"Product SKU: {product['SKU']}, Name: {product['Name']}")
else:
    print("No products found or error in fetching data.")
