import json
import unittest
import uuid
from dotenv import load_dotenv

from tests.base import BaseTestCase
from connections.src.apis.connection import setup_connection, test_connection, oauth2_callback
from models.integration.connection import ConnectionModel


class TestConnectionIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file

    def setUp(self):
        super().setUp(account_type="onexapis_admin")

    def test_connection_success(self):
        # Arrange: Set up a test connection
        connection_id = '0cf9dab4-39b0-4edc-9f83-5cad2f4d07f6'

        # Act: Test the connection
        test_event = self.create_lambda_event({}, path_params={'connection_id': connection_id}, is_authorized=True)
        test_context = self.create_lambda_context()
        result = test_connection(test_event, test_context)

        # Assert
        self.assertEqual(result['statusCode'], 200)
        result_body = json.loads(result['body'])
        self.assertEqual(result_body, {"message": "Connection test successful", "status": "success"})

        # Clean up
        ConnectionModel.get(self.company_id, connection_id).delete()

    def test_connection_failure(self):
        # Arrange: Set up a test connection with invalid settings
        setup_event = self.create_lambda_event({
            "connection_type": "test_connection",
            "settings": {
                "test_key": "invalid_value"
            }
        })
        setup_context = self.create_lambda_context()
        setup_response = setup_connection(setup_event, setup_context)
        setup_body = json.loads(setup_response['body'])
        connection_id = setup_body['connection_id']

        # Act: Test the connection
        test_event = self.create_lambda_event({}, {'connection_id': connection_id})
        test_context = self.create_lambda_context()
        result = test_connection(test_event, test_context)

        # Assert
        self.assertEqual(result['statusCode'], 200)
        result_body = json.loads(result['body'])
        self.assertEqual(result_body, {"message": "Connection test failed", "status": "failure"})

        # Clean up
        ConnectionModel.get(self.company_id, connection_id).delete()

    def test_connection_not_found(self):
        # Arrange
        non_existent_id = str(uuid.uuid4())
        test_event = self.create_lambda_event({}, {'connection_id': non_existent_id})
        test_context = self.create_lambda_context()

        # Act
        result = test_connection(test_event, test_context)

        # Assert
        self.assertEqual(result['statusCode'], 404)
        result_body = json.loads(result['body'])
        self.assertIn("Connection not found", result_body['message'])

    def test_missing_connection_id(self):
        # Arrange
        test_event = self.create_lambda_event({})
        test_context = self.create_lambda_context()

        # Act
        result = test_connection(test_event, test_context)

        # Assert
        self.assertEqual(result['statusCode'], 400)
        result_body = json.loads(result['body'])
        self.assertIn("Missing connection_id parameter", result_body['message'])

    def test_oauth2_callback(self):
        test_event = self.create_lambda_event(query_params={
            "app_key": "6fmsibjotg0ie",
            "code": "ROW_BZjpdwAAAADvXMz2_T5yEVJ0VTaPXemkQPBVanBZEw-tEzx6rX72dvoDg9ftEqLeZUr5bDpclT-2UQF3VC0hE3E31CYGz7DpfAikV4J0WRoPnfheZgslAmTgg_XEHlZfQNKliEGwn6659VHVlU-dlireLnfHEbGK",
            "locale": "en-GB",
            "shop_region": "SG",
            "state": "eJyrVkrOz8tLTS7JzM+Lz0xRslJQSk4xTE21NLDUNTBNM9A1MUs00U1KMzHVTU5KTTI1Nze0MLc0VKoFAAvKELM="
        }

        )
        test_context = self.create_lambda_context()

        # Act
        result = oauth2_callback(test_event, test_context)

    def test_connection_test_exception(self):
        # Arrange: Set up a test connection that will raise an exception
        setup_event = self.create_lambda_event({
            "connection_type": "test_connection",
            "settings": {
                "test_key": "raise_exception"
            }
        })
        setup_context = self.create_lambda_context()
        setup_response = setup_connection(setup_event, setup_context)
        setup_body = json.loads(setup_response['body'])
        connection_id = setup_body['connection_id']

        # Act: Test the connection
        test_event = self.create_lambda_event({}, {'connection_id': connection_id})
        test_context = self.create_lambda_context()
        result = test_connection(test_event, test_context)

        # Assert
        self.assertEqual(result['statusCode'], 200)
        result_body = json.loads(result['body'])
        self.assertIn("Connection test failed", result_body['message'])
        self.assertEqual(result_body['status'], "failure")

        # Clean up
        ConnectionModel.get(self.company_id, connection_id).delete()

    def test_test_connection(self):
        event = {'version': '2.0', 'routeKey': 'GET /connection/{connection_id}/test',
                 'rawPath': '/connection/ea8e035a-98d9-4329-a326-ee38bc531fe5/test',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.24FdPBWPt6QqjsHiYI8mgkzgWDEEMAuUzyenSWq2KFOVDR9GAGde1Y-jelmcaFqgH2_79sQKD71aLe7Wq7VpYlBUyL3vn7dj6n4O1AzTYaTjTfuMzt5vRcQzkViLOh6lp4ZFfrpoPN_FECaYvJAaMljbfgktW1LFL1eF4iDG0i8ERD2KSdphjHtxnlOsC6kS0Yjx-0pQkoWaNQiWmgxx7mIKkKJ_7OJaCh6yAzznU5Jlwk-0olTNTXk5JszQ6rSVNEI2w2_VfJBXuAX-t5dFSy9PLrQIoN-18xfMDeksPvtVDfPftIBQFz_QUq6ByPYAmoGdE-fNwGAcQ3u-KZRXDQ',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-US,en;q=0.6',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AARuzB0vDliNadZmb3C4im-CQg5WBI5SHv8Icgbgr84qsO6lAq1Fi4j0uj9i4Uw3NF2wLFrYeAiwESJ5BN2xJQOlQFsT7_D-RnWPsPpxOGG9HMc4gugHDHZYgoRMqF0fUVNaQJ_ojw3IRbuM6opUVBAVoA_pUKv0zt-1_4qQG4CUM4g8rcSVEkYOXpUiIAcLGtttqVXTiGcvhNsWrZ4haHjiUxBg3kOxZgWnpPCIytlEGsTVXLcRXjVv6YqSlDiyz3u4ATEFAeLpm2QysMTV9ogj1FBXcxz_s9J1p7DelFqgjA_JoVc5S1B18TzO7MRVp14x1JmkUh_r_dhoIo2jiw',
                             'content-length': '0', 'host': 'api-staging.optiwarehouse.com',
                             'origin': 'https://staging.optiwarehouse.com', 'priority': 'u=1, i',
                             'referer': 'https://staging.optiwarehouse.com/',
                             'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                             'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site', 'sec-gpc': '1',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                             'x-amzn-trace-id': 'Root=1-684816bd-44815dbc30848db413966959',
                             'x-forwarded-for': '**************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.24FdPBWPt6QqjsHiYI8mgkzgWDEEMAuUzyenSWq2KFOVDR9GAGde1Y-jelmcaFqgH2_79sQKD71aLe7Wq7VpYlBUyL3vn7dj6n4O1AzTYaTjTfuMzt5vRcQzkViLOh6lp4ZFfrpoPN_FECaYvJAaMljbfgktW1LFL1eF4iDG0i8ERD2KSdphjHtxnlOsC6kS0Yjx-0pQkoWaNQiWmgxx7mIKkKJ_7OJaCh6yAzznU5Jlwk-0olTNTXk5JszQ6rSVNEI2w2_VfJBXuAX-t5dFSy9PLrQIoN-18xfMDeksPvtVDfPftIBQFz_QUq6ByPYAmoGdE-fNwGAcQ3u-KZRXDQ'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': '56689f66-fb35-4565-9268-6afe8b81d46a', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': '7447e6b9-d309-4ebd-beb1-dea0ad17263c',
                                'origin_jti': '6a7188bb-995a-4c25-89a9-2c54ad7a6e41',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging',
                                    'http': {'method': 'GET',
                                             'path': '/connection/ea8e035a-98d9-4329-a326-ee38bc531fe5/test',
                                             'protocol': 'HTTP/1.1', 'sourceIp': '**************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
                                    'requestId': 'L8h9ugeqyQ0EPEw=', 'routeKey': 'GET /connection/{connection_id}/test',
                                    'stage': '$default', 'time': '10/Jun/2025:11:27:57 +0000',
                                    'timeEpoch': **********828},
                 'pathParameters': {'connection_id': 'ea8e035a-98d9-4329-a326-ee38bc531fe5'}, 'isBase64Encoded': False}

        test_context = self.create_lambda_context()
        result = test_connection(event, test_context)


if __name__ == '__main__':
    unittest.main()
