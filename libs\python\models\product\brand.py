from dataclasses import dataclass

from marshmallow import fields, EXCLUDE
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.media.image import Image, ImageSchema


@dataclass
class Brand(Attributes):
    id: str = None
    name: str = None
    image: Image = None


class BrandSchema(AttributesSchema):
    class Meta:
        unknown = EXCLUDE

    id = fields.Str(required=True)
    name = fields.Str(required=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)


@dataclass
class BrandAttributes(Brand, BasicAttributes):
    pass


class BrandAttributesSchema(BrandSchema, BasicAttributesSchema):
    pass


class BrandModel(BasicModel):
    table_name = 'brand'
    attributes: BrandAttributes
    attributes_schema = BrandAttributesSchema
    attributes_class = BrandAttributes

    query_mapping = {
        'query': ['name', 'id'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'image': 'image',
        }
    }

    @classmethod
    def put(cls, data, company_id):
        key = str(data.get('id'))
        record = cls.by_key({
            'id': key,
            'company_id': company_id
        })

        if record:
            record.update(data)
            return record
        record = cls.create(company_id, {'id': key, 'company_id': company_id, **data})
        return record
