from dataclasses import dataclass, field

from marshmallow import fields

from ..connection_base import OAuth2Connection, OAuth2ConnectionAttributes, OAuth2ConnectionSchema, \
    OAuth2SettingsSchema, OAuth2Settings


class HaravanSettingsSchema(OAuth2SettingsSchema):
    org_id = fields.Str(required=True)
    client_id = fields.Str(required=True)
    client_secret = fields.Str(required=True)
    base_url = fields.Url(default="https://apis.haravan.com")


@dataclass
class HaravanSettings(OAuth2Settings):
    org_id: str = None
    client_id: str = None
    client_secret: str = None
    base_url: str = "https://apis.haravan.com"


@dataclass
class HaravanConnectionAttributes(OAuth2ConnectionAttributes):
    channel_name: str = field(default='haravan')
    settings: HaravanSettings = field(default_factory=HaravanSettings)


class HaravanConnectionSchema(OAuth2ConnectionSchema):
    settings = fields.Nested(HaravanSettingsSchema(HaravanSettings))


class HaravanConnection(OAuth2Connection):
    attributes: HaravanConnectionAttributes = None
    attributes_schema = HaravanConnectionSchema
    attributes_class = HaravanConnectionAttributes
