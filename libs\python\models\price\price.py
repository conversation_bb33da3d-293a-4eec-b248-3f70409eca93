from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Model, Attributes, AttributesSchema

from models.price.price_group import PriceGroup, PriceGroupSchema


@dataclass
class PriceAttributes(Attributes):
    price: int
    price_group: PriceGroup


class PriceAttributesSchema(AttributesSchema):
    price = fields.Int(required=True)
    price_group = fields.Nested(PriceGroupSchema(PriceGroup), required=True)


class PriceModel(Model):
    key__id = 'id'
    table_name = 'price'
