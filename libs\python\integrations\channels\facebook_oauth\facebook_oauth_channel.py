from typing import Dict, Any, Tu<PERSON>, List, Optional

from helpers.custom_jsonpath import get_value_from_jsonpath
from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import AbstractChannel, OAuth2Channel
from integrations.channels.connection_types import ChannelType
from integrations.channels.facebook_oauth.facebook_oauth_connection import FacebookOAuthSettings
from integrations.channels.facebook_oauth.facebook_oauth_library import FacebookOAuthLibrary


class FacebookOAuthChannel(OAuth2Channel, AbstractChannel):
    channel_name = 'facebook_oauth'
    library_class = FacebookOAuthLibrary

    def _create_library(self, connection):
        settings: FacebookOAuthSettings = connection.attributes.settings
        return FacebookOAuthLibrary(
            app_id=settings.app_id,
            app_secret=settings.app_secret,
            access_token=connection.attributes.access_token,
            company_id=connection.attributes.company_id,
            redirect_uri=settings.redirect_uri,
            pages=get_value_from_jsonpath(connection.attributes_dict, '$.token_data.pages') or []
        )

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            'name': 'Facebook OAuth',
            'channel_type': ChannelType.MARKETING,
            'description': 'Connect your Facebook Page to manage messages and interactions.',
            'logo': cls._load_image_as_base64(__file__, 'facebook_oauth.png')
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.send_message:
            return {}
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.send_message:
            return {}
        else:
            return {}

    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        access_code = query_params.get('code')
        if not access_code:
            raise ValueError("Missing code in query parameters")

        token_data = self.library.fetch_token(access_code, state=self.connection.attributes.id)

        return (
            token_data['external_id'],
            token_data['access_token'],
            token_data['expires_at'],
            token_data['refresh_token'],
            token_data['token_data']
        )

    def refresh_oauth_token(self) -> Dict[str, Any]:
        pass

    def initiate_oauth_flow(self, redirect_url: str = None, base_url: str = None) -> str:
        return self.library.get_authorization_url(str(self.connection.attributes.id), redirect_url, base_url)

    def test_connection(self) -> bool:
        try:
            return self.library.test_connection()
        except Exception:
            return False

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        return self.library.extract_object_details(payload, headers, event_type)

    def process_webhook_event(self, event_type, payload) -> bool:
        return self.library.process_webhook_event(event_type, payload, self.connection)

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        if query_string_params and query_string_params['hub.mode'] == 'subscribe':
            return int(query_string_params['hub.challenge'])

        return False

    @classmethod
    def verify_webhook_signature(cls, payload: str, headers: dict, settings: FacebookOAuthSettings) -> bool:
        signature = headers.get('x-hub-signature-256')
        return FacebookOAuthLibrary.verify_webhook_signature(payload, signature, settings.app_secret)

    def webhook__webhook(self, settings: Dict[str, Any], message_data: Dict[str, Any]):
        return self.library.process_webhook(settings, message_data)

    def setup_webhooks(self):
        self.library.setup_webhooks(self.connection)

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        # For Facebook webhooks, the event type is determined by the messaging type
        entry = payload.get('entry', [{}])[0]
        messaging = entry.get('messaging', [{}])[0]

        if 'message' in messaging:
            return 'messages'
        elif 'postback' in messaging:
            return 'messaging_postbacks'
        elif 'optin' in messaging:
            return 'messaging_optins'

        return 'unknown'

    @classmethod
    def extract_merchant_id(cls, payload):
        return cls.library_class.extract_merchant_id(payload)
