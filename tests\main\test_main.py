import unittest
from main import print_board, check_winner, tic_tac_toe

class TestTicTacToe(unittest.TestCase):

    def test_print_board(self):
        board = [['X', 'O', 'X'], [' ', 'X', 'O'], ['O', 'X', ' ']]
        expected_output = "X | O | X\n---------\n  | X | O\n---------\nO | X |  \
---------\n"
        with unittest.mock.patch('sys.stdout', new_callable=io.StringIO) as mock_stdout:
            print_board(board)
            self.assertEqual(mock_stdout.getvalue(), expected_output)

    def test_check_winner(self):
        board_win_row = [['X', 'X', 'X'], [' ', 'O', ' '], ['O', ' ', ' ']]
        self.assertTrue(check_winner(board_win_row))
        board_win_col = [['X', ' ', 'O'], ['X', ' ', 'O'], ['X', 'O', ' ']]
        self.assertTrue(check_winner(board_win_col))
        board_win_diag = [['X', ' ', 'O'], [' ', 'X', 'O'], ['O', ' ', 'X']]
        self.assertTrue(check_winner(board_win_diag))
        board_no_win = [['X', 'O', 'X'], ['O', 'X', 'O'], ['O', 'X', 'X']]
        self.assertFalse(check_winner(board_no_win))

if __name__ == '__main__':
    unittest.main()