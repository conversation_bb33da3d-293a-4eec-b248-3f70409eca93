import json
import unittest

from dotenv import load_dotenv

from tests.base import BaseTestCase
from flows.src.events.step_2_fetch import fetch_lambda_handler


class TestFetchLambdaHandler(BaseTestCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.connection_id = "6b5ca363-434b-4127-8258-3bbf1afc5e50"
        cls.destination_id = "5abf07bc-dca7-4c43-872a-4c7f11791566"
        cls.source_key = "6b5ca363-434b-4127-8258-3bbf1afc5e50/get_product/product/product_39105678.json"
        cls.destination_key = "6b5ca363-434b-4127-8258-3bbf1afc5e50/get_product/product/product_39105678.haravan"
        cls.destination_version_id = "aC0vvrN4mRj05_L1oaOr_yF1x.mabNun"
        cls.source_version_id = "cVUR8zVbJCD5fnoxkfzdSX.YW4XjQ0ro"
        cls.object_type = 'product'
        cls.fetch_event_id = "4800fc6e-4250-408d-b6c5-a4f0f58c0358"

        if not all(
                [cls.connection_id, cls.destination_id, cls.source_key, cls.destination_key, cls.destination_version_id,
                 cls.source_version_id, cls.object_type, cls.fetch_event_id]):
            raise ValueError("Shopify credentials not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        # self.company_id = str(uuid.uuid4())
        # self.user_id = str(uuid.uuid4())
        # self.username = f"user_{self.user_id}"

        self.company_id = "4c8e6154-fba3-4837-909b-8a50dff16a2b"
        self.user_id = "56c8ca59-2214-4d3e-95f8-0fa3178d0031"
        self.username = "baababy_admin"

    def create_lambda_event(self, body):
        return {'Records': [{
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            },
        }]}

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_fetch_lambda_handler(self):
        fetch_event = {
            "object_data": None,
            "company_id": "4c8e6154-fba3-4837-909b-8a50dff16a2b",
            "created_at": "2025-05-27T08:51:26.677951+00:00",
            "event_time": "2025-05-27T08:51:26.677771+00:00",
            "meta": {
                "total_page": "0",
                "limit": "50",
                "current_params": {
                    "page": "1",
                    "icpp": "50",
                    "updatedDateTimeTo": None,
                    "updatedDateTimeFrom": None,
                    "updated_after": "2025-05-27T07:50:26.387336+00:00"
                },
                "continuation_token": None,
                "page": "1",
                "total_count": "0"
            },
            "status": "FAILED",
            "user": None,
            "batch_id": "d4495ccb-f556-4249-8a39-e676594f5117",
            "event_source": "scheduler",
            "channel": "nhanh",
            "destination_ids": None,
            "updated_at": "2025-05-27T08:53:43.141003+00:00",
            "action_type": "get_inventory",
            "retry_count": "3",
            "object_ids": None,
            "object_id": None,
            "error_msg": "Fetch attempt 3 failed: 'NoneType' object is not iterable",
            "id": "227a967c-a818-4a97-8ea6-2e6152f92967",
            "continuation_token": "INIT",
            "is_batch": False,
            "connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e50",
            "action_group": "inventory"
        }

        event_body = {key: value for key, value in fetch_event.items() if
                      key not in ['created_at', 'updated_at', 'user']}

        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = fetch_lambda_handler(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        self.assertEqual(json.loads(response['body']), 'Fetch Lambda execution completed')


if __name__ == '__main__':
    unittest.main()
