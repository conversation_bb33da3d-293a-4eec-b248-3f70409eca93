installConnection:
  handler: src.apis.install.install
  events:
    - httpApi:
        path: /install
        method: post

listPlans:
  handler: src.apis.install.list_plans
  events:
    - httpApi:
        path: /list_plans
        method: get
        authorizer:
          name: optiAuthorizer

subscribePlan:
  handler: src.apis.install.subscribe_plan
  events:
    - httpApi:
        path: /subscribe_plan
        method: post
        authorizer:
          name: optiAuthorizer

subscriptionCallback:
  handler: src.apis.install.subscription_callback
  events:
    - httpApi:
        path: /subscriptions/callback
        method: get
        authorizer:
          name: optiAuthorizer


