import uuid
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Dict, Any, List

from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.base_api_library import MetaData

DEFAULT_CONTINUE_TOKEN = "INIT"


class EventSource(str, Enum):
    webhook = "webhook"
    sync_record_api = "sync_record_api"
    sync_filter_api = "sync_filter_api"
    scheduler = "scheduler"
    import_record_api = "import_record_api"
    import_record_api_clone = "import_record_api_clone"


class FetchEventStatus(str, Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"
    COMPLETED = "COMPLETED"


@dataclass
class FetchEvent:
    id: str = None
    channel: str = None
    company_id: str = None
    connection_id: str = None
    action_type: ActionType = None
    action_group: ActionGroup = None
    event_time: str = None
    batch_id: str = None
    error_msg: str = None
    is_batch: bool = None
    continuation_token: Optional[str] = None
    retry_count: int = 0
    object_id: Optional[str] = None
    object_data: Optional[Dict[str, Any]] = None
    object_ids: Optional[List[str]] = None
    meta: Optional[MetaData] = None
    event_source: EventSource = EventSource.scheduler
    status: FetchEventStatus = FetchEventStatus.PENDING
    destination_ids: Optional[List[str]] = None

    def __init__(self, connection_id: str, action_type: ActionType, action_group: ActionGroup, event_time: str,
                 is_batch: bool, continuation_token: Optional[str] = DEFAULT_CONTINUE_TOKEN,
                 id: Optional[str] = None, company_id: str = None, channel: str = None, retry_count: int = 0,
                 batch_id: Optional[str] = None, object_id: Optional[str] = None,
                 object_data: Optional[Dict[str, Any]] = None, object_ids: Optional[List[str]] = None,
                 meta: Optional[MetaData] = None, error_msg: str = None,
                 event_source: EventSource = EventSource.scheduler,
                 status: FetchEventStatus = FetchEventStatus.PENDING, destination_ids: Optional[List[str]] = None):
        self.id = id or str(uuid.uuid4())
        self.connection_id = str(connection_id)
        self.channel = channel
        self.company_id = company_id
        self.action_type = action_type
        self.action_group = action_group
        self.event_time = event_time
        self.continuation_token = continuation_token
        self.retry_count = retry_count
        self.batch_id = batch_id or str(uuid.uuid4())
        self.is_batch = is_batch
        self.error_msg = error_msg
        self.object_id = object_id
        self.object_data = object_data
        self.object_ids = object_ids
        self.meta = meta
        self.event_source = event_source
        self.status = status
        self.destination_ids = destination_ids

    @property
    def next_page(self):
        return self.continuation_token if self.continuation_token != DEFAULT_CONTINUE_TOKEN else None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FetchEvent':
        data['action_type'] = ActionType(data['action_type'])
        data['action_group'] = ActionGroup(data['action_group'])
        data['event_source'] = EventSource(data['event_source'])
        data['status'] = FetchEventStatus(data['status'])
        if 'meta' in data and data['meta']:
            data['meta'] = MetaData(**data['meta'])
        return cls(**data)

    def to_dict(self) -> Dict[str, Any]:
        result = {
            'id': self.id,
            'channel': self.channel,
            'company_id': self.company_id,
            'connection_id': self.connection_id,
            'action_type': self.action_type.value if isinstance(self.action_type, Enum) else self.action_type,
            'action_group': self.action_group.value if isinstance(self.action_group, Enum) else self.action_group,
            'event_time': self.event_time,
            'batch_id': self.batch_id,
            'is_batch': self.is_batch,
            'continuation_token': self.continuation_token,
            'retry_count': self.retry_count,
            'object_id': self.object_id,
            'error_msg': self.error_msg,
            'object_data': self.object_data,
            'object_ids': self.object_ids,
            'destination_ids': self.destination_ids,
            'meta': self.meta.__dict__ if self.meta else None,
            'event_source': self.event_source.value if isinstance(self.event_source, Enum) else self.event_source,
            'status': self.status.value if isinstance(self.status, Enum) else self.status,
        }
        return result

    def copy(self) -> 'FetchEvent':
        return FetchEvent(**self.to_dict())

    @classmethod
    def create_single_object_event(cls, channel: str, connection_id: str, action_type: ActionType,
                                   action_group: ActionGroup,
                                   event_time: str, object_id: str, object_data: Dict[str, Any],
                                   **kwargs) -> 'FetchEvent':
        return cls(
            channel=channel,
            connection_id=connection_id,
            action_type=action_type,
            action_group=action_group,
            event_time=event_time,
            is_batch=False,
            object_id=object_id,
            object_data=object_data,
            **kwargs
        )

    @classmethod
    def create_batch_object_event(cls, channel: str, connection_id: str, action_type: ActionType,
                                  action_group: ActionGroup,
                                  event_time: str, object_ids: List[str],
                                  object_data: Dict[str, Any], **kwargs) -> 'FetchEvent':
        return cls(
            channel=channel,
            connection_id=connection_id,
            action_type=action_type,
            action_group=action_group,
            event_time=event_time,
            is_batch=True,
            object_ids=object_ids,
            object_data=object_data,
            **kwargs
        )
