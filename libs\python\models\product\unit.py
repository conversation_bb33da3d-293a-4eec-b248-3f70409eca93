from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


@dataclass
class Unit(Attributes):
    id: str = None
    name: str = None
    ratio: int = None


class UnitSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    ratio = fields.Int(required=True)


@dataclass
class UnitAttributes(Unit, BasicAttributes):
    pass


class UnitAttributesSchema(UnitSchema, BasicAttributesSchema):
    pass


class UnitModel(BasicModel):
    table_name = 'unit'
    attributes: UnitAttributes
    attributes_schema = UnitAttributesSchema
    attributes_class = UnitAttributes

    query_mapping = {
        'query': ['name', 'id'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'ratio': 'number',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
    }
