import unittest

from tests.base import BaseTestCase
from resources.src.events.send_notification import handle
from resources.tests.events.send_notification.data_send_noti import zns_event


class NotificationTestCase(BaseTestCase):
    def test_zns(self):
        response = handle(zns_event, {})
        self.assertTrue(True)


if __name__ == '__main__':
    # begin the unittest.main()
    unittest.main()
