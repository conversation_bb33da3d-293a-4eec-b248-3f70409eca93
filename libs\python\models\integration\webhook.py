from dataclasses import dataclass

from marshmallow import fields, EXCLUDE
from nolicore.adapters.db.model import Attributes, AttributesSchema, Model

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


@dataclass
class Webhook(BasicAttributes):
    id: str = None
    connection_id: str = None
    data: dict = None


class WebhookSchema(BasicAttributesSchema):
    class Meta:
        unknown = EXCLUDE

    id = fields.Str(required=True)
    connection_id = fields.Str(required=True)
    data = fields.Dict(allow_none=True)


class WebhookModel(BasicModel):
    key__id = 'id'
    key__ranges = ['connection_id']
    table_name = 'webhook'
    attributes: Webhook
    attributes_schema = WebhookSchema
    attributes_class = Webhook

    query_mapping = {
        'query': ['id', 'connection_id'],
        'filter': {
            'updated_at': 'range',
            'created_at': 'range',
        }
    }


class ConnectionIdIndex(WebhookModel):
    key__id = 'connection_id'
    key__ranges = ['id']
    index_name = 'connectionIdIndex'
