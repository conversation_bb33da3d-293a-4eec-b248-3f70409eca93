from ...base import DestinationTransformer


class SalesforceCustomerTransformer(DestinationTransformer):
    channel_name = 'salesforce'
    object_type = 'customer'

    def transform(self, data):
        return {
            'FirstName': data.get('first_name'),
            'LastName': data.get('last_name'),
            'Email': data.get('email'),
            'Phone': data.get('phone'),
            'MailingStreet': data.get('address', {}).get('street'),
            'MailingCity': data.get('address', {}).get('city'),
            'MailingState': data.get('address', {}).get('state'),
            'MailingPostalCode': data.get('address', {}).get('postal_code'),
            'MailingCountry': data.get('address', {}).get('country')
        }

    @property
    def input_schema(self):
        return {
            'type': 'object',
            'properties': {
                'first_name': {'type': 'string'},
                'last_name': {'type': 'string'},
                'email': {'type': 'string'},
                'phone': {'type': 'string'},
                'address': {
                    'type': 'object',
                    'properties': {
                        'street': {'type': 'string'},
                        'city': {'type': 'string'},
                        'state': {'type': 'string'},
                        'postal_code': {'type': 'string'},
                        'country': {'type': 'string'}
                    }
                }
            }
        }

    @property
    def output_schema(self):
        return {
            'type': 'object',
            'properties': {
                'FirstName': {'type': 'string'},
                'LastName': {'type': 'string'},
                'Email': {'type': 'string'},
                'Phone': {'type': 'string'},
                'MailingStreet': {'type': 'string'},
                'MailingCity': {'type': 'string'},
                'MailingState': {'type': 'string'},
                'MailingPostalCode': {'type': 'string'},
                'MailingCountry': {'type': 'string'}
            }
        }
