#!/usr/bin/env python3
import requests
import yaml
import json
import uuid
import os
import argparse
import sys

# Use raw string for Windows path
default_folders = {
    'son': r"C:\Users\<USER>\Desktop\Projects\noliwms-apis\integration\flows\yml\functions",
    'nhon': r"E:\work\main work\onexapis company\projects\optiwarehouse-integration\optiwarehouse-integration\connections\yml\functions",
    'thieu': r"D:\Code\OneXApis\optiwarehouse-integration\connections\yml\functions",
}
default_folder = default_folders.get('nhon', None)
prefix = "/" + default_folder.split('\\')[-3]
api_key = '****************************************************************'
workspace_id = 'eec78901-dfd0-4f61-bd88-a2eca1711324'
default_collection = 'optiwarehouse'


def generate_uuid():
    return str(uuid.uuid4())


def generate_sample_bodies(method):
    if method == 'POST':
        return [
            {
                "name": "Sample 1",
                "body": {
                    "key1": "value1",
                    "key2": "value2",
                    "key3": 0,
                    "key4": True
                }
            },
            {
                "name": "Sample 2",
                "body": {
                    "key1": "another_value1",
                    "key2": "another_value2",
                    "key3": 1,
                    "key4": False
                }
            }
        ]
    elif method == 'PUT':
        return [
            {
                "name": "Update Sample 1",
                "body": {
                    "key1": "updated_value1",
                    "key2": "updated_value2",
                    "key3": 1,
                    "key4": False
                }
            },
            {
                "name": "Update Sample 2",
                "body": {
                    "key1": "another_updated_value1",
                    "key2": "another_updated_value2",
                    "key3": 2,
                    "key4": True
                }
            }
        ]
    return []


def load_or_create_body_samples(yaml_file_path, yaml_content):
    json_file_path = os.path.splitext(yaml_file_path)[0] + '.json'
    if os.path.exists(json_file_path):
        with open(json_file_path, 'r') as json_file:
            return json.load(json_file)

    # If JSON file doesn't exist, create it based on YAML content
    body_samples = {}
    yaml_data = yaml.safe_load(yaml_content)

    for endpoint_name, endpoint_data in yaml_data.items():
        body_samples[endpoint_name] = {}
        for event in endpoint_data.get('events', []):
            if 'httpApi' in event:
                method = event['httpApi']['method'].upper()
                if method in ['POST', 'PUT']:
                    body_samples[endpoint_name][method] = generate_sample_bodies(method)

    with open(json_file_path, 'w') as json_file:
        json.dump(body_samples, json_file, indent=2)

    print(f"Generated sample body file: {json_file_path}")
    return body_samples


def convert_yaml_to_postman(yaml_content, file_path, _prefix=""):
    body_samples = load_or_create_body_samples(file_path, yaml_content)
    data = yaml.safe_load(yaml_content)

    folder_name = os.path.splitext(os.path.basename(file_path))[0]
    folder_item = {
        "name": folder_name,
        "item": [],
        "_postman_isSubFolder": True
    }

    for endpoint_name, endpoint_data in data.items():
        events = endpoint_data.get('events', [])
        if not events:
            continue
        endpoint_folder = {
            "name": endpoint_name,
            "item": [],
            "_postman_isSubFolder": True
        }

        for event in events:
            if 'httpApi' in event:
                http_api = event['httpApi']
                full_path = _prefix + http_api['path']
                method = http_api['method'].upper()

                base_item = {
                    "name": f"{method} {endpoint_name}",
                    "request": {
                        "auth": {
                            "type": "bearer",
                            "bearer": [
                                {
                                    "key": "token",
                                    "value": "{{token}}",
                                    "type": "string"
                                }
                            ]
                        },
                        "method": method,
                        "header": [],
                        "url": {
                            "raw": "{{url}}" + full_path,
                            "host": ["{{url}}"],
                            "path": full_path.strip('/').split('/')
                        }
                    },
                    "response": []
                }

                # Add sample request bodies for POST and PUT methods
                if method in ['POST', 'PUT']:
                    sample_bodies = get_sample_bodies(body_samples, endpoint_name, method)
                    if sample_bodies:
                        for sample in sample_bodies:
                            item = base_item.copy()
                            item["name"] = f"{method} {endpoint_name} - {sample['name']}"
                            item["request"]["body"] = {
                                "mode": "raw",
                                "raw": json.dumps(sample['body'], indent=2),
                                "options": {
                                    "raw": {
                                        "language": "json"
                                    }
                                }
                            }
                            endpoint_folder["item"].append(item)
                    else:
                        endpoint_folder["item"].append(base_item)
                else:
                    endpoint_folder["item"].append(base_item)

        folder_item["item"].append(endpoint_folder)

    return folder_item


def get_sample_bodies(body_samples, endpoint_name, method):
    if endpoint_name in body_samples and method in body_samples[endpoint_name]:
        return body_samples[endpoint_name][method]
    return []


def process_folder(folder_path):
    postman_collection = {
        "info": {
            "name": default_collection,
            "description": "Generated from YAML configuration",
            "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
        },
        "item": [
            {
                "name": prefix.split('/')[-1].title(),
                "item": [],
                "_postman_isSubFolder": True
            }
        ],
        "variable": [
            {
                "key": "url",
                "value": "https://api.example.com",
                "type": "string"
            }
        ]
    }

    for filename in os.listdir(folder_path):
        if filename.endswith(".yml") or filename.endswith(".yaml"):
            file_path = os.path.join(folder_path, filename)
            with open(file_path, 'r') as file:
                yaml_content = file.read()
                folder_item = convert_yaml_to_postman(yaml_content, file_path, prefix)
                postman_collection["item"][0]['item'].append(folder_item)

    return postman_collection


def get_existing_collection(api_key, workspace_id, collection_name):
    url = f"https://api.getpostman.com/collections?workspace={workspace_id}"
    headers = {"X-API-Key": api_key}

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        collections = response.json()['collections']
        for collection in collections:
            if collection['name'] == collection_name:
                return collection['uid']
    except requests.exceptions.RequestException as err:
        print(f"Error fetching collections: {err}")

    return None


def import_to_postman(collection, api_key, workspace_id):
    existing_collection_id = get_existing_collection(api_key, workspace_id, default_collection)

    if existing_collection_id:
        url = f"https://api.getpostman.com/collections/{existing_collection_id}"

        # Fetch existing collection details
        try:
            existing_response = requests.get(url, headers={"X-API-Key": api_key})
            existing_collection = existing_response.json().get('collection', {})
            existing_items = existing_collection.get('item', [])

            # Merge folders, replacing those with matching names
            merged_items = []
            for new_folder in collection['item']:
                # Check if folder already exists
                existing_folder = next((item for item in existing_items if item['name'] == new_folder['name']), None)

                if existing_folder:
                    # Replace existing folder with new one
                    merged_items.append(new_folder)
                    existing_items = [item for item in existing_items if item['name'] != new_folder['name']]
                else:
                    # Add new folder
                    merged_items.append(new_folder)

            # Add any remaining existing folders
            merged_items.extend(existing_items)

            # Update collection
            collection = {**existing_collection, "item": merged_items}
        except Exception as e:
            print(f"Error fetching existing collection: {e}")
            return

        method = requests.put
        action = "updated"
    else:
        url = f"https://api.getpostman.com/collections?workspace={workspace_id}"
        method = requests.post
        action = "created"

    headers = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }
    payload = json.dumps({"collection": collection})

    try:
        response = method(url, headers=headers, data=payload)
        response.raise_for_status()
        response_data = response.json()

        if 'collection' in response_data:
            new_collection_id = response_data['collection']['uid']
            new_collection_name = response_data['collection']['name']
            print(f"Collection successfully {action} in Postman workspace")
            print(f"Collection ID: {new_collection_id}")
            print(f"Collection Name: {new_collection_name}")

            # Double-check if the collection now exists in the workspace
            check_collection = get_existing_collection(api_key, workspace_id, new_collection_name)
            if check_collection:
                print(f"Verified: Collection exists in workspace with ID: {check_collection}")
            else:
                print(
                    "Warning: Collection was created but not found in workspace. This might be due to API caching or delays.")
        else:
            print(f"Unexpected response format. Full response: {response_data}")

    except requests.exceptions.HTTPError as err:
        print(f"HTTP Error occurred: {err}")
        print("Response Details:")
        print(f"Status Code: {response.status_code}")
        print(f"Response Body: {response.text}")
        print("\nPossible issues:")
        print("1. The API key may not have the necessary permissions.")
        print("2. You may not have the required access to the specified workspace.")
        print("3. The workspace ID might be incorrect.")
        print("4. The API key might be invalid or expired.")
    except requests.exceptions.RequestException as err:
        print(f"An error occurred while making the request: {err}")


def main():
    parser = argparse.ArgumentParser(
        description="Convert YAML API definitions to Postman collection and import to workspace")
    parser.add_argument("--folder", default=default_folder,
                        help="Folder containing YAML files (default: specified project folder)")
    parser.add_argument("--prefix", default=prefix, help="Prefix to add to all paths (default: /api)")
    parser.add_argument("--api_key", default=api_key, help="Postman API Key (overrides environment variable)")
    parser.add_argument("--workspace_id", default=workspace_id, help="Postman Workspace ID")
    args = parser.parse_args()

    folder_path = os.path.abspath(args.folder)
    if not os.path.isdir(folder_path):
        print(f"Error: {folder_path} is not a valid directory")
        sys.exit(1)

    postman_collection = process_folder(folder_path)

    # Import or update the collection in Postman workspace
    import_to_postman(postman_collection, args.api_key, args.workspace_id)


if __name__ == "__main__":
    main()
