from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema, Model


@dataclass
class ProvinceAttributes(Attributes):
    id: str
    name: str
    slug: str
    name_with_type: str
    code: str


class ProvinceSchemaAttributes(AttributesSchema):
    id = fields.UUID(required=True)
    name = fields.Str(required=True)
    slug = fields.Str(required=True)
    name_with_type = fields.Str(required=True)
    code = fields.Str(required=True)


class ProvinceModel(Model):
    table_name = 'province'
    attributes: ProvinceAttributes
    attributes_schema = ProvinceSchemaAttributes
    attributes_class = ProvinceAttributes

    query_mapping = {
        'query': ['id', 'name', 'slug', 'name_with_type', 'code'],
        'filter': {
        },
    }

