import importlib
import os
from typing import Dict, Type

from integrations.common.transformers.transformer import BaseTransformer



class TransformerLoader:
    def __init__(self, transformer_dir: str = 'transformers'):
        self.transformer_dir = transformer_dir
        self.transformers: Dict[str, Type[BaseTransformer]] = {}
        self.load_transformers()

    def load_transformers(self):
        for filename in os.listdir(self.transformer_dir):
            if filename.endswith('.py') and not filename.startswith('__'):
                module_name = filename[:-3]  # Remove .py extension
                module = importlib.import_module(f'{self.transformer_dir}.{module_name}')
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if isinstance(attr, type) and issubclass(attr, BaseTransformer) and attr != BaseTransformer:
                        self.transformers[attr.name] = attr

    def get_transformer(self, name: str, config: dict = None) -> BaseTransformer:
        transformer_class = self.transformers.get(name)
        if not transformer_class:
            raise ValueError(f"No transformer found with name: {name}")
        return transformer_class(config)


# transformer_loader = TransformerLoader()
