import os

from boto3.dynamodb.types import TypeDeserializer

from helpers.notification import send_notification_to_zalo

deserializer = TypeDeserializer()

SERVICE = os.getenv('SERVICE')
ENV = os.getenv('ENV')


def handle(event, context):
    for record in event['Records']:
        if record['eventName'] in ['MODIFY', 'INSERT']:
            record_image = record['dynamodb']['NewImage']
            record_data = {key: deserializer.deserialize(value) for key, value in record_image.items()}
            send_notification_to_zalo(record_data)
