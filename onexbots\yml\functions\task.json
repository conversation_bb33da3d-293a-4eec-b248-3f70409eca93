{"create_task": {"POST": [{"name": "Create a new task", "body": {"name": "Test Task", "description": "Test task description", "prompt_content": "Test prompt content with [language]", "variables": {"language": "string"}}}]}, "get_task": {"GET": [{"name": "Get task by ID", "path_params": {"id": "task-id-123"}}]}, "list_tasks": {"GET": [{"name": "List all tasks", "query_params": {"page": "0", "limit": "20"}}]}, "update_task": {"PUT": [{"name": "Update task", "path_params": {"id": "task-id-123"}, "body": {"data": {"name": "Updated Task Name", "description": "Updated description"}}}]}, "delete_task": {"DELETE": [{"name": "Delete task", "path_params": {"id": "task-id-123"}}]}}