graph TD
    A[Start] --> B[Receive API Gateway Request]
    B --> C[Extract connection_type and setup_data from request body]
    C --> D[Call _setup_connection function]
    D --> E{Is connection class OAuth2Connection?}
    E -->|Yes| F[Get authorization URL]
    F --> G[Save connection]
    G --> H[Return auth URL and connection ID]
    E -->|No| I[Test connection]
    I --> J{Connection test successful?}
    J -->|Yes| K[Activate connection]
    K --> L[Save connection]
    L --> M[Return success message and connection ID]
    J -->|No| N[Raise BadRequest exception]
    D --> O{Exception occurred?}
    O -->|Yes| P[Raise BadRequest exception]
    O -->|No| E
    H --> Q[End]
    M --> Q
    N --> Q
    P --> Q
