import json
import unittest
import uuid
import os
from dotenv import load_dotenv
from tests.base import BaseTestCase

from integrations.channels.tiktokshop.tiktokshop_connection import Region
from connections.src.apis.connection import setup_connection
from integrations.channels.shopify.shopify_connection import ShopifyConnection


class TiktokshopConnectionSetupIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.app_key = "6emv3rous6f2u",
        cls.app_secret = "b2285d947e5f4e1795124406ab3e415393c5d855",
        cls.service_id = "7448925715311937285",
        cls.shop_id = "7496026029151128085",
        cls.region = Region.non_us.value,

        if not all([cls.app_key, cls.app_secret, cls.service_id, cls.region]):
            raise ValueError("Shopify credentials not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"

    def create_lambda_event(self, body):
        return {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_setup_tiktok_shop_connection_success(self):
        # Arrange
        event_body = {
            "connection_type": "tiktok_shop",
            "settings": {
                "app_key" : "6emv3rous6f2u",
                "app_secret" : "b2285d947e5f4e1795124406ab3e415393c5d855",
                "service_id" : "7448925715311937285",
                "region" : "non-us",
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        # self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        # self.assertEqual(response_body['message'], 'shopify connection setup successful')
        self.assertIn('connection_id', response_body)
        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = ShopifyConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.shop_url, self.shop_url)
        self.assertEqual(created_connection.attributes.api_key, self.api_key)
        self.assertEqual(created_connection.attributes.secret_key, self.secret_key)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status, 'ACTIVE')

        # Clean up
        created_connection.delete()

    def test_oauth2_callback(self):
        event_body = {
            "connection_type": "tiktok_shop",
            "settings": {
                "app_key": "6emv3rous6f2u",
                "app_secret": "b2285d947e5f4e1795124406ab3e415393c5d855",
                "service_id": "7448925715311937285",
                "region": "non-us",
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()
        # response = self.
        self.assertTrue(True)



    def test_setup_shopify_connection_invalid_credentials(self):
        # Arrange
        event_body = {
            "connection_type": "shopify",
            "setup_data": {
                "shop_url": self.shop_url,
                "api_key": "invalid_api_key",
                "secret_key": "invalid_secret_key",
                "access_token": self.access_token
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['error'], 'Bad request')
        self.assertIn('Invalid Shopify credentials', response_body['message'])

    def test_setup_shopify_connection_invalid_shop_url(self):
        # Arrange
        event_body = {
            "connection_type": "shopify",
            "setup_data": {
                "shop_url": "https://nonexistent-shop.myshopify.com",
                "api_key": self.api_key,
                "secret_key": self.secret_key,
                "access_token": self.access_token
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['error'], 'Bad request')
        self.assertIn('Invalid Shopify shop URL', response_body['message'])


    def test_test_connection(self):
        # Arrange
        event_body = {
            "connection_type": "tiktok_shop",
            "settings": {
                "app_key" : "6emv3rous6f2u",
                "app_secret" : "b2285d947e5f4e1795124406ab3e415393c5d855",
                "service_id" : "7448925715311937285",
                "region" : "non-us",
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        # self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        # self.assertEqual(response_body['message'], 'shopify connection setup successful')
        self.assertIn('connection_id', response_body)
        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = ShopifyConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.shop_url, self.shop_url)
        self.assertEqual(created_connection.attributes.api_key, self.api_key)
        self.assertEqual(created_connection.attributes.secret_key, self.secret_key)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status, 'ACTIVE')

        # Clean up
        created_connection.delete()

if __name__ == '__main__':
    unittest.main()
