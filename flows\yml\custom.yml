pythonRequirements:
    fileName: "requirements.txt"
    layer: true
    noDeploy:
        - boto3
        - botocore

# Lấy bucket name từ param (dev/prod)
deploymentBucketName: ${param:bucket_name}

prune:
    automatic: true
    number: 3

fetchQueueArn:
    Fn::GetAtt: [FetchQueue, Arn]

publishQueueArn:
    Fn::GetAtt: [PublishQueue, Arn]

transformQueueUrl:
    Fn::GetAtt: [TransformQueue, QueueUrl]

publishQueueUrl:
    Fn::GetAtt: [PublishQueue, QueueUrl]

publishDeadLetterQueueUrl:
    Fn::GetAtt: [PublishDeadLetterQueue, QueueUrl]

publishDeadLetterQueueArn:
    Fn::GetAtt: [PublishDeadLetterQueue, Arn]

syncMappingQueueArn:
    Fn::GetAtt: [SyncMappingQueue, Arn]

syncMappingQueueUrl:
    Fn::GetAtt: [SyncMappingQueue, QueueUrl]
