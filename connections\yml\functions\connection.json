{"channels": {}, "connection_setup_fields": {}, "setup_default_connection": {"POST": [{"name": "setup default connection", "path_params": {"connection_type": "facebook_oauth"}, "body": {}}]}, "setup_connection": {"POST": [{"name": "shopify", "body": {"connection_type": "shopify", "settings": {"shop_url": "https://www.pinkpoppy.com.au", "api_key": "9336ddcdee3c276b47687ec44091b821", "secret_key": "9336ddcdee3c276b47687ec44091b821", "access_token": "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"}}}, {"name": "nhanh", "body": {"connection_type": "nhanh", "settings": {"app_id": "74307", "secret": "9336ddcdee3c276b47687ec44091b821"}}}]}, "oauth2_callback": {}, "connection_by_id": {}, "connections_by_company": {}, "authorization_link": {}, "get_pages": {"GET": [{"name": "Get pages", "path_params": {"connection_id": "123"}}]}, "get_default_connections": {"GET": [{"name": "Get default Facebook connections", "path_params": {"connection_type": "facebook_oauth"}, "response": [{"id": "fb_123456", "status": "active", "is_expired": false, "channel_name": "facebook_oauth"}]}, {"name": "Get default Zalo connections", "path_params": {"connection_type": "zalo_oa"}, "response": [{"id": "zalo_789012", "status": "active", "is_expired": false, "channel_name": "zalo_oa"}]}]}}