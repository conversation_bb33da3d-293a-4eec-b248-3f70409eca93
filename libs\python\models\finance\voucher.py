from dataclasses import dataclass
from enum import Enum

import pendulum
from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


class Type(Enum):
    EXPENSE = 'EXPENSE'
    PAYMENT = 'PAYMENT'


class ReservedVoucher(Enum):
    PURCHASE_ORDER_VOUCHER = 'PURCHASE_ORDER_VOUCHER'
    ORDER_PAYMENT_VOUCHER = 'ORDER_PAYMENT_VOUCHER'
    RETURN_ORDER_VOUCHER = 'RETURN_ORDER_VOUCHER'


@dataclass
class TransactionVoucher(Attributes):
    id: str = None
    name: str = None
    type: Type = Type.EXPENSE
    description: str = None


class TransactionVoucherSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    type = EnumField(Type, required=True, default=Type.EXPENSE)
    description = fields.Str(allow_none=True)


@dataclass
class TransactionVoucherAttributes(TransactionVoucher, BasicAttributes):
    pass


class TransactionVoucherAttributesSchema(TransactionVoucherSchema, BasicAttributesSchema):

    @staticmethod
    def sample():
        return {
            "created_at": "2023-09-06T08:03:10.951292+00:00",
            "type": "PAYMENT",
            "description": "Thanh toán đơn hàng",
            "name": "Thanh toán đơn hàng",
            "id": "202dfee9-441a-4358-af77-da0368c6a428",
            "company_id": "e9da356c-b0c1-70f6-4290-8ca64e679fc5",
            "updated_at": "2023-09-06T08:05:20.825920+00:00"
        }


class TransactionVoucherModel(BasicModel):
    table_name = 'transactionVoucher'
    attributes: TransactionVoucherAttributes
    attributes_schema = TransactionVoucherAttributesSchema
    attributes_class = TransactionVoucherAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'name': 'query',
            'type': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }

    reserved_voucher = {
        'PURCHASE_ORDER_VOUCHER': {
            'id': 'PURCHASE_ORDER_VOUCHER',
            'name': 'Purchase order payment',
            'type': 'EXPENSE'
        },
        'ORDER_PAYMENT_VOUCHER': {
            'id': 'ORDER_PAYMENT_VOUCHER',
            'name': 'Order payment',
            'type': 'PAYMENT'
        },
        'RETURN_ORDER_VOUCHER': {
            'id': 'RETURN_ORDER_VOUCHER',
            'name': 'Return order payment',
            'type': 'EXPENSE'
        },
    }

    @classmethod
    def get_transaction_voucher(cls, voucher_id, company_id):
        if voucher_id in cls.reserved_voucher:
            return TransactionVoucherModel({
                **cls.reserved_voucher[voucher_id],
                'company_id': company_id,
                'created_at': pendulum.now().to_iso8601_string(),
                'updated_at': pendulum.now().to_iso8601_string()
            })

        return TransactionVoucherModel.by_key({
            'id': voucher_id,
            'company_id': company_id
        })
