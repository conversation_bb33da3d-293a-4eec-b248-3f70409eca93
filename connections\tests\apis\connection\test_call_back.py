import unittest

from connections.src.apis.connection import (
    oauth2_callback_post
)
from connections.tests.apis.connection.data_test import call_back_post_event
from tests.base import BaseTestCase


class ConnectionApiTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

    def test_oauth2_callback(self):
        response = oauth2_callback_post(call_back_post_event, {})
        self.assertTrue(True)


if __name__ == '__main__':
    unittest.main()
