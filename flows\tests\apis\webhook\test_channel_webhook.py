import unittest

from tests.base import BaseTestCase
from flows.src.apis.webhook import webhook_handler_by_channel_name
from flows.tests.apis.webhook.data_test import zalo_oa_event, facebook_oauth_test_event, shopify_oauth_webhook_event, \
    facebook_oauth_event


class TestChannelWebhookApiTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

    def test_channel_webhook(self):
        webhook_handler_by_channel_name(zalo_oa_event, {})


if __name__ == '__main__':
    unittest.main()
