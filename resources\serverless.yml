org: lebinhnguyen1991
app: ${env:SERVICE}
useDotenv: true

service: ${env:SERVICE}

params:
    default:
        user_pool_id: ap-southeast-1_Tmn6Tbm0H
        app_client_id: 3fh2s28e3g8l23068d6j573l9a
        bucket_name: optiwarehouse-staging
        es_host: https://**************:9200
        es_username: elastic
        es_password: OneXAPIES999
        profile: noliwms_staging
        onexapis: https://api-staging.onexapis.com
        haravan_api: https://haravan-staging.onexapis.com
        env: dev
    prod:
        user_pool_id: ap-southeast-1_Tmn6Tbm0H
        app_client_id: 3fh2s28e3g8l23068d6j573l9a
        bucket_name: optiwarehouse-prod
        es_host: https://**************:9200
        es_username: elastic
        es_password: OneXAPIES999
        profile: optiwarehouse-prod
        onexapis: https://api.onexapis.com
        haravan_api: https://haravan.onexapis.com
        env: prod

provider:
    name: aws
    runtime: python3.9
    region: ap-southeast-1
    profile: ${env:PROFILE}
    stage: ${opt:stage, env:STAGE, 'dev'}
    memorySize: 256
    timeout: ${env:TIMEOUT}
    deploymentBucket:
        name: ${self:custom.deploymentBucketName}
    environment:
        LOG_LEVEL: 40
        ENV: ${param:env}
        USER_POOL_ID: ${param:user_pool_id}
        APP_CLIENT_ID: ${param:app_client_id}
        BUCKET_NAME: ${param:bucket_name}
        ONEXAPIS: ${param:onexapis}
        ELASTIC_SEARCH_HOST: ${param:es_host}
        ELASTIC_SEARCH_USERNAME: ${param:es_username}
        ELASTIC_SEARCH_PASSWORD: ${param:es_password}
        PYTHONWARNINGS: "ignore:Unverified HTTPS request"
        RETRIEVER_QUEUE_URL:
            Fn::GetAtt: [ RetrieverQueue, QueueUrl ]

    iam: ${file(yml/iam.yml)}
    layers:
        - Ref: PythonRequirementsLambdaLayer
        - Ref: IntegrationLibsLambdaLayer

    httpApi:
        cors: true
        authorizers:
            optiAuthorizer:
                name: optiAuthorizer
                type: jwt
                identitySource: "$request.header.Authorization"
                issuerUrl: "https://cognito-idp.${opt:region, self:provider.region}.amazonaws.com/${opt:aws.cognito.userPoolId, self:provider.environment.USER_POOL_ID}"
                audience:
                    - "${opt:aws.cognito.userPoolClientId, self:provider.environment.APP_CLIENT_ID}"

layers:
    IntegrationLibs:
        path: ../libs

functions:
    - ${file(yml/functions/events.yml)}
    - ${file(yml/functions/index_dynamo_table.yml)}

resources: ${file(yml/resources.yml)}

plugins:
    - serverless-python-requirements
    - serverless-dotenv-plugin
    - serverless-dynamodb-local
    - serverless-prune-plugin
    - serverless-s3-local
    - serverless-dynamodb-autoscaling
#    - serverless-offline

package:
    patterns:
        - src/**
        - "!.git/**"
        - "!node_modules/**"

custom: ${file(yml/custom.yml)}
