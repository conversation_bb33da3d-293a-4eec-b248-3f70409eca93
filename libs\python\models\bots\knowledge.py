from dataclasses import dataclass
from enum import Enum
from typing import List, Dict, Any

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


class Status(str, Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    READY = "READY"
    ERROR = "ERROR"


class Source(str, Enum):
    FILE = "FILE"
    URL = "URL"
    DIRECT_TEXT = "DIRECT_TEXT"


@dataclass
class KnowledgeAttributes(BasicAttributes):
    name: str = None
    s3_key: str = None
    virtual_staff_ids: List[str] = None
    description: str = None
    summary_content: str = None
    status: Status = None
    source: Source = None
    meta_data: Dict[str, Any] = None
    size: int = 0


class KnowledgeAttributesSchema(BasicAttributesSchema):
    name = fields.Str(required=True)
    s3_key = fields.Str(required=True)
    virtual_staff_ids = fields.List(fields.Str(), required=True)
    description = fields.Str(required=False, allow_none=True)
    summary_content = fields.Str(required=False,allow_none=True)
    status = EnumField(Status, required=True)
    source = EnumField(Source, required=True)
    size = fields.Int(required=False, default=0, allow_none=True)
    meta_data = fields.Dict(keys=fields.Str(), values=fields.Raw(), allow_none=True)


class KnowledgeModel(BasicModel):
    table_name = 'knowledge'
    attributes: KnowledgeAttributes
    attributes_schema = KnowledgeAttributesSchema
    attributes_class = KnowledgeAttributes

    query_mapping = {
        'query': ['id', 'name', 'status', 'source'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'status': 'query',
            'source': 'query',
            'size': 'number',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
    }
