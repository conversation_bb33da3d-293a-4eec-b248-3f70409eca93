from typing import Dict, Any

from nolicore.utils.exceptions import BadRequest

from models.bots.conversation import ConversationModel, ConversationCustomerAndStaffIdIndex


def create_conversation(company_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new conversation or return existing one.
    
    Args:
        company_id: Company ID
        data: Conversation data including:
            - name: Conversation name
            - customer_id: Customer ID
            - image: Image URL (optional)
            - members: List of member IDs
            - files: List of file IDs (optional)
            - links: List of link URLs (optional)
            - assignee_id: Assignee ID
            - staff_id: Staff ID
            - role: Role (USER or VIRTUAL_STAFF)
            
    Returns:
        Dictionary containing conversation data
    """
    try:
        # Check for existing conversation

        existing_conversations = ConversationCustomerAndStaffIdIndex.list({'customer_id': data['customer_id'], 'staff_id': data['staff_id']}, limit=1000)[
            'Items']

        if len(existing_conversations):
            return {
                'success': True,
                'message': 'Existing conversation found',
                'data': existing_conversations[0]
            }

        # Create new conversation if none exists
        conversation = ConversationModel.create(company_id, data)
        return {
            'success': True,
            'message': 'Conversation created successfully',
            'data': conversation.attributes_dict
        }
    except Exception as e:
        raise BadRequest(f"Failed to create conversation: {str(e)}")


def get_conversation(conversation_id: str) -> Dict[str, Any]:
    """Get a conversation by ID.
    
    Args:
        conversation_id: Conversation ID
        
    Returns:
        Dictionary containing conversation data
    """
    conversation = ConversationModel.get_by_id(
        _id=conversation_id)
    if not conversation:
        raise BadRequest("Conversation not found")

    return {
        'success': True,
        'message': 'Conversation retrieved successfully',
        'data': conversation.attributes_dict
    }


def delete_conversation(conversation_id: str) -> Dict[str, Any]:
    """Delete a conversation and its associated messages.
    
    Args:
        conversation_id: Conversation ID
        
    Returns:
        Dictionary containing success message
    """
    # Get conversation first to check if it exists
    conversation = ConversationModel.get_by_id(
        _id=conversation_id
    )

    if not conversation:
        raise BadRequest("Conversation not found")

    # Delete conversation
    key = {
        ConversationModel.key__id: conversation_id,
        'company_id': str(conversation.attributes.company_id)
    }
    ConversationModel.delete(key=key)

    return {
        'success': True,
        'message': 'Conversation deleted successfully'
    }
