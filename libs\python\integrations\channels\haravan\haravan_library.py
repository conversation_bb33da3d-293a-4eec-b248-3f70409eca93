import json
import logging
import re
import uuid
from datetime import datetime, timed<PERSON>ta
from functools import lru_cache
from typing import Dict, Any, List, Optional

import pendulum
import requests
from nolicore.utils.exceptions import BadRequest, UnauthorizedExp
from nolicore.utils.utils import logger

from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, SyncAPIResponse,
    MetaData, StandardOrder, StandardProduct, PublishMetaData, StandardInventory, StandardReturnOrder,
    StandardPurchaseOrder, SyncAPIResponseList
)
from integrations.common.event import FetchEvent


class HaravanEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.PRODUCTS = EndpointType(type="rest", method="GET", path="com/products.json")
        self.CREATE_PRODUCT = EndpointType(type="rest", method="POST", path="com/products.json")
        self.UPDATE_PRODUCT = EndpointType(type="rest", method="PUT", path="com/products/{product_id}.json")
        self.ORDERS = EndpointType(type="rest", method="GET", path="com/orders.json")
        self.UPDATE_ORDER = EndpointType(type="rest", method="PUT", path="com/orders/{order_id}.json")
        self.GET_ORDER = EndpointType(type="rest", method="GET", path="com/orders/{order_id}.json")
        self.CONFIRM_ORDER = EndpointType(type="rest", method="POST", path="com/orders/{order_id}/confirm.json")
        self.CLOSE_ORDER = EndpointType(type="rest", method="POST", path="com/orders/{order_id}/close.json")
        self.CANCEL_ORDER = EndpointType(type="rest", method="POST", path="com/orders/{order_id}/cancel.json")
        self.SEARCH_CUSTOMER = EndpointType(type="rest", method="GET", path="com/customers/search.json")
        self.CREATE_CUSTOMER = EndpointType(type="rest", method="POST", path="com/customers.json")
        self.UPDATE_CUSTOMER = EndpointType(type="rest", method="PUT", path="com/customers/{customer_id}.json")
        self.ADJUST_INVENTORY = EndpointType(type="rest", method="POST", path="com/inventories/adjustorset.json")
        self.GET_LOCATIONS = EndpointType(type="rest", method="GET", path="com/locations.json")
        self.DELETE_IMAGE = EndpointType(type="rest", method="DELETE",
                                         path="com/products/{product_id}/images/{image_id}.json")
        self.ADD_IMAGE = EndpointType(type="rest", method="POST", path="com/products/{product_id}/images.json")
        self.GET_IMAGES = EndpointType(type="rest", method="GET", path="com/products/{product_id}/images.json")
        self.UPDATE_IMAGE = EndpointType(type="rest", method="PUT",
                                         path="com/products/{product_id}/images/{image_id}.json")
        self.GET_VARIANTS = EndpointType(type="rest", method="GET", path="com/products/{product_id}/variants.json")
        self.GET_VARIANT = EndpointType(type="rest", method="GET",
                                        path="com/products/{product_id}/variants/{variant_id}.json")
        self.CREATE_VARIANT = EndpointType(type="rest", method="POST", path="com/products/{product_id}/variants.json")
        self.UPDATE_VARIANT = EndpointType(type="rest", method="PUT", path="com/variants/{variant_id}.json")
        self.DELETE_VARIANT = EndpointType(type="rest", method="DELETE",
                                           path="com/products/{product_id}/variants/{variant_id}.json")


class HaravanLibrary(BaseAPILibrary):

    def __init__(self, client_id: str, client_secret: str, org_id: str, access_token: str = None,
                 base_url: str = "https://apis.haravan.com"):
        super().__init__(base_url)
        self.client_id = client_id
        self.org_id = org_id
        self.client_secret = client_secret
        self.access_token = access_token
        self.endpoints = HaravanEndpoints(base_url)

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None,
                      path_params: Dict[str, Any] = None) -> Dict[str, Any]:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}"
        }

        response = self._make_request_sync(endpoint, headers=headers, params=params, path_params=path_params, json=data)
        return response

    def get_authorization_url(self, connection_id: str, redirect_url: str = None, base_url: str = None) -> str:
        app_callback_url = self.get_callback_url(base_url)
        state = self.generate_state(connection_id, redirect_url)
        app_callback_url += f'?state={state}'

        scopes = [
            'openid profile address email phone org userinfo grant_service',  ## wh_api
            'com.write_shippings',
            'com.write_orders',
            'com.write_shipping_zones',
            'com.write_products',
            'com.write_salechannels',
            'com.write_customers',
            'com.write_discounts',
            'com.write_inventories',
            'com.read_shop'
        ]

        return (f"https://accounts.haravan.com/connect/authorize?"
                f"response_mode=form_post&"
                f"response_type=code&"
                f"scope={' '.join(scopes)}&"
                f"client_id={self.client_id}&"
                f"redirect_uri={app_callback_url}&"
                f"nonce={str(uuid.uuid4())}&"
                f"orgid={self.org_id}"
                )

    @staticmethod
    def _encode_state(connection_id: str, company_id: str) -> str:
        import base64
        return base64.b64encode(json.dumps({'id': connection_id, 'company_id': company_id}).encode()).decode()

    def fetch_token(self, access_code: str, state) -> Dict[str, Any]:
        token_url = "https://accounts.haravan.com/connect/token"
        data = {
            'grant_type': 'authorization_code',
            'code': access_code,
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'redirect_uri': f'{self.get_callback_url()}?state={state}'
        }
        response = requests.post(token_url, data=data)
        token_data = response.json()
        if 'access_token' not in token_data:
            raise UnauthorizedExp(token_data)

        # Process the token data
        access_token = token_data['access_token']
        expires_in = token_data.get('expires_in', 3600)  # Default to 1 hour if not provided
        expires_at = datetime.utcnow() + timedelta(seconds=int(expires_in))
        refresh_token = token_data.get('refresh_token')

        # Update the library's access token
        self.access_token = access_token

        return {
            'external_id': self.org_id,  # Haravan uses 'org_id' for the merchant ID
            'access_token': access_token,
            'expires_at': expires_at,
            'refresh_token': refresh_token,
            'token_data': token_data
        }

    def refresh_token(self) -> Dict[str, Any]:
        token_url = "https://accounts.haravan.com/connect/token"
        data = {
            'grant_type': 'refresh_token',
            'refresh_token': self.refresh_token,
            'client_id': self.client_id,
            'client_secret': self.client_secret
        }
        response = requests.post(token_url, data=data)
        response.raise_for_status()
        token_data = response.json()

        # Process the token data
        access_token = token_data['access_token']
        expires_in = token_data.get('expires_in', 3600)  # Default to 1 hour if not provided
        expires_at = datetime.utcnow() + timedelta(seconds=int(expires_in))
        refresh_token = token_data.get('refresh_token')

        # Update the library's access token
        self.access_token = access_token

        return {
            'access_token': access_token,
            'expires_at': expires_at.isoformat(),
            'refresh_token': refresh_token,
            'token_data': token_data
        }

    def test_connection(self) -> bool:
        try:
            self._make_request(self.endpoints.PRODUCTS, params={"limit": 1})
            return True
        except Exception:
            return False

    def get_orders(self, params: Dict[str, Any] = None, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        if params is None:
            params = {}

        page = params.get('page', 1)
        updated_at_min = params.get('updated_at_min')
        updated_at_max = params.get('updated_at_max')

        data = params.copy()  # Copy all params
        data['page'] = page
        if updated_at_min:
            data['updated_at_min'] = pendulum.parse(updated_at_min).format('YYYY-MM-DD HH:mm:ss')
        if updated_at_max:
            data['updated_at_max'] = pendulum.parse(updated_at_max).format('YYYY-MM-DD HH:mm:ss')

        order_data = self._make_request(self.endpoints.ORDERS, params=data)
        orders = [self.standardize_order(order, fetch_event_id) for order in order_data['orders']]

        next_page = page + 1 if len(order_data['orders']) > 0 else None
        meta = MetaData(
            total_count=order_data.get('count', 0),
            page=page,
            limit=len(orders),
            current_params=data
        )
        return FetchAPIResponse(success=True, data=orders, meta=meta)

    def get_products(self, params: Dict[str, Any] = None, fetch_event_id: str = None) -> FetchAPIResponse[
        StandardProduct]:
        if params is None:
            params = {}

        page = params.get('page', 1)
        limit = params.get('limit', 50)
        updated_at_min = params.get('updated_at_min')
        updated_at_max = params.get('updated_at_max')

        data = params.copy()  # Copy all params
        data.update({
            'page': page,
            'limit': limit
        })
        if updated_at_min:
            data['updated_at_min'] = updated_at_min
        if updated_at_max:
            data['updated_at_max'] = updated_at_max

        product_data = self._make_request(self.endpoints.PRODUCTS, params=data)
        products = [self.standardize_product(product, fetch_event_id) for product in product_data['products']]

        next_page = page + 1 if len(product_data['products']) > 0 else None
        meta = MetaData(
            total_count=product_data.get('count', 0),
            page=page,
            limit=limit,
            current_params=data,
            continuation_token=next_page
        )
        return FetchAPIResponse(success=True, data=products, meta=meta)

    def sync_product(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardProduct]:
        try:
            product = product_data['product']
            images = product.pop('images', [])
            inventories = []
            image_names = {image['name'].lower() for image in images}
            sku_image_mapping = {}
            for variant in product['variants']:
                image = variant.pop('image', None)
                if image:
                    sku_image_mapping[variant['sku']] = image
                    if image['name'].lower() not in image_names:
                        images.append(image)

                inventory = variant.pop('inventory') if 'inventory' in variant else None
                if inventory:
                    inventories.append(inventory)

            skus = [v['sku'] for v in product['variants']]
            remote_products = []
            remote_product_ids = set()
            for sku in skus:
                products_response = self.get_products({'sku': sku})
                products = products_response.data
                for p in products:
                    if p.id not in remote_product_ids:
                        remote_products.append(p.raw_data)
                        remote_product_ids.add(p.id)

            if len(remote_products) > 2:
                raise BadRequest('SKUs belong to two products')

            product = {k: v for k, v in product.items() if v}
            if not remote_products:
                remote_product = self._make_request(self.endpoints.CREATE_PRODUCT, data={'product': product})['product']
                if 'errors' in remote_product:
                    raise BadRequest(remote_product)
            else:
                remote_product = remote_products[0]
                product['id'] = remote_product['id']
                remote_variants = remote_product.pop('variants', [])
                variants = product.pop('variants')
                self.sync_variants(remote_variants, variants)
                remote_product = self._make_request(self.endpoints.UPDATE_PRODUCT,
                                                    path_params={'product_id': remote_product['id']},
                                                    data={'product': product})['product']

            self.sync_images(remote_product, images, sku_image_mapping)
            self.sync_inventories_of_product(inventories)

            standardized_product = self.standardize_product(remote_product)
            return SyncAPIResponse(success=True, meta=PublishMetaData(updated_at=datetime.now()),
                                   data=standardized_product)
        except Exception as e:
            logging.exception(f"Failed to sync product: {str(e)}")
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync product: {str(e)}")

    def sync_order(self, order_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[StandardOrder]:
        try:
            status = order_data.get('status')
            order_id = order_data['id']
            if 'HRV' in order_id:
                order_id = order_id.replace('HRV', '')

            remote_order = self._make_request(self.endpoints.GET_ORDER, path_params={'order_id': order_id})

            if status == 'confirmed' and remote_order['confirmed_status'] == 'unconfirmed':
                updated_order = self._make_request(self.endpoints.CONFIRM_ORDER, path_params={'order_id': order_id})
            elif status == 'closed' and remote_order['closed_status'] == 'unclosed':
                updated_order = self._make_request(self.endpoints.CLOSE_ORDER, path_params={'order_id': order_id})
            elif status == 'cancelled' and remote_order['cancelled_status'] == 'uncancelled':
                updated_order = self._make_request(self.endpoints.CANCEL_ORDER, path_params={'order_id': order_id})
            else:
                updated_order = remote_order

            return SyncAPIResponse(success=True, data=self.standardize_order(updated_order),
                                   meta=PublishMetaData(updated_at=datetime.now()), raw_response=updated_order)
        except Exception as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync order: {str(e)}")

    def sync_inventory(self, inventory_data: Dict[str, Any]) -> SyncAPIResponse[StandardInventory]:
        try:
            inventory = inventory_data['inventory']
            inventory['line_items'] = self.enrich_line_items(inventory['line_items'])
            if 'location_id' not in inventory:
                locations = self.get_locations()
                default_location = next(location for location in locations if location['type'] == 'default')
                inventory['location_id'] = default_location['id']
            inventory_response = self.adjust_inventory({'inventory': inventory})
            return SyncAPIResponse(success=True, meta=PublishMetaData(updated_at=datetime.now()),
                                   data=None, message=str(inventory_response))
        except Exception as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync inventory: {str(e)}")
    
    def batch_sync_inventory(self, inventory_data: List[Dict[str, Any]]) -> SyncAPIResponseList[StandardInventory]:
        """
        Process batch inventory with optimized batch size and error handling.
        
        Args:
            inventory_data: List of inventory to sync
            
        Returns:
            List[SyncAPIResponse]: List of sync results for each inventory
        """
        results = []
        BATCH_SIZE = 100
        
        try:
            # Get list of locations
            locations = self.get_locations()
            default_location = next(
                (loc for loc in locations if loc['type'] == 'default'),
                locations[0] if locations else None
            )
            if not default_location:
                raise ValueError("Default location not found")
                
            # Group inventory by location_id
            inventory_by_location: Dict[str, Dict[str, Any]] = {}
            
            for inventory in inventory_data:
                inventory = inventory['inventory']
                location_id = inventory.get('location_id', default_location['id'])
                
                # Initialize inventory for location if not exists
                if location_id not in inventory_by_location:
                    inventory_by_location[location_id] = {
                        **inventory,
                        'line_items': {}
                    }
                
                # Update line_items with latest value for each SKU
                for item in inventory['line_items']:
                    inventory_by_location[location_id]['line_items'][item['sku']] = item
            
            # Process each location
            for location_id, inventory in inventory_by_location.items():
                line_items = list(inventory['line_items'].values())
                
                # Split line_items into batches
                for i in range(0, len(line_items), BATCH_SIZE):
                    batch_items = line_items[i:i + BATCH_SIZE]
                    batch_items = self.enrich_line_items(batch_items)
                    if not batch_items:
                        results.append(SyncAPIResponse(
                            success=False,
                            data=None,
                            meta=PublishMetaData(updated_at=datetime.now()),
                            message="Sku not found"
                        ))
                        continue
                    inventory_payload = {
                        'inventory': {
                            **inventory,
                            'location_id': location_id,
                            'line_items': batch_items
                        }
                    }
                    
                    try:
                        inventory_response = self.adjust_inventory(inventory_payload)
                        results.append(SyncAPIResponse(
                            success=True,
                            meta=PublishMetaData(updated_at=datetime.now()),
                            data=inventory_response,
                            message=str(inventory_response)
                        ))
                        logger.info(f"Sync inventory successfully for location {location_id}: {len(batch_items)} items")
                    except Exception as e:
                        error_msg = f"Error when sync inventory for location {location_id}: {str(e)}"
                        logger.error(error_msg)
                        results.append(SyncAPIResponse(
                            success=False,
                            data=None,
                            meta=PublishMetaData(updated_at=datetime.now()),
                            message=error_msg
                        ))
                        
        except Exception as e:
            error_msg = f"Error when processing batch inventory: {str(e)}"
            logger.error(error_msg)
            results.append(SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=error_msg
            ))
        
         # Calculate batch status
        success_count = sum(1 for r in results if r.success)
        failed_count = len(results) - success_count
        
        # Determine status and message based on results
        if failed_count == len(results):
            status_info = {
                'message': "Batch sync inventory failed",
                'success': False
            }
        elif failed_count > 0:
            status_info = {
                'message': "Batch sync inventory partially failed",
                'success': False
            }
        else:
            status_info = {
                'message': "Batch sync inventory successfully",
                'success': True
            }
        return SyncAPIResponseList(
            data=results,
            message=status_info['message'],
            success=status_info['success']
        )

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        return StandardOrder(
            id=str(raw_order["id"]),
            fetch_event_id=fetch_event_id,
            order_number=raw_order["name"],
            total_price=float(raw_order["total_price"]),
            currency=raw_order["currency"],
            status=raw_order.get("financial_status", ""),
            created_at=datetime.fromisoformat(raw_order["created_at"].rstrip('Z')),
            raw_data=raw_order
        )

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        return StandardProduct(
            id=str(raw_product["id"]),
            fetch_event_id=fetch_event_id,
            title=raw_product["title"],
            raw_data=raw_product
        )

    @staticmethod
    def get_image_name(input_string):
        image_name, image_id = re.split(r'_(?=[^_]+\.(?:jpeg|jpg|png|gif|bmp|tiff|jfif)$)', input_string)
        return '.'.join([image_name, image_id.split('.')[-1]])

    def sync_variants(self, remote_variants: List[Dict[str, Any]], variants: List[Dict[str, Any]]):
        remote_skus = {v['sku'] for v in remote_variants}
        remote_skus_dict = {v['sku']: v for v in remote_variants}
        local_skus = {v['sku'] for v in variants}
        local_skus_dict = {v['sku']: v for v in variants}

        skus_to_delete = remote_skus - local_skus
        skus_to_add = local_skus - remote_skus
        skus_to_update = remote_skus.intersection(local_skus)

        # Delete variants
        for sku in skus_to_delete:
            variant_id = remote_skus_dict[sku]['id']
            self._make_request(self.endpoints.DELETE_VARIANT, path_params={
                'product_id': remote_skus_dict[sku]['product_id'],
                'variant_id': variant_id
            })

        # Add new variants
        for sku in skus_to_add:
            new_variant = local_skus_dict[sku]
            self._make_request(self.endpoints.CREATE_VARIANT, path_params={
                'product_id': new_variant['product_id']
            }, data={'variant': new_variant})

        # Update existing variants
        for sku in skus_to_update:
            remote_variant = remote_skus_dict[sku]
            local_variant = local_skus_dict[sku]

            # Check if any fields need updating
            fields_to_update = {'option1', 'option2', 'title', 'sku', 'price'}
            variant_data = {key: value for key, value in local_variant.items() if key in fields_to_update}

            self._make_request(self.endpoints.UPDATE_VARIANT, path_params={
                'product_id': remote_variant['product_id'],
                'variant_id': remote_variant['id']
            }, data={'variant': variant_data})

        # Refresh remote variants after changes
        updated_remote_variants = self._make_request(self.endpoints.GET_VARIANTS, path_params={
            'product_id': remote_variants[0]['product_id']
        })
        return updated_remote_variants['variants']

    def sync_images(self, remote_product: Dict[str, Any], images: List[Dict[str, Any]],
                    sku_image_mapping: Dict[str, Dict[str, Any]]):
        product_id = remote_product['id']
        remote_images = remote_product['images']
        remote_image_names = {self.get_image_name(image['filename']) for image in remote_images}
        remote_image_names_dict = {}
        duplicated_images = set()
        for image in remote_images:
            origin_name = self.get_image_name(image['filename'])
            if origin_name not in remote_image_names_dict:
                remote_image_names_dict[origin_name] = image
            else:
                duplicated_images.add(image['id'])
        remote_image_names_position_dict = {image_name: position for position, image_name in
                                            enumerate(remote_image_names_dict, 1)}
        image_names = {image['name'].lower() for image in images}
        image_names_dict = {image['name'].lower(): image for image in images}
        image_names_position_dict = {image_name: position for position, image_name in enumerate(image_names_dict, 1)}
        sku_variant_id = {variant['sku']: variant['id'] for variant in remote_product['variants']}
        image_to_delete = remote_image_names - image_names
        image_to_add = image_names - remote_image_names
        image_to_update = remote_image_names.intersection(image_names)

        for image_name in image_to_delete:
            self._make_request(self.endpoints.DELETE_IMAGE, path_params={
                'product_id': product_id,
                'image_id': remote_image_names_dict[image_name]['id']
            })

        for image_id in duplicated_images:
            self._make_request(self.endpoints.DELETE_IMAGE, path_params={
                'product_id': product_id,
                'image_id': image_id
            })

        for image_name in image_to_add:
            skus = [sku for sku, image in sku_image_mapping.items() if image_name == image['name'].lower()]
            image_payload = {
                "image": {
                    "variant_ids": [sku_variant_id[sku] for sku in skus],
                    "src": image_names_dict[image_name]['src'],
                    "filename": image_names_dict[image_name]['name']
                }
            }
            self._make_request(self.endpoints.ADD_IMAGE, path_params={'product_id': product_id},
                               data=image_payload)

        if image_to_delete or image_to_add:
            new_remote_images = self._make_request(self.endpoints.GET_IMAGES,
                                                   path_params={'product_id': product_id})
            remote_image_names_dict = {self.get_image_name(image['src'].split('/')[-1]): image for image in
                                       new_remote_images['images']}
            remote_image_names_position_dict = {image_name: position for position, image_name in
                                                enumerate(remote_image_names_dict, 1)}

        for image_name in image_to_update:
            skus = [sku for sku, image in sku_image_mapping.items() if image_name == image['name'].lower()]
            image_payload = {
                "image": {
                    "variant_ids": [sku_variant_id[sku] for sku in skus],
                    'position': image_names_position_dict[image_name]
                }
            }
            if (set(remote_image_names_dict[image_name]['variant_ids']) == set(image_payload['image']['variant_ids'])
                    and remote_image_names_position_dict[image_name] == image_names_position_dict[image_name]):
                continue
            self._make_request(self.endpoints.UPDATE_IMAGE, path_params={
                'product_id': product_id,
                'image_id': remote_image_names_dict[image_name]['id']
            }, data=image_payload)

    def sync_inventories_of_product(self, inventories: List[Dict[str, Any]]):
        if not inventories:
            return

        location_dict: Dict[str, Dict[str, Any]] = {}

        for inventory in inventories:
            if not inventory['line_items']:
                continue
            inventory['line_items'] = self.enrich_line_items(inventory['line_items'])
            if 'location_id' not in inventory:
                locations = self.get_locations()
                default_location = next(location for location in locations if location['type'] == 'default')
                inventory['location_id'] = default_location['id']
            if inventory['location_id'] in location_dict:
                location_dict[inventory['location_id']]['line_items'].extend(inventory['line_items'])
            else:
                location_dict[inventory['location_id']] = inventory
        inventory_response = []
        for key, value in location_dict.items():
            try:
                inventory_response.append(self.adjust_inventory({'inventory': value}))
                logger.info(f'Sync inventory successfully: {inventory_response}')
            except Exception as e:
                logger.error(f'Error when sync inventory: {e}')
        return inventory_response

    def search_customer(self, query: str) -> Dict[str, Any]:
        customer_data = self._make_request(self.endpoints.SEARCH_CUSTOMER, params={'query': query})
        if customer_data['customers']:
            return customer_data['customers'][0]
        return None

    def create_customer(self, customer: Dict[str, Any]) -> Dict[str, Any]:
        return self._make_request(self.endpoints.CREATE_CUSTOMER, data=customer)

    def sync_customer(self, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        customer = customer_data['customer']
        customer['send_email_welcome'] = False
        customer['verified_email'] = True
        remote_customer = self.search_customer(customer['phone'])
        if remote_customer:
            return self._make_request(self.endpoints.UPDATE_CUSTOMER,
                                      path_params={'customer_id': remote_customer['id']}, data=customer_data)
        return self.create_customer(customer_data)

    def adjust_inventory(self, inventory_data: Dict[str, Any]) -> Dict[str, Any]:
        return self._make_request(self.endpoints.ADJUST_INVENTORY, data=inventory_data)

    @lru_cache(maxsize=128)
    def get_locations(self) -> List[Dict[str, Any]]:
        response = self._make_request(self.endpoints.GET_LOCATIONS)
        return response['locations']

    def enrich_line_items(self, line_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        sku_dict = {}
        for line_item in line_items:
            sku = line_item['sku']
            if sku not in sku_dict:
                products_response = self.get_products({'sku': sku})
                products = products_response.data
                for product in products:
                    for variant in product.raw_data['variants']:
                        sku_dict.setdefault(variant['sku'], []).append(variant)

        items = []
        for item in line_items:
            if item['sku'] not in sku_dict:
                continue

            for variant in sku_dict[item['sku']]:
                quantity_committed = variant.get('inventory_advance', {}).get('qty_commited', 0)
                quantity = int(quantity_committed) + int(item['quantity'])
                items.append({
                    'product_id': variant['product_id'],
                    'product_variant_id': variant['id'],
                    'quantity': quantity,
                })

        return items

    @staticmethod
    def verify_webhook_signature(payload: str, signature: str, secret: str) -> bool:
        # Implement Haravan's webhook signature verification method here
        return True

    @staticmethod
    def extract_merchant_id(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return headers.get('x-haravan-org-id')

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return headers.get('x-haravan-topic')

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> FetchEvent:
        topic = headers.get('x-haravan-topic')
        org_id = headers.get('x-haravan-org-id')
        event_id = None
        object_type = ActionGroup.UNKNOWN
        action_type = ActionType.webhook
        action_group = ActionGroup.webhook

        if 'orders' in topic:
            event_id = headers.get('x-haravan-order-id')
            object_type = ActionGroup.order
            action_type = ActionType.get_order
            action_group = ActionGroup.order
        elif 'products' in topic:
            event_id = headers.get('x-haravan-product-id')
            object_type = ActionGroup.product
            action_type = ActionType.get_product
            action_group = ActionGroup.product

        return FetchEvent(
            channel="haravan",
            connection_id=str(uuid.uuid4()),
            action_type=action_type,
            action_group=action_group,
            event_time=pendulum.now().to_iso8601_string(),
            is_batch=False,
            object_type=object_type,
            object_id=event_id,
            object_data={
                'event': payload,
                'headers': headers,
                'org_id': org_id,
                'topic': topic
            }
        )

    def webhook(self, payload: Dict[str, Any], headers: Dict[str, str]) -> FetchEvent:
        return self.extract_object_details(payload, headers)

    def webhook_subscribe(self) -> Dict[str, Any]:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.access_token}'
        }
        response = requests.post('https://webhook.haravan.com/api/subscribe', json={}, headers=headers)
        response.raise_for_status()
        return response.json()