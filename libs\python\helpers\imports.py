import re
from decimal import Decimal

from elasticsearch import NotFoundError
import uuid
import pendulum
from nolicore.utils.exceptions import BadRequest

from helpers.destination_data import invoke_import_destination_data
from helpers.purchase_order import invoke_import_purchase_order
from helpers.return_order import invoke_import_return_order
from helpers.stock_adjustments import invoke_import_stock_adjustment
from models.imports.imports import ImportModel, ImportType
from helpers.product import invoke_import_product
from helpers.order import invoke_import_order

from helpers.utils import RESOURCE_SERVICE
from models.actor.customer_group import CustomerGroupModel
from models.basic import BasicAttributes
from models.price.price_group import PriceGroupModel
from models.product.unit import UnitModel
from models.product.variant import VariantSkuIndex
from models.product.brand import BrandModel
from models.product.category import CategoryModel


def return_price_groups(data, company_id):
    price_groups_name = {}
    for item in data:
        name = item.replace("pl_", "").capitalize()
        price_groups_name[item] = name
    price_groups_obj = {}
    for item in data:
        append_new_obj(price_groups_name[item], item, company_id, price_groups_obj)
    return price_groups_obj


def prices(row, price_groups, price_groups_obj):
    return [
        {"price": row[price_item] or 0, "price_group": price_groups_obj.get(price_item)}
        for price_item in price_groups
    ]


def format_unit(item, unit_obj, company_id):
    default_units = ['bộ', 'Hộp', 'vỉ',
                     'Cái', '1', 'lọ', 'lốc',
                     'cuộn', '100gr', 'viên', 'lớp',
                     'set', 'tuýp', 'cái', 'combo', 'con', 'hộp', 'gói',
                     'Hộp 85', 'cây', 'mét', '10 sợi',
                     'Cuộn', 'Bộ', 'đôi', 'tấm', None]
    weight_unit = {
        "1/2kg": 5,
        "1kg": 10,
        "10": 10,
        "Mica trái tim chuyên làm túi xách (2 tấm)": 2,
    }
    unit = None
    if item not in default_units or weight_unit.get(item):
        ratio = weight_unit.get(item)
        if not ratio:
            def extract_number_before_space(text):
                try:
                    match = re.search(r'(\d+)\s', text)
                    if match:
                        return match.group(1)
                    else:
                        return None
                except ValueError:
                    return None

            temp_ratio = extract_number_before_space(item)
            if temp_ratio is None:
                return unit
            ratio = int(temp_ratio)
        unit = get_obj(item, unit_obj, company_id, 'unit', {"ratio": ratio})
    return unit


def format_product_in_xlsx_file(header, data, company_id):
    products = []
    categories_obj = {}
    brands_obj = {}
    unit_obj = {}
    price_groups = [item for item in header if item.startswith("pl_")]
    price_groups_obj = return_price_groups(price_groups, company_id)

    for index, item in enumerate(data):
        brand = get_obj(item.get('nhãn hiệu'), brands_obj, company_id, 'brand')
        category = get_obj(item.get('loại sản phẩm'), categories_obj, company_id, 'category')
        measurements = {
            "height_unit": "cm",
            "height_value": 0,
            "length_unit": "cm",
            "length_value": 0,
            "weight_unit": item.get("đơn vị khối lượng"),
            "weight_value": item.get("khối lượng") or 0,
            "width_unit": "cm",
            "width_value": 0,
        }
        if item.get("tên sản phẩm*"):
            options = [
                {"name": item[f"thuộc tính {i}"], "values": [item[f"giá trị thuộc tính {i}"]]}
                for i in range(1, 4) if item.get(f"giá trị thuộc tính {i}")
            ]
            new_product = {
                "name": item["tên sản phẩm*"],
                "brand": brand,
                "category": category,
                "company_id": company_id,
                "description": "",
                "images": [{"url": item["ảnh đại diện"]}] if item.get("ảnh đại diện") else [],
                "options": options,
                "measurements": measurements,
                "inventories": None,
                "prices": prices(item, price_groups, price_groups_obj),
                "publish": False,
                "shortDescription": item.get("mô tả sản phẩm") or "",
                "sku": item["mã sku*"],
                "tags": item.get("tags"),
                "barcode": item.get("barcode"),
                "variants": [],
            }
            products.append(new_product)
        else:
            last_product = products[-1] if products else None
            if last_product:
                for i in range(1, 4):
                    attr_key = f"giá trị thuộc tính {i}"
                    attr_value = item.get(attr_key)
                    if attr_value:
                        if attr_value not in last_product["options"][i - 1]["values"]:
                            last_product["options"][i - 1]["values"].append(attr_value)

                def create_variant(new_variant, variants=None):
                    unit = format_unit(new_variant.get('đơn vị', '1'), unit_obj, company_id)
                    variant_measurements = {
                        "height_unit": "cm",
                        "height_value": 0,
                        "length_unit": "cm",
                        "length_value": 0,
                        "weight_unit": new_variant.get("đơn vị khối lượng"),
                        "weight_value": new_variant.get("khối lượng") or 0,
                        "width_unit": "cm",
                        "width_value": 0,
                    }
                    if unit and variants:
                        original_variant = next(
                            (item for item in variants if
                             item.get("barcode") == new_variant.get("barcode") or item.get(
                                 "barcode") in new_variant.get("barcode")), None)
                        if original_variant is None:
                            original_variant = variants[-1]
                            original_sku = original_variant.get('sku')
                            option1 = original_variant.get('option1')
                        else:
                            original_sku = original_variant.get('sku')
                            option1 = original_variant.get('option1')
                    else:
                        original_sku = new_variant["mã sku*"]
                        option1 = new_variant.get("giá trị thuộc tính 1")
                    return {
                        "barcode": new_variant.get("barcode"),
                        "images": [{"url": new_variant.get("ảnh đại diện")}] if new_variant.get("ảnh đại diện") else [],
                        "inventories": None,
                        "prices": prices(new_variant, price_groups, price_groups_obj),
                        "brand": brand,
                        "category": category,
                        "measurements": variant_measurements,
                        "name": new_variant.get("tên phiên bản sản phẩm"),
                        "option1": option1,
                        "option2": new_variant.get("giá trị thuộc tính 2"),
                        "option3": new_variant.get("giá trị thuộc tính 3"),
                        "sku": new_variant["mã sku*"],
                        "original_sku": original_sku,
                        "unit": unit
                    }

                if not last_product["variants"]:
                    first_variant = create_variant(data[index - 1])
                    new_variants = [first_variant, create_variant(item, [first_variant])]
                    last_product["variants"] = new_variants
                    last_product.pop("barcode", None)
                    last_product.pop("inventories", None)
                    last_product.pop("prices", None)
                else:
                    last_product["variants"].append(create_variant(item, last_product["variants"]))

    return products


def format_purchase_order_in_xlsx_file(data, company_id, location, user, supplier):
    purchase_orders = []
    order_line_items = []
    for index, item in enumerate(data):
        quantity = item.get("Số lượng")
        variant = VariantSkuIndex.get_variant_by_sku(item.get("Mã SKU"), company_id).attributes_dict
        if variant is None:
            break
        price_result = item.get("Đơn giá")
        if price_result is None:
            for price in variant.get("prices"):
                if price["price_group"]["name"] == "Giá nhập":
                    price_result = item["price"]
                    break
        discount_variant = item.get("Chiết khấu sản phẩm.VND") if item.get("Chiết khấu sản phẩm.VND") else (Decimal(
            price_result) * Decimal(item.get("Chiết khấu sản phẩm.%")) / 100 if item.get(
            "Chiết khấu sản phẩm.%") else 0)
        new_order_line_item = {
            "sku": variant.get("sku"),
            "unit_price": Decimal(str(price_result)),
            "sale_price": Decimal(str(price_result)) - Decimal(str(discount_variant)),
            "name": variant.get("name"),
            "variant_name": variant.get("name"),
            "quantity": quantity,
            "image_url": (lambda x: x[0]["url"] if x else None)(variant.get("images")),
            "note": item.get("Ghi chú sản phẩm"),
            "location": location,
            "discount": Decimal(str(discount_variant)),
            "product_id": variant.get("product_id"),
            "variant_id": variant.get("id")
        }
        if variant.get("brand") is not None:
            new_order_line_item['brand'] = {
                'id': variant.get("brand").get("id"),
                'name': variant.get("brand").get("name")
            }
        if variant.get("category") is not None:
            new_order_line_item['category'] = {
                'id': variant.get("category").get("id"),
                'name': variant.get("category").get("name")
            }
        order_line_items.append(new_order_line_item)

    limit = 20
    for i in range(0, len(order_line_items), limit):
        purchase_orders.append(
            {
                "staff_id": user["id"],
                "location_id": location['id'],
                "supplier": supplier,
                "order_line_items": order_line_items[i:i + limit],
                "user": user
            }
        )
    return purchase_orders


def format_customer_in_xlsx_file(data, company_id):
    customers = []
    customer_groups = {}

    for index, item in enumerate(data):
        customer_group = get_obj(item.get('hạng thành viên'), customer_groups, company_id, 'customerGroup')
        loyal_customer = {
            "point": item.get("điểm"),
            "used_point": item.get("số điểm đã sử dụng")
        }
        sale_order = {
            "purchased_order": item.get("tổng đơn hàng"),
            "total_spent": item.get("chi tiêu tích lũy")
        }
        new_customer_group = {
            "first_name": item.get("họ và tên"),
            "phone": item.get("số điện thoại"),
            "customer_group": customer_group,
            "loyal_customer": loyal_customer,
            "sale_order": sale_order
        }
        customers.append(new_customer_group)
    return customers


def create_new_obj(name, company_id, model='price group', extra_data=None):
    params = {'name': name}
    if isinstance(extra_data, dict):
        params = {**extra_data, 'name': name}
    if model == 'brand':
        obj = BrandModel(BasicAttributes.add_basic_attributes(params, company_id))
    elif model == 'category':
        obj = CategoryModel(BasicAttributes.add_basic_attributes(params, company_id))
    elif model == 'unit':
        obj = UnitModel(BasicAttributes.add_basic_attributes(params, company_id))
    elif model == 'customerGroup':
        obj = CustomerGroupModel(BasicAttributes.add_basic_attributes(params, company_id))
    else:
        obj = PriceGroupModel(BasicAttributes.add_basic_attributes(params, company_id))
    obj.save()
    new_obj = obj.attributes_dict
    new_obj.pop('user')
    new_obj.pop('updated_at')
    new_obj.pop('created_at')
    new_obj.pop('company_id')
    return new_obj


def append_new_obj(name, key_name, company_id, obj, model='price group', extra_data=None):
    params = {
        "query": {
            "bool": {
                "must": [
                    {"term": {"name.keyword": name}},
                    {"term": {"company_id.keyword": company_id}},
                ]
            }
        }
    }
    try:
        if model == 'brand':
            list_item = BrandModel.report(params, service=RESOURCE_SERVICE).body['hits']['hits']
        elif model == 'category':
            list_item = CategoryModel.report(params, service=RESOURCE_SERVICE).body['hits']['hits']
        elif model == 'unit':
            list_item = UnitModel.report(params, service=RESOURCE_SERVICE).body['hits']['hits']
        elif model == 'customerGroup':
            list_item = CustomerGroupModel.report(params, service=RESOURCE_SERVICE).body['hits']['hits']
        else:
            list_item = PriceGroupModel.report(params, service=RESOURCE_SERVICE).body['hits']['hits']
        list_obj = [item['_source'] for item in list_item]
    except NotFoundError as ex:
        list_obj = []
    if len(list_obj) > 0:
        found_item = next(
            (sub_item for sub_item in list_obj if sub_item['name'] == name), None)
        if found_item is not None:
            new_obj = found_item
            new_obj.pop('updated_at')
            new_obj.pop('created_at')
            new_obj.pop('company_id')
            if 'product_count' in new_obj:
                new_obj.pop('product_count')
            if 'terminal_count' in new_obj:
                new_obj.pop('terminal_count')
        else:
            new_obj = create_new_obj(name, company_id, model, extra_data)
    else:
        new_obj = create_new_obj(name, company_id, model, extra_data)
    obj[key_name] = new_obj


def get_obj(item_value, obj_dict, company_id, model, extra_data=None):
    if item_value is None:
        return None

    item_key = item_value.lower()
    if obj_dict.get(item_key):
        return obj_dict.get(item_key)
    else:
        append_new_obj(item_value, item_key, company_id, obj_dict, model, extra_data)
        return obj_dict.get(item_key)


def validate_purchase_order_file(data):
    filter_data = []
    default_units = ['bộ', 'Hộp', 'vỉ',
                     'Cái', '1', 'lọ', 'lốc',
                     'cuộn', '100gr', 'viên', 'lớp',
                     'set', 'tuýp', 'cái', 'combo', 'con', 'hộp', 'gói',
                     'Hộp 85', 'cây', 'mét', '10 sợi',
                     'Cuộn', 'Bộ', 'đôi', 'tấm', None]
    for item in data:
        if item.get("Mã SKU") is None:
            raise BadRequest("SKU required")
        if item.get("Số lượng") is None:
            raise BadRequest("Quantity required")
        if item.get("Số lượng") > 0 and item.get("Đơn vị") in default_units:
            filter_data.append(item)
    if len(filter_data) == 0:
        raise BadRequest("File has no valid rows")
    return filter_data


def import_master_data(company_id, object_type, master_data):
    request_id = str(uuid.uuid4())
    company_id = str(company_id)
    if object_type == 'inventory':
        import_type = 'stock_adjustment'
    else:
        import_type = object_type
    try:
        ImportModel.save_data(company_id, ImportType[import_type], request_id, [master_data])
        ImportModel({
            'id': request_id,
            'company_id': company_id,
            'import_type': ImportType[import_type].value,
            'imported_at': pendulum.now().to_iso8601_string()
        }).save()

        if import_type == 'product':
            response = invoke_import_product(request_id, company_id, start=0)
        elif import_type == 'order':
            response = invoke_import_order(request_id, company_id, start=0)
        elif import_type == 'stock_adjustment':
            response = invoke_import_stock_adjustment(request_id, company_id, start=0)
        elif import_type == 'purchase_order':
            response = invoke_import_purchase_order(request_id, company_id, start=0)
        elif import_type == 'return_order':
            response = invoke_import_return_order(request_id, company_id, start=0)
        else:
            raise ValueError(f"Unsupported object_type: {import_type}")

        return {
            'request_id': request_id,
            'response': response
        }
    except Exception as e:
        raise BadRequest(str(e))


def import_destination_data(company_id, object_type, connection_id, raw_data):
    request_id = str(uuid.uuid4())
    company_id = str(company_id)
    if object_type == 'inventory':
        import_type = 'stock_adjustment'
    else:
        import_type = object_type
    try:
        ImportModel.save_data(company_id, ImportType[import_type], request_id, [raw_data])
        ImportModel({
            'id': request_id,
            'company_id': company_id,
            'import_type': ImportType[import_type].value,
            'is_clone': True,
            'imported_at': pendulum.now().to_iso8601_string()
        }).save()
        response = invoke_import_destination_data(request_id, company_id, connection_id, object_type, start=0)
        return {
            'request_id': request_id,
            'response': response
        }
    except Exception as e:
        raise BadRequest(str(e))


def format_stock_adjustment_in_xlsx_file(header, data, company_id):
    stock_adjustments = []
    location_obj = {}
    stock_adjustment_dict = {}

    for index, item in enumerate(data):
        available = item.get('chi nhánh', 0)
        sku = item.get('mã sku')
        if available == 0 or sku is None:
            continue
        location = get_obj(item.get('chi nhánh'), location_obj, company_id, 'brand')
        location_id = location.get('id')
        inventory = {'available': available, 'sku': sku}
        if location_id in stock_adjustment_dict:
            stock_adjustment_dict[location_id].append(inventory)
        else:
            stock_adjustment_dict[location_id] = [inventory]
    for key, value in stock_adjustment_dict.items():
        stock_adjustments.append({'location_id': key, 'inventories': value})
    return stock_adjustments
