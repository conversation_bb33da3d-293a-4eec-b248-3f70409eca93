import base64
import os
import uuid
from io import BytesIO
from urllib.parse import quote_plus

import requests
from nolicore.utils.aws.boto_helper import get_s3_client
from nolicore.utils.utils import logger

from models.media.image import ImageModel

s3 = get_s3_client()

BUCKET_NAME = os.getenv('BUCKET_NAME')


def upload_image(company_id, prefix, image):
    # not update if had image id
    if 'id' in image:
        return image

    # download image content from url
    if 'url' in image or 'src' in image:
        image_url = image.get('url') or image.get('src')
        response = requests.get(image_url)
        image_data = response.content
        file_name = os.path.basename(image_url)
        file_type = response.headers['Content-Type']
    # prepare image content from base 64
    else:
        file_name = image['name'] if 'name' in image else str(uuid.uuid4())
        image_encoded = image['image'].split(',')[-1]
        file_type = image['image'].split(';')[0].split(':')[-1]
        image_data = base64.b64decode(image_encoded)

    key = f'{company_id}/images/{prefix}/{quote_plus(file_name)}'
    # Upload the image file to S3
    image_file = BytesIO(image_data)
    s3.upload_fileobj(image_file, BUCKET_NAME, key,
                      ExtraArgs={'ContentType': file_type, 'ACL': 'public-read'})

    public_url = f'https://{BUCKET_NAME}.s3.amazonaws.com/{key}'
    logger.info(public_url)

    image_obj = ImageModel.create(company_id, {
        'id': base64.b64encode(key.encode()).decode(),
        'url': public_url,
        'name': file_name
    })
    return image_obj.attributes_dict
