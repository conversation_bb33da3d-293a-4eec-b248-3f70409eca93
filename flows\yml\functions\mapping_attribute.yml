get_attributes:
  handler: src.apis.mapping_attribute.get_attributes
  events:
    - httpApi:
        path: /{connection_id}/{type}/attributes
        method: post
        authorizer:
          name: optiAuthorizer

get_mapping_attribute:
  handler: src.apis.mapping_attribute.get_mapping_attribute
  events:
    - httpApi:
        path: /{connection_id}/{type}/mapping_attributes
        method: get
        authorizer:
          name: optiAuthorizer

save_mapping_attribute:
  handler: src.apis.mapping_attribute.save_mapping_attribute
  events:
    - httpApi:
        path: /{connection_id}/{type}/mapping_attributes
        method: post
        authorizer:
          name: optiAuthorizer

remove_mapping_attribute:
  handler: src.apis.mapping_attribute.remove_mapping_attribute
  events:
    - httpApi:
        path: /{connection_id}/{type}/mapping_attributes
        method: delete
        authorizer:
          name: opti<PERSON><PERSON><PERSON>zer