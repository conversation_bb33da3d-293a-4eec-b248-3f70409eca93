import logging
from typing import Dict, Any
import pendulum

from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.transformers.base import SourceTransformer
from models.common.status import PaymentStatus
from models.order.order import OrderAttributesSchema

logger = logging.getLogger(__name__)


class TiktokShopOrderTransformer(SourceTransformer):
    channel_name = 'tiktok_shop'
    object_type = ActionGroup.order.value

    def _map_object_type_to_action_type(self) -> str:
        return ActionType.get_order.value

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.order

    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # Map order status
        status_mapping = {
            "UNPAID": "PENDING",  # Order placed but not paid
            "ON_HOLD": "DRAFT",  # Order accepted but awaiting fulfillment
            "AWAITING_SHIPMENT": "AWAIT_PACKING",  # Ready to be shipped
            "PARTIALLY_SHIPPING": "PARTIAL_SHIPPING",  # Some items shipped
            "AWAITING_COLLECTION": "READY",  # Waiting for carrier pickup
            "IN_TRANSIT": "SHIPPING",  # In delivery process
            "DELIVERED": "DELIVERED",  # Package delivered to buyer
            "COMPLETED": "COMPLETED",  # Order completed, no returns/refunds allowed
            "CANCELLED": "CANCELLED"  # Order cancelled
        }

        try:
            # Input validation
            self.validate_input(data)

            raw_data = data
            shipping_address = raw_data.get('recipient_address', {})
            payment_info = raw_data.get('payment', {})

            # Build address dictionary
            address = {
                "name": shipping_address.get('name'),
                "phone": shipping_address.get('phone_number'),
                "address1": shipping_address.get('address_line1', ''),
                "address2": shipping_address.get('address_line2', ''),
                "city": next((info.get('address_name', '') for info in shipping_address.get('district_info', [])
                              if info.get('address_level') == 'L2'), ''),
                "country": next((info.get('address_name', '') for info in shipping_address.get('district_info', [])
                                 if info.get('address_level') == 'L0'), ''),
                "district": '',
                "ward": ''
            }

            order_id = str(raw_data.get('id', ''))

            transformed_data = {
                "external_id": order_id,
                "customer": {
                    "addresses": [address],
                    "customer_code": raw_data.get("user_id", ""),
                    "company_name": "",
                    "billing_address": address,
                    "last_name": shipping_address.get("last_name") or "tiktokshop_customer",
                    "first_name": shipping_address.get('first_name') or shipping_address.get("name"),
                    "id": raw_data.get("user_id", ""),
                    "email": raw_data.get("buyer_email"),
                    "phone": shipping_address.get("phone_number"),
                    "tags": ""
                },
                "status": status_mapping.get(raw_data.get("status", ""), "SYNC_ERROR"),
                "billing_address": address,
                "shipping_address": address,
                "source": {
                    "id": raw_data.get("shop_id", ""),
                    "name": "TikTok Shop",
                    "origin_id": order_id,
                    "origin": "TikTok Shop"
                },
                "payment_status": (
                    PaymentStatus.COD.value if raw_data.get("is_cod", False)
                    else PaymentStatus.PAID.value if raw_data.get("status", "") != 'UNPAID' and raw_data.get(
                        "paid_time")
                    else PaymentStatus.UNPAID.value
                ),

                "order_line_items": [
                    {
                        "unit_price": float(item.get("original_price", 0)),
                        "sale_price": float(item.get("sale_price", 0)),
                        "discount": float(item.get("seller_discount", 0)),
                        "sku": item.get("seller_sku"),
                        "name": item.get("product_name"),
                        "variant_name": item.get("sku_name"),
                        "image_url": item.get("sku_image", ""),
                        "quantity": int(item.get("quantity", 1)),
                        "product_id": item.get("product_id"),
                        "variant_id": item.get("sku_id"),
                        "location": {}
                    }
                    for item in raw_data.get("line_items", [])
                ],
                "note": raw_data.get("buyer_message", ""),
                "tags": "",
                "total": float(payment_info.get("total_amount", 0)),
                "sub_total": float(payment_info.get("sub_total", 0)),
                "discount": float(payment_info.get('seller_discount', 0)),
                "shipping_fee": float(payment_info.get('shipping_fee', 0)),
                "tax": float(payment_info.get('tax', 0)),
                "updated_at": pendulum.from_timestamp(raw_data.get("update_time")).to_iso8601_string(),
                "created_at": pendulum.from_timestamp(raw_data.get("create_time")).to_iso8601_string()
            }

            # Output validation
            self.validate_output(transformed_data)
            return transformed_data

        except Exception as e:
            logger.error(f"Error transforming TikTok Shop order data: {str(e)}")
            raise

    def validate_input(self, data: Dict[str, Any]):
        # Implement input validation logic using self.input_schema
        pass

    def validate_output(self, data: Dict[str, Any]):
        # Implement output validation logic using self.output_schema
        pass

    @property
    def input_schema(self):
        return {
            "type": "object",
            "properties": {
                "raw_data": {
                    "type": "object",
                    "properties": {
                        "order_id": {"type": "string"},
                        "user_id": {"type": "string"},
                        "shop_id": {"type": "string"},
                        "order_status": {"type": "string"},
                        "buyer_email": {"type": "string"},
                        "buyer_message": {"type": "string"},
                        "create_time": {"type": "string"},
                        "update_time": {"type": "string"},
                        "recipient_address": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string"},
                                "phone_number": {"type": "string"},
                                "address_line1": {"type": "string"},
                                "address_line2": {"type": ["string", "null"]},
                                "district_info": {"type": "array"}
                            }
                        },
                        "payment": {
                            "type": "object",
                            "properties": {
                                "total_amount": {"type": "number"},
                                "promotion_amount": {"type": "number"},
                                "shipping_fee": {"type": "number"},
                                "taxes": {"type": "number"}
                            }
                        },
                        "line_items": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "sale_price": {"type": "number"},
                                    "seller_sku": {"type": "string"},
                                    "product_name": {"type": "string"},
                                    "quantity": {"type": "integer"},
                                    "product_id": {"type": "string"},
                                    "sku_id": {"type": "string"}
                                }
                            }
                        }
                    }
                }
            }
        }

    @property
    def output_schema(self):
        return OrderAttributesSchema().dump(OrderAttributesSchema())
