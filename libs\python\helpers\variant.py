import os
import uuid

from deepdiff import DeepDiff
from nolicore.utils.utils import compress, logger

from helpers.image import upload_image
from helpers.lambda_client import lambda_client_invoke_async
from models.product.product import ProductModel
from models.product.variant import VariantModel


ENV = os.getenv('ENV')


def sync_variant(company_id, variant, remote_variant: VariantModel = None):
    new_images = variant.pop('images', [])
    if isinstance(new_images, list):
        if len(new_images) > 0:
            images = []
            for image in new_images:
                if "url" in image:
                    if image['url'].startswith('http'):
                        images.append(upload_image(company_id, 'products', image))
                else:
                    images.append(upload_image(company_id, 'products', image))
            variant['images'] = images
    if remote_variant:
        remote_variant.update(variant)
        return remote_variant.attributes_dict
    return VariantModel.create(company_id, variant).attributes_dict


def need_update_variants(product_obj: ProductModel, variants):
    if not product_obj:
        return True

    current_variants = sorted(product_obj.attributes_dict['variants'], key=lambda x: x['id'])
    new_variants = sorted(variants, key=lambda x: x.get('id', str(uuid.uuid4())))
    if len(current_variants) != len(new_variants):
        return True
    logger.info(current_variants)
    logger.info(new_variants)
    diff = DeepDiff(current_variants, new_variants,
                    exclude_regex_paths=[r"root\[.*\]\['created_at'\]", r"root\[.*\]\['updated_at'\]"])
    return any(key in diff for key in (
        'values_changed', 'iterable_item_added', 'iterable_item_removed', 'dictionary_item_added',
        'dictionary_item_removed'))


def invoke_delete_variant_handler(product_id, company_id):
    payload = {
        'product_id': product_id,
        'company_id': str(company_id),
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-products-service-{ENV}-deleteVariantsHandler')
