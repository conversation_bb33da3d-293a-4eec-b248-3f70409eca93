import requests
from nolicore.utils.utils import logger

from helpers.auth import login_helper, get_user_info_by_sub_api
from integrations.common.connection_helper import get_connection_by_id
from models.basic import BasicAttributes
from models.common.settings import NameSettings
from models.common.status import Status
from models.notification.notification import NotificationModel, NotificationType
from models.settings.settings import SettingsModel


def send_notification_to_zalo(order):
    try:
        customer_id = order.get("customer", {}).get("id")
        company_id = order.get("company_id")

        if customer_id is None or company_id is None:
            return

        response = login_helper("", "", is_admin=True)
        token = response['IdToken']
        access_token = response['AccessToken']
        user_attributes = get_user_info_by_sub_api(token, access_token, customer_id)

        user_attributes_obj = {'Username': user_attributes['Username']}
        for item in user_attributes['Attributes']:
            user_attributes_obj[item['Name']] = item['Value']

        zalo_user_id = user_attributes_obj.get('custom:zalo_user_id')
        if zalo_user_id is None:
            return

        # save noti
        data = generate_notification(order, customer_id)
        noti_obj = NotificationModel(BasicAttributes.add_basic_attributes(data, company_id))
        noti_obj.save()

        # send noti to zalo
        loyalty_app_settings_obj = SettingsModel.by_key({
            'setting_name': NameSettings.loyalty_app.value,
            'company_id': company_id
        })
        if loyalty_app_settings_obj is None:
            return
        zalo_connection_id = loyalty_app_settings_obj.attributes_dict.get("setting_value", {}).get("default_zalo_setting",
                                                                                                   {}).get('id')
        if zalo_connection_id is None:
            return
        connection_obj = get_connection_by_id(zalo_connection_id, company_id=company_id)
        app_id = connection_obj.attributes_dict.get("settings", {}).get("app_id")
        api_key = connection_obj.attributes_dict.get("settings", {}).get("api_key")
        app_secret = connection_obj.attributes_dict.get("settings", {}).get("app_secret")

        if app_id is None or app_secret is None or api_key is None:
            return

        url = 'https://openapi.mini.zalo.me/notification/template'
        headers = {
            'X-Api-Key': f'Bearer {api_key}',
            'X-User-Id': zalo_user_id,
            'X-MiniApp-Id': app_id,
            'Content-Type': 'application/json'
        }
        order_id = order.get('id')
        data = {
            "templateId": "00126fd75392bacce383",
            "templateData": {
                "buttonText": "Xem chi tiết đơn hàng",
                "buttonUrl": f"https://zalo.me/s/{app_id}/order-details/{order_id}",
                "title": noti_obj.attributes.title,
                "contentTitle": noti_obj.attributes.content_title,
                "contentDescription": noti_obj.attributes.content_description
            }
        }
        res = requests.post(url, headers=headers, json=data)
        print(res.json())
    except Exception as e:
        logger.exception(e)
        return


def generate_notification(order, customer_id):
    status = order.get('status')
    order_code = order.get('number')  # Lấy mã đơn hàng

    # Tạo tiêu đề ngắn hơn nếu vượt quá 32 ký tự
    def shorten_title(text):
        return text if len(text) <= 32 else text[:29] + "..."

    # Tạo nội dung tùy thuộc vào trạng thái đơn hàng
    if status == Status.DRAFT.value:
        title = shorten_title(f"ĐH {order_code} chờ xác nhận")
        description = f"Đơn hàng {order_code} của bạn hiện đang chờ xác nhận."
    elif status == Status.PENDING.value:
        title = shorten_title(f"ĐH {order_code} chờ xử lý")
        description = f"Chúng tôi đang xem xét đơn hàng {order_code} của bạn."
    elif status == Status.BLOCKING.value:
        title = shorten_title(f"ĐH {order_code} chờ giải quyết")
        description = f"Đơn hàng {order_code} của bạn đang chờ được xử lý."
    elif status == Status.AWAIT_PACKING.value:
        title = shorten_title(f"ĐH {order_code} chờ gói")
        description = f"Chúng tôi đang chuẩn bị gói đơn hàng {order_code} của bạn."
    elif status == Status.PACKING.value:
        title = shorten_title(f"Đang gói ĐH {order_code}")
        description = f"Đơn hàng {order_code} của bạn đang được gói."
    elif status == Status.READY.value:
        title = shorten_title(f"ĐH {order_code} sẵn sàng gửi")
        description = f"Đơn hàng {order_code} của bạn đã được gói và sẵn sàng để gửi."
    elif status == Status.SHIPPING.value:
        title = shorten_title(f"ĐH {order_code} đang vận chuyển")
        description = f"Đơn hàng {order_code} của bạn đang được giao."
    elif status == Status.DELIVERED.value:
        title = shorten_title(f"ĐH {order_code} đã giao")
        description = f"Đơn hàng {order_code} của bạn đã được giao thành công."
    elif status == Status.CANCELLED.value:
        title = shorten_title(f"ĐH {order_code} đã bị huỷ")
        description = f"Đơn hàng {order_code} của bạn đã bị huỷ theo yêu cầu."
    elif status == Status.RETURNING.value:
        title = shorten_title(f"ĐH {order_code} đang hoàn trả")
        description = f"Đơn hàng {order_code} của bạn đang trong quá trình hoàn trả."
    elif status == Status.COMPLETED.value:
        title = shorten_title(f"ĐH {order_code} đã hoàn thành")
        description = f"Cảm ơn bạn! Đơn hàng {order_code} của bạn đã hoàn thành."
    else:
        title = shorten_title(f"Trạng thái ĐH {order_code} không xác định")
        description = f"Xin lỗi, trạng thái đơn hàng {order_code} của bạn hiện tại không xác định."

    # Trả về template với nội dung động
    data = {
        "user_id": customer_id,
        "title": title,
        "content_title": title,
        "content_description": description,
        "type": NotificationType.ORDER.value,
        "data": order,
    }
    return data

