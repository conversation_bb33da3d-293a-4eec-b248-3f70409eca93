import json

import boto3
from datetime import datetime
from botocore.exceptions import ClientError

dynamodb = boto3.resource('dynamodb')
sqs = boto3.client('sqs')


class SchedulerError(Exception):
    pass


def get_connections_to_fetch(table_name):
    table = dynamodb.Table(table_name)
    try:
        response = table.scan()
        return response['Items']
    except ClientError as e:
        raise SchedulerError(f"Failed to scan connections table: {str(e)}")


def should_fetch(connection, current_time):
    schedule = connection.get('schedule', {})
    last_run = datetime.fromisoformat(connection['status'].get('last_run', '1970-01-01T00:00:00+00:00'))

    if schedule['type'] == 'interval':
        interval = schedule['value']
        return (current_time - last_run).total_seconds() >= interval
    elif schedule['type'] == 'cron':
        # Implement cron logic here. You might want to use a library like croniter for this.
        pass
    else:
        raise SchedulerError(f"Unsupported schedule type: {schedule['type']}")


def enqueue_fetch_job(connection_id, queue_url):
    try:
        sqs.send_message(
            QueueUrl=queue_url,
            MessageBody=json.dumps({'connection_id': connection_id})
        )
    except ClientError as e:
        raise SchedulerError(f"Failed to enqueue fetch job: {str(e)}")


def update_connection_status(table_name, connection_id, status):
    table = dynamodb.Table(table_name)
    try:
        table.update_item(
            Key={'id': connection_id},
            UpdateExpression="SET status.last_run = :now, status.last_status = :status",
            ExpressionAttributeValues={
                ':now': datetime.utcnow().isoformat(),
                ':status': status
            }
        )
    except ClientError as e:
        raise SchedulerError(f"Failed to update connection status: {str(e)}")


def run_scheduler(table_name, queue_url):
    current_time = datetime.utcnow()
    connections = get_connections_to_fetch(table_name)

    for connection in connections:
        try:
            if should_fetch(connection, current_time):
                enqueue_fetch_job(connection['id'], queue_url)
                update_connection_status(table_name, connection['id'], 'SCHEDULED')
        except Exception as e:
            print(f"Error processing connection {connection['id']}: {str(e)}")
            update_connection_status(table_name, connection['id'], 'SCHEDULE_FAILED')
