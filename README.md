# optiwarehouse-integration-sample

This README provides instructions for setting up, deploying, and troubleshooting the onexapis-sample project using the Serverless Framework with AWS and Python.

## Prerequisites

1. Node.js and npm
   # Check versions
   ```
   node --version
   npm --version
   ```
2. Python 3.9
   ```
   python --version
   ```
3. AWS CLI
   ```
   aws --version
   ```
4. Serverless Framework
   # Install globally
   ```
   npm install -g serverless
   ```
   # Verify installation
   ```
   serverless --version
   ```
5. Git
   ```
   git --version
   ```
   

## Installation

1. Clone the repository:
   Option 1: Clone via HTTPS
   ```
   git clone https://github.com/OneXApis/optiwarehouse-integration.git
   cd optiwarehouse-integration
   ```
   Option 2: Clone via SSH (Recommended)
   ```
   <NAME_EMAIL>:OneXApis/optiwarehouse-integration.git
   cd optiwarehouse-integration
   ```

2. Install npm dependencies:
   ```
   npm install
   ```

3. Install Serverless plugins:
   ```
   serverless plugin install -n serverless-python-requirements
   serverless plugin install -n serverless-prune-plugin
   serverless plugin install -n serverless-dotenv-plugin
   serverless plugin install -n serverless-dynamodb-autoscaling
   ```

4. Set up a Python virtual environment:
Check python version in `.python-version` file. To automatically select the correct python version, install `pyenv` and run the following commands:
   ```
   # Install pyenv (Package Manager)
   ## macOS:
   brew install pyenv
   
   ## Ubuntu/Debian:
   curl https://pyenv.run | bash
   
   ## Add to your shell configuration (~/.bashrc, ~/.zshrc, etc.):
   export PATH="$HOME/.pyenv/bin:$PATH"
   eval "$(pyenv init -)"
   
   # Install and set Python version
   pyenv install 3.9.6
   pyenv local 3.9.6
   
   # Create and activate virtual environment
   python -m venv venv
   
   ## On Unix/macOS:
   source venv/bin/activate
   
   ## On Windows:
   venv\Scripts\activate
   ```

5. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```

## AWS Profile Setup (for deployer)

Set up two AWS profiles for different environments:

1. For staging (dev):
   ```
   aws configure --profile noliwms_staging
   # Enter when prompted:
   # - AWS Access Key ID
   # - AWS Secret Access Key
   # - Default region (e.g., ap-southeast-1)
   # - Default output format (json)
   ```

2. For production:
   ```
   aws configure --profile optiwarehouse-prod
   ```

## Verify AWS Profile Configuration (for deployer)
   ``
   #List configured profiles
   aws configure list-profiles
   #Test profile access
   aws sts get-caller-identity --profile noliwms_staging
   aws sts get-caller-identity --profile optiwarehouse-prod
   ``


## GitHub SSH Setup (for private libraries)

1. Generate an SSH key:
   ```
   ssh-keygen -t ed25519 -C "<EMAIL>"
   # Press Enter to accept default file location
   # Optionally enter a passphrase
   ```

2. Add the SSH key to your ssh-agent:
   ```
   # Start the ssh-agent in the background
   eval "$(ssh-agent -s)"
   # Add SSH private key
   ssh-add ~/.ssh/id_ed25519
   ```

3. Add the public key to your GitHub account
   
   # Copy the public key to clipboard
   ## On macOS:
   ``
   pbcopy < ~/.ssh/id_ed25519.pub
   ``
   ## On Linux:
   ``
   xclip -sel clip < ~/.ssh/id_ed25519.pub
   ``
   ## On Windows:
   ``
   type %userprofile%\.ssh\id_ed25519.pub | clip
   ``
   Then:
   - Go to GitHub.com → Settings
   - Navigate to "SSH and GPG keys"
   - Click "New SSH key"
   - Give it a descriptive title
   - Paste the key and click "Add SSH key"

4. Test your SSH connection:
   ```
   ssh -T **************
   # Should see: "Hi username! You've successfully authenticated..."
   ```

## Environment Variables

Create a `.env` file in the project root and add your environment variables. In your Python code, use:

```python
from dotenv import load_dotenv
import os

load_dotenv()
public_bucket_name = os.getenv('PUBLIC_BUCKET_NAME')
```

## Deployment

### Staging Deployment
```
serverless deploy --stage dev
```

### Production Deployment
```
serverless deploy --stage prod
```

## Common Issues and Resolutions

### Issue: Unable to import module 'rpds.rpds'
**Error Message:** 
```
Unable to import module XXXX: No module named 'rpds.rpds'
```
**Resolution:** 
This error often occurs when a module is broken. Debug to identify the problematic module and reinstall or update it as necessary.

## Private Libraries

This project uses a private library. Ensure you have SSH set up for GitHub and install it using:

```
pip install git+ssh://**************/lebinhnguyen/nolicore.git@2.4.0
```

## Additional Resources

- [Serverless Framework Documentation](https://www.serverless.com/framework/docs/)
- [AWS CLI Documentation](https://awscli.amazonaws.com/v2/documentation/api/latest/index.html)
- [Python Documentation](https://docs.python.org/3.9/)

## Support

If you encounter any issues or have questions, please open an issue in the GitHub repository or contact the project maintainers.