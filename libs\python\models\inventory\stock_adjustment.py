from dataclasses import dataclass
from typing import List

from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.default_obj import DefaultObject, DefaultObjectSchema
from models.common.source_object import SourceObjectSchema, SourceObject
from models.common.status import StockAdjustmentStatus
from models.common.stock_line import StockLineItem, StockLineItemSchema


@dataclass
class StockAdjustment(Attributes):
    id: str = None
    code: str = None
    location: DefaultObject = None
    stock_line_items: List[StockLineItem] = None
    checking_staff: DefaultObject = None
    creating_staff: DefaultObject = None
    balancing_staff: DefaultObject = None
    status: StockAdjustmentStatus = StockAdjustmentStatus.AWAIT_CHECKING
    balanced_at: str = None
    note: str = None
    tags: str = None
    source: SourceObject = None


@dataclass
class StockAdjustmentAttributes(StockAdjustment, BasicAttributes):
    pass


class StockAdjustmentSchema(AttributesSchema):
    id = fields.Str(required=True)
    code = fields.Str(required=True)
    location = fields.Nested(DefaultObjectSchema(DefaultObject), required=True)
    stock_line_items = fields.List(fields.Nested(StockLineItemSchema(StockLineItem)), required=True)
    creating_staff = fields.Nested(DefaultObjectSchema(DefaultObject), required=True)
    checking_staff = fields.Nested(DefaultObjectSchema(DefaultObject), required=True)
    balancing_staff = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    status = EnumField(StockAdjustmentStatus, allow_none=True, default=StockAdjustmentStatus.AWAIT_CHECKING)
    balanced_at = fields.Str(allow_none=True)
    note = fields.Str(allow_none=True)
    tags = fields.Str(allow_none=True)
    source = fields.Nested(SourceObjectSchema(SourceObject), allow_none=True)


class StockAdjustmentAttributesSchema(StockAdjustmentSchema, BasicAttributesSchema):
    pass


class StockAdjustmentModel(BasicModel):
    table_name = 'stockAdjustment'
    attributes: StockAdjustmentAttributes
    attributes_schema = StockAdjustmentAttributesSchema
    attributes_class = StockAdjustmentAttributes

    query_mapping = {
        'query': ['code', 'id'],
        'filter': {
            'id': 'query',
            'status': 'query',
            'location.id': 'query',
            'balancing_staff.id': 'query',
            'checking_staff.id': 'query',
            'creating_staff.id': 'query',
            'company_id': 'query',
            'source.id': 'query',
            'source.channel_name': 'query',
            'balanced_at': 'range',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'note': 'note',
            'location': 'location',
            'balancing_staff': 'balancing_staff',
            'checking_staff': 'checking_staff',
            'creating_staff': 'creating_staff',
            'source': 'source'
        }
    }
