import json
import os
from enum import Enum
from pathlib import Path
from typing import Dict, Any, List

import pendulum
from nolicore.utils.exceptions import BadRequest

from helpers.image import upload_image
from helpers.lambda_client import lambda_client_invoke_async
from models.bots.knowledge import KnowledgeModel, Status
from models.bots.task import TaskModel
from models.bots.task_execution import TaskExecutionModel, TriggerType
from models.bots.virtual_staff import Role
from ..helpers.knowledge import invoke_remove_staff_from_knowledge

ENV = os.getenv('ENV')


class UpdateTypeEnum(str, Enum):
    """Types of updates that can be performed on a virtual staff."""
    STYLES = "STYLES"
    KNOWLEDGE = "KNOWLEDGE"
    TASK = "TASK"
    INFORMATION = "INFORMATION"
    SKILL = "SKILL"


def get_env_or_default(key: str, default: Any) -> Any:
    """Get value from environment or return default."""
    return os.getenv(key) or default


# def encode_api_key(api_key: str) -> str:
#     """Encode API key using JWT.

#     Args:
#         api_key: The API key to encode

#     Returns:
#         JWT encoded API key
#     """
#     # Get JWT secret from environment or use a default
#     secret = get_env_or_default("JWT_SECRET_KEY", "onexbots")

#     # Create payload with expiration
#     payload = {
#         'api_key': api_key,
#     }

#     # Encode the payload
#     return jwt.encode(payload, secret, algorithm='HS256')


def load_roles_and_expertises() -> Dict[str, list]:
    """Load roles and expertises from JSON file."""
    current_dir = Path(__file__).parent
    json_path = current_dir / "virtual_staff_roles.json"

    if not os.path.exists(json_path):
        raise FileNotFoundError(
            f"Roles configuration file not found at {json_path}"
        )

    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Create a mapping of role to expertises
    role_expertises = {}
    for role_data in data['roles']:
        role_name = role_data['role'].upper().replace(' ', '_')
        role_expertises[role_name] = role_data['expertize']

    return role_expertises


def get_valid_expertises(role: Role) -> list:
    """Get valid expertises for a role."""
    role_expertises = load_roles_and_expertises()
    return role_expertises.get(role.value, [])


def validate_expertises(role: Role, expertises: list) -> bool:
    """Validate expertises for a role."""
    if not expertises:
        return True

    valid_expertises = get_valid_expertises(role)
    return all(expertise in valid_expertises for expertise in expertises)


def validate_role_and_expertises(data: Dict[str, Any]) -> None:
    """Validate role and expertises in the data."""
    role = data.get('role')
    if not role:
        return

    try:
        role_enum = Role(role)
    except ValueError:
        raise BadRequest(
            f"Invalid role. Must be one of: {[r.value for r in Role]}"
        )

    expertises = data.get('domain_expertises', [])
    if not validate_expertises(role_enum, expertises):
        valid_expertises = get_valid_expertises(role_enum)
        raise BadRequest(
            f"Invalid expertises for role {role}. "
            f"Valid expertises are: {valid_expertises}"
        )


def prepare_virtual_staff_data(company_id: str, body: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare virtual staff data for creation or update.
    
    Args:
        body: The request body containing virtual staff data

    Returns:
        Dict containing prepared virtual staff data
    """
    # Validate role and expertises
    validate_role_and_expertises(body)

    config = body.get('configuration', {})

    # Get LLM settings from request or environment
    llm_settings = config.get('llm_settings', {})

    # Get API key from environment or request
    api_key = llm_settings.get('llm_api_key') or os.getenv('LLM_API_KEY')
    if not api_key:
        raise ValueError(
            "LLM API key is required. Please provide it in the request or set "
            "LLM_API_KEY environment variable."
        )

    # Create model dict
    llm_model = {
        "provider": (
                llm_settings.get('llm_model', {}).get('provider') or
                get_env_or_default('LLM_MODEL_PROVIDER', 'openai')
        ),
        "model_name": (
                llm_settings.get('llm_model', {}).get('model_name') or
                get_env_or_default('LLM_MODEL_NAME', 'gpt-4o-mini')
        ),
        "custom_url": (
                llm_settings.get('llm_model', {}).get('custom_url') or
                get_env_or_default('LLM_MODEL_CUSTOM_URL', None)
        )
    }

    # Create LLM settings dict
    llm_config = {
        "llm_api_key": api_key,
        "llm_model": llm_model,
        "default_llm_temperature": float(
            llm_settings.get('default_llm_temperature') or
            get_env_or_default('LLM_TEMPERATURE', 0.7)
        ),
        "max_tokens": int(
            llm_settings.get('max_tokens') or
            get_env_or_default('LLM_MAX_TOKENS', 1000)
        ),
        "max_llm_call_retries": int(
            llm_settings.get('max_llm_call_retries') or
            get_env_or_default('LLM_MAX_RETRIES', 3)
        ),
        "other_kwargs": llm_settings.get('other_kwargs', {})
    }

    # Create personality settings
    personality = config.get('personality', {})
    if personality:
        personality['personal_trait'] = personality.get('personal_trait', {
            "formality": 50,
            "detailed": 50,
            "creativity": 50
        })

    # Create knowledge base settings
    knowledge_base = config.get('knowledge_base', {})
    if knowledge_base:
        # Map domain_expertise to domain_expertise_ids if not present
        if 'domain_expertise_ids' not in knowledge_base and body.get('domain_expertise'):
            knowledge_base['domain_expertise_ids'] = body.get('domain_expertise')

    # Create tools settings
    tools = config.get('tools', {})
    if tools:
        # Ensure config exists for each enabled tool
        tools_config = tools.get('config', {})
        for tool_name in tools.get('enabled', []):
            if tool_name not in tools_config:
                tools_config[tool_name] = {"enabled": True}
        tools['config'] = tools_config

    # Create MCP settings
    mcp = config.get('mcp', {})
    if mcp:
        # Ensure adapters_config exists for each enabled adapter
        adapters_config = mcp.get('adapters_config', {})
        for adapter_name in mcp.get('enabled_adapters', []):
            if adapter_name not in adapters_config:
                adapters_config[adapter_name] = {"enabled": True}
        mcp['adapters_config'] = adapters_config

    # Create conversation settings
    conversation = config.get('conversation', {})
    if conversation:
        conversation.setdefault('max_history_length', 10)
        conversation.setdefault('context_window', 5)
        conversation.setdefault('response_timeout', 30)
        conversation.setdefault('fallback_message',
                                "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.")

    # Create final configuration dict
    configuration = {
        "instruction": config.get('instruction', ''),
        "llm_settings": llm_config,
        "personality": personality,
        "knowledge_base": knowledge_base,
        "tools": tools,
        "mcp": mcp,
        "conversation": conversation
    }

    # Update body with prepared data
    prepared_data = body.copy()
    prepared_data['configuration'] = configuration

    image = body.get('image')
    # temp code
    if isinstance(image, str):
        image = {"url": image}
    if image is not None:
        image = upload_image(company_id, 'virtual_staffs', image)
        prepared_data['image'] = image
    return prepared_data


def handle_style_update(data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle style update for virtual staff.
    
    Args:
        data: Update data containing style information
        
    Returns:
        Updated data with style changes
    """
    instructions = "This is testt needed instructions..."
    if 'configuration' not in data:
        data['configuration'] = {}
    data['configuration']['instruction'] = instructions
    return data


def handle_knowledge_update(
        virtual_staff_id: str,
        data: Dict[str, Any],
        current_attribute: Dict[str, Any],
        company_id: str
) -> Dict[str, Any]:
    """Handle knowledge update for virtual staff.
    
    Args:
        virtual_staff_id: ID of the virtual staff
        data: Update data containing knowledge information
        current_attribute: current attribute of Staff
        company_id: company_id
    Returns:
        Updated data with knowledge changes
    """
    new_knowledge_ids = data.get('configuration', {}).get('knowledge_base', {}).get('knowledge_ids', [])
    current_knowledge = current_attribute.get('configuration', {}).get('knowledge_base', {}).get('knowledge_ids',
                                                                                                 []) or []

    # Find knowledge IDs that were removed
    removed_knowledge_ids = [
        k_id for k_id in current_knowledge
        if k_id not in new_knowledge_ids
    ]

    if removed_knowledge_ids:
        # Remove staff from knowledge records that were removed
        invoke_remove_staff_from_knowledge(
            company_id=company_id,
            staff_id=virtual_staff_id,
            knowledge_ids=removed_knowledge_ids
        )

    # Add staff to new knowledge records
    for knowledge_id in new_knowledge_ids:
        knowledge = KnowledgeModel.by_key({
            'company_id': company_id,
            'id': knowledge_id
        })
        if knowledge:
            knowledge_dict = knowledge.attributes_dict
            if knowledge_dict['status'] not in [Status.READY.value]:
                raise BadRequest(
                    f"Knowledge is not ready: {knowledge_id}"
                )
            virtual_staff_ids = knowledge_dict.get('virtual_staff_ids', [])
            if virtual_staff_id not in virtual_staff_ids:
                virtual_staff_ids.append(virtual_staff_id)
                knowledge.update({
                    'virtual_staff_ids': virtual_staff_ids
                })
        else:
            raise BadRequest(f"Knowledge not found: {knowledge_id}")

    return data


def handle_task_update(
        virtual_staff_id: str,
        company_id: str,
        data: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle task update for virtual staff.
    
    Args:
        virtual_staff_id: ID of the virtual staff
        company_id: Company ID
        data: Update data containing task information
        
    Returns:
        Updated data with task changes
    """
    task_ids = data.get('task_ids', [])
    for task_id in task_ids:
        task = TaskModel.get_by_id(task_id)
        if task:
            task_execution_data = {
                'virtual_staff_id': virtual_staff_id,
                'task_id': task_id,
                'trigger_id': virtual_staff_id,
                'trigger_type': TriggerType.CONVERSATION.value,
                'execute_time': pendulum.now().isoformat(),
                'result': None,
                'feedback': None
            }
            TaskExecutionModel.create(company_id, task_execution_data)
        else:
            raise BadRequest(f"Task not found: {task_id}")
    return {}


def handle_information_update(
        company_id: str,
        data: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle information update for virtual staff.
    
    Args:
        virtual_staff_id: ID of the virtual staff
        company_id: Company ID
        data: Update data containing information
    """
    image = data.get('image')
    if image is not None:
        image = upload_image(company_id, 'virtual_staffs', image)
        data['image'] = image
    return data

def handle_virtual_staff_update(
        virtual_staff_id: str,
        company_id: str,
        data: Dict[str, Any],
        update_type: UpdateTypeEnum,
        current_attributes: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle different types of virtual staff updates.
    
    Args:
        virtual_staff_id: ID of the virtual staff to update
        company_id: Company ID
        data: Update data
        update_type: Type of update to perform
        current_attributes: current attributes of Staff
        
    Returns:
        Updated data dictionary
    """
    # Validate role and expertises if they are being updated
    if 'role' in data or 'domain_expertises' in data:
        validate_role_and_expertises(data)

    if update_type == UpdateTypeEnum.STYLES.value:
        return handle_style_update(data)
    elif update_type == UpdateTypeEnum.KNOWLEDGE.value:
        return handle_knowledge_update(virtual_staff_id, data, current_attributes, company_id)
    elif update_type == UpdateTypeEnum.TASK.value:
        return handle_task_update(virtual_staff_id, company_id, data)
    elif update_type == UpdateTypeEnum.INFORMATION.value:
        return handle_information_update(company_id, data)
    return data


def invoke_remove_knowledge_from_staff(
        company_id: str,
        knowledge_id: str,
        staff_ids: List[str]
) -> None:
    """
    Remove knowledge ID from virtual staff's knowledge list.

    Args:
        company_id (str): Company ID
        knowledge_id (str): Knowledge ID to remove
        staff_ids (List[str]): List of staff IDs to update
    """

    if not staff_ids:
        return
    payload = {
        'staff_ids': staff_ids,
        'company_id': str(company_id),
        'knowledge_id': knowledge_id
    }

    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-onexbots-service-{ENV}-removeKnowledgeFromStaff')
