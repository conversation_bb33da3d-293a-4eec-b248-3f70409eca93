import json
import unittest

from dotenv import load_dotenv

from tests.base import BaseTestCase
from flows.src.events.step_4_publish import publish_lambda_handler


class TestPublish(BaseTestCase):

    def setUp(self):
        super().setUp(account_type="onexapis_admin")

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.connection_id = "764b8784-c48f-4378-80ec-f0085c20f13b"  ## source connection id
        cls.destination_id = "15bae4ff-eb3c-4a3d-b43d-ee1951dab7b2"  ## destination connection id
        cls.source_key = "764b8784-c48f-4378-80ec-f0085c20f13b/get_order/order/order_580193227838883850.json" ## sync_record_id
        cls.destination_key = "764b8784-c48f-4378-80ec-f0085c20f13b/get_order/order/shopify_oauth.json"  ## destination transformed_record_id
        cls.destination_version_id = "bHWi0TM8yw9h3iHq0ND9KcMwFx7v7fP2"  ## destination transformed_record_version
        cls.source_version_id = "6OeeRa1bXDjkDSnVkGYGRL39u7PdFCaV"  ## source transformed_record_version
        cls.object_type = "order"
        cls.fetch_event_id = "bbbe7664-16a6-4d6e-9b3b-cdd86fa3018c"

        if not all(
                [cls.connection_id, cls.destination_id, cls.source_key, cls.destination_key, cls.destination_version_id,
                 cls.source_version_id, cls.object_type, cls.fetch_event_id]):
            raise ValueError("Shopify credentials not found in environment variables")

    def test_publish_lambda_handler(self):
        # Arrange
        event_body = {'Records': [{
            'httpMethod': 'POST',
            'body': json.dumps({"connection_id": self.connection_id,
                      "destination_id": self.destination_id,
                      "source_key": self.source_key,
                      "destination_key": self.destination_key,
                      "destination_version_id": self.destination_version_id,
                      "source_version_id": self.source_version_id,
                      "retry_count": 0,
                      "object_type": self.object_type,
                      "fetch_event_id": self.fetch_event_id
                      }),
        }]}
        test_events = {'Records': [{'messageId': '02b63cfb-1e4c-4dff-94b2-ef671af46bd2', 'receiptHandle': 'AQEBHHGbNFuyAlEoB7BiZ2sIQQmmkhUthjvMTLML4Nnpz4EcOIOJVnFijxLZii79YWbnHiq2ebXaO5hX9whXWC7mBJqdZ5C/tg3dyTipK9p6wcwWGovldWyPjXmPv04s+7tbgkEllryZb3H8y4mFM0OlGe7b6bIqljBlpnvfitCxWZNjGKiDofMXY2dAAaNIlR23hFNNhLlqrshP/yTw1uzTR797g7uGTrq6Xd8m4uoS24MTfV9AVKa2MljwUGskFEOiqh6CSkxcJzUVkxaTzwwAd4PzOBA92y3SfX/5pEgnRXvZwK7vnStt4PH3ptMWJ7dAay56axP5SN7JRVwz9vA4uaawPVVXENri5cqMAp/OyylU/XDPed8KDsHxuupLOyqV+D8AmPcTZuCre7dVx+cS60ylXGbNrW3J3X0MR4Vqwj957kv9I03wvfLEmNIkDFQR', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106962.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "QbY7pwFWrvWQ57Xfq8eVWrbeSupWifZG", "source_version_id": "I4_F0JX3xPDeWZg2Yq4MB_FRUl2j5Vew", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450820631', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450820638'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'b5a17f43f2d458325d2aa44d58d266b6', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '10f9450b-9cbd-47c3-9988-c28bdf0c9dd3', 'receiptHandle': 'AQEBdMib6G6z0znBN9C+N8duUKH+UI2Bp4OsFuVqiZHhhwDuwNB4o7uYplIlkyptW6sEa0oO4pSgGTuc7/T6/L30oIXJDeAJo+UXRyCH2nJ3BIT1lvbN5ZUEFe3T6kihwv00LtlZ7kIJEDw+EJbkNgqueYnPFmVYF2eJmCf0qP9vmLH64WagpZRm4oR66oyx1slCGP5YirsKEwNaIRN2Es7t1optasXSeI9SBzMp1PZgapy1YnRcK4/U2zTGZ28cXn0yrbn9oR1IIfGVVl4Z4jDMtQR7HCD8umlofrsS8Ip6bIu1We/RNeGfArLF4m8K+b02XZz7qcU5s+MRs1FNM5uVhaJtmigVfTZ/RwLQ18R8Bu/VC7VfXTCFmeAnBStU1LXDSG9oovwoVdAX3Bpln+XQcVATwT60fP8K5swGlGAQ+Kjz7YxjS8PYFmjEmkb3v1vB', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106960.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "s2vcNIf4f.tHxwp5fsNMCCPlPgVRk.Zw", "source_version_id": "dNgsBWI1bfIMkiu_IgI5TQ4YH2tKNVDR", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450820625', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450820627'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'a0282ac106f00ae6689c690fdfe7dd58', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '63b0e40d-878b-437d-a4dd-a8d88ac233ae', 'receiptHandle': 'AQEBUirsBxaNlAxG0x2p+1/TiwhZq+JCaCPy/gSWkRPQUJipv07FkVm+XY6s4BpzaK4fDRUToQ3iu9BWOvm+IkgIuV5LhCu0LDSJU6CKE2eavxGH4olSehmtqO46IqVVXvfqzDnJhmnfBUB1a6e+FdIe95e32+yzUvGIS+A7wnk9F5H9+NkQkndQYPkogrQU0Q014hhd/4hoiUz/qePjmqvNDbxk5bBU1/mcSCNyTVWfvigYQ8GDZhQOXk6IiMAqEGbfTxeoghKACRC+YkhtJ07759ozo1ad5mXHqe4Ou5lFngsIXAPTwwW42gXrbt0nOJoCmj02+rWmf1tYhkBUxaEG+9OOpTxRaudQSrMxO1CHB+8SSfTIFFbQwcD+ABpciPIbce6Dcfm6Wr8Aw8IM1TuvrIppGB2+39WfwSGMybT5LGdoTvIQyL9Gqm4DYGjIZrSJ', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106970.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "mslsB4goPHo4dowgKIChpoOBZ4tmFscf", "source_version_id": "bZ5T7hqftTYfW0ub.WI8ehW_i66o8IyO", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450820661', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450820662'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': '5f096a0eec21b7952aa1715d39a29cf4', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '2769e4e6-93d4-42e6-9455-b652ab3f22f7', 'receiptHandle': 'AQEBDnoaDdkEr5gUGC6MVBofmHd+GKqW8waTiF3yWoz3KJyjkJfy5pGReiS5v/i6Inie2hsU29BTjoSL3A+MuxZU82aflI9XIeMNFHKWGwa90VH/HAnbDKc5BpWkv61BroA/Zd8t2FYzIJ23dYa7RxwmZQlXXTjPnqnNK4UJjCrYU2Vjamgf2zUwSsJobdgIIuZyS4qSYzmJe8oFvyifVgltS53x47INASJC//0ukrDAuXiE9n1h06b9mOFWIN3UfeG0EqkUZlvHlGvQFX0fj7OKVTSMPO1VKtozR2f7yTMF/sNqxXJRtS1dj89bsFO2pVLHtiKDSHVeLyUoeKLoH+zn4qYPMeJJbkAMQBH3w1tu2lZW2ewTX6GeMU4aQMSebKntjexwg+D7tKynvMYP4ODrCWbFzx6y/jZAZK1ORzQv2IeiYCvRJEZiCIgWe8/CX60j', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106966.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "Me2RXBHKS7Z3sb4n4lToFfbbEbJ2jd6d", "source_version_id": "SLRDKVkqAjXOrBrLvfKYzDhNn4umgFqu", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450820668', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450820669'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'd768cc00900da4227efbc22764546dd6', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '94321961-6bdf-4af6-b6e3-05d14ae9a59a', 'receiptHandle': 'AQEBkfNBbpkekOJ4Lv9RHGnQvU8kiAfAaeWRZ2GO33r8U6gOrv7o21UOvBo2wLt+SB0s8wEShnbinx3DedxDn4JIIiSzyxJuyoqHwqLYww7aCUcvp24+ZJZ3ih8+7K+EfWFfGSpfJi1dOkaeWk0ziHzmzGl73f/fmIfuyG4ddBrcT88ZEMRxVuomEd8/lMZJEK/dTIgYLQjCCrz5c+W0LYB3RGHRlVbDRnEUW+pnlAQCIie6MwxMNksSnrhiE3Kbt84nlj/kwFqr5CMW/CxStYvgmYNxc/7pZJZhUv/fkcfH6QeDogD7kPMIa8RAKMlXWE5sqKlOGqE0QmBqFIbQT7IHL+J3wtEV0bxTwbnZl07X+QBWF7GHBkp4FZ9G/ArR3X2NZJEyrlWWChK5o0AfPQCxZv6qa+LLpWArHAPgR4o7FKQaCXL5IBz7fDIRm1esA4sv', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106959.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "YgSXQtHwi1fiQbQtYZLHKSMYR2P6kUFa", "source_version_id": "e8Nljb.p0Khy85bP_RL145YZ_pc5zj2d", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450820811', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450820812'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'e06b8d23efadab5107970ee16d86e685', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': 'd2282474-d3f8-4477-9945-55879f27bbb4', 'receiptHandle': 'AQEBI+kpTxcEw2goJBIro7eZpIv8NS47PH5pvRluLsKvc5MIUyXZQxg+PAltn7zxIjTeYvMNUKvTx90uoLABJQkFjQUQM5ROOvx0gFbTWcuDeuHNBqBpl2/2evMtWUKLQ+IdIXtYAEx31toufIG+iKpSaFjfGAOERd1CeI1AXj4D7iByV/UsU6czwgrnfhohYNyJB+vfW0wTNeVCU5cndINijJaYh1VEbt1HBg0c/RVQ2TiLTkeWsVS/BXE1hEC29pOHY89SH5hYGEFRlDg/sQZSLTe7BXAnoO67f45vFNPlTuT7zYE+8Ilah1ZN7QzHQ+NY+iv4knwSc4QffaPWQEO+7sn6AdObU8U/DmzqqnUYAdL9LQVV6PJOz9CXHt++B3aXc3TSTC1m/PxCa9NF3RWFb5LIDDNShfja8SXu5x90PWEQZFRdlpvyGx4BoEzZOWoa', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106938.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "k5k6FcwGJrLhIDr4SKi_Il7xlu3hrOS3", "source_version_id": "P7xfepqu5qFBZvzh7jtL6fdnMa.PEnLd", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450820795', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450820796'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'd033ec84275f9a65a79a04d121d42a7e', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '17319362-fbbc-4a10-bc8f-1abe9e60da09', 'receiptHandle': 'AQEB4OG/dq+zX+I0zQQMi9pKpIDWxZFyy4SVEIp3iYFo5TJ1M/6bukAMVxDR0uxcBf23CKTOLTEv/1XdN/RSN09QQTZ7i6Wc+mvI+NXiOM/qowAKepPMSTCQDAgcnH1U0q8GaghY5pc4eCZQbIZGG0Tk5cNQksygU8aRwTVhzj3PupE3T6NWSbcQa8bj12WjtSHIT6PH3hmC74hDYdmKZ+s3g6yN4MeV7Opi8R24qFL1L7nCJTcZOrbeD14BtoylN1Sa+Qht2U2DCaPEnHjV8u5E++i1KqzGdiWZw3AaIvflLT6K5wz9WhlmUvjUL/5oxHAWB7pa1x9fpx2ETXqT2LF0K5ZsCzmkLCvHYFdFXbz25OErCc0GeIGQsepIkOUpfh6xhd5JNjDpVRdxaI9rkyk9pfGv3BJfCKb6gBUWoUcrr8lqr3KiBVqHJy84WuMqiRYJ', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106968.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "p4jAJttslOEySIIWTpt_2wdJ08arctsp", "source_version_id": "jK.fKWA8bUKxT_Vpvp0ccuXJPHpWafSH", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450820936', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450820937'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'cdc9a6ef96a4b1ef19524e41b693b0b2', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '372a25aa-0bbd-4624-b4e7-006710d82528', 'receiptHandle': 'AQEBO1Q5zEEvYhchlgT0iaZ8D2Gpkz2ySGM/ZfU4Sldh5caWyTcgG90pb0cIIdh0uCIqe7GYnJWifxSE45GbCMZdj7n+oz9Gk74JxEhfpw3+ogySAH41NtItvR3MoTfiigqypwZV0WQIjpsjrFtNX0SuSH4rmUl/3SQbDb1YCjVXYqdlGfheT5KVspMNJYF945Hq03fv6PXUNxn/6efcyNmywSVfQCD27W8JnRL3yeH7vHhvKe4KifNgkhOdUUf/MNK1BsriGQmoQQwt+yOyiDq8KEBD0SoG9G4Wkt2UpE+Gt+aKZZ+yeYOz8sw92Mj+FvdqvsPEHBffiZJFKHS4wg2yD7hKqltGZ3DGI5E1ZNGoUMPS9kMcDdNgA/jpLqusL7jXmcPTxUCEG2ky3izdLtMmEgUNxngIxmAi6iwbqih9kEvzTI7tXeQ1DS63POW6jOG/', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106963.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "u0dICRGWWpTbjkru3hrp41N5GGtGPyWH", "source_version_id": "W3DY9QBw_mE23Cklzpe4KJaw4l33_08z", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450821108', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450821109'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'aa3c876973b68e57e200bee9bc444d7c', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': 'c89e44e1-db38-42ba-9f84-a2562d1f6e59', 'receiptHandle': 'AQEBCen9hf1ihDMdIGLQ6pt6nOYwlDDRcPfxlty3TEFvmQtg+IHyQd+zzC4seA5P4EHDNqoMzVyBKxPBsofjzDcNFdstQ4grIZSDmkWtLOW3SizTLwpUMzs/dJRM9C1l0kJ6WRSD8kq/kREz2VSPM3grVsHcnsdnOtAn3hhFWOkDPAATRCvhU6AwwJ5LRxT2sp/MxDkeyT6HgT/f9MDNo/sQXXA1pBcvl0aufbBrL0mxYDdBb1B2k+wqrDp324NmmX5Q9bc2CLQp3K7e5p5xLCiytdFUj/tTeFOvwo87DWkZZF/9tOkUH7uOECBkhILYVLNmPfsG2R5SnwCXollI5dEL8PKu0ZEOraFFKLywdwra9BOcthC0zqJs/sWy5aermKjkVdBzj5MYMlqtYnB2YuSBrI/RJgLjlWQxd539roOspS/AjuhnzDumspaTLo8IP6sV', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106955.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "yvT4l5JxpHthHCX34pPQ_3ZiEOf3xyFs", "source_version_id": "lGY15SVbPm1_4c9imVkOm3VbGQdrxhgS", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450821301', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450821302'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'a563d4603a4f700e0d82a4893576088c', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': 'd77ec85d-9342-435c-8340-dc099b66256a', 'receiptHandle': 'AQEBlcSH0XWWz41HPaobF3FNOwumttvDFPlMZjx9ARk2pBsoC4VK/vB8jotMVdEwBpWOXJxBXK9yx4tApsdg7ro0c67+HkmH8HWrYFw1pvy24p4GKzUURm88j3wqtkBDvLhwT74mUK+Dt9DfD5+Qq+3nV+m03sdTckgaAkxNlF2u5zUy2aog0rlgsx1gugyWZK3A623AkLMRdNu8r8RtNWUUH1+W4vW5lu7AcuAdyYiDqFgr8BpU2AH2IexOiC6aBJZFIcbc6jiy+ta8Zc6x5C26Vxvd00FmYcIC52q9nXGXTxutCDtJnIjhqVCZIcuz/JMpcpKecgTqIGZrgyizEau9RzMuVCzFvVn4DGyEL1K3L21Na/ve3aIQQzSBDVNplk97gMDP2RQ01aGeIbdLTwy0hSBJoZj8HfbwfvKxBJ+zBCmMqvCk1eGrfPjd2L1c5CCc', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106950.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "JjIywB_63EdHAolRGIYqrAcVz3ZwhgLJ", "source_version_id": "BUHxJA1dTtc5Nz_uHGCvcdtLQoQu.tJW", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450821305', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450821306'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': '4353c2d01633d5fd668eed3269758111', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '7f4aef1b-3db7-4779-90a6-a4529e1fb61c', 'receiptHandle': 'AQEBudss02i11yHuifSXqcMBP8zITChFtuRlzaTxBOFpeRB6q3SFq2RxmJgcj/rKmOwhaqms8PJ6d68L7n/FTfdCfTZBGK2TLBK3zmdtNtI7fBaUBGiy43f58lW9uVEWMaWC6BhAdiwJu/Q8JqY7Crkrv3QFLnQjALiVlAvlHWk1RefLvuOPb57U8py+uloXSw6xZTrmVuV89kXCuirKSU6oJy90getmefIhMmImyfBjCN3RQxMuEpLr/H+hf4NqkOOa67ZsVDZbq9I4mWleid5HZH8qx3rGEiIUMrv3TiUHVLRJxytiKdbVq7NjgiFtACUAKe5oSd814tmf8dpvKWDjmZ6s9fxP/L7TlclBgtLD7ZNzRW2cHueJQb6lURt+H9FtNwTX+TtuPcctuMpc3lwSmokj9iyp18/hRLM761QIfJwLI/iFnRyQ49U1tfW6/aMY', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106957.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "qw9aFQ9HZ6n1vDJ7sDQymNiA_LV.P_fr", "source_version_id": "gmBh2R1k9PcgxVIXPZOFVDjrYk0Xo1Hl", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450821277', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450821278'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': '8e1c175ddf1eba6d3a27b0d6af3b13ab', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '6065612b-1aa3-4501-8945-33d6efa61019', 'receiptHandle': 'AQEBuaAVghtew5vqBevGWZqHQLEYHVKeDyvOVI5XUPVsLr1ceqz5N+GJAr8b2KQEaMqi4lyEm5qfU8fdc1oVyTcnuH+KXKFCDzskD/+kdA9+2YCtm5k9+HsDfHcgA8+mpb9gFT546ICZigtNOvN5lODvc7h5j++qdgXK+EI9WWc73XIjWZDWjRhaOXM2tA3BECEs8XVxuVhKPf+aZhOEme/lyP8dqnXSNdRMg7Y/nOjcvF+WuELbjAoQSAhVg60u9WbpVADx48D2t3PGVGGILyHjMj4KLv/6ocEBVT2bO2mYIKeK9A+/QOsRg+UoJtyg86MrdgAoxDpTuZjetfMB+ku0JV9q5FAeMuFxIMny5x2cuOlmZffqI1NdIV8K1QNLTljg5OKtyQNfyxkGJs1K2lrRms7s2biT3KgZuWg/XbmJRp60i0OUlu+aHjMIcX2uFlk2', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106942.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "LAchvY4lnNHKU0mPZ1D.kd_sJlwz1N95", "source_version_id": "s.lpMTiDG2y8xBSgbdgGJAAZ6CLtc9cW", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450821256', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450821257'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'f4c5ee36aa105fdcf89d9dd166699c0d', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '2feb54e7-f447-4ed4-9002-7bcd5ea7f989', 'receiptHandle': 'AQEBsdpiRSVP1qMUCcWsiZynGOMUSlqSF0F34bXQ3Uddcs9xLYKIlYxo4t/N7pCttxDxj1POLjW4CvK+ryusZXA1lZZg8dBJI7s29Qw8zaR6HIPfKv2tvyGDFB/ObEYH37aFFJ3+3Akygkd8rHhaV7lw1qUiplbCY07JcPsL4EqoDfXco2oxiTTDZsdsC3zowHoPxyvD/RhNAr4259704X/caGPTpnUJsvJF6SA95cvG4gq0ZciUqsZ0PWZHjVd1P2tW4SXAp9ITdhrQNbJUw1iRgX+tRib+Xdc53u3zf4mFN0SmKRIAWWaA81xNVJZ9ZfMB4TSzmJ+tc5xDR5yWPA4lbZk4bAoVIX9jgo3lhf5mchIM0eTO3yLzq5ss/iaKvTBEtU+zLCb6RQfTNpNm1VLRS8UYAO+QOLjiZBThGuyn/NQGo8YZPnS1ztxkVNPfw7Ys', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106945.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "Iy0IjKEuJsYQ01SsFmXyGafwExG04EXY", "source_version_id": "muIL96pcsAM3Tnkodsfv9dYqbe1yauKI", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450821647', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450821648'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': 'de54f33d272a96dc70701b5a07e3abfd', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}, {'messageId': '464ad338-b1d5-4c8f-990f-897d8c247c75', 'receiptHandle': 'AQEBOIrtMibDUXZ6F5W8tKhnlrGFgZ+BgDphLf4Fi61kcdhuTrwVpPZheg4+H7oJtVs/xsjERh71tQbgHZgfCYAjl5uAremW7fM+SSCoeAjQkpqPVkn+xv8+yGXoutq92M+KlAjBdbcMxGZhYIg3QriINdUzj0d2uy7+EbXoO5kS3L/98U0mGjctQfqFsefMv3sxyrtS2Mq/mCR25Uii03zM+QglKxcFzQLZDjPCrY1/jOAKNa0HaJt5MUl5SwAC2AUSD6yzXp2vYUoPHeXaH/urLEaQugwk9B81V7Q61Cs+kI1QowkkO1CPEtCjO520xq6kRRnJxnwEQWCfL8MwECzlnNYd0rv5WSqyUtYVdRQRJDgvvnZSM4HzoHBnvWQ6oiBPOHtwkSJ2NtEs9NOFgLpDfHhYzRnCdmx3kQKsBO7ruauFfwlxe0be48GJUZvZj4HD', 'body': '{"connection_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51", "destination_id": "5b33f469-9e58-4805-9956-b08c2313969f", "source_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/inventory_39106936.json", "destination_key": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_inventory/inventory/haravan.json", "destination_version_id": "kk37vUGJMCJb.gU5Yo2hSdwyYw_5tXRI", "source_version_id": "ctSvbmBslnDSDWO0SrkXcKAZx_phVwSD", "retry_count": 0, "object_type": "inventory", "fetch_event_id": "4c28d821-61e8-46de-95f8-816be5291645"}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-68373df5-078257fa8f759b4f62752a4e;Parent=6630e1799d7c3fa8;Sampled=0;Lineage=3:2374f16b:0', 'SentTimestamp': '1748450822634', 'SenderId': 'AROASI5ONEWD4HMOISCBJ:optiwarehouse-flow-service-dev-transform_handler', 'ApproximateFirstReceiveTimestamp': '1748450822635'}, 'messageAttributes': {}, 'md5OfMessageAttributes': None, 'md5OfBody': '0ec1a53dba041fe6b8a41a86ce63ec2b', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-southeast-1:156595201415:optiwarehouse-flow-service-dev-publish-queue', 'awsRegion': 'ap-southeast-1'}]}

        event = self.create_lambda_event(body=test_events, is_invoke=True)
        context = self.create_lambda_context()

        # Act
        response = publish_lambda_handler(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'shopify connection setup successful')
        self.assertIn('connection_id', response_body)


if __name__ == '__main__':
    unittest.main()
