from dataclasses import dataclass
from decimal import Decimal

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema, Attributes


@dataclass
class MeasurementAttributes(Attributes):
    weight_unit: str = 'g'
    weight_value: Decimal = 0
    width_unit: str = 'cm'
    width_value: Decimal = 0
    length_unit: str = 'cm'
    length_value: Decimal = 0
    height_unit: str = 'cm'
    height_value: Decimal = 0


class MeasurementAttributesSchema(AttributesSchema):
    weight_unit = fields.Str(allow_none=True, default='g')
    weight_value = fields.Decimal(allow_none=True, default=0)
    width_unit = fields.Str(allow_none=True, default='cm')
    width_value = fields.Decimal(allow_none=True, default=0)
    length_unit = fields.Str(allow_none=True, default='cm')
    length_value = fields.Decimal(allow_none=True, default=0)
    height_unit = fields.Str(allow_none=True, default='cm')
    height_value = fields.Decimal(allow_none=True, default=0)
