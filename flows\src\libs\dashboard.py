import os

from integrations.channels.connection_types import ConnectionStatus
from integrations.common.event import EventSource, FetchEventStatus
from models.integration.connection import ConnectionModel
from models.integration.fetch_event import FetchEventModel
from models.integration.sync_record import SyncRecordStatus, SyncRecordModel

RESOURCE_SERVICE = os.getenv('RESOURCE_SERVICE')


def connection_execute_query(company_id):
    query = {
        "size": 0,
        "query": {
            "bool": {
                "must": [
                    {
                        "match": {
                            "company_id.keyword": company_id
                        }
                    },
                ]
            }
        },
        "aggs": {
            "num_connections": {
                "value_count": {
                    "field": "id.keyword"
                }
            },
            "status_counts": {
                "terms": {
                    "field": "status.keyword"
                }
            },
        }
    }
    return ConnectionModel.report(query, service=RESOURCE_SERVICE)


def generate_connection_report(result):
    num_connections = result['aggregations']['num_connections']['value']
    status_counts = {bucket['key']: bucket['doc_count']
                     for bucket in result['aggregations']['status_counts']['buckets']}

    report = {
        "num_connections": num_connections,
        "num_active": status_counts.get(ConnectionStatus.ACTIVE.value, 0),
        "num_inactive": status_counts.get(ConnectionStatus.INACTIVE.value, 0),
        "num_pending": status_counts.get(ConnectionStatus.PENDING.value, 0),
    }
    return report


# Function to execute Elasticsearch query
def fetch_event_execute_query(company_id, start_date, end_date):
    query = {
        "size": 0,
        "query": {
            "bool": {
                "must": [
                    {
                        "match": {
                            "company_id.keyword": company_id
                        }
                    },
                    {
                        "range": {
                            "created_at": {
                                "gte": f"{start_date}T00:00:00Z",
                                "lt": f"{end_date}T00:00:00Z"
                            }
                        }
                    }
                ]
            }
        },
        "aggs": {
            "num_fetch_events": {
                "value_count": {
                    "field": "id.keyword"
                }
            },
            "status_counts": {
                "terms": {
                    "field": "status.keyword"
                }
            },
            "event_source_counts": {
                "terms": {
                    "field": "event_source.keyword"
                }
            },
        }
    }
    return FetchEventModel.report(query, service=RESOURCE_SERVICE)


# Function to print report
def generate_fetch_event_report(period, result, comparison_result=None):
    num_fetch_events = result['aggregations']['num_fetch_events']['value']
    status_counts = {bucket['key']: bucket['doc_count']
                     for bucket in result['aggregations']['status_counts']['buckets']}
    event_source_counts = {bucket['key']: bucket['doc_count']
                           for bucket in result['aggregations']['event_source_counts']['buckets']}

    report = {
        "period": period.capitalize(),
        "num_fetch_events": num_fetch_events,
        "status_counts": {
            "num_pending": status_counts.get(FetchEventStatus.PENDING.value, 0),
            "num_processing": status_counts.get(FetchEventStatus.PROCESSING.value, 0),
            "num_completed": status_counts.get(FetchEventStatus.COMPLETED.value, 0),
            "num_failed": status_counts.get(FetchEventStatus.FAILED.value, 0),
        },
        "event_source_counts": {
            "num_webhook": event_source_counts.get(EventSource.webhook.value, 0),
            "num_scheduler": event_source_counts.get(EventSource.scheduler.value, 0),
            "num_sync_record_api": event_source_counts.get(EventSource.sync_record_api.value, 0),
            "num_sync_filter_api": event_source_counts.get(EventSource.sync_filter_api.value, 0),
        }
    }

    if comparison_result:
        comparison_num_fetch_events = comparison_result['aggregations']['num_fetch_events']['value']
        comparison_status_counts = {bucket['key']: bucket['doc_count']
                                    for bucket in comparison_result['aggregations']['status_counts']['buckets']}
        comparison_event_source_counts = {bucket['key']: bucket['doc_count']
                                          for bucket in
                                          comparison_result['aggregations']['event_source_counts']['buckets']}
        comparison_report = {
            "period": "Comparison period",
            "num_fetch_events": comparison_num_fetch_events,
            "status_counts": {
                "num_pending": comparison_status_counts.get(FetchEventStatus.PENDING.value, 0),
                "num_processing": comparison_status_counts.get(FetchEventStatus.PROCESSING.value, 0),
                "num_completed": comparison_status_counts.get(FetchEventStatus.COMPLETED.value, 0),
                "num_failed": comparison_status_counts.get(FetchEventStatus.FAILED.value, 0),
            },
            "event_source_counts": {
                "num_webhook": comparison_event_source_counts.get(EventSource.webhook.value, 0),
                "num_scheduler": comparison_event_source_counts.get(EventSource.scheduler.value, 0),
                "num_sync_record_api": comparison_event_source_counts.get(EventSource.sync_record_api.value, 0),
                "num_sync_filter_api": comparison_event_source_counts.get(EventSource.sync_filter_api.value, 0),
            }
        }
        report["comparison_report"] = comparison_report
    return report


def sync_record_execute_query(company_id, start_date, end_date):
    query = {
        "size": 0,
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "company_id.keyword": company_id,
                        }
                    },
                    {
                        "range": {
                            "updated_at": {
                                "gte": f"{start_date}T00:00:00Z",
                                "lt": f"{end_date}T00:00:00Z"
                            }
                        }
                    }
                ]
            }
        },
        "aggs": {
            "by_connection": {
                "terms": {
                    "field": "connection_id.keyword",
                    "size": 1000
                },
                "aggs": {
                    "error_count": {
                        "filter": {
                            "term": {
                                "status.keyword": SyncRecordStatus.ERROR.value
                            }
                        }
                    },
                    "skipped_count": {
                        "filter": {
                            "term": {
                                "status.keyword": SyncRecordStatus.SKIPPED.value
                            }
                        }
                    },
                    "completed_records": {
                        "filter": {
                            "bool": {
                                "must": [
                                    {
                                        "term": {
                                            "status.keyword": SyncRecordStatus.COMPLETED.value
                                        }
                                    },
                                    {
                                        "term": {
                                            "is_source": False
                                        }
                                    }
                                ]
                            }
                        },
                        "aggs": {
                            "avg_fetch_to_transform_time": {
                                "avg": {
                                    "script": {
                                        "source": "if (doc['transformed_at'].size() > 0 && doc['fetched_at'].size() > 0) { return doc['transformed_at'].value.toInstant().toEpochMilli() - doc['fetched_at'].value.toInstant().toEpochMilli(); }"
                                    }
                                }
                            },
                            "avg_transform_to_publish_time": {
                                "avg": {
                                    "script": {
                                        "source": "if (doc['published_at'].size() > 0 && doc['transformed_at'].size() > 0) { return doc['published_at'].value.toInstant().toEpochMilli() - doc['transformed_at'].value.toInstant().toEpochMilli(); }"
                                    }
                                }
                            },
                            "avg_total_processing_time": {
                                "avg": {
                                    "script": {
                                        "source": "if (doc['published_at'].size() > 0 && doc['fetched_at'].size() > 0) { return doc['published_at'].value.toInstant().toEpochMilli() - doc['fetched_at'].value.toInstant().toEpochMilli(); }"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return SyncRecordModel.report(query, service=RESOURCE_SERVICE)


def get_all_connection_ids(company_id):
    result = ConnectionModel.report({
        "size": 1000,
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "company_id.keyword": company_id,
                        }
                    },
                ]
            }
        }
    }, service=RESOURCE_SERVICE)
    return [item['_source']['id'] for item in result['hits']['hits']]


def generate_sync_record_report(period, result, company_id, comparison_result=None):
    connection_ids = get_all_connection_ids(company_id)
    by_connection = {}
    buckets = result['aggregations']['by_connection']['buckets']
    for connection_id in connection_ids:
        connection = next((bucket for bucket in buckets if bucket['key'] == connection_id), None)
        if connection:
            by_connection[connection_id] = {
                'total': connection['doc_count'],
                'skipped_count': connection['skipped_count']['doc_count'],
                'error_count': connection['error_count']['doc_count'],
                'sync_completed_count': connection['completed_records']['doc_count'],
                'avg_total_processing_time': connection['completed_records']['avg_total_processing_time'],
                'avg_transform_to_publish_time': connection['completed_records']['avg_transform_to_publish_time'],
                'avg_fetch_to_transform_time': connection['completed_records']['avg_fetch_to_transform_time'],
            }
        else:
            by_connection[connection_id] = {
                'total': 0,
                'skipped_count': 0,
                'error_count': 0,
                'sync_completed_count': 0,
                'avg_total_processing_time': None,
                'avg_transform_to_publish_time': None,
                'avg_fetch_to_transform_time': None,
            }
    report = {
        "period": period.capitalize(),
        "by_connection": by_connection,
    }

    if comparison_result:
        comparison_by_connection = {}
        comparison_buckets = comparison_result['aggregations']['by_connection']['buckets']

        for connection_id in connection_ids:
            connection = next((bucket for bucket in comparison_buckets if bucket['key'] == connection_id), None)
            if connection:
                comparison_by_connection[connection_id] = {
                    'total': connection['doc_count'],
                    'skipped_count': connection['skipped_count']['doc_count'],
                    'error_count': connection['error_count']['doc_count'],
                    'sync_completed_count': connection['completed_records']['doc_count'],
                    'avg_total_processing_time': connection['completed_records']['avg_total_processing_time'],
                    'avg_transform_to_publish_time': connection['completed_records']['avg_transform_to_publish_time'],
                    'avg_fetch_to_transform_time': connection['completed_records']['avg_fetch_to_transform_time'],
                }
            else:
                comparison_by_connection[connection_id] = {
                    'total': 0,
                    'skipped_count': 0,
                    'error_count': 0,
                    'sync_completed_count': 0,
                    'avg_total_processing_time': None,
                    'avg_transform_to_publish_time': None,
                    'avg_fetch_to_transform_time': None,
                }
        comparison_report = {
            "period": "Comparison period",
            "by_connection": comparison_by_connection,
        }
        report["comparison_report"] = comparison_report
    return report
