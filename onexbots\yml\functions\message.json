{"create_message": {"POST": [{"name": "Create a new message in conversation", "path_params": {"id": "conversation-id-123"}, "body": {"content": "Hello, how can I help you?", "role": "virtual_staff", "connection_id": "conn-123"}, "response": {"success": true, "message": "Message created successfully", "data": {"id": "message-id-123", "conversation_id": "conversation-id-123", "content": "Hello, how can I help you?", "role": "virtual_staff", "connection_id": "conn-123", "created_at": "2024-01-01T00:00:00Z"}}}, {"name": "Create a user message", "path_params": {"id": "conversation-id-123"}, "body": {"content": "I need help with my order", "role": "user", "user_id": "user-123", "connection_id": "conn-123"}}, {"name": "Create an external user message", "path_params": {"id": "conversation-id-123"}, "body": {"content": "What's the status of my order?", "role": "external_user", "external_user_id": "ext-123", "connection_id": "conn-123"}}]}, "list_messages": {"GET": [{"name": "List messages in conversation", "path_params": {"id": "conversation-id-123"}, "query_params": {"role": "virtual_staff", "created_at_start": "2024-01-01T00:00:00Z", "created_at_end": "2024-12-31T23:59:59Z"}, "response": {"success": true, "message": "Messages retrieved successfully", "data": {"items": [{"id": "message-id-123", "conversation_id": "conversation-id-123", "content": "Hello, how can I help you?", "role": "virtual_staff", "connection_id": "conn-123", "created_at": "2024-01-01T00:00:00Z"}], "total": 1}}}]}, "list_public_messages": {"GET": [{"name": "List messages in conversation", "path_params": {"id": "conversation-id-123"}, "query_params": {"created_at_start": "2024-01-01T00:00:00Z", "created_at_end": "2024-12-31T23:59:59Z"}, "response": {"success": true, "message": "Messages retrieved successfully", "data": {"items": [{"id": "message-id-123", "conversation_id": "conversation-id-123", "content": "Hello, how can I help you?", "role": "virtual_staff", "connection_id": "conn-123", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}], "total": 1}}}]}}