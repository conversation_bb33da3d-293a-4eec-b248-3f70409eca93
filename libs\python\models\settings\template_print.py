from dataclasses import dataclass

from marshmallow import fields
from marshmallow_enum import EnumField

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes
from models.common.settings import PrintTypes, SizeTemplate


@dataclass
class TemplatePrint(BasicAttributes):
    id: str = None
    template_type: str = None
    location_id: str = None
    size: str = None
    template: str = None
    company_id: str = None


class TemplatePrintSchema(BasicAttributesSchema):
    id = fields.UUID(required=True)
    template_type = EnumField(PrintTypes, allow_none=True, default=PrintTypes.ORDER)
    location_id = fields.Str(required=True)
    size = EnumField(SizeTemplate, allow_none=True, default=SizeTemplate.A4)
    template = fields.Str(required=True)
    company_id = fields.UUID(required=True)


class TemplatePrintModel(BasicModel):
    key__id = 'company_id'
    key__ranges = ['id']
    table_name = 'printTemplate'
    attributes: TemplatePrint
    attributes_schema = TemplatePrintSchema
    attributes_class = TemplatePrint

    query_mapping = {
        'query': ['name'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }

    @classmethod
    def get_all_templates(cls, company_id, limit):
        try:
            return cls.list({'company_id': company_id}, limit=limit).get('Items', [])
        except IndexError:
            return []
