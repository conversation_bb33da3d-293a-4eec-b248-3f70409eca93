from typing import Dict, Type, Any

from integrations.channels.action_types import ActionGroup, ActionKind, ScheduleType, ACTION_GROUP_MAPPING
from integrations.channels.channel_library import AbstractChannel
from integrations.channels.business_central.business_central_channel import BusinessCentralChannel
from integrations.channels.cin7.cin7_channel import Cin7Channel
from integrations.channels.haravan.haravan_channel import HaravanChannel
from integrations.channels.misa_amis.misa_amis_channel import MisaAmisChannel
from integrations.channels.nhanh.nhanh_channel import NhanhChannel
from integrations.channels.shopee.shopee_channel import ShopeeChannel
from integrations.channels.shopify.shopify_channel import ShopifyChannel
from integrations.channels.shopify_oauth.shopify_oauth_channel import ShopifyOAuthChannel
from integrations.channels.squarespace.squarespace_channel import SquarespaceChannel
from integrations.channels.zalo.zalo_channel import ZaloChannel
from integrations.channels.tiktokshop.tiktokshop_channel import TiktokShopChannel
from integrations.channels.zalo_oa.zalo_oa_channel import ZaloOAChannel
from integrations.channels.facebook_oauth.facebook_oauth_channel import FacebookOAuthChannel


class ChannelFactory:
    _channels: Dict[str, Type[AbstractChannel]] = {}

    @classmethod
    def register_channel(cls, channel: Type[AbstractChannel]):
        cls._channels[channel.channel_name] = channel

    @classmethod
    def get_channel(cls, channel_name: str) -> Type[AbstractChannel]:
        return cls._channels.get(channel_name)

    @classmethod
    def create_default_connection_settings(cls, channel_name: str) -> Dict[str, Any]:
        channel_class = cls.get_channel(channel_name)
        if not channel_class:
            raise ValueError(f"Unsupported channel: {channel_name}")

        settings = {
            "channel_name": channel_name,
            "action_groups": {}
        }

        supported_actions = channel_class.get_all_supported_actions()

        for action, action_info in supported_actions.items():
            group = ACTION_GROUP_MAPPING.get(action)
            if not group:
                continue

            group_value = group.value
            if group_value not in settings["action_groups"]:
                settings["action_groups"][group_value] = {
                    "group_name": group_value,
                    "fetch_actions": {},
                    "publish_actions": {}
                }

            group_settings = settings["action_groups"][group_value]

            if action_info["kind"] == ActionKind.FETCH:
                group_settings["fetch_actions"][action.value] = {
                    "enabled": False,
                    "response_mapping": {},
                    "rate_limit": 60,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "custom_settings": action_info["default_settings"],
                    "schedule": {
                        "type": ScheduleType.INTERVAL.value,
                        "value": "3600"  # Default to hourly
                    },
                    "status": {
                        "last_run": None,
                        "next_run": None
                    }
                }
            elif action_info["kind"] == ActionKind.PUBLISH:
                group_settings["publish_actions"][action.value] = {
                    "enabled": False,
                    "payload_template": {},
                    "rate_limit": 60,
                    "retry_settings": {
                        "max_retries": 3,
                        "retry_delay": 5,
                        "retry_backoff": 2
                    },
                    "custom_settings": action_info["default_settings"]
                }

        settings["webhook_settings"] = {
            topic: {
                'enable': True,
                # 'value': value
            } for topic, value in channel_class.library_class.AVAILABLE_WEBHOOKS.items()
        }

        return settings


# Register channels
ChannelFactory.register_channel(ShopifyChannel)
ChannelFactory.register_channel(ShopifyOAuthChannel)
ChannelFactory.register_channel(BusinessCentralChannel)
ChannelFactory.register_channel(ShopeeChannel)
ChannelFactory.register_channel(NhanhChannel)
ChannelFactory.register_channel(HaravanChannel)
ChannelFactory.register_channel(ZaloChannel)
ChannelFactory.register_channel(SquarespaceChannel)
ChannelFactory.register_channel(Cin7Channel)
ChannelFactory.register_channel(MisaAmisChannel)
ChannelFactory.register_channel(TiktokShopChannel)
ChannelFactory.register_channel(ZaloOAChannel)
ChannelFactory.register_channel(FacebookOAuthChannel)
