from dataclasses import dataclass
from enum import Enum
from typing import List, Dict, Any

from elasticsearch import NotFoundError
from marshmallow import fields
from marshmallow_enum import En<PERSON><PERSON>ield
from nolicore.adapters.db.model import AttributesSchema, Attributes

from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema
from models.media.image import Image, ImageSchema


class Role(str, Enum):
    CUSTOMER_SUPPORT_AGENT = "CUSTOMER_SUPPORT_AGENT"
    SALES_ASSISTANT = "SALES_ASSISTANT"
    VIRTUAL_PERSONAL_ASSISTANT = "VIRTUAL_PERSONAL_ASSISTANT"
    TECHNICAL_SUPPORT_SPECIALIST = "TECHNICAL_SUPPORT_SPECIALIST"
    HR_RECRUITMENT_ASSISTANT = "HR_RECRUITMENT_ASSISTANT"
    MARKETING_ASSISTANT = "MARKETING_ASSISTANT"
    CONTENT_CREATOR = "CONTENT_CREATOR"
    DATA_ANALYST = "DATA_ANALYST"
    EDUCATIONAL_TUTOR = "EDUCATIONAL_TUTOR"
    SCHEDULING_ASSISTANT = "SCHEDULING_ASSISTANT"
    RESEARCH_ASSISTANT = "RESEARCH_ASSISTANT"
    FINANCIAL_ADVISOR = "FINANCIAL_ADVISOR"
    VIRTUAL_TRAVEL_AGENT = "VIRTUAL_TRAVEL_AGENT"
    LEGAL_ASSISTANT = "LEGAL_ASSISTANT"
    CODE_REVIEW_SPECIALIST = "CODE_REVIEW_SPECIALIST"
    HEALTHCARE_COACH = "HEALTHCARE_COACH"
    MENTAL_HEALTH_COMPANION = "MENTAL_HEALTH_COMPANION"
    VIRTUAL_EVENT_PLANNER = "VIRTUAL_EVENT_PLANNER"
    REAL_ESTATE_ADVISOR = "REAL_ESTATE_ADVISOR"
    SECURITY_ANALYST = "SECURITY_ANALYST"
    UX_UI_DESIGNER_AGENT = "UX_UI_DESIGNER_AGENT"
    PROJECT_MANAGEMENT_ASSISTANT = "PROJECT_MANAGEMENT_ASSISTANT"
    VIRTUAL_STAFF = "VIRTUAL_STAFF"


class ResponseLength(str, Enum):
    SHORT = "SHORT",
    MEDIUM = "MEDIUM",
    LONG = "LONG"


@dataclass
class PersonalTrait(Attributes):
    formality: int = 50
    detailed: int = 50
    creativity: int = 50


class PersonalTraitSchema(AttributesSchema):
    formality = fields.Int(required=False, default=50)
    detailed = fields.Int(required=False, default=50)
    creativity = fields.Int(required=False, default=50)


@dataclass
class InteractionStyle(Attributes):
    tone: str = None
    language: str = "vi"
    response_length: ResponseLength = None
    personal_trait: PersonalTrait = None
    ethical_constraints: bool = None


class InteractionStyleSchema(AttributesSchema):
    tone = fields.Str(required=False, allow_none=True)
    language = fields.Str(default="vi", required=False, allow_none=True)
    response_length = EnumField(
        ResponseLength, required=False, allow_none=True
    )
    personal_trait = fields.Nested(
        PersonalTraitSchema(PersonalTrait), required=False
    )
    ethical_constraints = fields.Bool(required=False, allow_none=True)


@dataclass
class LLMModel(Attributes):
    provider: str = None
    model_name: str = None
    custom_url: str = None


class LLMModelSchema(AttributesSchema):
    provider = fields.Str(required=True)
    model_name = fields.Str(required=True)
    custom_url = fields.Str(required=False, allow_none=True)


@dataclass
class LLMSettings(Attributes):
    llm_api_key: str = None
    llm_model: LLMModel = None
    default_llm_temperature: float = None
    max_tokens: int = None
    max_llm_call_retries: int = None
    other_kwargs: Dict[str, Any] = None


class LLMSettingsSchema(AttributesSchema):
    llm_api_key = fields.Str(required=True)
    llm_model = fields.Nested(LLMModelSchema(LLMModel), required=True)
    default_llm_temperature = fields.Float(required=True)
    max_tokens = fields.Int(required=True)
    max_llm_call_retries = fields.Int(required=True)
    other_kwargs = fields.Dict(
        keys=fields.Str(), values=fields.Raw(), required=True
    )


@dataclass
class PersonalitySettings(Attributes):
    tone: str = None
    language: str = "vi"
    personal_trait: PersonalTrait = None
    farewell: str = None
    ethical_constraints: bool = None


class PersonalitySettingsSchema(AttributesSchema):
    tone = fields.Str(required=False, allow_none=True)
    language = fields.Str(default="vi", required=False, allow_none=True)
    personal_trait = fields.Nested(PersonalTraitSchema(PersonalTrait), required=False, allow_none=True)
    farewell = fields.Str(required=False, allow_none=True)
    ethical_constraints = fields.Bool(required=False, allow_none=True)


@dataclass
class KnowledgeBaseSettings(Attributes):
    enabled: bool = True
    knowledge_ids: List[str] = None
    domain_expertise_ids: List[str] = None
    max_context_tokens: int = None
    min_similarity_score: float = None
    context_template: str = None


class KnowledgeBaseSettingsSchema(AttributesSchema):
    enabled = fields.Bool(required=False, default=True)
    knowledge_ids = fields.List(fields.Str(), required=False, allow_none=True)
    domain_expertise_ids = fields.List(fields.Str(), required=False, allow_none=True)
    max_context_tokens = fields.Int(required=False, allow_none=True)
    min_similarity_score = fields.Float(required=False, allow_none=True)
    context_template = fields.Str(required=False, allow_none=True)


@dataclass
class ToolConfig(Attributes):
    enabled: bool = True
    allowed_operations: List[str] = None
    max_expression_length: int = None
    max_number: int = None
    api_key: str = None
    allowed_locations: List[str] = None
    cache_duration: int = None
    max_results: int = None
    safe_search: bool = None


class ToolConfigSchema(AttributesSchema):
    enabled = fields.Bool(required=True, default=True)
    allowed_operations = fields.List(fields.Str(), required=False, allow_none=True)
    max_expression_length = fields.Int(required=False, allow_none=True)
    max_number = fields.Int(required=False, allow_none=True)
    api_key = fields.Str(required=False, allow_none=True)
    allowed_locations = fields.List(fields.Str(), required=False, allow_none=True)
    cache_duration = fields.Int(required=False, allow_none=True)
    max_results = fields.Int(required=False, allow_none=True)
    safe_search = fields.Bool(required=False, allow_none=True)


@dataclass
class ToolsSettings(Attributes):
    enabled: List[str] = None
    config: Dict[str, ToolConfig] = None


class ToolsSettingsSchema(AttributesSchema):
    enabled = fields.List(fields.Str(), required=False, allow_none=True)
    config = fields.Dict(
        keys=fields.Str(),
        values=fields.Nested(ToolConfigSchema(ToolConfig)),
        required=False,
        allow_none=True
    )


@dataclass
class AdapterConfig(Attributes):
    enabled: bool = True
    action: str = None
    patterns: List[str] = None
    max_messages: int = None
    time_window: int = None


class AdapterConfigSchema(AttributesSchema):
    enabled = fields.Bool(required=True, default=True)
    action = fields.Str(required=False, allow_none=True)
    patterns = fields.List(fields.Str(), required=False, allow_none=True)
    max_messages = fields.Int(required=False, allow_none=True)
    time_window = fields.Int(required=False, allow_none=True)


@dataclass
class MCPSettings(Attributes):
    enabled: bool = True
    server_url: str = None
    enabled_adapters: List[str] = None
    adapters_config: Dict[str, AdapterConfig] = None


class MCPSettingsSchema(AttributesSchema):
    enabled = fields.Bool(required=False, default=True)
    server_url = fields.Str(required=False, allow_none=True)
    enabled_adapters = fields.List(fields.Str(), required=False, allow_none=True)
    adapters_config = fields.Dict(
        keys=fields.Str(),
        values=fields.Nested(AdapterConfigSchema(AdapterConfig)),
        required=False,
        allow_none=True
    )


@dataclass
class ConversationSettings(Attributes):
    max_history_length: int = None
    context_window: int = None
    response_timeout: int = None
    fallback_message: str = None


class ConversationSettingsSchema(AttributesSchema):
    max_history_length = fields.Int(required=False, allow_none=True)
    context_window = fields.Int(required=False, allow_none=True)
    response_timeout = fields.Int(required=False, allow_none=True)
    fallback_message = fields.Str(required=False, allow_none=True)


@dataclass
class Configuration(Attributes):
    instruction: str = None
    llm_settings: LLMSettings = None
    personality: PersonalitySettings = None
    knowledge_base: KnowledgeBaseSettings = None
    tools: ToolsSettings = None
    mcp: MCPSettings = None
    conversation: ConversationSettings = None


class ConfigurationSchema(AttributesSchema):
    instruction = fields.Str(allow_none=True, required=False)
    llm_settings = fields.Nested(LLMSettingsSchema(LLMSettings), required=True)
    personality = fields.Nested(PersonalitySettingsSchema(PersonalitySettings), required=False, allow_none=True)
    knowledge_base = fields.Nested(KnowledgeBaseSettingsSchema(KnowledgeBaseSettings), required=False, allow_none=True)
    tools = fields.Nested(ToolsSettingsSchema(ToolsSettings), required=False, allow_none=True)
    mcp = fields.Nested(MCPSettingsSchema(MCPSettings), required=False, allow_none=True)
    conversation = fields.Nested(ConversationSettingsSchema(ConversationSettings), required=False, allow_none=True)


@dataclass
class VirtualStaffAttributes(BasicAttributes):
    name: str = None
    department_id: str = None
    image: Image = None
    role: Role = None
    skills: List[str] = None
    greeting: str = None
    domain_expertise: List[str] = None
    configuration: Configuration = None
    color: str = None


class VirtualStaffAttributesSchema(BasicAttributesSchema):
    name = fields.Str(required=True)
    department_id = fields.Str(required=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    role = EnumField(Role, required=True)
    skills = fields.List(fields.Str(), required=False, allow_none=True)
    greeting = fields.Str(allow_none=True)
    domain_expertise = fields.List(fields.Str(), required=False, allow_none=True)
    configuration = fields.Nested(ConfigurationSchema(Configuration), required=True)
    color = fields.Str(required=False, allow_none=True)


class VirtualStaffModel(BasicModel):
    table_name = 'virtualStaff'
    attributes: VirtualStaffAttributes
    attributes_schema = VirtualStaffAttributesSchema
    attributes_class = VirtualStaffAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'department_id': 'query',
            'role': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'image': 'image',
            'domain_expertise': 'domain_expertise',
        }
    }

    @classmethod
    def count_staff(cls, params):
        try:
            count_params = {
                "query": {
                    "bool": {
                        "filter": {
                            "term":
                                params
                        }
                    }
                }
            }
            file_count = cls.count(count_params, service=RESOURCE_SERVICE).body['count']
            return file_count
        except NotFoundError:
            return 0


class VirtualStaffDepartmentIdIndex(VirtualStaffModel):
    key__ranges = ['department_id']
    index_name = 'virtualStaffByDepartmentIndex'
