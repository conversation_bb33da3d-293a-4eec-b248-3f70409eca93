indexDynamoTable:
  handler: src/events/index_dynamo_table.handle
  timeout: 60
  memorySize: 192
  events:
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - order
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - returnOrder
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - product
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - imports
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - importRecord
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - customer
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - supplier
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - package
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - purchaseOrder
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - variant
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - unit
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - priceGroup
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - brand
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - category
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - location
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - filter
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - account
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - transaction
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - transactionVoucher
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - paymentMethod
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - saleChannel
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - store
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - shippingProvider
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - discount
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - wishlist
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - history
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - voucher
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - voucherCode
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - usedVoucher
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - customerGroup
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - role
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - permission
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - blog
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - blogCategory
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - comment
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - province
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - district
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - ward
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - posSocket
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - screenSocket
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - shift
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - shiftPayLineItem
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - terminal
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - draftOrder
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - stockAdjustment
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - stockRelocate
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - inventoryItem
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - inventoryTransaction
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - version
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - loyaltyProgram
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - rewardProgram
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - connection
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - notification
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - syncRecord
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - fetchEvent
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - destinationData
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - virtualStaff
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - department
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - task
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - taskExecution
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - conversation
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - message
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - knowledge
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - feedback
            - StreamArn

inventory_report:
  handler: src/events/inventory_report.handle
  timeout: 300
  memorySize: 128
  events:
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - order
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - purchaseOrder
            - StreamArn

save_history:
  handler: src/events/save_history.handle
  timeout: 30
  memorySize: 128
  events:
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - printTemplate
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - order
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - package
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - mappingAttribute
            - StreamArn

cache_version:
  handler: src/events/cache_version.handle
  timeout: 30
  memorySize: 128
  events:
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - printTemplate
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - location
            - StreamArn
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - settings
            - StreamArn

send_notification:
  handler: src/events/send_notification.handle
  timeout: 30
  memorySize: 128
  events:
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - order
            - StreamArn

retriever_dynamo_table:
  handler: src/events/retriever_dynamo_table.handle
  timeout: 30
  memorySize: 128
  events:
    - stream:
        type: dynamodb
        arn:
          Fn::GetAtt:
            - product
            - StreamArn