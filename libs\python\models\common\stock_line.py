from dataclasses import dataclass
from decimal import Decimal

from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import AttributesSchema, Attributes

from models.common.status import StockAdjustmentReason


@dataclass
class StockLineItem(Attributes):
    id: str
    quantity: Decimal
    sku: str
    unit_price: Decimal = 0
    received_quantity: Decimal = 0
    return_quantity: Decimal = 0
    product_id: str = None
    variant_id: str = None
    variant_name: str = None
    image_url: str = None
    name: str = None
    reason: StockAdjustmentReason = None
    note: str = None


class StockLineItemSchema(AttributesSchema):
    id = fields.UUID(required=True)
    quantity = fields.Decimal(required=True)
    sku = fields.Str(required=True)
    unit_price = fields.Decimal(allow_none=True)
    received_quantity = fields.Decimal(allow_none=True)
    return_quantity = fields.Decimal(allow_none=True)
    product_id = fields.UUID(allow_none=True)
    variant_id = fields.UUID(allow_none=True)
    variant_name = fields.Str(allow_none=True)
    image_url = fields.Url(allow_none=True)
    name = fields.Str(allow_none=True)
    reason = EnumField(StockAdjustmentReason, allow_none=True)
    note = fields.Str(allow_none=True)

