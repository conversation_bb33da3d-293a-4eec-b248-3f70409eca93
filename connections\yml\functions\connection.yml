channels:
  handler: src.apis.connection.get_connection_types
  events:
    - httpApi:
        path: /channels
        method: get

get_channel:
  handler: src.apis.connection.get_connection_type
  events:
    - httpApi:
        path: /channels/{channel_name}
        method: get

connection_setup_fields:
  handler: src.apis.connection.get_connection_setup_fields
  events:
    - httpApi:
        path: /setup_fields/{connection_type}
        method: get
        authorizer:
          name: optiAuthorizer

setup_default_connection:
  handler: src.apis.connection.setup_default_connection
  events:
    - httpApi:
        path: /setup_default/{connection_type}
        method: post
        authorizer:
          name: optiAuthorizer

setup_connection:
  handler: src.apis.connection.setup_connection
  events:
    - httpApi:
        path: /setup/{connection_type}
        method: post
        authorizer:
          name: optiAuthorizer

oauth2_callback:
  handler: src.apis.connection.oauth2_callback
  events:
    - httpApi:
        path: /oauth2_callback
        method: get

oauth2_callback_post:
  handler: src.apis.connection.oauth2_callback_post
  events:
    - httpApi:
        path: /oauth2_callback
        method: post

oauth2_callback_path:
  handler: src.apis.connection.oauth2_callback_path
  events:
    - httpApi:
        path: /oauth2_callback/{state}
        method: get

connection_by_id:
  handler: src.apis.connection.get_connection
  events:
    - httpApi:
        path: /connection/{connection_id}
        method: get
        authorizer:
          name: optiAuthorizer

update_connection_settings:
  handler: src.apis.connection.update_connection_settings
  events:
    - httpApi:
        path: /connection/{connection_id}/settings
        method: put
        authorizer:
          name: optiAuthorizer

connections_by_company:
  handler: src.apis.connection.get_connections_by_company
  events:
    - httpApi:
        path: /list
        method: get
        authorizer:
          name: optiAuthorizer

get_default_connections:
  handler: src.apis.connection.get_default_connections
  events:
    - httpApi:
        path: /connections/default/{connection_type}
        method: get
        authorizer:
          name: optiAuthorizer

authorization_link:
  handler: src.apis.connection.get_authorization_link
  events:
    - httpApi:
        path: /connection/{connection_id}/authorization_link
        method: get
        authorizer:
          name: optiAuthorizer

test_connection:
  handler: src.apis.connection.test_connection
  events:
    - httpApi:
        path: /connection/{connection_id}/test
        method: get
        authorizer:
          name: optiAuthorizer

handle_dynamic_settings:
  handler: src.apis.setting.handle_dynamic_settings
  events:
    - httpApi:
        path: /connections/{connection_id}/dynamic-settings
        method: put
        authorizer:
          name: optiAuthorizer

handle_location_mapping:
  handler: src.apis.setting.handle_location_mapping
  events:
    - httpApi:
        path: /connections/{connection_id}/location-mapping
        method: put
        authorizer:
          name: optiAuthorizer

switch_connection_status:
  handler: src.apis.connection.switch_connection_status
  events:
    - httpApi:
        path: /connections/{connection_id}/switch_connection_status
        method: post
        authorizer:
          name: optiAuthorizer
