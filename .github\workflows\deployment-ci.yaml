name: 🚀 Server Deployment

on:
  push:
    branches:
      - dev
    paths:
      - "connections/**"
      - "flows/**"
      - "onexbots/**"
      - "resources/**"
      - "libs/**"
  workflow_dispatch:

jobs:
  filter-job:
    runs-on: ubuntu-latest
    outputs:
      connections: ${{ steps.set-flags.outputs.connections }}
      flows: ${{ steps.set-flags.outputs.flows }}
      onexbots: ${{ steps.set-flags.outputs.onexbots }}
      resources: ${{ steps.set-flags.outputs.resources }}
      libs: ${{ steps.set-flags.outputs.libs }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Run paths-filter
        id: filter
        uses: dorny/paths-filter@v2
        with:
          base: dev
          filters: |
            connections:
              - "connections/**"
            flows:
              - "flows/**"
            onexbots:
              - "onexbots/**"
            resources:
              - "resources/**"
            libs:
              - "libs/**"

      - name: Set deployment flags
        id: set-flags
        run: |
          if [[ "$LIBS_CHANGED" == 'true' ]]; then
            echo "connections=true" >> $GITHUB_OUTPUT
            echo "flows=true" >> $GITHUB_OUTPUT
            echo "onexbots=true" >> $GITHUB_OUTPUT
            echo "resources=true" >> $GITHUB_OUTPUT
          else
            echo "connections=$CONNECTIONS_CHANGED" >> $GITHUB_OUTPUT
            echo "flows=$FLOWS_CHANGED" >> $GITHUB_OUTPUT
            echo "onexbots=$ONEXBOTS_CHANGED" >> $GITHUB_OUTPUT
            echo "resources=$RESOURCES_CHANGED" >> $GITHUB_OUTPUT
          fi
        env:
          LIBS_CHANGED: ${{ steps.filter.outputs.libs }}
          CONNECTIONS_CHANGED: ${{ steps.filter.outputs.connections }}
          FLOWS_CHANGED: ${{ steps.filter.outputs.flows }}
          ONEXBOTS_CHANGED: ${{ steps.filter.outputs.onexbots }}
          RESOURCES_CHANGED: ${{ steps.filter.outputs.resources }}

# Define shared deployment steps as a reusable workflow step snippet
# Then each job (deploy-connections, etc.) reuses the logic below

  deploy-connections:
    needs: filter-job
    if: needs.filter-job.outputs.connections == 'true'
    uses: ./.github/workflows/deploy-template.yml
    with:
      service: connections
      jira_link: ${{ vars.JIRA_LINK }}
      slack_dev_user_ids: ${{ vars.SLACK_DEV_USER_IDS }}
    secrets:
      SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      DEFAULT_REGION: ${{ secrets.DEFAULT_REGION }}
      SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

  deploy-flows:
    needs: filter-job
    if: needs.filter-job.outputs.flows == 'true'
    uses: ./.github/workflows/deploy-template.yml
    with:
      service: flows
      jira_link: ${{ vars.JIRA_LINK }}
      slack_dev_user_ids: ${{ vars.SLACK_DEV_USER_IDS }}
    secrets:
      SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      DEFAULT_REGION: ${{ secrets.DEFAULT_REGION }}
      SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

  deploy-onexbots:
    needs: filter-job
    if: needs.filter-job.outputs.onexbots == 'true'
    uses: ./.github/workflows/deploy-template.yml
    with:
      service: onexbots
      jira_link: ${{ vars.JIRA_LINK }}
      slack_dev_user_ids: ${{ vars.SLACK_DEV_USER_IDS }}
    secrets:
      SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      DEFAULT_REGION: ${{ secrets.DEFAULT_REGION }}
      SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

  deploy-resources:
    needs: filter-job
    if: needs.filter-job.outputs.resources == 'true'
    uses: ./.github/workflows/deploy-template.yml
    with:
      service: resources
      jira_link: ${{ vars.JIRA_LINK }}
      slack_dev_user_ids: ${{ vars.SLACK_DEV_USER_IDS }}
    secrets:
      SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      DEFAULT_REGION: ${{ secrets.DEFAULT_REGION }}
      SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

