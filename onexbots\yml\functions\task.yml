create_task:
  handler: src.apis.task.create_task
  events:
    - httpApi:
        path: /tasks
        method: post
        authorizer:
          name: optiAuth<PERSON>zer

get_task:
  handler: src.apis.task.get_task
  events:
    - httpApi:
        path: /tasks/{id}
        method: get
        authorizer:
          name: optiAuthorizer

list_tasks:
  handler: src.apis.task.list_tasks
  events:
    - httpApi:
        path: /tasks
        method: get
        authorizer:
          name: optiAuthorizer

update_task:
  handler: src.apis.task.update_task
  events:
    - httpApi:
        path: /tasks/{id}
        method: put
        authorizer:
          name: opti<PERSON><PERSON><PERSON>zer

delete_task:
  handler: src.apis.task.delete_task
  events:
    - httpApi:
        path: /tasks/{id}
        method: delete
        authorizer:
          name: opti<PERSON><PERSON><PERSON>zer

