from typing import Dict, Any, List
from integrations.common.transformers.base import SourceTransformer
from integrations.channels.action_types import ActionGroup


class TikTokShopInventoryTransformer(SourceTransformer):
    channel_name = 'tiktok_shop'
    object_type = 'inventory'

    def _map_object_type_to_action_type(self) -> str:
        return 'get_inventory'

    def _transform(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        try:
            self.validate_input(data)
            location_mapping = self.dynamic_settings.get('location_mapping', {}).get(
                'mapping', {})
            staff_id = self.dynamic_settings.get('staff_mapping', {}).get("staff_id")
            inventory_data = {
                'sku': data['seller_sku'],
                'inventories': [],
            }
            if staff_id:
                inventory_data['staff'] = {
                    'id': staff_id
                }
            if 'inventory' in data:
                for inventory in data['inventory']:
                    inventory = {
                        'location_id': str(location_mapping.get(inventory['warehouse_id'], inventory['warehouse_id'])),
                        'available': int(inventory['quantity'])
                    }
                    inventory_data['inventories'].append(inventory)

            self.validate_output(inventory_data)
            return inventory_data
        except Exception as e:
            raise ValueError(f"Error transforming TikTokShop inventory data: {str(e)}")

    def validate_input(self, data: Dict[str, Any]):
        # Implement input validation logic using self.input_schema
        pass

    def validate_output(self, data: List[Dict[str, Any]]):
        # Implement output validation logic using self.output_schema
        pass

    @property
    def input_schema(self):
        return {
            'type': 'object',
            'properties':  {
                'type': 'object',
                'properties': {
                    'id': {'type': 'string'},
                    'seller_sku': {'type': 'string'},
                    'price': {
                        'type': 'object',
                        'properties': {
                            'currency': {'type': 'string'},
                            'sale_price': {'type': 'string'},
                            'tax_exclusive_price': {'type': 'string'}
                        }
                    },
                    'inventory': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'warehouse_id': {'type': 'string'},
                                'quantity': {'type': 'integer'}
                            }
                        }
                    },
                    'sales_attributes': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'id': {'type': 'string'},
                                'name': {'type': 'string'},
                                'value_id': {'type': 'string'},
                                'value_name': {'type': 'string'},
                                'sku_img': {
                                    'type': 'object',
                                    'properties': {
                                        'uri': {'type': 'string'},
                                        'urls': {'type': 'array', 'items': {'type': 'string'}}
                                    }
                                }
                            }
                        }
                    },
                    'identifier_code': {
                        'type': 'object',
                        'properties': {
                            'code': {'type': 'string'},
                            'type': {'type': 'string'}
                        }
                    },
                    'external_sku_id': {'type': 'string'},
                    'list_price': {
                        'type': 'object',
                        'properties': {
                            'amount': {'type': 'string'},
                            'currency': {'type': 'string'}
                        }
                    }
                }
            }
        }

    @property
    def output_schema(self):
        return {
            'type': 'array',
            # 'items': InventoryAttributesSchema().dump(InventoryAttributesSchema())
        }

    @property
    def action_group(self) -> ActionGroup:
        return ActionGroup.inventory
