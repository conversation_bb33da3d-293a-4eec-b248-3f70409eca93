create_mapping_channel_bot:
  handler: src.apis.mapping_channel_bot.create_mapping_channel_bot
  events:
    - httpApi:
        path: /mapping/channel/bot/{bot_id}/{connection_id}/{channel_id}
        method: POST
        authorizer:
          name: optiAuthorizer

delete_mapping_channel_bot:
  handler: src.apis.mapping_channel_bot.delete_mapping_channel_bot
  events:
    - httpApi:
        path: /mapping/channel/bot/{connection_id}/{channel_id}
        method: DELETE
        authorizer:
          name: optiAuthorizer

get_pages:
  handler: src.apis.mapping_channel_bot.get_pages
  events:
    - httpApi:
        path: /mapping/channel/bot/{bot_id}/{connection_id}/pages
        method: get
        authorizer:
          name: optiAuthorizer
