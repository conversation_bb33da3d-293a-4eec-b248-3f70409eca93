from dataclasses import dataclass
from decimal import Decimal
from typing import List

from marshmallow import fields, pre_load
from marshmallow_enum import En<PERSON><PERSON><PERSON>
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.common import get_default_source
from models.actor.customer import Customer, CustomerSchema
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.address import Address, AddressSchema
from models.common.default_obj import DefaultObject, DefaultObjectSchema

from models.common.order_line import OrderLineItem, OrderLineItemSchema
from models.common.source_object import SourceObjectSchema, SourceObject
from models.common.status import PaymentHistory, PaymentStatus, ReturnReason, ReturnStatus, \
    PaymentHistorySchema, ReturnType
from models.finance.transaction import Transaction, TransactionSchema


@dataclass
class Return(Attributes):
    order_id: str = None
    external_id: str = None
    customer: Customer = None
    return_order_line_items: List[OrderLineItem] = None
    total: Decimal = None
    billing_address: Address = None
    shipping_address: Address = None
    total_exchanged_amount: Decimal = None
    exchanged_order_id: str = None
    return_order_number: str = None
    order_number: str = None
    exchanged_order_number: str = None
    payments: List[Transaction] = None
    payment_histories: List[PaymentHistory] = None
    note: str = None
    reference: str = None
    tags: str = None
    location: DefaultObject = None
    staff: DefaultObject = None
    status: ReturnStatus = ReturnStatus.NOT_RECEIVED
    reason: ReturnReason = None
    payment_status: PaymentStatus = PaymentStatus.UNPAID
    returned_at: str = None
    cancelled_at: str = None
    source: SourceObject = None
    type: ReturnType = ReturnType.STANDARD


class ReturnSchema(AttributesSchema):
    order_id = fields.Str(allow_none=True)
    external_id = fields.Str(allow_none=True)
    billing_address = fields.Nested(AddressSchema(Address), allow_none=True)
    shipping_address = fields.Nested(AddressSchema(Address), allow_none=True)
    customer = fields.Nested(CustomerSchema(Customer), required=True)
    return_order_line_items = fields.List(fields.Nested(OrderLineItemSchema(OrderLineItem)), required=True)
    total = fields.Decimal(required=True)
    total_exchanged_amount = fields.Decimal(allow_none=True)
    return_order_number = fields.Str(allow_none=True)
    order_number = fields.Str(allow_none=True)
    exchanged_order_number = fields.Str(allow_none=True)
    exchanged_order_id = fields.Str(allow_none=True)
    payments = fields.List(fields.Nested(TransactionSchema(Transaction)), allow_none=True)
    payment_histories = fields.List(fields.Nested(PaymentHistorySchema(PaymentHistory)), allow_none=True)
    note = fields.Str(allow_none=True)
    reference = fields.Str(allow_none=True)
    tags = fields.Str(allow_none=True)
    location = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    staff = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    status = EnumField(ReturnStatus, allow_none=True, default=ReturnStatus.NOT_RECEIVED)
    reason = EnumField(ReturnReason, allow_none=True, default=None)
    payment_status = EnumField(PaymentStatus, allow_none=True, default=PaymentStatus.UNPAID)
    returned_at = fields.DateTime(allow_none=True)
    cancelled_at = fields.DateTime(allow_none=True)
    source = fields.Nested(SourceObjectSchema(SourceObject), allow_none=True)
    type = EnumField(ReturnType, allow_none=True, default=ReturnType.STANDARD)


@dataclass
class ReturnAttributes(Return, BasicAttributes):
    sync_record_id: str = None


class ReturnAttributesSchema(ReturnSchema, BasicAttributesSchema):
    sync_record_id = fields.Str(allow_none=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['source'] = data['source'] if 'source' in data else get_default_source()
        return data


class ReturnModel(BasicModel):
    table_name = 'returnOrder'
    attributes: ReturnAttributes
    attributes_schema = ReturnAttributesSchema
    attributes_class = ReturnAttributes

    query_mapping = {
        'query': ['id', 'customer.phone', 'customer.email', 'customer.first_name', 'customer.last_name',
                  'return_order_line_items.sku', 'order_id', 'exchanged_order_id', 'return_order_number',
                  'order_number', 'exchanged_order_number', 'external_id'],
        'filter': {
            'id': 'query',
            'status': 'query',
            'reason': 'query',
            'payment_status': 'query',
            'tags': 'tags',
            'total': 'query',
            'total_exchange_amount': 'query',
            'location.id': 'query',
            'staff.id': 'query',
            'user.id': 'query',
            'company_id': 'query',
            'return_order_line_items.id': 'query',
            'return_order_line_items.sku': 'query',
            'returned_at': 'range',
            'cancelled_at': 'range',
            'updated_at': 'range',
            'created_at': 'range',
            'source.id': 'query',
            'source.channel_name': 'query',
            'type': 'query',
        },
        'mapping': {
            'shipping_address': 'shipping_address',
            'billing_address': 'billing_address',
            'location': 'location',
            'staff': 'staff',
            'return_order_line_items': 'return_order_line_items',
            'customer.phone': 'customer.phone',
            'customer.email': 'customer.email',
            'customer.first_name': 'customer.first_name',
            'customer.last_name': 'customer.last_name',
            'user': 'user',
            'source': 'source',
        }
    }


class ReturnExternalIdIndex(ReturnModel):
    key__id = 'external_id'
    index_name = 'returnExternalIdIndex'
