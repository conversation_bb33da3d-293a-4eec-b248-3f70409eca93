from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


@dataclass
class Cancel(Attributes):
    id: str
    name: str


class CancelSchema(AttributesSchema):
    id = fields.UUID(required=True)
    name = fields.Str(required=True)


@dataclass
class CancelAttributes(BasicAttributes, Cancel):
    pass


class CancelAttributesSchema(BasicAttributesSchema, CancelSchema):
    pass


class CancelModel(BasicModel):
    table_name = 'cancel'
    attributes: CancelAttributes
    attributes_schema = CancelAttributesSchema
    attributes_class = CancelAttributes
