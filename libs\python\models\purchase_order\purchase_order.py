from dataclasses import dataclass
from decimal import Decimal
from typing import List

from marshmallow import fields, pre_load
from marshmallow_enum import En<PERSON><PERSON>ield

from helpers.common import get_default_source
from models.actor.supplier import Supplier, SupplierSchema
from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema
from models.common.fee import Fee, FeeSchema
from models.common.order_line import OrderLineItem, OrderLineItemSchema
from models.common.source_object import SourceObject, SourceObjectSchema
from models.common.status import Status, PaymentHistory, PaymentStatus, PaymentHistorySchema, PurchaseOrderType
from models.finance.transaction import TransactionSchema, Transaction
from models.order.order import HistorySchema, History


@dataclass
class PurchaseOrder(BasicAttributes):
    purchase_order_number: str = None
    staff_id: str = None
    location_id: str = None
    supplier: Supplier = None
    order_line_items: List[OrderLineItem] = None
    sub_total: Decimal = None
    total: Decimal = None
    quantity: int = None
    fees: List[Fee] = None
    source: SourceObject = None
    payments: List[Transaction] = None
    histories: List[History] = None
    payment_histories: List[PaymentHistory] = None
    note: str = None
    status: Status = Status.DRAFT
    payment_status: PaymentStatus = PaymentStatus.UNPAID
    purchase_type: PurchaseOrderType = PurchaseOrderType.STANDARD
    discount: Decimal = 0
    external_id: str = None
    tags: str = None


class PurchaseOrderSchema(BasicAttributesSchema):
    purchase_order_number = fields.Str(required=True)
    staff_id = fields.UUID(required=True)
    location_id = fields.UUID(required=True)
    supplier = fields.Nested(SupplierSchema(Supplier), required=True)
    order_line_items = fields.List(fields.Nested(OrderLineItemSchema(OrderLineItem)), required=True)
    sub_total = fields.Decimal(required=True)
    total = fields.Decimal(required=True)
    quantity = fields.Int(required=True)
    source = fields.Nested(SourceObjectSchema(SourceObject), allow_none=True)
    fees = fields.List(fields.Nested(FeeSchema(Fee)), allow_none=True)
    payments = fields.List(fields.Nested(TransactionSchema(Transaction)), allow_none=True)
    histories = fields.List(fields.Nested(HistorySchema(History)), allow_none=True)
    payment_histories = fields.List(fields.Nested(PaymentHistorySchema(PaymentHistory)), allow_none=True)
    note = fields.Str(allow_none=True)
    status = EnumField(Status, allow_none=True, default=Status.DRAFT)
    payment_status = EnumField(PaymentStatus, allow_none=True, default=PaymentStatus.UNPAID)
    purchase_type = EnumField(PurchaseOrderType, allow_none=True, default=PurchaseOrderType.STANDARD)
    discount = fields.Decimal(allow_none=True)
    external_id = fields.Str(allow_none=True)
    tags = fields.Str(allow_none=True)


@dataclass
class PurchaseOrderAttributes(PurchaseOrder):
    sync_record_id: str = None


class PurchaseOrderAttributesSchema(PurchaseOrderSchema):
    sync_record_id = fields.Str(allow_none=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['source'] = data['source'] if data.get('source') is not None else get_default_source()
        return data


class PurchaseOrderModel(BasicModel):
    table_name = 'purchaseOrder'
    attributes: PurchaseOrderAttributes
    attributes_schema = PurchaseOrderAttributesSchema
    attributes_class = PurchaseOrderAttributes

    query_mapping = {
        'query': ['id', 'supplier.phone', 'supplier.email', 'supplier.first_name', 'supplier.last_name',
                  'purchase_order_number'],
        'filter': {
            'location_id': 'query',
            'status': 'query',
            'payment_status': 'query',
            'purchase_type': 'query',
            'tags': 'tags',
            'staff_id': 'query',
            'order_line_items.id': 'query',
            'order_line_items.sku': 'query',
            'company_id': 'query',
            'source.id': 'query',
            'source.channel_name': 'query',
            'id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'supplier.phone': 'supplier.phone',
            'supplier.email': 'supplier.email',
            'supplier.first_name': 'supplier.first_name',
            'supplier.last_name': 'supplier.last_name',
            'order_line_items': 'order_line_items',
            'source': 'source'
        }
    }


class PurchaseOrderExternalIdIndex(PurchaseOrderModel):
    key__id = 'external_id'
    index_name = 'purchaseOrderExternalIdIndex'
