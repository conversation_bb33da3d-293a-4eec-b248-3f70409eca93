channel_webhook_event = {'version': '2.0', 'routeKey': 'POST /webhook/channel/{channel_name}',
                         'rawPath': '/webhook/channel/nhanh', 'rawQueryString': '',
                         'headers': {'accept': '*/*', 'content-length': '356', 'content-type': 'application/json',
                                     'host': 'api-staging.optiwarehouse.com', 'user-agent': 'Nhanh.vn Open API v2.0',
                                     'x-amzn-trace-id': 'Root=1-66ed266e-197d2fa86a1e9625251cefe2',
                                     'x-forwarded-for': '**************', 'x-forwarded-port': '443',
                                     'x-forwarded-proto': 'https'},
                         'requestContext': {'accountId': '************', 'apiId': 'gojzx6aljc',
                                            'domainName': 'api-staging.optiwarehouse.com',
                                            'domainPrefix': 'api-staging',
                                            'http': {'method': 'POST', 'path': '/webhook/channel/nhanh',
                                                     'protocol': 'HTTP/1.1', 'sourceIp': '**************',
                                                     'userAgent': 'Nhanh.vn Open API v2.0'},
                                            'requestId': 'eZLxXikvyQ0EPfw=',
                                            'routeKey': 'POST /webhook/channel/{channel_name}', 'stage': '$default',
                                            'time': '20/Sep/2024:07:38:22 +0000', 'timeEpoch': *************},
                         'pathParameters': {'channel_name': 'nhanh'},
                         'body': '{"event":"orderUpdate","businessId":58863,"data":{"orderId":*********,"shopOrderId":"HRV1635236839","status":"Confirmed","statusDescription":"\\u0110\\u00e3 x\\u00e1c nh\\u1eadn","depotId":78673,"reason":"","deliveryDate":"","trackingUrl":"https:\\/\\/nhanh.vn\\/tracking?businessId=58863&id=*********&key=d0134a75f8de"},"webhooksVerifyToken":"Optiwarehouse2024"}',
                         'isBase64Encoded': False}

channel_webhook_event_1 = {'version': '2.0', 'routeKey': 'POST /webhook/channel/{channel_name}',
                           'rawPath': '/webhook/channel/nhanh', 'rawQueryString': '',
                           'headers': {'accept': '*/*', 'content-length': '357', 'content-type': 'application/json',
                                       'host': 'api.optiwarehouse.com', 'user-agent': 'Nhanh.vn Open API v2.0',
                                       'x-amzn-trace-id': 'Root=1-67efef69-4a1534487dc517315b4439d2',
                                       'x-forwarded-for': '************', 'x-forwarded-port': '443',
                                       'x-forwarded-proto': 'https'},
                           'requestContext': {'accountId': '************', 'apiId': 'vxwk2of2ii',
                                              'domainName': 'api.optiwarehouse.com', 'domainPrefix': 'api',
                                              'http': {'method': 'POST', 'path': '/webhook/channel/nhanh',
                                                       'protocol': 'HTTP/1.1', 'sourceIp': '************',
                                                       'userAgent': 'Nhanh.vn Open API v2.0'},
                                              'requestId': 'IgJYegjySQ0EJqA=',
                                              'routeKey': 'POST /webhook/channel/{channel_name}', 'stage': '$default',
                                              'time': '04/Apr/2025:14:40:41 +0000', 'timeEpoch': *************},
                           'pathParameters': {'channel_name': 'nhanh'},
                           'body': '{"event":"orderUpdate","businessId":58863,"data":{"orderId":*********,"shopOrderId":"250404CJU4SPWC","status":"Confirmed","statusDescription":"\\u0110\\u00e3 x\\u00e1c nh\\u1eadn","depotId":78673,"reason":"","deliveryDate":"","trackingUrl":"https:\\/\\/nhanh.vn\\/tracking?businessId=58863&id=*********&key=bd929d694241"},"webhooksVerifyToken":"Optiwarehouse2024"}',
                           'isBase64Encoded': False}

tiktokshop_webhook_event = {'version': '2.0', 'routeKey': 'POST /webhook/direct/{connection_id}',
                            'rawPath': '/webhook/direct/764b8784-c48f-4378-80ec-f0085c20f13b', 'rawQueryString': '',
                            'headers': {
                                'authorization': 'f267b22409a2875f6cc379f038bdfd1bdc4d46153a69a1d150a071668122c158',
                                'content-length': '235', 'content-type': 'application/json',
                                'host': 'api-staging.optiwarehouse.com',
                                'rpc-persist-ori-path': '/flows/webhook/direct/764b8784-c48f-4378-80ec-f0085c20f13b',
                                'rpc-persist-ori-psm': 'oec.open.gw_event_consumer', 'source-cluster': 'default',
                                'source-idc': 'maliva', 'source-instance': 'fed-dp-e28ab60c3a-66ddbd675c-7gw6d',
                                'source-service': 'oec.open.gw_event_consumer', 'user-agent': 'fasthttp',
                                'x-amzn-trace-id': 'Root=1-682b03c6-610f19e851c41256454f35df',
                                'x-envoy-return-response-flags': '1', 'x-forwarded-for': '**************',
                                'x-forwarded-port': '443', 'x-forwarded-proto': 'https',
                                'x-tt-logid': '021747649477937fdbddc61001901410000000000000019bc9a36'},
                            'requestContext': {'accountId': '************', 'apiId': 'gojzx6aljc',
                                               'domainName': 'api-staging.optiwarehouse.com',
                                               'domainPrefix': 'api-staging', 'http': {'method': 'POST',
                                                                                       'path': '/webhook/direct/764b8784-c48f-4378-80ec-f0085c20f13b',
                                                                                       'protocol': 'HTTP/1.1',
                                                                                       'sourceIp': '**************',
                                                                                       'userAgent': 'fasthttp'},
                                               'requestId': 'Kz2HKhTFyQ0EMng=',
                                               'routeKey': 'POST /webhook/direct/{connection_id}', 'stage': '$default',
                                               'time': '19/May/2025:10:11:19 +0000', 'timeEpoch': 1747649479062},
                            'pathParameters': {'connection_id': '764b8784-c48f-4378-80ec-f0085c20f13b'},
                            'body': '{"type":1,"tts_notification_id":"7506087828351633208","shop_id":"7496132401517595230","timestamp":1747649477,"data":{"is_on_hold_order":false,"order_id":"580193256559773706","order_status":"AWAITING_SHIPMENT","update_time":1747649476}}',
                            'isBase64Encoded': False}

shopify_oauth_webhook_event = {'version': '2.0', 'routeKey': 'POST /webhook/direct/{connection_id}',
                               'rawPath': '/webhook/direct/********-15b0-47bf-90e5-3e47cba24702', 'rawQueryString': '',
                               'headers': {
                                   'authorization': 'c2bc90331f34a125e15244a39e7420c0dd9d525175f29703d96c63208d4a522e',
                                   'content-length': '235', 'content-type': 'application/json',
                                   'host': 'api-staging.optiwarehouse.com',
                                   'rpc-persist-ori-path': '/flows/webhook/direct/********-15b0-47bf-90e5-3e47cba24702',
                                   'rpc-persist-ori-psm': 'oec.open.gw_event_consumer', 'source-cluster': 'default',
                                   'source-idc': 'maliva', 'source-instance': 'fed-dp-e28ab60c3a-66ddbd675c-7gw6d',
                                   'source-service': 'oec.open.gw_event_consumer', 'user-agent': 'fasthttp',
                                   'x-amzn-trace-id': 'Root=1-682eed18-4fa771ec4820fb8d4e8f56b4',
                                   'x-envoy-return-response-flags': '1', 'x-forwarded-for': '**************',
                                   'x-forwarded-port': '443', 'x-forwarded-proto': 'https',
                                   'x-tt-logid': '021747905815443fdbddc6100190141000000000000001954a883'},
                               'requestContext': {'accountId': '************', 'apiId': 'gojzx6aljc',
                                                  'domainName': 'api-staging.optiwarehouse.com',
                                                  'domainPrefix': 'api-staging', 'http': {'method': 'POST',
                                                                                          'path': '/webhook/direct/********-15b0-47bf-90e5-3e47cba24702',
                                                                                          'protocol': 'HTTP/1.1',
                                                                                          'sourceIp': '**************',
                                                                                          'userAgent': 'fasthttp'},
                                                  'requestId': 'K9n75i-jSQ0EMxg=',
                                                  'routeKey': 'POST /webhook/direct/{connection_id}',
                                                  'stage': '$default', 'time': '22/May/2025:09:23:36 +0000',
                                                  'timeEpoch': 1747905816555},
                               'pathParameters': {'connection_id': '********-15b0-47bf-90e5-3e47cba24702'},
                               'body': '{"type":1,"tts_notification_id":"7507192004271507205","shop_id":"7496132401517595230","timestamp":1747905815,"data":{"is_on_hold_order":false,"order_id":"580194051484977162","order_status":"AWAITING_SHIPMENT","update_time":1747905814}}',
                               'isBase64Encoded': False}

subscription_event = {'version': '2.0', 'routeKey': 'POST /webhook/direct/{connection_id}',
                      'rawPath': '/webhook/direct/17c5c97d-0e8e-4396-bb9d-87d67eae372e', 'rawQueryString': '',
                      'headers': {'authorization': 'e5f169d270be261fed2d896d9d53982f0a8b6515f146f2e0714beb1738310d08',
                                  'content-length': '237', 'content-type': 'application/json',
                                  'host': 'api-staging.optiwarehouse.com',
                                  'rpc-persist-ori-path': '/flows/webhook/direct/17c5c97d-0e8e-4396-bb9d-87d67eae372e',
                                  'rpc-persist-ori-psm': 'oec.open.gw_event_consumer', 'source-cluster': 'default',
                                  'source-idc': 'maliva', 'source-instance': 'fed-dp-3b5c001747-65c877c6fc-4lkxr',
                                  'source-service': 'oec.open.gw_event_consumer', 'user-agent': 'fasthttp',
                                  'x-amzn-trace-id': 'Root=1-67ff8f5a-72c86ee817caeefe37a69582',
                                  'x-envoy-return-response-flags': '1', 'x-forwarded-for': '**************',
                                  'x-forwarded-port': '443', 'x-forwarded-proto': 'https',
                                  'x-tt-logid': '021744801625868fdbddc61001900670000000000000024d4f0b2'},
                      'requestContext': {'accountId': '************', 'apiId': 'gojzx6aljc',
                                         'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging',
                                         'http': {'method': 'POST',
                                                  'path': '/webhook/direct/17c5c97d-0e8e-4396-bb9d-87d67eae372e',
                                                  'protocol': 'HTTP/1.1', 'sourceIp': '**************',
                                                  'userAgent': 'fasthttp'}, 'requestId': 'JHNWRgjXSQ0EJEw=',
                                         'routeKey': 'POST /webhook/direct/{connection_id}', 'stage': '$default',
                                         'time': '16/Apr/2025:11:07:06 +0000', 'timeEpoch': *************},
                      'pathParameters': {'connection_id': '17c5c97d-0e8e-4396-bb9d-87d67eae372e'},
                      'body': '{"type":1,"tts_notification_id":"7493865913801934598","shop_id":"7496132401517595230","timestamp":1744801625,"data":{"is_on_hold_order":false,"order_id":"580188905832810506","order_status":"AWAITING_COLLECTION","update_time":1744801624}}',
                      'isBase64Encoded': False}

zalo_oa_event = {'version': '2.0', 'routeKey': 'POST /webhook/channel/{channel_name}',
                 'rawPath': '/webhook/channel/zalo_oa', 'rawQueryString': '',
                 'headers': {'content-length': '233', 'content-type': 'application/json',
                             'host': 'api-dev.onexbots.com', 'user-agent': 'ZaloWebhook',
                             'x-amzn-trace-id': 'Root=1-68469ec1-0e535e007ce649fa1405e32b',
                             'x-forwarded-for': '***********', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https',
                             'x-zevent-server': 'OA OpenAPI',
                             'x-zevent-signature': 'mac=32327e9faf61bcba793757361ac58e025337b666517e9b20af61d782be134b8c'},
                 'requestContext': {'accountId': '************', 'apiId': 'gojzx6aljc',
                                    'domainName': 'api-dev.onexbots.com', 'domainPrefix': 'api-dev',
                                    'http': {'method': 'POST', 'path': '/webhook/channel/zalo_oa',
                                             'protocol': 'HTTP/1.1', 'sourceIp': '***********',
                                             'userAgent': 'ZaloWebhook'}, 'requestId': 'L42-QhrdyQ0EPJw=',
                                    'routeKey': 'POST /webhook/channel/{channel_name}', 'stage': '$default',
                                    'time': '09/Jun/2025:08:43:45 +0000', 'timeEpoch': *************},
                 'pathParameters': {'channel_name': 'zalo_oa'},
                 'body': '{"event_name":"user_send_text","app_id":"1447241861612872028","sender":{"id":"9096806706525930147"},"recipient":{"id":"2421215564594712167"},"message":{"text":"hello 3-43","msg_id":"b9057fee3abdc6e59faa"},"timestamp":"*************"}',
                 'isBase64Encoded': False}

facebook_oauth_test_event = {'version': '2.0', 'routeKey': 'GET /webhook/channel/{channel_name}',
                             'rawPath': '/webhook/channel/facebook_oauth',
                             'rawQueryString': 'hub.mode=subscribe&hub.challenge=**********&hub.verify_token=1',
                             'headers': {'accept': '*/*', 'accept-encoding': 'deflate, gzip', 'content-length': '0',
                                         'host': 'api-dev.onexbots.com',
                                         'user-agent': 'facebookplatform/1.0 (+http://developers.facebook.com)',
                                         'x-amzn-trace-id': 'Root=1-********-6ff0baa734e768c173d60f49',
                                         'x-forwarded-for': '************', 'x-forwarded-port': '443',
                                         'x-forwarded-proto': 'https'},
                             'queryStringParameters': {'hub.challenge': '**********', 'hub.mode': 'subscribe1',
                                                       'hub.verify_token': '1'},
                             'requestContext': {'accountId': '************', 'apiId': 'gojzx6aljc',
                                                'domainName': 'api-dev.onexbots.com', 'domainPrefix': 'api-dev',
                                                'http': {'method': 'GET', 'path': '/webhook/channel/facebook_oauth',
                                                         'protocol': 'HTTP/1.1', 'sourceIp': '************',
                                                         'userAgent': 'facebookplatform/1.0 (+http://developers.facebook.com)'},
                                                'requestId': 'LX8nhiI8yQ0EMkA=',
                                                'routeKey': 'GET /webhook/channel/{channel_name}', 'stage': '$default',
                                                'time': '30/May/2025:09:04:22 +0000', 'timeEpoch': *************},
                             'pathParameters': {'channel_name': 'facebook_oauth'},
                             'body': '{"field": "messages","value": {"sender": {"id": "12334"},"recipient": {"id": "23245"},"timestamp": "1527459824","message": {"mid": "test_message_id","text": "test_message","commands": [{"name": "command123"},{"name": "command456"}]}}}',
                             'isBase64Encoded': False}

facebook_oauth_event = {'version': '2.0', 'routeKey': 'POST /webhook/channel/{channel_name}',
                        'rawPath': '/webhook/channel/facebook_oauth', 'rawQueryString': '',
                        'headers': {'accept': '*/*', 'accept-encoding': 'deflate, gzip', 'content-length': '318',
                                    'content-type': 'application/json', 'facebook-api-version': 'v23.0',
                                    'host': 'api-dev.onexbots.com', 'user-agent': 'facebookexternalua',
                                    'x-amzn-trace-id': 'Root=1-6843df22-529634b95f1922616f713518',
                                    'x-forwarded-for': '**************', 'x-forwarded-port': '443',
                                    'x-forwarded-proto': 'https',
                                    'x-hub-signature': 'sha1=2f17c04cdba85f71f98ac017af5f4fe7a5a27031',
                                    'x-hub-signature-256': 'sha256=2863a977f4c7ec00bfcbe7e2fc8d0a1271ff60c25258fe2fcd7c2f791a67d2ed'},
                        'requestContext': {'accountId': '************', 'apiId': 'gojzx6aljc',
                                           'domainName': 'api-dev.onexbots.com', 'domainPrefix': 'api-dev',
                                           'http': {'method': 'POST', 'path': '/webhook/channel/facebook_oauth',
                                                    'protocol': 'HTTP/1.1', 'sourceIp': '**************',
                                                    'userAgent': 'facebookexternalua'}, 'requestId': 'Lx_NfjS6SQ0EP6w=',
                                           'routeKey': 'POST /webhook/channel/{channel_name}', 'stage': '$default',
                                           'time': '07/Jun/2025:06:41:38 +0000', 'timeEpoch': *************},
                        'pathParameters': {'channel_name': 'facebook_oauth'},
                        'body': '{"object":"page","entry":[{"time":*************,"id":"***************","messaging":[{"sender":{"id":"*****************"},"recipient":{"id":"***************"},"timestamp":1749278497525,"message":{"mid":"m_R7CPtqjMzqI-Raoveym9sytHn6hvnE_-DY6JXVGutCU-rMKzJRepDWc0s1LcSKcy87OO3W0C996xktKPXduY5A","text":"hello 13-41"}}]}]}',
                        'isBase64Encoded': False}
