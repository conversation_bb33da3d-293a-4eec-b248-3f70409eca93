from integrations.common.transformers import registry

print("In test_transformer.py:")
print("Source transformers:", registry.source_transformers)
print("Destination transformers:", registry.destination_transformers)


def transform_data(data, source, object_type, destination):
    source_transformer_class = registry.get_source_transformer(source, object_type)
    destination_transformer_class = registry.get_destination_transformer(destination, object_type)

    if not source_transformer_class or not destination_transformer_class:
        raise ValueError(
            f"No transformer found for source: {source}, destination: {destination}, object_type: {object_type}")

    source_transformer = source_transformer_class()
    destination_transformer = destination_transformer_class()

    master_data = source_transformer.transform(data)
    result = destination_transformer.transform(master_data)

    return result


# Example usage
input_data = {
    "id": "123",
    "first_name": "<PERSON>",
    "last_name": "Do<PERSON>",
    "email": "<EMAIL>",
    "phone": "************",
    "default_address": {
        "address1": "123 Main St",
        "city": "Anytown",
        "province": "CA",
        "zip": "12345",
        "country": "USA"
    }
}

result = transform_data(input_data, "shopify", "customer", "salesforce")
print(result)
