from dataclasses import dataclass
from decimal import Decimal
from typing import List

from marshmallow import fields, pre_load
from marshmallow_enum import <PERSON><PERSON><PERSON><PERSON>
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.common import get_default_source
from helpers.utils import RESOURCE_SERVICE
from models.actor.customer import Customer, CustomerSchema
from models.basic import BasicAttributesSchema, BasicModel, User, UserSchema
from models.common.address import Address, AddressSchema
from models.common.default_obj import DefaultObject, DefaultObjectSchema
from models.common.order_line import OrderLineItem, OrderLineItemSchema
from models.common.source_object import SourceObject, SourceObjectSchema
from models.common.status import History, HistorySchema, Status, PaymentStatus, PaymentHistorySchema, PaymentHistory, \
    update_status, Priority, OrderType
from models.finance.transaction import Transaction, TransactionSchema
from models.loyalty.loyalty_program import LoyaltyProgramSchema, LoyaltyProgram
from models.loyalty.reward_program import RewardProgramSchema, RewardProgram
from models.order.cancel import Cancel, CancelSchema
from models.order.order_return import ReturnAttributes, ReturnAttributesSchema
from models.order.package import Package, PackageSchema, PackageOrderIdIndex
from models.purchase_order.purchase_order import Fee, FeeSchema


@dataclass
class Order(Attributes):
    customer: Customer
    order_line_items: List[OrderLineItem]
    sub_total: Decimal
    total: Decimal
    billing_address: Address = None
    shipping_address: Address = None
    priority: Priority = None
    user: User = None
    cancels: List[Cancel] = None
    packages: List[Package] = None
    returns: List[ReturnAttributes] = None
    exchange_order: ReturnAttributes = None
    payments: List[Transaction] = None
    vouchers: List[DefaultObject] = None
    promotions: List[DefaultObject] = None
    histories: List[History] = None
    payment_histories: List[PaymentHistory] = None
    note: str = None
    location: DefaultObject = None
    source: SourceObject = None
    staff: DefaultObject = None
    store: SourceObject = None
    number: str = None
    external_id: str = None
    external_link: str = None
    external_status: str = None
    status: Status = Status.DRAFT
    order_type: OrderType = OrderType.ECOMMERCE
    payment_status: PaymentStatus = PaymentStatus.UNPAID
    discount: Decimal = 0
    discount_by_customer_group: Decimal = 0
    other_fees: List[Fee] = None
    shipping_fee: Decimal = 0
    tax: Decimal = 0
    tax_rate: Decimal = 0
    id: str = None
    company_id: str = None
    tags: str = None
    created_at: str = None
    updated_at: str = None
    delivered_at: str = None
    discount_id: str = None
    voucher_code: str = None
    voucher_id: str = None
    sub_voucher_total: Decimal = 0
    shift_id: str = None
    loyal_point: Decimal = None
    loyalty_program: LoyaltyProgram = None
    redeem_point: Decimal = None
    redeem_money: Decimal = None
    reward_program: RewardProgram = None


class OrderSchema(AttributesSchema):
    number = fields.Str(allow_none=True)
    external_id = fields.Str(allow_none=True)
    external_link = fields.Url(allow_none=True)
    external_status = fields.Str(allow_none=True)
    discount_id = fields.UUID(allow_none=True)
    billing_address = fields.Nested(AddressSchema(Address), allow_none=True)
    shipping_address = fields.Nested(AddressSchema(Address), allow_none=True)
    status = EnumField(Status, allow_none=True, default=Status.DRAFT)
    order_type = EnumField(OrderType, allow_none=True, default=OrderType.ECOMMERCE)
    payment_status = EnumField(PaymentStatus, allow_none=True, default=PaymentStatus.UNPAID)
    cancels = fields.List(fields.Nested(CancelSchema(Cancel)), allow_none=True)
    packages = fields.List(fields.Nested(PackageSchema(Package)), allow_none=True)
    returns = fields.List(fields.Nested(ReturnAttributesSchema(ReturnAttributes)), allow_none=True)
    exchange_order = fields.Nested(ReturnAttributesSchema(ReturnAttributes), allow_none=True),
    payments = fields.List(fields.Nested(TransactionSchema(Transaction)), allow_none=True)
    histories = fields.List(fields.Nested(HistorySchema(History)), allow_none=True)
    payment_histories = fields.List(fields.Nested(PaymentHistorySchema(PaymentHistory)), allow_none=True)
    customer = fields.Nested(CustomerSchema(Customer), required=True)
    order_line_items = fields.List(fields.Nested(OrderLineItemSchema(OrderLineItem)), required=True)
    sub_total = fields.Decimal(required=True)
    total = fields.Decimal(required=True)
    note = fields.Str(allow_none=True)
    location = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    source = fields.Nested(SourceObjectSchema(SourceObject), allow_none=True)
    staff = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    store = fields.Nested(SourceObjectSchema(SourceObject), allow_none=True)
    tags = fields.Str(allow_none=True)
    discount = fields.Decimal(allow_none=True)
    discount_by_customer_group = fields.Decimal(allow_none=True)
    other_fees = fields.List(fields.Nested(FeeSchema(Fee)), allow_none=True)
    shipping_fee = fields.Decimal(allow_none=True)
    tax = fields.Decimal(allow_none=True)
    tax_rate = fields.Decimal(allow_none=True)
    id = fields.UUID(allow_none=True)
    company_id = fields.UUID(allow_none=True)
    created_at = fields.DateTime(allow_none=True)
    updated_at = fields.DateTime(allow_none=True)
    delivered_at = fields.DateTime(allow_none=True)
    user = fields.Nested(UserSchema(User), allow_none=True, default=None)
    priority = EnumField(Priority, allow_none=True)
    voucher_code = fields.Str(allow_none=True)
    voucher_id = fields.Str(allow_none=True)
    sub_voucher_total = fields.Decimal(allow_none=True)
    shift_id = fields.Str(allow_none=True)
    loyal_point = fields.Decimal(allow_none=True)
    loyalty_program = fields.Nested(LoyaltyProgramSchema(LoyaltyProgram), allow_none=True)
    redeem_point = fields.Decimal(allow_none=True)
    redeem_money = fields.Decimal(allow_none=True)
    reward_program = fields.Nested(RewardProgramSchema(RewardProgram), allow_none=True)


@dataclass
class OrderAttributes(Order):
    sync_record_id: str = None


class OrderAttributesSchema(BasicAttributesSchema, OrderSchema):
    sync_record_id = fields.Str(allow_none=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['source'] = data['source'] if data.get('source') is not None else get_default_source()
        return data


class OrderModel(BasicModel):
    table_name = 'order'
    attributes: OrderAttributes
    attributes_schema = OrderAttributesSchema
    attributes_class = OrderAttributes

    query_mapping = {
        'query': ['id', 'external_id', 'customer.phone', 'customer.email', 'customer.first_name', 'customer.last_name',
                  'order_line_items.sku', 'number'],
        'filter': {
            'id': 'query',
            'status': 'query',
            'shift_id': 'query',
            'payment_status': 'query',
            'external_status': 'query',
            'tags': 'tags',
            'order_type': 'query',
            'store.id': 'query',
            'staff.id': 'query',
            'customer.id': 'query',
            'location.id': 'query',
            'source.id': 'query',
            'source.channel_name': 'query',
            'order_line_items.id': 'query',
            'order_line_items.sku': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
            'delivered_at': 'range',
        },
        'mapping': {
            'total': 'total',
            'sub_total': 'sub_total',
            'company_id': 'company_id',
            'order_line_items': 'order_line_items',
            'external_id': 'external_id',
            'staff': 'staff',
            'location': 'location',
            'source': 'source',
            'store': 'store',
            'customer.id': 'customer.id',
            'customer.phone': 'customer.phone',
            'customer.email': 'customer.email',
            'customer.first_name': 'customer.first_name',
            'customer.last_name': 'customer.last_name',
            'customer.loyal_customer': 'customer.loyal_customer',
            'customer.customer_group': 'customer.customer_group',
            'customer.sale_order': 'customer.sale_order',
            'user': 'user',
            'payments': 'payments',
            'shipping_fee': 'shipping_fee',
            'other_fees': 'other_fees',
            'discount': 'discount',
            'discount_by_customer_group': 'discount_by_customer_group',
            'note': 'note',
            'loyal_point': 'loyal_point',
            'loyalty_program': 'loyalty_program',
            'redeem_point': 'redeem_point',
            'redeem_money': 'redeem_money',
            'reward_program': 'reward_program',
        }
    }

    @classmethod
    def get_packages(cls, company_id, order_id):
        return PackageOrderIdIndex.list({
            'order_id': str(order_id),
            'company_id': str(company_id)
        }, limit=1000).get('Items', [])

    @classmethod
    def update_order_packing_status(cls, user, company_id, order_obj):
        current_packages = OrderModel.get_packages(company_id, order_obj.attributes.id)
        # packed_quantity = sum(
        #     [sum([item['quantity'] for item in package['order_line_items']]) for package in current_packages]
        # )
        # total_quantity = sum([item.quantity for item in order_obj.attributes.order_line_items])

        if all([package['status'] == Status.PENDING.value for package in current_packages]):
            order_status = Status.AWAIT_PACKING
        elif any([package['status'] == Status.PACKING.value for package in current_packages]):
            order_status = Status.PACKING
        else:
            order_status = Status.PARTIAL_PACKING

        update_status(user, order_obj, order_status)

    @classmethod
    def status_count(cls):
        search_params = {
            "aggs": {
                "status_count": {
                    "terms": {
                        "field": "status.keyword"
                    }
                }
            }
        }
        return cls.report(search_params, service=RESOURCE_SERVICE)['aggregations']['status_count']['buckets']


class OrderExternalIdIndex(OrderModel):
    key__id = 'external_id'
    index_name = 'orderExternalIdIndex'
