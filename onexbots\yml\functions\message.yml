create_message:
  handler: src.apis.message.create_message
  events:
    - httpApi:
        path: /conversations/{id}/messages
        method: post
        authorizer:
          name: optiAuthorizer
    - httpApi:
        path: /services/conversations/{id}/messages
        method: post
        authorizer:
          name: onexbotAuthorizer

list_messages:
  handler: src.apis.message.list_messages
  events:
    - httpApi:
        path: /conversations/{id}/messages
        method: get
        authorizer:
          name: optiAuthorizer
    - httpApi:
        path: /services/conversations/{id}/messages
        method: get
        authorizer:
          name: onexbotAuthorizer

list_public_messages:
  handler: src.apis.message.list_public_messages
  events:
    - httpApi:
        path: /public/conversations/{id}/messages
        method: get

deleteMessagesHandler:
  handler: src.apis.message.delete_messages_handler
  timeout: 900
  memorySize: 256