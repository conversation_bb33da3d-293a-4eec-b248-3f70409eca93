from typing import Dict, Any, Tuple, List, Optional

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import AbstractChannel, OAuth2Channel
from integrations.channels.connection_types import ChannelType
from integrations.channels.shopify_oauth.shopify_oauth_connection import ShopifyOAuthSettings
from integrations.channels.shopify_oauth.shopify_oauth_library import ShopifyOAuthLibrary
from integrations.common.event import FetchEvent


class ShopifyOAuthChannel(OAuth2Channel, AbstractChannel):
    channel_name = 'shopify_oauth'
    library_class = ShopifyOAuthLibrary

    def _create_library(self, connection):
        settings: ShopifyOAuthSettings = connection.attributes.settings
        return ShopifyOAuthLibrary(
            api_key=settings.api_key,
            secret_key=settings.secret_key,
            shop_url=settings.shop_url,
            api_version=settings.api_version,
            access_token=connection.attributes.access_token
        )

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            'name': 'Shopify OAuth',
            'channel_type': ChannelType.ECOMMERCE,
            'description': 'Connect your Shopify OAuth instance to sync products, orders, and customers.',
            'logo': cls._load_image_as_base64(__file__, 'shopify_oauth.png')
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:

        if action == ActionType.get_order:
            return {}
        elif action == ActionType.get_product:
            return {}
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_order:
            return {}
        elif action == ActionType.sync_product:
            return {}
        else:
            return {}

    def get_product__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_product_params()

    def get_product__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}

        if fetch_event.object_id:
            # Case 1: Single product
            return self.library.get_product(product_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        else:
            # Case 2: Filter-based query
            return self.library.get_products(params=params, settings=settings, next_page=fetch_event.next_page,
                                             fetch_event_id=fetch_event.id)
        # return self.library.get_products(settings)

    def sync_order__publish(self, settings: Dict[str, Any], order_data: Dict[str, Any]):
        return self.library.sync_order(order_data)

    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        access_code = query_params.get('code')
        if not access_code:
            raise ValueError("Missing code in query parameters")

        token_data = self.library.fetch_token(access_code, state=self.connection.attributes.id)

        return (
            token_data['external_id'],
            token_data['access_token'],
            token_data['expires_at'],
            token_data['refresh_token'],
            token_data['token_data']
        )

    def refresh_oauth_token(self) -> Dict[str, Any]:
        pass

    def initiate_oauth_flow(self, redirect_url: str = None, base_url: str = None) -> str:
        return self.library.get_authorization_url(str(self.connection.attributes.id), redirect_url, base_url)

    def test_connection(self) -> bool:
        try:
            return self.library.test_connection()
        except Exception:
            return False

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        return self.library.extract_object_details(payload, headers, event_type)

    def process_webhook_event(self, event_type, payload) -> bool:
        return self.library.process_webhook_event(event_type, payload, self.channel_name)

    def webhook__webhook(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        return self.library.process_webhook(settings, product_data)

    def setup_webhooks(self):
        self.library.setup_webhooks(self.connection)

    @classmethod
    def verify_webhook_signature(cls, payload: str, headers: dict, settings: ShopifyOAuthSettings) -> bool:
        return ShopifyOAuthLibrary.verify_webhook_signature(payload, headers.get('x-shopify-hmac-sha256'),
                                                            settings.secret_key)

    @classmethod
    def extract_merchant_id(cls, payload):
        return cls.library_class.extract_merchant_id(payload)

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        # # check if the webhook is from Shopify
        # if headers:
        #     topic = headers.get('x-shopify-topic', '')
        #     shop_domain = headers.get('x-shopify-shop-domain', '')
        #
        #     if topic and shop_domain:
        #         return {
        #             "status": "success",
        #             "message": f"Shopify webhook received successfully",
        #             "shop": shop_domain,
        #             "topic": topic
        #         }
        #
        # # cannot identify the webhook type
        # return {
        #     "status": "error",
        #     "message": "Unknown webhook format"
        # }
        pass

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return cls.library_class.extract_event_type(payload, headers)

    @classmethod
    def _get_attributes(cls, type: str):
        attribute_getters = {
            'product': cls.library_class._get_product_attributes
        }
        return attribute_getters.get(type, lambda: {})()
