from dataclasses import dataclass

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from marshmallow import fields
from nolicore.utils.utils import logger


@dataclass
class BotChannelConnectionAttributes(BasicAttributes):
    bot_id: str = None
    connection_id: str = None
    channel_id: str = None
    channel_name: str = None


class BotChannelConnectionAttributesSchema(BasicAttributesSchema):
    bot_id = fields.Str(required=True)
    connection_id = fields.Str(required=True)
    channel_id = fields.Str(required=True)
    channel_name = fields.Str(required=True)


class BotChannelConnectionModel(BasicModel):
    key__id = 'connection_id'
    key__ranges = ['channel_id']
    table_name = 'botChannelConnection'
    attributes: BotChannelConnectionAttributes
    attributes_schema = BotChannelConnectionAttributesSchema
    attributes_class = BotChannelConnectionAttributes

    query_mapping = {
        'query': ['bot_id', 'connection_id', 'channel_id', 'channel_name'],
        'filter': {
            'bot_id': 'query',
            'connection_id': 'query',
            'channel_id': 'query',
            'channel_name': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range'
        }
    }

    @classmethod
    def get_bot_channel_connection(cls, connection_id, channel_id):
        return cls.by_key({
            'connection_id': connection_id,
            'channel_id': channel_id
        })

    # create or update bot channel connection
    @classmethod
    def create_bot_channel_connection(cls, bot_id, connection_id, channel_id, channel_name, company_id=None):
        record = cls.by_key({
            'connection_id': connection_id,
            'channel_id': channel_id
        })

        if record:
            cls.delete({
                'connection_id': connection_id,
                'channel_id': channel_id
            })

        record = cls.create(company_id,
                            {'bot_id': bot_id, 'connection_id': connection_id,
                             'channel_id': channel_id, 'channel_name': channel_name})
        return record

    @classmethod
    def delete_bot_channel_connection(cls, connection_id, channel_id):
        return cls.delete({
            'connection_id': connection_id,
            'channel_id': channel_id
        })


class BotChannelConnectionByBotIdAndConnectionIdIndex(BotChannelConnectionModel):
    key__id = 'bot_id'
    key__ranges = ['connection_id']
    index_name = 'botChannelConnectionByBotIdAndConnectionIdIndex'

    @classmethod
    def get_bot_channel_connection_by_bot_id_and_connection_id(cls, bot_id, connection_id):
        """
        Get bot channel connection by bot_id and connection_id
        
        Args:
            bot_id (str): The bot ID
            connection_id (str): The connection ID
            
        Returns:
            List[Dict]: List of matching bot channel connections
        """
        try:
            return cls.list({
                cls.key__id: bot_id,
                cls.key__ranges[0]: connection_id
            }, limit=1).get('Items', [])
        except Exception as e:
            logger.error(f"No data found in get_bot_channel_connection_by_bot_id_and_connection_id: {str(e)}")
            return []
