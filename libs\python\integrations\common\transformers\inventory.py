from typing import Dict, Any, List

from models.inventory.inventory_item import InventoryItemAttributesSchema, InventoryItemAttributes


class InventoryTransformer:
    @staticmethod
    def transform(data: Dict[str, Any]) -> List[Dict[str, Any]]:
        inventories = []
        for location_id, inventory in data['inventory']['depots'].items():
            inventory_data = InventoryItemAttributes(
                id=str(data['idNhanh']),
                sku=data['code'],
                product_id=str(data['parentId']),
                location_id=location_id,
                available=inventory['available'],
                on_hand=inventory.get('remain', 0),
                shipping=inventory.get('shipping', 0),
                packing=inventory.get('holding', 0),
                returning=inventory.get('damaged', 0),
                incoming=inventory.get('warranty', 0),
            )
            inventories.append(InventoryItemAttributesSchema().dump(inventory_data))
        return inventories