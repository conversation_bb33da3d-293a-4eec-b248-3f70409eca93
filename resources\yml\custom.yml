pythonRequirements:
    fileName: "requirements.txt"
    layer: true
    noDeploy:
        - boto3
        - botocore

# Lấy bucket name từ param (dev/prod)
deploymentBucketName: ${param:bucket_name}

prune:
    automatic: true
    number: 3

retrieverQueueArn:
    Fn::GetAtt: [RetrieverQueue, Arn]

retrieverQueueUrl:
    Fn::GetAtt: [RetrieverQueue, QueueUrl]

retrieverDeadLetterQueueArn:
    Fn::GetAtt: [RetrieverDeadLetterQueue, Arn]

retrieverDeadLetterQueueUrl:
    Fn::GetAtt: [RetrieverDeadLetterQueue, QueueUrl]

capacities:
    - table: product # DynamoDB Resource
      index:
          - productSkuIndex
          - productSlugIndex
      read:
          minimum: 3 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.75 # Targeted usage percentage
      write:
          minimum: 1 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60 # Wait 1 minute before reducing
          out:
              cooldown: 30 # Scale up quickly after 30s
    - table: customer # DynamoDB Resource
      read:
          minimum: 1 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.75 # Targeted usage percentage
      write:
          minimum: 1 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5
      scaling:
          in:
              cooldown: 60 # Wait 1 minute before reducing
          out:
              cooldown: 30 # Scale up quickly after 30s
    - table: category # DynamoDB Resource
      read:
          minimum: 1 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      write:
          minimum: 1 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5
      scaling:
          in:
              cooldown: 60 # Wait 1 minute before reducing
          out:
              cooldown: 30 # Scale up quickly after 30s
    - table: variant # DynamoDB Resource
      index:
          - variantSlugIndex
          - productIdIndex
          - variantSkuIndex
          - variantOriginalSkuIndex
      read:
          minimum: 3 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      write:
          minimum: 3 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60 # Wait 1 minute before reducing
          out:
              cooldown: 30 # Scale up quickly after 30s
    - table: order # DynamoDB Resource
      index: # List or single index name
          - orderExternalIdIndex
      read:
          minimum: 1 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      write:
          minimum: 3 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60
          out:
              cooldown: 30
    - table: purchaseOrder # DynamoDB Resource
      index:
          - purchaseOrderExternalIdIndex
      read:
          minimum: 3 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      write:
          minimum: 3 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60 # Wait 1 minute before reducing
          out:
              cooldown: 30 # Scale up quickly after 30s
    - table: returnOrder # DynamoDB Resource
      index:
          - returnExternalIdIndex
      read:
          minimum: 3 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      write:
          minimum: 3 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60
          out:
              cooldown: 30
    - table: image # DynamoDB Resource
      read:
          minimum: 1 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.75 # Targeted usage percentage
      write:
          minimum: 1 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60
          out:
              cooldown: 30
    - table: imports # DynamoDB Resource
      read:
          minimum: 1 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.75 # Targeted usage percentage
      write:
          minimum: 1 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60
          out:
              cooldown: 30
    - table: importRecord # DynamoDB Resource
      read:
          minimum: 3 # Minimum read capacity
          maximum: 15 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      write:
          minimum: 3 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60
          out:
              cooldown: 30
    - table: history # DynamoDB Resource
      read:
          minimum: 2 # Minimum read capacity
          maximum: 15 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      write:
          minimum: 1 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60
          out:
              cooldown: 30
    - table: counter # DynamoDB Resource
      read:
          minimum: 1 # Minimum read capacity
          maximum: 10 # Maximum read capacity
          usage: 0.5 # Targeted usage percentage
      write:
          minimum: 1 # Minimum write capacity
          maximum: 10 # Maximum write capacity
          usage: 0.5 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60
          out:
              cooldown: 30
    - table: connection # DynamoDB Resource
      index: # List or single index name
          - connectionByCompanyIndex
      read:
          minimum: 3 # Minimum read capacity
          maximum: 15 # Maximum read capacity
          usage: 0.75 # Targeted usage percentage
      write:
          minimum: 3 # Minimum read capacity
          maximum: 15 # Maximum read capacity
          usage: 0.75 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60 # Wait 1 minute before reducing
          out:
              cooldown: 30 # Scale up quickly after 30s
    - table: syncRecord # DynamoDB Resource
      index: # List or single index name
          - syncRecordByCompanyIndex
          - syncRecordConnectionIdIndex
      read:
          minimum: 3 # Minimum read capacity
          maximum: 15 # Maximum read capacity
          usage: 0.75 # Targeted usage percentage
      write:
          minimum: 3 # Minimum write capacity
          maximum: 15 # Maximum write capacity
          usage: 0.75 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60 # Wait 1 minute before reducing
          out:
              cooldown: 30 # Scale up quickly after 30s
    - table: fetchEvent # DynamoDB Resource
      index: # List or single index name
          - fetchEventByCompanyIndex
      read:
          minimum: 3 # Minimum read capacity
          maximum: 15 # Maximum read capacity
          usage: 0.75 # Targeted usage percentage
      write:
          minimum: 3 # Minimum write capacity
          maximum: 15 # Maximum write capacity
          usage: 0.75 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60 # Wait 1 minute before reducing
          out:
              cooldown: 30 # Scale up quickly after 30s

    - table: destinationData # DynamoDB Resource
      read:
          minimum: 3 # Minimum write capacity
          maximum: 15 # Maximum write capacity
          usage: 0.75 # Targeted usage percentage
      write:
          minimum: 3 # Minimum write capacity
          maximum: 15 # Maximum write capacity
          usage: 0.75 # Targeted usage percentage
      scaling:
          in:
              cooldown: 60
          out:
              cooldown: 30

dynamodb:
    # If you only want to use DynamoDB Local in some stages, declare them here
    stages:
        - dev
    start:
        port: 8001
        inMemory: true
        heapInitial: 200m
        heapMax: 1g
        migrate: true
        seed: true
        convertEmptyValues: true
    # Uncomment only if you already have a DynamoDB running locally
    #      noStart: true

    seed:
        noliwms:
            sources:
                - table: location
                  sources: [./seed/location.json]
                - table: priceGroup
                  sources: [./seed/price_group.json]
                - table: supplier
                  sources: [./seed/supplier.json]
                - table: product
                  sources: [./seed/product.json]
                - table: paymentMethod
                  sources: [./seed/payment_method.json]
                - table: account
                  sources: [./seed/account.json]
                - table: wishlist
                  sources: [./seed/wishlist.json]
                - table: category
                  sources: [./seed/category.json]
                - table: discount
                  sources: [./seed/discount.json]
                - table: variant
                  sources: [./seed/variable.json]
                - table: order
                  sources: [./seed/order.json]

serverless-offline:
    httpPort: 4000
    stageVariables:
        foo: "bar"
s3:
    host: localhost
    directory: /tmp
    buckets:
        - noliwms
