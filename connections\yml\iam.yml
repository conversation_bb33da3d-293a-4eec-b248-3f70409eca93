role:
  statements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
      Resource: "*"
    - Effect: Allow
      Action:
        - cognito-idp:AdminListGroupsForUser
        - cognito-idp:ListUsers
        - cognito-idp:AdminCreateUser
        - cognito-idp:AdminAddUserToGroup
        - cognito-idp:ListUsersInGroup
        - cognito-idp:CreateGroup
        - cognito-idp:AdminSetUserPassword
        - cognito-idp:AdminUpdateUserAttributes
      Resource:
        - "arn:aws:cognito-idp:${opt:region, self:provider.region}:*:userpool/ap-southeast-1_MfCAeQm09"
    - Effect: Allow
      Action:
        - "s3:PutObject"
        - "s3:GetObject"
        - "s3:DeleteObject"
        - "s3:ListBucket"
        - "s3:ListObjectsV2"
        - "s3:PutObjectAcl"
        - "s3:CreateMultipartUpload"
        - "s3:CompleteMultipartUpload"
      Resource:
        - "arn:aws:s3:::${param:bucket_name}/*"
        - "arn:aws:s3:::${param:bucket_name}"
    - Effect: Allow
      Action:
        - dynamodb:Get*
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Scan
        - dynamodb:Query
      Resource:
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/connection"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/webhook"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/location"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/syncRecord"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/location/index/locationCompanyIdIndex"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/connection/index/connectionByCompanyIndex"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/destinationData"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/mappingAttribute"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/syncRecord/index/syncRecordByCompanyIndex"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/mappingAttribute/index/mappingAttributeTypeIndex"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/mappingAttribute/index/syncRecordConnectionIdIndex"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/botChannelConnection"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/botChannelConnection/index/botChannelConnectionByBotIdAndConnectionIdIndex"
