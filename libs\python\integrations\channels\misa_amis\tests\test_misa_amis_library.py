import unittest
from datetime import datetime

from integrations.channels.misa_amis.misa_amis_library import MisaAmisLibrary
from integrations.common.base_api_library import StandardOrder, StandardProduct


class TestMisaAmisLibrary(unittest.TestCase):

    def setUp(self):
        self.misa_amis_library = MisaAmisLibrary(
            app_id="74307",
            business_id="your_business_id",
            secret_key="9336ddcdee3c276b47687ec44091b821",
            auth_url="https://auth.misa_amis.vn",
            base_url="https://open.misa_amis.vn"
        )
        # You might need to set up the access token here if it's required for most API calls
        # self.misa_amis_library.fetch_token("your_access_code")

    def test_get_authorization_url(self):
        connection_id = "test_connection_id"
        auth_url = self.misa_amis_library.get_authorization_url(connection_id)
        self.assertIn("auth.misa_amis.vn", auth_url)
        self.assertIn(self.misa_amis_library.app_id, auth_url)
        self.assertIn("returnLink", auth_url)

    def test_fetch_token(self):
        # Note: This test will actually try to fetch a token from the MisaAmis API
        token_data = self.misa_amis_library.fetch_token("your_access_code")

        self.assertIn("accessToken", token_data)
        self.assertIsNotNone(self.misa_amis_library.access_token)

    def test_test_connection(self):
        # This will actually test the connection to the MisaAmis API
        self.assertTrue(self.misa_amis_library.test_connection())

    def test_get_orders(self):
        response = self.misa_amis_library.get_orders({"page": 1})

        self.assertTrue(response.success)
        self.assertGreater(len(response.data), 0)
        self.assertIsInstance(response.data[0], StandardOrder)

    def test_get_products(self):
        response = self.misa_amis_library.get_products({"page": 1})

        self.assertTrue(response.success)
        self.assertGreater(len(response.data), 0)
        self.assertIsInstance(response.data[0], StandardProduct)

    def test_sync_order(self):
        order_data = {
            "id": "TEST-001",
            "customerMobile": "1234567890",
            "totalAmount": 100.00,
            "status": "Pending"
        }

        response = self.misa_amis_library.sync_order(order_data)

        self.assertTrue(response.success)
        self.assertIsInstance(response.data, StandardOrder)

    def test_standardize_order(self):
        # You'll need to fetch a real order from the API to test this
        raw_order = self.misa_amis_library.get_orders({"page": 1}).data[0].raw_data

        standard_order = self.misa_amis_library.standardize_order(raw_order)

        self.assertIsInstance(standard_order, StandardOrder)
        self.assertIsInstance(standard_order.id, str)
        self.assertIsInstance(standard_order.order_number, str)
        self.assertIsInstance(standard_order.total_price, float)
        self.assertIsInstance(standard_order.status, str)
        self.assertIsInstance(standard_order.created_at, datetime)

    def test_standardize_product(self):
        # You'll need to fetch a real product from the API to test this
        raw_product = self.misa_amis_library.get_products({"page": 1}).data[0].raw_data

        standard_product = self.misa_amis_library.standardize_product(raw_product['id'], raw_product)

        self.assertIsInstance(standard_product, StandardProduct)
        self.assertIsInstance(standard_product.id, str)
        self.assertIsInstance(standard_product.title, str)
        self.assertIsInstance(standard_product.price, float)
        self.assertIsInstance(standard_product.inventory_quantity, int)
        self.assertIn(standard_product.status, ["active", "inactive"])
        self.assertIsInstance(standard_product.created_at, datetime)

    def test_verify_webhook_signature(self):
        # This test assumes the current implementation always returns True
        self.assertTrue(self.misa_amis_library.verify_webhook_signature("payload", "signature", "secret"))

    def test_extract_merchant_id(self):
        payload = {"businessId": "test_business_id"}
        self.assertEqual(self.misa_amis_library.extract_merchant_id(payload), "test_business_id")

    def test_extract_event_type(self):
        payload = {"eventType": "order.created"}
        headers = {"X-MisaAmis-Event": "product.updated"}

        self.assertEqual(self.misa_amis_library.extract_event_type(payload, headers), "order.created")
        self.assertEqual(self.misa_amis_library.extract_event_type({}, headers), "product.updated")

    def test_sync_product(self):
        with self.assertRaises(NotImplementedError):
            self.misa_amis_library.sync_product({})


if __name__ == '__main__':
    unittest.main()
