import hashlib
import hmac
import json
from datetime import datetime
from typing import Dict, Any, Union, List, Tuple

import pendulum
import requests
from nolicore.utils.exceptions import BadRequest

from integrations.channels.action_types import ActionGroup
from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, SyncAPIResponse,
    MetaData, PublishMetaData, StandardOrder, StandardProduct, StandardReturnOrder, StandardPurchaseOrder
)


class ShopifyEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.ORDERS = EndpointType(type="rest", method="GET", path=f"{self.base_url}/orders.json")
        self.PRODUCTS = EndpointType(type="rest", method="GET", path=f"{self.base_url}/products.json")
        self.SYNC_ORDER = EndpointType(type="rest", method="POST", path=f"{self.base_url}/orders.json")
        self.SYNC_PRODUCT = EndpointType(type="rest", method="POST", path=f"{self.base_url}/products.json")
        self.INVENTORY_ADJUST = EndpointType(type="rest", method="POST",
                                             path=f"{self.base_url}/inventory_levels/adjust.json")
        self.INVENTORY_SET = EndpointType(type="rest", method="POST", path=f"{self.base_url}/inventory_levels/set.json")
        self.SHOP = EndpointType(type="rest", method="GET", path=f"{self.base_url}/shop.json")
        self.GRAPHQL = EndpointType(type="graphql", method="POST", path=f"{self.base_url}/graphql.json")
        self.CREATE_WEBHOOK = EndpointType(type="rest", method="POST", path=f"{self.base_url}/webhooks.json")


class ShopifyLibrary(BaseAPILibrary):
    AVAILABLE_WEBHOOKS = {
        "orders/create": "/shopify/order/created",
        "orders/updated": "/shopify/order/updated",
        "products/create": "/shopify/product/created",
        "products/update": "/shopify/product/updated",
        "inventory_levels/update": "/shopify/inventory/updated",
    }

    def __init__(self, shop_url: str, access_token: str, api_version: str = "2023-07"):
        super().__init__(f"{shop_url}/admin/api/{api_version}")
        self.access_token = access_token
        self.endpoints = ShopifyEndpoints(self.endpoints.base_url)

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None) -> Dict[
        str, Any]:
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": self.access_token
        }

        if endpoint.type == "rest":
            response = self._make_request_sync(endpoint, headers=headers, params=params, data=data)
            return response
        elif endpoint.type == "graphql":
            return self._make_graphql_request(data["query"], data.get("variables"))

    def _make_graphql_request(self, query: str, variables: Dict[str, Any] = None) -> Dict[str, Any]:
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": self.access_token
        }
        payload = {
            "query": query,
            "variables": variables or {}
        }
        response = requests.post(self.endpoints.GRAPHQL.path, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()

    def test_connection(self) -> bool:
        try:
            self._make_request(self.endpoints.SHOP)
            return True
        except Exception:
            return False

    def get_orders(self, params: Dict[str, Any] = None) -> FetchAPIResponse[StandardOrder]:
        default_params = {
            "limit": 50,
            "status": "any",
            "financial_status": "any",
            "fulfillment_status": "any",
        }
        params = {**default_params, **(params or {})}

        if 'updated_at_min' not in params:
            updated_after = params.get('updated_after')
            if updated_after:
                params['updated_at_min'] = pendulum.parse(updated_after).to_iso8601_string()

        response = self._make_request(self.endpoints.ORDERS, params=params)

        orders = [self.standardize_order(order) for order in response["orders"]]

        return FetchAPIResponse(
            success=True,
            data=orders,
            meta=MetaData(total_count=len(orders), page=1, limit=params["limit"])
        )

    def get_products(self, params: Dict[str, Any] = None) -> FetchAPIResponse[StandardProduct]:
        default_params = {
            "limit": 50,
            "status": "active",
        }
        params = {**default_params, **(params or {})}

        if 'updated_at_min' not in params:
            updated_after = params.get('updated_after')
            if updated_after:
                params['updated_at_min'] = pendulum.parse(updated_after).to_iso8601_string()

        response = self._make_request(self.endpoints.PRODUCTS, params=params)

        products = [self.standardize_product(product) for product in response["products"]]

        return FetchAPIResponse(
            success=True,
            data=products,
            meta=MetaData(total_count=len(products), page=1, limit=params["limit"])
        )

    def sync_order(self, order_data: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        response = self._make_request(self.endpoints.SYNC_ORDER, data={"order": order_data})

        synced_order = self.standardize_order(response["order"])

        return SyncAPIResponse(
            success=True,
            data=synced_order,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Order {synced_order.order_number} synced successfully"
        )

    def sync_product(self, product_data: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        response = self._make_request(self.endpoints.SYNC_PRODUCT, data={"product": product_data})

        synced_product = self.standardize_product(response["product"])

        return SyncAPIResponse(
            success=True,
            data=synced_product,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Product {synced_product.title} synced successfully"
        )

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        return StandardOrder(
            id=str(raw_order["id"]),
            order_number=raw_order["name"],
            total_price=float(raw_order["total_price"]),
            currency=raw_order["currency"],
            status=raw_order["status"],
            created_at=datetime.fromisoformat(raw_order["created_at"].rstrip('Z')),
            raw_data=raw_order
        )

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        return StandardProduct(
            id=str(raw_product["id"]),
            title=raw_product["title"],
            price=float(raw_product["variants"][0]["price"]),
            currency=raw_product["variants"][0]["presentment_prices"][0]["price"]["currency_code"],
            inventory_quantity=raw_product["variants"][0]["inventory_quantity"],
            status=raw_product["status"],
            created_at=datetime.fromisoformat(raw_product["created_at"].rstrip('Z')),
            raw_data=raw_product
        )

    def get_product_by_id(self, product_id: str) -> SyncAPIResponse[StandardProduct]:
        query = """
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            title
            description
            variants(first: 1) {
              edges {
                node {
                  price
                  inventoryQuantity
                }
              }
            }
            status
            createdAt
          }
        }
        """
        variables = {"id": f"gid://shopify/Product/{product_id}"}

        response = self._make_graphql_request(query, variables)

        if "errors" in response:
            return SyncAPIResponse(success=False, data=None, meta=None, message=str(response["errors"]))

        raw_product = response["data"]["product"]
        product = self.standardize_product({
            "id": raw_product["id"].split("/")[-1],
            "title": raw_product["title"],
            "variants": [{
                "price": raw_product["variants"]["edges"][0]["node"]["price"],
                "inventory_quantity": raw_product["variants"]["edges"][0]["node"]["inventoryQuantity"],
                "presentment_prices": [{"price": {"currency_code": "USD"}}]  # Assuming USD, adjust as needed
            }],
            "status": raw_product["status"],
            "created_at": raw_product["createdAt"],
            **raw_product  # Include all raw data
        })

        return SyncAPIResponse(
            success=True,
            data=product,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Product {product.title} retrieved successfully"
        )

    def adjust_inventory(self, inventory_item_id: str, location_id: str, available_adjustment: int) -> SyncAPIResponse[
        Dict[str, Any]]:
        data = {
            "inventory_item_id": inventory_item_id,
            "location_id": location_id,
            "available_adjustment": available_adjustment
        }
        response = self._make_request(self.endpoints.INVENTORY_ADJUST, data=data)

        return SyncAPIResponse(
            success=True,
            data=response["inventory_level"],
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Inventory adjusted successfully for item {inventory_item_id}"
        )

    def set_inventory(self, inventory_item_id: str, location_id: str, available: int) -> SyncAPIResponse[
        Dict[str, Any]]:
        data = {
            "inventory_item_id": inventory_item_id,
            "location_id": location_id,
            "available": available
        }
        response = self._make_request(self.endpoints.INVENTORY_SET, data=data)

        return SyncAPIResponse(
            success=True,
            data=response["inventory_level"],
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Inventory set successfully for item {inventory_item_id}"
        )

    def process_webhook(self, event_type: str, payload: Dict[str, Any]) -> SyncAPIResponse[
        Union[StandardOrder, StandardProduct, Dict[str, Any]]]:
        event_processors = {
            'orders/create': self._process_order_created,
            'orders/updated': self._process_order_updated,
            'products/create': self._process_product_created,
            'products/update': self._process_product_updated,
            'inventory_levels/update': self._process_inventory_updated
        }

        processor = event_processors.get(event_type)
        if not processor:
            raise ValueError(f"Unsupported event type: {event_type}")

        return processor(payload)

    def _process_order_created(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        order = self.standardize_order(payload)
        return SyncAPIResponse(
            success=True,
            data=order,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed new order: {order.order_number}"
        )

    def _process_order_updated(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        order = self.standardize_order(payload)
        return SyncAPIResponse(
            success=True,
            data=order,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed updated order: {order.order_number}"
        )

    def _process_product_created(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        product = self.standardize_product(payload)
        return SyncAPIResponse(
            success=True,
            data=product,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed new product: {product.title}"
        )

    def _process_product_updated(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        product = self.standardize_product(payload)
        return SyncAPIResponse(
            success=True,
            data=product,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed updated product: {product.title}"
        )

    def _process_inventory_updated(self, payload: Dict[str, Any]) -> SyncAPIResponse[Dict[str, Any]]:
        inventory_data = {
            "product_id": payload['product_id'],
            "variant_id": payload['variant_id'],
            "inventory_quantity": payload['available']
        }
        return SyncAPIResponse(
            success=True,
            data=inventory_data,
            meta=PublishMetaData(updated_at=datetime.now()),
            message=f"Processed inventory update for product: {payload['product_id']}"
        )

    @staticmethod
    def verify_webhook_signature(payload: str, hmac_header: str, secret: str) -> bool:
        calculated_hmac = hmac.new(secret.encode('utf-8'), payload.encode('utf-8'), hashlib.sha256).hexdigest()
        return hmac.compare_digest(calculated_hmac, hmac_header)

    def handle_webhook(self, event_type: str, payload: str, hmac_header: str, secret: str) -> SyncAPIResponse[
        Union[StandardOrder, StandardProduct, Dict[str, Any]]]:
        if not self.verify_webhook_signature(payload, hmac_header, secret):
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid webhook signature"
            )

        try:
            payload_dict = json.loads(payload)
            return self.process_webhook(event_type, payload_dict)
        except json.JSONDecodeError:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid JSON payload"
            )
        except ValueError as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=str(e)
            )

    def setup_webhooks(self, connection):
        webhooks_to_create = [
            {"topic": topic, "address": connection.get_webhook_url(self.AVAILABLE_WEBHOOKS[topic])}
            for topic in self.AVAILABLE_WEBHOOKS
            if connection.attributes.webhook_settings.get(topic, False)
        ]

        results = []
        for webhook in webhooks_to_create:
            result = self._create_webhook(webhook)
            results.append((webhook['topic'], result[0]))

        for topic, success in results:
            if success:
                print(f"Successfully created webhook: {topic}")
            else:
                print(f"Failed to create webhook: {topic}")

    def _create_webhook(self, webhook_data: Dict[str, str]) -> Tuple[bool, str]:
        try:
            response = self._make_request(
                self.endpoints.CREATE_WEBHOOK,
                data={"webhook": webhook_data}
            )
            return True, ""
        except Exception as e:
            return False, str(e)

    @staticmethod
    def extract_merchant_id(payload):
        return payload.get('shop_id')

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return headers.get('X-Shopify-Topic') or payload.get('event_type')