import json
import unittest
import uuid

from tests.base import BaseTestCase
from connections.src.apis.connection import get_connection_types, get_authorization_link, oauth2_callback_post, oauth2_callback, oauth2_callback_path
from connections.tests.apis.connection.data_test import call_back_post_event

class ConnectionApiTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

    def create_lambda_event(self, method='GET', path='/', body=None, query_params=None, path_params=None, user_id=None,
                            username=None, company_id=None):
        if user_id is None:
            user_id = str(uuid.uuid4())
        if username is None:
            username = f"user_{user_id}"
        if company_id is None:
            company_id = str(uuid.uuid4())

        event = {
            'httpMethod': method,
            'body': json.dumps(body) if body else None,
            'queryStringParameters': query_params or {},
            'pathParameters': path_params or {},
            'resource': path,
            'path': path,
            'headers': {},
            'stageVariables': {},
            'requestContext': {
                'requestId': 'test_request_id',
                'stage': 'test',
                'apiId': 'test_api_id',
                'httpMethod': method,
                'path': path,
                'resourcePath': path,
                'accountId': '************',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': user_id,
                            'cognito:username': username,
                            'custom:company_id': company_id
                        }
                    }
                }
            },
            'isBase64Encoded': False
        }
        return event

    def test_get_connection_types(self):
        response = get_authorization_link(call_back_post_event, {})
        self.assertTrue(True)

    def test_oauth2_callback(self):
        event = self.create_lambda_event(
            method='GET',
            query_params={'state': 'b686e98e-788d-4050-a9bd-a644121a15b2', 'code': 'e11ad9e8bbc2da2d75ac6e43882c1627'}
        )
        event1={'version': '2.0', 'routeKey': 'GET /oauth2_callback', 'rawPath': '/oauth2_callback', 'rawQueryString': 'code=65791d76331f4eac25bccdc9866e55dd&hmac=927789bc25dcd9fd8e5669be3167ab6e7a51dff0d53b9afea1eddf09b899b7d1&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvc29ubnYyMDAzNA&redirect_path=shopify-choose-plan&shop=sonnv20034.myshopify.com&state=4373c121-2e72-4942-8d27-96b334d2ec81&timestamp=**********', 'headers': {'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en,vi;q=0.9,en-US;q=0.8', 'content-length': '0', 'host': 'api-staging.optiwarehouse.com', 'priority': 'u=0, i', 'referer': 'https://admin.shopify.com/', 'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'sec-fetch-dest': 'document', 'sec-fetch-mode': 'navigate', 'sec-fetch-site': 'cross-site', 'sec-fetch-user': '?1', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'x-amzn-trace-id': 'Root=1-67db9e9b-2ec2c77c3994fd2568ea3bbc', 'x-forwarded-for': '************', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https'}, 'queryStringParameters': {'code': '65791d76331f4eac25bccdc9866e55dd', 'hmac': '927789bc25dcd9fd8e5669be3167ab6e7a51dff0d53b9afea1eddf09b899b7d1', 'host': 'YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvc29ubnYyMDAzNA', 'redirect_path': 'shopify-choose-plan', 'shop': 'sonnv20034.myshopify.com', 'state': '4373c121-2e72-4942-8d27-96b334d2ec81', 'timestamp': '**********'}, 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging', 'http': {'method': 'GET', 'path': '/oauth2_callback', 'protocol': 'HTTP/1.1', 'sourceIp': '************', 'userAgent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}, 'requestId': 'HtW4Uhj5yQ0EMtQ=', 'routeKey': 'GET /oauth2_callback', 'stage': '$default', 'time': '20/Mar/2025:04:50:35 +0000', 'timeEpoch': *************}, 'isBase64Encoded': False}
        response = oauth2_callback(event, {})
        self.assertTrue(True)

if __name__ == '__main__':
    unittest.main()

