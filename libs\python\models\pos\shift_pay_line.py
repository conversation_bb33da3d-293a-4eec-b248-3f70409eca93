from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


class ShiftPayLineItemType(Enum):
    PAID_IN = 'PAID_IN'
    PAID_OUT = 'PAID_OUT'


@dataclass
class ShiftPayLineItemAttributes(BasicAttributes):
    type: ShiftPayLineItemType = ShiftPayLineItemType.PAID_OUT
    amount: Decimal = None
    shift_id: str = None
    note: str = None


class ShiftPayLineItemAttributesSchema(BasicAttributesSchema):
    type = EnumField(ShiftPayLineItemType, required=True, default=ShiftPayLineItemType.PAID_OUT)
    amount = fields.Decimal(required=True)
    shift_id = fields.Str(required=True)
    note = fields.Str(required=True)


class ShiftPayLineItemModel(BasicModel):
    table_name = 'shiftPayLineItem'
    attributes: ShiftPayLineItemAttributes
    attributes_schema = ShiftPayLineItemAttributesSchema
    attributes_class = ShiftPayLineItemAttributes

    query_mapping = {
        'query': ['id'],
        'filter': {
            'id': 'query',
            'type': 'query',
            'shift_id': 'query',
            'amount': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'note': 'note',
            'user': 'user',
        }
    }
