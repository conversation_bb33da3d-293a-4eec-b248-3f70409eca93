from typing import Dict, Any, List, Union

from nolicore.utils.exceptions import BadRequest

from integrations.channels.action_types import ActionGroup
from integrations.common.base_api_library import BaseAPILibrary, APIEndpoints, EndpointType, SyncAPIResponse, \
    StandardPurchaseOrder, FetchAPIResponse, StandardReturnOrder, StandardOrder, StandardProduct
from integrations.common.event import FetchEvent


class ZaloEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        # Define Zalo API endpoints here
        self.GET_FOLLOWERS = EndpointType(type="rest", method="GET", path="/getfollowers")
        self.SEND_MESSAGE = EndpointType(type="rest", method="POST", path="/message")


class ZaloLibrary(BaseAPILibrary):

    def __init__(self, app_id: str, app_secret: str, api_key: str, access_token: str = None):
        super().__init__("https://openapi.zalo.me")
        self.app_id = app_id
        self.app_secret = app_secret
        self.api_key = api_key
        self.access_token = access_token
        self.endpoints = ZaloEndpoints(self.endpoints.base_url)

    def get_authorization_url(self, state: str) -> str:
        # Implement Zalo authorization URL generation
        pass

    def fetch_token(self, code: str) -> Dict[str, Any]:
        # Implement token fetching from Zalo
        pass

    def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        # Implement token refreshing for Zalo
        pass

    def test_connection(self) -> bool:
        # Implement connection testing for Zalo
        return True

    def get_orders(self, settings: Dict[str, Any]) -> List[Dict[str, Any]]:
        # Zalo doesn't have orders, so we'll return an empty list
        return []

    def get_products(self, settings: Dict[str, Any]) -> List[Dict[str, Any]]:
        # Zalo doesn't have products, so we'll return an empty list
        return []

    def standardize_order(self, order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        # Zalo doesn't have orders, so we'll return an empty dictionary
        return {}

    def standardize_product(self, product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        # Zalo doesn't have products, so we'll return an empty dictionary
        return {}

    def sync_order(self, settings: Dict[str, Any], order_data: Dict[str, Any]) -> Dict[str, Any]:
        # Zalo doesn't support order syncing, so we'll return an empty dictionary
        return {}

    def sync_product(self, settings: Dict[str, Any], product_data: Dict[str, Any]) -> Dict[str, Any]:
        # Zalo doesn't support product syncing, so we'll return an empty dictionary
        return {}

    def get_followers(self) -> List[Dict[str, Any]]:
        # Implement getting followers from Zalo
        response = self._make_request(self.endpoints.GET_FOLLOWERS, params={'access_token': self.access_token})
        return response.get('data', [])

    def send_message(self, user_id: str, message: str) -> Dict[str, Any]:
        # Implement sending a message to a Zalo user
        payload = {
            'recipient': {'id': user_id},
            'message': {'text': message}
        }
        response = self._make_request(self.endpoints.SEND_MESSAGE, data=payload,
                                      params={'access_token': self.access_token})
        return response

    # Implement other Zalo-specific methods here

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass