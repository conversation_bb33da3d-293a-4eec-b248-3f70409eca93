from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Optional, Any

from marshmallow import fields, INCLUDE, EXCLUDE
from nolicore.adapters.db.model import AttributesSchema, Attributes

from integrations.channels.connection_types import AuthType, ConnectionStatus
from models.integration.connection import ConnectionAttributes, ConnectionSchema, ConnectionModel


@dataclass
class OAuth2Settings(Attributes):
    redirect_url: Optional[str] = None


class OAuth2SettingsSchema(AttributesSchema):
    redirect_url = fields.Str(required=False, allow_none=True)

    class Meta:
        unknown = EXCLUDE


@dataclass
class OAuth2ConnectionAttributes(ConnectionAttributes):
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_type: Optional[str] = None
    expires_at: Optional[datetime] = None
    token_data: Optional[Dict[str, Any]] = None
    auth_type: AuthType = field(default=AuthType.OAUTH)


class OAuth2ConnectionSchema(ConnectionSchema):
    access_token = fields.Str(allow_none=True)
    refresh_token = fields.Str(allow_none=True)
    token_type = fields.Str(allow_none=True)
    expires_at = fields.DateTime(allow_none=True)
    token_data = fields.Dict(allow_none=True)


class OAuth2Connection(ConnectionModel):
    attributes: OAuth2ConnectionAttributes = None
    attributes_schema = OAuth2ConnectionSchema
    attributes_class = OAuth2ConnectionAttributes

    def get_token(self) -> Dict[str, Any]:
        """
        Get the current token information for this connection.

        :return: A dictionary containing token information
        """

        return {
            'access_token': self.attributes.access_token,
            'refresh_token': self.attributes.refresh_token,
            'expires_at': self.attributes.expires_at.isoformat() if self.attributes.expires_at else None,
            'token_type': self.attributes.token_type,
            'token_data': self.attributes.token_data
        }

    def update_token(self, external_id: str, access_token: str, expires_at: datetime, refresh_token: str,
                     token_data: Dict[str, Any]) -> None:
        """
        Update the connection's token information.

        :param external_id: External ID from the OAuth provider
        :param access_token: New access token
        :param expires_at: Expiration datetime of the access token
        :param refresh_token: New refresh token (if available)
        :param token_data: Original token data from the OAuth provider
        """
        self.attributes.external_id = external_id
        self.attributes.access_token = access_token
        self.attributes.expires_at = expires_at
        self.attributes.refresh_token = refresh_token
        self.attributes.token_data = token_data
        self.attributes.token_type = token_data.get('token_type', 'Bearer')
        self.attributes.last_token_refresh = datetime.utcnow()

        # Update the connection status
        self.attributes.status = ConnectionStatus.ACTIVE

        self.save()

    def is_token_expired(self) -> bool:
        if self.attributes.expires_at is None:
            return True
        return datetime.utcnow() > self.attributes.expires_at
