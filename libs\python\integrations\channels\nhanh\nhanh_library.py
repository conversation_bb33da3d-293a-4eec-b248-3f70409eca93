import json
import re
import uuid
from datetime import datetime, timedelta, timezone
from functools import lru_cache
from typing import Dict, Any, Optional, List, Union
from urllib.parse import quote

import pendulum
import requests
from nolicore.utils.exceptions import BadRequest
from nolicore.utils.utils import decompress, compress

from integrations.channels.action_types import ActionGroup, ActionType
from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, InventoryLocation, SyncAPIResponse,
    MetaData, PublishMetaData, StandardOrder, StandardProduct, StandardInventory, StandardReturnOrder,
    StandardPurchaseOrder, StandardVariant
)
from integrations.common.event import FetchEvent


class NhanhEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.PRODUCTS = EndpointType(type="rest", method="POST", path=f"api/product/search")
        self.PRODUCT_DETAIL = EndpointType(type="rest", method="POST", path=f"api/product/detail")
        self.ORDERS = EndpointType(type="rest", method="POST", path=f"api/order/index")
        self.BILLS = EndpointType(type="rest", method="POST", path=f"api/bill/search")
        self.DEPOTS = EndpointType(type="rest", method="POST", path=f"api/store/depot")
        self.CREATE_ORDER = EndpointType(type="rest", method="POST", path=f"api/order/add")
        self.UPDATE_ORDER = EndpointType(type="rest", method="POST", path=f"api/order/update")
        self.CUSTOMERS = EndpointType(type="rest", method="POST", path=f"api/customer/search")
        self.CATEGORIES = EndpointType(type="rest", method="POST", path=f"api/product/category")
        self.INVENTORY = EndpointType(type="rest", method="POST", path=f"api/product/inventory")


class NhanhLibrary(BaseAPILibrary):

    def __init__(self,
                 app_id: str,
                 business_id: str,
                 secret_key: str,
                 auth_url: str,
                 base_url: str = "https://open.nhanh.vn",
                 access_token: Optional[str] = None):
        super().__init__(base_url)
        self.app_id = app_id
        self.business_id = business_id
        self.secret_key = secret_key
        self.auth_url = auth_url
        self.endpoints = NhanhEndpoints(self.endpoints.base_url)
        self.target_time_zone = "Asia/Bangkok"
        self.access_token = access_token
        self.api_version = '2.0'
        self.date_time_format = 'YYYY-MM-DD HH:mm:ss'
        self.date_format = 'YYYY-MM-DD'

    def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None) -> Dict[
        str, Any]:
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }

        payload = {
            'version': self.api_version,
            'appId': self.app_id,
            'businessId': self.business_id,
            'accessToken': self.access_token
        }

        if data:
            payload['data'] = json.dumps(data)

        response = self._make_request_sync(endpoint, headers=headers, params=params, data=payload)
        if 'No records' in json.dumps(response) or 'No Products' in json.dumps(response):
            return {}
        if 'code' in response and response['code'] == 0:
            raise BadRequest(response)
        return response.get('data', {})

    def get_authorization_url(self, connection_id: str, redirect_url: str = None, base_url: str = None) -> str:
        state = self.generate_state(connection_id, redirect_url)
        return f'{self.auth_url}?version=2.0&appId={self.app_id}&returnLink={quote(self.get_callback_url(base_url, state))}'

    def fetch_token(self, access_code: str) -> Dict[str, Any]:
        headers = {
            'secret_key': self.secret_key
        }

        payload = {
            'version': self.api_version,
            'appId': self.app_id,
            'accessCode': access_code,
            'secretKey': self.secret_key
        }

        response = requests.post(f'{self.endpoints.base_url}/api/oauth/access_token', data=payload, headers=headers)
        response.raise_for_status()
        token_data = response.json()

        if 'accessToken' not in token_data:
            raise BadRequest(token_data)

        # Process the token data
        access_token = token_data['accessToken']
        expires_in = token_data.get('expiresIn', 3600)  # Default to 1 hour if not provided
        expires_at = datetime.utcnow() + timedelta(seconds=int(expires_in))
        refresh_token = token_data.get('refreshToken')
        external_id = token_data.get('businessId')  # Adjust this if Nhanh uses a different field

        # Update the library's access token
        self.access_token = access_token

        # Return the processed token data
        return {
            'external_id': external_id,
            'access_token': access_token,
            'expires_at': expires_at,
            'refresh_token': refresh_token,
            'token_data': token_data
        }

    def test_connection(self) -> bool:
        try:
            self._make_request(self.endpoints.PRODUCTS, data={"page": 1, "icpp": 1})
            return True
        except Exception:
            return False

    def get_order(self, order_id: str, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        response = self._make_request(self.endpoints.ORDERS, data={"id": order_id})

        if not response or "orders" not in response or not response["orders"]:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"Order with ID {order_id} not found"
            )

        order = self.standardize_order(next(iter(response["orders"].values())), fetch_event_id)
        return FetchAPIResponse(
            success=True,
            data=[order],
            meta=MetaData(total_count=1, page=1, limit=1)
        )

    def get_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None,
                   next_page=None, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        default_params = {
            "updatedDateTimeFrom": None,
            "updatedDateTimeTo": None,
            "page": 1,
            "icpp": 50
        }
        params = {**default_params, **(params or {})}
        updated_after = settings.get('updated_after') if settings is not None else None
        if 'fromDeliveryDate' in params or 'toDeliveryDate' in params:
            date_fields = ["fromDeliveryDate", "toDeliveryDate"]
            date_format = self.date_format
        elif 'fromDate' in params or 'toDate' in params:
            date_fields = ["fromDate", "toDate"]
            date_format = self.date_format
        else:
            date_fields = ["updatedDateTimeFrom", "updatedDateTimeTo"]
            date_format = self.date_time_format
        return self._process_paginated_request(
            endpoint=self.endpoints.ORDERS,
            params=params,
            updated_after=updated_after,
            date_fields=date_fields,
            date_format=date_format,
            response_key="orders",
            standardize_func=self.standardize_order,
            next_page=next_page,
            fetch_event_id=fetch_event_id,
            max_icpp=50
        )

    def get_offline_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None,
                           next_page=None, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        default_params = {
            "fromDate": None,
            "toDate": None,
            "page": 1,
            "icpp": 20,
            "type": 2,
            "modes": [2, 4]
        }
        params = {**default_params, **(params or {})}
        updated_after = settings.get('updated_after') if settings is not None else None
        return self._process_paginated_request(
            endpoint=self.endpoints.BILLS,
            params=params,
            updated_after=updated_after,
            date_fields=["fromDate", "toDate"],
            date_format=self.date_format,
            response_key="bill",
            standardize_func=self.standardize_order,
            next_page=next_page,
            fetch_event_id=fetch_event_id,
            max_icpp=20
        )

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None,
                            next_page=None, fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        default_params = {
            "fromDate": None,
            "toDate": None,
            "page": 1,
            "icpp": 20,
            "type": 1,
            "mode": 5
        }
        params = {**default_params, **(params or {})}
        updated_after = settings.get('updated_after') if settings is not None else None
        return self._process_paginated_request(
            endpoint=self.endpoints.BILLS,
            params=params,
            updated_after=updated_after,
            date_fields=["fromDate", "toDate"],
            date_format=self.date_format,
            response_key="bill",
            standardize_func=self.standardize_purchase_order,
            next_page=next_page,
            fetch_event_id=fetch_event_id,
            max_icpp=20
        )

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None,
                          next_page=None, fetch_event_id: str = None) -> FetchAPIResponse[StandardOrder]:
        default_params = {
            "fromDate": None,
            "toDate": None,
            "page": 1,
            "icpp": 20,
            "type": 1,
            "modes": [2, 4]
        }
        params = {**default_params, **(params or {})}
        updated_after = settings.get('updated_after') if settings is not None else None
        return self._process_paginated_request(
            endpoint=self.endpoints.BILLS,
            params=params,
            updated_after=updated_after,
            date_fields=["fromDate", "toDate"],
            date_format=self.date_format,
            response_key="bill",
            standardize_func=self.standardize_return_order,
            next_page=next_page,
            fetch_event_id=fetch_event_id,
            max_icpp=20
        )

    def get_product(self, product_id: str, fetch_event_id: str) -> FetchAPIResponse[StandardProduct]:
        response = self._make_request(self.endpoints.PRODUCT_DETAIL, data=int(product_id))

        if not response:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"Product with ID {product_id} not found"
            )

        product = self.standardize_product(response, fetch_event_id)
        return FetchAPIResponse(
            success=True,
            data=[product],
            meta=MetaData(total_count=1, page=1, limit=1)
        )

    def enrich_product(self, product):
        categories = self.get_categories()
        for product_id, _product in product.items():
            if _product['parentId'] in [-1, -2]:
                category_id = str(_product['categoryId']) if _product['categoryId'] else None
                if category_id is None:
                    continue
                _product['category'] = categories.get(category_id)
        return product

    def get_products(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                     fetch_event_id: str = None, is_fetch_inventory: bool = False) -> \
            FetchAPIResponse[StandardProduct]:
        default_params = {
            "page": 1,
            "icpp": 50,
            "updatedDateTimeFrom": None,
            "updatedDateTimeTo": None
        }
        params = {**default_params, **(params or {})}
        updated_after = settings.get('updated_after') if settings is not None else None

        return self._process_paginated_request(
            endpoint=self.endpoints.PRODUCTS,
            params=params,
            updated_after=updated_after,
            date_fields=["updatedDateTimeFrom", "updatedDateTimeTo"],
            date_format=self.date_time_format,
            response_key="products",
            standardize_func=self.extract_products if not is_fetch_inventory else self.extract_inventories,
            next_page=next_page,
            fetch_event_id=fetch_event_id,
            max_icpp=50
        )

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def extract_products(self, raw_products: Dict[str, Any], fetch_event_id: str) -> List[StandardProduct]:
        parent_ids = [product['idNhanh'] for product_id, product in raw_products.items() if
                      product.get('parentId') in [None, -1, -2, '']]
        product_dict = {}
        for parent_id in parent_ids:
            response = self._make_request(self.endpoints.PRODUCT_DETAIL, data=int(parent_id))
            product_dict[parent_id] = response
        return [self.standardize_product(products, fetch_event_id) for products in product_dict.values()]

    def extract_inventories(self, raw_products: Dict[str, Any], fetch_event_id: str) -> List[StandardProduct]:
        inventories = []
        for product_id, product_data in raw_products.items():
            if product_data.get('parentId') in [-1, -2, "", None]:
                continue
            if not product_data.get('inventory', {}).get('depots', {}):
                continue
            inventory = product_data.get('inventory', {})
            inventory['code'] = product_data['code']
            inventories.append(
                self.standardize_inventory(product_id, inventory, fetch_event_id)
            )
        return inventories

    def to_flat_category_dict(self, categories: list):
        category_dict = {}
        for category in categories:
            children = category.pop('childs', None)
            category_dict[str(category['id'])] = category
            if children:
                category_dict.update(self.to_flat_category_dict(children))
        return category_dict

    @lru_cache(maxsize=128)
    def get_categories(self):
        category_data = self._make_request(self.endpoints.CATEGORIES)
        return self.to_flat_category_dict(category_data)

    def get_locations(self, params: Dict[str, Any] = None):
        response = self._make_request(self.endpoints.DEPOTS, data=params)
        return [location for _, location in response.items()]

    def get_inventory(self,
                      product_id: Optional[str] = None,
                      product_ids: Optional[List[str]] = None,
                      settings: Dict[str, Any] = None,
                      params: Optional[Dict[str, Any]] = None,
                      fetch_event_id: str = None,
                      ) -> FetchAPIResponse[StandardInventory]:
        if product_id:
            # Case 1: Single product_id
            response = self._make_request(self.endpoints.PRODUCT_DETAIL, data=int(product_id))
            inventory = self.standardize_inventory(product_id, response, fetch_event_id)
            return FetchAPIResponse(
                success=True,
                data=[inventory],
                meta=MetaData(total_count=1, page=1, limit=1, current_params={"id": product_id})
            )

        elif product_ids:
            # Case 2: List of product_ids (batch)
            inventories = []
            for pid in product_ids:
                response = self._make_request(self.endpoints.PRODUCT_DETAIL, data=int(pid))
                if not response.get('inventory', {}).get('depots', {}):
                    continue
                inventories.append(self.standardize_inventory(pid, response, fetch_event_id))
            return FetchAPIResponse(
                success=True,
                data=inventories,
                meta=MetaData(total_count=len(inventories), page=1, limit=len(inventories),
                              current_params={"ids": product_ids})
            )

        else:
            response = self.get_products(params=params, settings=settings, next_page=None,
                                         fetch_event_id=fetch_event_id, is_fetch_inventory=True)

            current_page = int(response.meta.page or 0)
            total_pages = int(response.meta.total_page or 0)
            total_count = int(response.meta.total_count or 0)
            limit = int(response.meta.limit or 0)

            return FetchAPIResponse(
                success=True,
                data=response.data,
                meta=MetaData(
                    total_count=total_count,
                    page=current_page,
                    limit=limit,
                    total_page=total_pages,
                    continuation_token=str(current_page + 1) if current_page < total_pages else None,
                    current_params=params
                )
            )

    def sync_order(self, order_data: Dict[str, Any], settings: Dict[str, Any] = None) -> \
            SyncAPIResponse[StandardOrder]:
        try:
            remote_order = self.get_order_by_mobile(order_data['customerMobile'], order_data['id'])

            if order_data.get('status') == 'uncancelled':
                order_data.pop('status', None)
            elif order_data.get('status') == 'cancelled':
                order_data['status'] = 'Canceled'

            if remote_order:
                response = self._make_request(self.endpoints.UPDATE_ORDER,
                                              data={"orderId": remote_order['id'], **order_data})
            else:
                response = self._make_request(self.endpoints.CREATE_ORDER, data=order_data)

            synced_order = self.standardize_order(response)
            return SyncAPIResponse(
                success=True,
                data=synced_order,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Order {synced_order.order_number} synced successfully"
            )
        except Exception as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to sync order: {str(e)}"
            )

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        total_price = raw_order.get("calcTotalMoney") or raw_order.get("money")
        created_at = pendulum.from_format(raw_order['createdDateTime'], self.date_time_format, tz="Asia/Bangkok")
        return StandardOrder(
            fetch_event_id=fetch_event_id,
            id=str(raw_order.get("shopOrderId") or raw_order.get("id")),
            order_number=raw_order["id"],
            total_price=float(total_price) if total_price else 0,
            currency="VND",
            status=raw_order.get("statusCode", ""),
            created_at=created_at,
            updated_at=created_at,
            raw_data=raw_order
        )

    def standardize_return_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardReturnOrder:
        total_price = raw_order.get("calcTotalMoney") or raw_order.get("money")
        created_at = pendulum.from_format(raw_order['createdDateTime'], self.date_time_format, tz="Asia/Bangkok")

        return StandardReturnOrder(
            fetch_event_id=fetch_event_id,
            id=str(raw_order.get("shopOrderId") or raw_order.get("id")),
            order_number=raw_order["id"],
            total_price=float(total_price) if total_price else 0,
            currency="VND",
            status=raw_order.get("statusCode", ""),
            created_at=created_at,
            updated_at=created_at,
            raw_data=raw_order
        )

    def get_order_by_mobile(self, customer_mobile: str, order_id: str) -> Optional[Dict[str, Any]]:
        try:
            response = self._make_request(self.endpoints.ORDERS, data={"customerMobile": customer_mobile})
            orders = response.get("orders", {})
            return next(
                (order for order in orders.values() if order['shopOrderId'] and order['shopOrderId'] in order_id), None)
        except Exception:
            return None

    def _format_date_params(self, params: Dict[str, Any], date_fields: List[str], format_type: str) -> Dict[str, Any]:
        for field in date_fields:
            if params[field]:
                params[field] = pendulum.parse(params[field]).in_timezone(self.target_time_zone).format(format_type)
        return params

    @staticmethod
    def _encode_state(connection_id: str, company_id: str) -> str:
        import base64
        return base64.b64encode(json.dumps({'id': connection_id, 'company_id': company_id}).encode()).decode()

    @staticmethod
    def verify_webhook_signature(payload: str, signature: str, secret: str) -> bool:
        # Implement Nhanh's webhook signature verification method here
        return True

    @staticmethod
    def extract_merchant_id(payload: Dict[str, Any]) -> str:
        return payload.get('businessId')

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return payload.get('event') or headers.get('X-Nhanh-Event')

    def sync_product(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None, fetch_event_id: str = None) -> \
            SyncAPIResponse[
                StandardProduct]:
        # Implement product sync if Nhanh API supports it
        raise NotImplementedError("Product sync is not implemented for Nhanh")

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> FetchEvent:
        event_type = payload.get('event')
        current_time = pendulum.now().to_iso8601_string()

        if event_type in ['orderUpdate', 'orderAdd']:
            order_data = payload.get('data', {})
            object_id = str(order_data.get('orderId', ''))

            return FetchEvent.create_single_object_event(
                connection_id=str(uuid.uuid4()),
                channel="nhanh",
                action_type=ActionType.get_order,
                action_group=ActionGroup.order,
                event_time=current_time,
                object_id=object_id,
                object_data=order_data
            )

        elif event_type == 'inventoryChange':
            inventory_data = payload.get('data', {})
            object_ids = list(inventory_data.keys())
            return FetchEvent.create_batch_object_event(
                connection_id=str(uuid.uuid4()),
                channel="nhanh",
                action_type=ActionType.get_inventory,
                action_group=ActionGroup.inventory,
                event_time=current_time,
                object_ids=object_ids,
                object_data=inventory_data
            )
        elif event_type in ['productUpdate', 'productAdd']:
            product_data = payload.get('data', {})
            object_id = str(
                product_data['productId'] if not product_data.get('parentId') or product_data['parentId'] in [-1,
                                                                                                              -2] else
                product_data['parentId'])
            return FetchEvent.create_single_object_event(
                connection_id=str(uuid.uuid4()),
                channel="nhanh",
                action_type=ActionType.get_product,
                action_group=ActionGroup.product,
                event_time=current_time,
                object_id=object_id,
                object_data=product_data
            )

        else:
            # For unknown event types, return generic information
            return FetchEvent(
                channel="nhanh",
                connection_id=str(uuid.uuid4()),
                action_type=ActionType.webhook,
                action_group=ActionGroup.webhook,
                event_time=current_time,
                is_batch=False,
                object_data=payload
            )

    def webhook(self, payload: Dict[str, Any], headers: Dict[str, str]) -> FetchEvent:
        event = self.extract_object_details(payload)
        # Note: FetchEvent doesn't have merchant_id or event_type attributes
        # You might need to add these to the object_data if needed
        return event

    @staticmethod
    def add_prefix(token):
        prefix = "continuation_token_"
        return token if token.startswith(prefix) else prefix + token

    @staticmethod
    def remove_prefix(token):
        return re.sub(r"^continuation_token_", "", token)

    @staticmethod
    def get_continuation_token(
            current_page: int,
            total_pages: int,
            current_params: Dict[str, Any],
            date_fields: List[str],
            init_end_date: str = None,
            date_format: str = "YYYY-MM-DD",
            target_time_zone: str = "Asia/Bangkok"
    ) -> Optional[str]:
        """
        Generate a continuation token for pagination with date range support.

        Args:
            current_page: Current page number
            total_pages: Total number of pages
            current_params: Dictionary containing current date range parameters
            date_fields: Tuple of (start_date_field, end_date_field) names
            init_end_date: Optional maximum end date
            date_format: Date format string (default: "YYYY-MM-DD")
            target_time_zone: Target timezone for date formatting

        Returns:
            Compressed continuation token or None if no more pages
        """

        # If there are more pages in current date range, continue pagination
        if current_page < total_pages:
            return compress({
                "actual_page": current_page + 1,
                "current_start_date": pendulum.parse(current_params[date_fields[0]],
                                                     tz=target_time_zone).to_iso8601_string() if current_params[
                    date_fields[0]] else None,
                "current_end_date": pendulum.parse(current_params[date_fields[1]],
                                                   tz=target_time_zone).to_iso8601_string() if current_params[
                    date_fields[1]] else None
            })

        # If no init_end_date specified, we're done
        if not init_end_date:
            return None

        # Check if we need to move to next date range
        end_date = pendulum.parse(current_params[date_fields[1]], tz=target_time_zone)
        max_end_date = pendulum.parse(init_end_date)

        if max_end_date <= end_date:
            return None

        # Calculate next date range
        offset_days = 0 if date_format == "YYYY-MM-DD HH:mm:ss" else 1
        next_start_date = end_date.add(days=offset_days)
        next_end_date = min(
            next_start_date.add(days=10),
            max_end_date
        )

        # Return None if next start date exceeds the end date
        if next_start_date > next_end_date:
            return None

        # Generate token for next date range
        return compress({
            "actual_page": 1,
            "current_start_date": next_start_date.to_iso8601_string(),
            "current_end_date": next_end_date.to_iso8601_string()
        })

    def _process_paginated_request(
            self,
            endpoint: EndpointType,
            params: Dict[str, Any],
            date_fields: List[str],
            date_format: str,
            response_key: str,
            standardize_func,
            next_page: Union[Dict[str, Any], str, None] = None,
            updated_after: str = None,
            fetch_event_id: str = None,
            max_icpp: int = 50
    ) -> FetchAPIResponse:
        """
        Hàm xử lý chung cho các request có phân trang và giới hạn ngày
        :param endpoint: Endpoint để gọi API
        :param params: Params của request
        :param date_fields: List các trường ngày cần format
        :param date_format: Format của ngày
        :param updated_after: Thời gian update gần nhất
        :param response_key: Key để lấy dữ liệu từ response
        :param standardize_func: Hàm để chuẩn hóa dữ liệu
        :param next_page: param của trang tiếp theo
        :param fetch_event_id: ID của fetch event
        :param max_icpp: Giới hạn tối đa của icpp
        """
        # Xử lý icpp
        params['icpp'] = int(params.get('icpp', max_icpp))
        if params['icpp'] > 0:
            params['icpp'] = min(params['icpp'], max_icpp)
        else:
            params['icpp'] = max(params['icpp'], max_icpp)

        current_params = params.copy()

        if next_page:
            try:
                next_params = decompress(self.remove_prefix(next_page))
                current_params['page'] = int(next_params.get('actual_page', 1))
                current_start_date = next_params.get('current_start_date')
                current_end_date = next_params.get('current_end_date')
                if current_start_date and current_end_date:
                    current_params[date_fields[0]] = current_start_date
                    current_params[date_fields[1]] = current_end_date
            except Exception as e:
                raise BadRequest(f"continuation_token must be dict and compress as string")
        else:
            init_start_date = current_params[date_fields[0]]
            init_end_date = current_params[date_fields[1]]

            if not init_start_date and updated_after:
                updated_after = pendulum.parse(updated_after).format(date_format)
                current_params[date_fields[0]] = updated_after
                init_start_date = updated_after

            if not init_end_date:
                now = pendulum.now().format(date_format)
                current_params[date_fields[1]] = now
                init_end_date = now
            if init_start_date and init_end_date:
                current_start_date = pendulum.parse(init_start_date)
                current_end_date = pendulum.parse(init_end_date)
                if current_start_date > current_end_date:
                    raise BadRequest(f"{date_fields[0]} must less than {date_fields[1]}")
                if response_key not in ["products"]:
                    days_diff = current_start_date.diff(current_end_date).in_days()
                    if days_diff > 10:
                        offset_day = 10 if date_format == self.date_format else 9
                        current_params[date_fields[1]] = min(current_start_date.add(days=offset_day),
                                                             current_end_date).to_iso8601_string()
        current_params = self._format_date_params(current_params, date_fields, date_format)
        try:
            response = self._make_request(endpoint, data=current_params)
            current_page = int(response.get('page') or response.get('currentPage') or 1)
            total_pages = int(response.get('totalPages', 1))
            total_records = int(response.get('totalRecords', 0))
            if not response or response_key not in response or not response[response_key]:
                return FetchAPIResponse(
                    success=True,
                    data=None,
                    meta=MetaData(
                        total_count=total_records,
                        total_page=total_pages,
                        page=current_page,
                        limit=current_params['icpp'],
                        current_params=params
                    ),
                )
            if response_key in ["products"]:
                items = standardize_func(response.get(response_key, {}), fetch_event_id)
            else:
                items = [
                    standardize_func(item, fetch_event_id)
                    for item in response.get(response_key, {}).values()
                ]

            continuation_token = self.get_continuation_token(current_page=current_page,
                                                             total_pages=total_pages,
                                                             current_params=current_params,
                                                             date_fields=date_fields,
                                                             init_end_date=params[date_fields[1]],
                                                             date_format=date_format,
                                                             target_time_zone=self.target_time_zone)
            return FetchAPIResponse(
                success=True,
                data=items,
                meta=MetaData(
                    total_count=total_records,
                    total_page=total_pages,
                    page=current_page,
                    limit=current_params['icpp'],
                    continuation_token=self.add_prefix(continuation_token) if continuation_token is not None else None,
                    current_params=params
                )
            )
        except Exception as e:
            return FetchAPIResponse(
                success=False,
                data=None,
                meta=MetaData(total_count=0, page=1, limit=1),
                message=f"{e}"
            )

    def standardize_inventory(self, product_id: str, raw_inventory: Dict[str, Any],
                              fetch_event_id: str = None) -> StandardInventory:
        locations = []
        depots = raw_inventory.get('depots', {})
        if isinstance(depots, list):
            depots = {}
        for depot_id, depot_data in depots.items():
            locations.append(InventoryLocation(
                location_id=str(depot_id),
                location_name=f"Depot {depot_id}",  # You might want to fetch actual depot names if available
                quantity=int(depot_data.get('remain', 0)),
                available_quantity=int(depot_data.get('available', 0))
            ))

        updated_at = datetime.now(timezone.utc)

        return StandardInventory(
            fetch_event_id=fetch_event_id,
            id=str(product_id),
            locations=locations,
            created_at=updated_at,  # Nhanh doesn't provide a created timestamp, so we use current time
            updated_at=updated_at,  # Nhanh doesn't provide an updated timestamp, so we use current time
            raw_data=raw_inventory
        )

    def standardize_purchase_order(self, raw_purchase_order: Dict[str, Any],
                                   fetch_event_id: str) -> StandardPurchaseOrder:
        total_price = raw_purchase_order.get("calcTotalMoney") or raw_purchase_order.get("money")
        created_at = pendulum.from_format(raw_purchase_order['createdDateTime'], self.date_time_format,
                                          tz="Asia/Bangkok")
        return StandardPurchaseOrder(
            fetch_event_id=fetch_event_id,
            id=str(raw_purchase_order.get("shopOrderId") or raw_purchase_order.get("id")),
            order_number=raw_purchase_order["id"],
            total_price=float(total_price) if total_price else 0,
            currency="VND",
            status=raw_purchase_order.get("statusCode", ""),
            updated_at=created_at,
            created_at=created_at,
            raw_data=raw_purchase_order
        )

    def standardize_variant(self, raw_variant: Dict[str, Any]) -> StandardVariant:
        return StandardVariant(
            id=raw_variant['idNhanh'],
            title=raw_variant['name'],
            sku=raw_variant['code'],
            price=raw_variant['price'],
            image=raw_variant['image']
        )

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None,
                            is_product_inventory: bool = False) -> StandardProduct:
        enrich_data = self.enrich_product(raw_product)
        product_data = None
        for item_id, item_data in enrich_data.items():
            if item_data.get('parentId') in [None, -1, -2, '']:
                product_data = item_data
                break
        if not product_data:
            raise ValueError("No product-level data found in raw_product")
        variants = [self.standardize_variant(variant) for variant in enrich_data.values()]
        created_at = pendulum.from_format(product_data['createdDateTime'], self.date_time_format, tz="Asia/Bangkok")
        return StandardProduct(
            fetch_event_id=fetch_event_id,
            id=str(product_data['idNhanh']),
            title=product_data['name'],
            sku=product_data['code'],
            variants=variants,
            created_at=created_at,
            updated_at=created_at,
            raw_data=enrich_data
        )

    @staticmethod
    def get_order_params():
        return FetchAPIResponse(
            success=True,
            data={
                'page': 'Number',
                'fromDate': 'DateTime|ISO',
                'toDate': 'DateTime|ISO',
                'id': 'String',
                'customerMobile': 'String',
                'customerId': 'Number',
                'statuses': 'Array',
                'fromDeliveryDate': 'DateTime|ISO',
                'toDeliveryDate': 'DateTime|ISO',
                'carrierId': 'Number',
                'carrierCode': 'String',
                'type': 'Number',
                'customerCityId': 'Number',
                'customerDistrictId': 'Number',
                'handoverId': 'Number',
                'depotId': 'Number',
                'updatedDateTimeFrom': 'DateTime|ISO',
                'updatedDateTimeTo': 'DateTime|ISO',
                'dataOptions': 'Array',
            },
            meta=None
        )

    @staticmethod
    def get_bill_params():
        return FetchAPIResponse(
            success=True,
            data={
                'page': 'Number',
                'icpp': 'Number',
                'id': 'String',
                'depotId': 'Number',
                'customerId': 'Number',
                'customerMobile': 'String',
                'fromDate': 'DateTime|ISO',
                'toDate': 'DateTime|ISO',
                'dataOptions': 'Array',
            },
            meta=None
        )

    @staticmethod
    def get_product_params():
        return FetchAPIResponse(
            success=True,
            data={
                'page': 'Number',
                'icpp': 'Number',
                'name': 'String',
                'parentId': 'Number',
                'categoryId': 'Number',
                'brandId': 'Number',
                'status': 'String',
                'priceFrom': 'Number',
                'priceTo': 'Number',
                'imei': 'String',
                'showHot': 'Number',
                'showNew': 'Number',
                'showHome': 'Number',
                'updatedDateTimeFrom': 'DateTime|ISO',
                'updatedDateTimeTo': 'DateTime|ISO',
            },
            meta=None
        )

    @staticmethod
    def get_inventory_params():
        return FetchAPIResponse(
            success=True,
            data={
                'page': 'Number',
                'icpp': 'Number',
                'name': 'String',
                'parentId': 'Number',
                'categoryId': 'Number',
                'brandId': 'Number',
                'status': 'String',
                'priceFrom': 'Number',
                'priceTo': 'Number',
                'imei': 'String',
                'showHot': 'Number',
                'showNew': 'Number',
                'showHome': 'Number',
                'updatedDateTimeFrom': 'DateTime|ISO',
                'updatedDateTimeTo': 'DateTime|ISO',
            },
            meta=None
        )
