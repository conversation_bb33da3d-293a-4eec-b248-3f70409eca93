import json
import unittest
import uuid

from tests.base import BaseTestCase
from dotenv import load_dotenv

from flows.src.apis.connection import get_destination_data, get_details_destination_data, search_records, \
    list_sync_records, get_sync_records, \
    list_fetch_events, sync_records, get_fetch_events, import_records_clone, import_destination_data_handle


class TestChannelWebhookApiTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.period = 'custom'
        cls.date_from = '2024-01-01'
        cls.date_to = '2025-01-01'
        cls.fetch_event_id = '26a619f3-1245-4d28-b3a8-b012eaaa2398'
        if not cls.period:
            raise ValueError("Invalid period")
        if cls.period == 'custom':
            if not all([cls.period, cls.date_from, cls.date_to]):
                raise ValueError("period/date_from/date_to not found in environment variables")

    def setUp(self):
        super().setUp(account_type="onexapis_admin")

    def test_search_records(self):
        body = {
            "action_type": "get_inventory",
            "params": {
                "updatedDateTimeFrom": "2025-05-25T17:00:00.000Z",
                "updatedDateTimeTo": "2025-05-27T17:00:00.000Z"
            }
        }

        event = self.create_lambda_event(
            path_params={'connection_id': '6b5ca363-434b-4127-8258-3bbf1afc5e51'},
            body=body,
            is_authorized=True
        )
        context = self.create_lambda_context()

        # Act
        response = search_records(event, context)
        self.assertEqual(response['statusCode'], 200)

    def test_list_sync_records(self):
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to}
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = list_sync_records(event, context)
        self.assertEqual(response['statusCode'], 200)

    def test_get_sync_record(self):
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to, "is_source": False}
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = get_sync_records(event, context)
        self.assertEqual(response['statusCode'], 200)

    def test_list_fetch_events(self):
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to}
        event = self.create_lambda_event(event_body, path_params={"fetch_event_id": self.fetch_event_id})
        context = self.create_lambda_context()

        # Act
        response = list_fetch_events(event, context)
        self.assertEqual(response['statusCode'], 200)

    def test_sync_records(self):
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to}
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = sync_records(event, context)
        self.assertEqual(response['statusCode'], 200)

    def test_get_fetch_events(self):
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to}
        event = self.create_lambda_event(event_body, path_params={"fetch_event_id": self.fetch_event_id})
        context = self.create_lambda_context()

        # Act
        response = get_fetch_events(event, context)
        self.assertEqual(response['statusCode'], 200)

    def test_import_records_clone(self):
        event_body = {'version': '2.0', 'routeKey': 'POST /connections/{connection_id}/import_records_clone', 'rawPath': '/connections/764b8784-c48f-4378-80ec-f0085c20f13b/import_records_clone', 'rawQueryString': '', 'headers': {'accept': '*/*', 'accept-encoding': 'gzip, deflate, br', 'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Di5ePoORkOW2fj3wfyfYHWGHrptQY6jXcEwUrhee3QYNULkgzADIm-4nesubiXZ-LH0tjWvAECnXEYprSW2eFZiiZ9jNsOAgbmANAqYuiqoVFZ5Tjb5Wzor1eh9Hlxp4FU1XBjpJ7tltCTDwE9tiUpHjf1xDqBHjIGG89u9JiGjBA2VBP_mll3WC_xXM8juTVMqE3YPIJt6zy4MLaqtPl_tBhvw7YuX-XmmLFpm4DALr_Hu4H-E1khVbtnxWzxCkDKdjPZBJ5w9IgCJXmbh3eaNx7dTER0RaA9IlyO7C3iwmdwGwQapzXEnccuTr607de-Tz0PZGkzEMHfiUugvC2g', 'content-length': '57', 'content-type': 'application/json', 'host': 'api-staging.optiwarehouse.com', 'postman-token': 'd0373bbb-cd07-429a-9210-7eb99df967c1', 'user-agent': 'PostmanRuntime/7.43.3', 'x-amzn-trace-id': 'Root=1-680a4d13-2a37483f734194fa39014888', 'x-forwarded-for': '***********', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https'}, 'requestContext': {'accountId': '************', 'apiId': 'gojzx6aljc', 'authorizer': {'jwt': {'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********', 'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]', 'cognito:username': 'onexapis_admin', 'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE', 'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>', 'event_id': '********-cf26-4672-8fbb-517f561c268a', 'exp': '**********', 'iat': '**********', 'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H', 'jti': '7470cfa3-3c25-47b6-904e-b31f901d04ab', 'origin_jti': 'b9caf7a6-f1ab-4e39-bd4f-21c182db4485', 'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}}, 'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging', 'http': {'method': 'POST', 'path': '/connections/764b8784-c48f-4378-80ec-f0085c20f13b/import_records_clone', 'protocol': 'HTTP/1.1', 'sourceIp': '***********', 'userAgent': 'PostmanRuntime/7.43.3'}, 'requestId': 'JiD7Hg0CyQ0EP9Q=', 'routeKey': 'POST /connections/{connection_id}/import_records_clone', 'stage': '$default', 'time': '24/Apr/2025:14:39:15 +0000', 'timeEpoch': 1745505555557}, 'pathParameters': {'connection_id': '764b8784-c48f-4378-80ec-f0085c20f13b'}, 'body': '{\r\n    "action_type": "get_product",\r\n    "params": {}\r\n}', 'isBase64Encoded': False}
        context = self.create_lambda_context()
        response = import_records_clone(event_body, context)

    def test_import_product(self):
        import_event = {'company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'connection_id': '764b8784-c48f-4378-80ec-f0085c20f13b', 'request_id': '95d8103e-8012-4546-87d0-1e507b60b724', 'start': 0, 'type': 'product'}
        response = import_destination_data_handle(import_event, {})
        self.assertTrue(True)
        
    def test_get_destination_data(self):
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to}
        event = self.create_lambda_event(body= event_body, path_params={
            "type": "product",
            "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
        })
        context = self.create_lambda_context()

        # Act
        response = get_destination_data(event, context)
        self.assertEqual(response['statusCode'], 200)

    def test_get_details_destination_data(self):
        event_body = {"period": self.period, "date_from": self.date_from, "date_to": self.date_to}
        event = self.create_lambda_event(body=event_body, path_params={
            "group": "product",
            "connection_id": "764b8784-c48f-4378-80ec-f0085c20f13b",
            "destination_data_id": '1731143367632324190'
        })
        context = self.create_lambda_context()

        # Act
        response = get_details_destination_data(event, context)
        self.assertEqual(response['statusCode'], 200)

if __name__ == '__main__':
    unittest.main()
