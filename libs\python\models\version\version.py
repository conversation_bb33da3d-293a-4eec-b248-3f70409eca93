from dataclasses import dataclass

from marshmallow import fields

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


@dataclass
class VersionAttributes(BasicAttributes):
    version: str = None
    data: dict = None


class VersionAttributesSchema(BasicAttributesSchema):
    version = fields.Str(required=True)
    data = fields.Dict(allow_none=True)


class VersionModel(BasicModel):
    key__id = 'company_id'
    table_name = 'version'
    attributes: VersionAttributes
    attributes_schema = VersionAttributesSchema
    attributes_class = VersionAttributes

    query_mapping = {
        'query': ['id', 'company_id', 'version'],
        'filter': {
            'id': 'query',
            'version': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }
