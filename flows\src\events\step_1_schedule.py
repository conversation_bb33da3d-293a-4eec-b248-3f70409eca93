from datetime import datetime
from typing import Dict, Any, List

import pendulum
from nolicore.utils.aws.boto_helper import get_client
from nolicore.utils.aws.decorator import invoke
from nolicore.utils.aws.request import LambdaRequest
from nolicore.utils.utils import json_dumps

from integrations.common.base_api_library import MetaData
from integrations.common.connection_helper import get_connection_instance
from integrations.common.event import FetchEvent
from integrations.common.queue_helper import enqueue_fetch_event
# Assume these are defined elsewhere or imported
from models.integration.connection import (
    ConnectionModel, ActionType, ACTION_GROUP_MAPPING
)

sqs = get_client('sqs')


def get_due_actions(connection: ConnectionModel) -> List[Dict[str, Any]]:
    """Get all actions that are due to run for a connection."""
    now = pendulum.now()
    due_actions = []

    for action_type in ActionType:
        if connection.should_fetch(action_type, now):
            action_group = ACTION_GROUP_MAPPING.get(action_type)
            fetch_settings = connection.get_fetch_action_settings(action_group, action_type)
            if fetch_settings:
                custom_settings = fetch_settings.custom_settings
                due_action = {
                    'action_type': action_type,
                    'action_group': action_group,
                    'settings': fetch_settings,
                    'custom_settings': custom_settings
                }
                due_actions.append(due_action)
    return due_actions


def update_action_schedule(connection: ConnectionModel, action_type: ActionType, run_time: datetime):
    """Update the schedule for a specific action."""
    connection.update_fetch_action_status(action_type, run_time)


def process_connection(connection: ConnectionModel):
    """Process a single connection, enqueueing events for due actions and updating schedules."""
    due_actions = get_due_actions(connection)
    now = pendulum.now()

    for action in due_actions:
        updated_after = action['settings'].status.last_run.isoformat() if action['settings'].status.last_run else None
        meta = MetaData(
            total_count=0,
            page=1,
            limit=action['custom_settings'].get('limit', 50),
            current_params={**action['custom_settings'],
                            'updated_after': updated_after}
        )
        event = FetchEvent(
            channel=connection.attributes.channel_name,
            connection_id=connection.attributes.id,
            action_type=action['action_type'],
            action_group=action['action_group'],
            event_time=now.to_iso8601_string(),
            is_batch=False,
            meta=meta
        )

        #   WARNING: if updated_after is None and schedule handler will execute all previous data
        if updated_after is not None:
            enqueue_fetch_event(event, connection.attributes.company_id)
        update_action_schedule(connection, action['action_type'], now)

    if due_actions:
        connection.save()  # Save the updated connection with new schedule information


@invoke()
def schedule_handler(lambda_request: LambdaRequest):
    """Main handler function for the scheduler."""
    connections = ConnectionModel.get_all_active_connections()

    for connection in connections:
        connection_model = get_connection_instance(connection)
        process_connection(connection_model)

    return {
        'statusCode': 200,
        'body': json_dumps('Scheduler run completed')
    }
