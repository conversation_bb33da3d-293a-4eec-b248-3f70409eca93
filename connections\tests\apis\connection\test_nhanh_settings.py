import unittest

from tests.base import BaseTestCase
from connections.src.apis.setting import set_fetch_action_settings


class NhanhConnectionSetupIntegrationTestCase(BaseTestCase):
    def test_settings(self):
        event = {'version': '2.0', 'routeKey': 'POST /connection/{connection_id}/fetch_action_settings',
                 'rawPath': '/connection/d4af2ad0-7b37-476c-862f-3bd9c32e1b6a/fetch_action_settings',
                 'rawQueryString': 'access_token=eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dfpFMHbSdrjZ_j2jt6C9huwRJNY-5_Ex5JVdd0lAt6h1cuBsOMlflVtOGJk7vn3Ap3noGukeDEKqXMlPo1JPsPR3ghweJDTn5Z4h8JMH2y0NShpLrfhDmqXZBCdO7ugH9_uzjdkeP3w-cndwED1r3z8vRugB3DHQMjLg4YSGnwKN7Z-i1Sdny1zq5pDfYG7jQXu29rK2LXqUNEg2WNeBNhR59IHguZf9oh6MIeazzSAH6dSFaT2P2BubuiylmhZ81XiyF0NDQTBfzeIn9o-LSg7cHtN53GQe2AQJ9lbpi_0ePbkKC16vGq1cXNDkNQI_1BDVRTtheBlYXLXVo1BmPw',
                 'headers': {'accept': 'application/json, text/plain, */*',
                             'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en,vi;q=0.9,en-US;q=0.8',
                             'authorization': 'Bearer eyJraWQiOiIxb2JOTXRtYml1dzlBcFhDYkZ5aU1EVTJkb0pDbkh4NG9hVm5hZ1haaGdnPSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1QK0g8LZh08ygWyII-UpgPXcQHZYVgMcUM-TXcjHuX0-elEfgrY6soxKR2nPlQhke4twsnLhxF3FS17nFF_NrKIhcHomS4vRFnkQ4jyBF_sJZx4QpdXVOy6D6R3jTARR4QAY9ajURhuOOgCvMWTpYKTnFYhUoNTGnQPiXvs_OouhHcKZ3ZfkXBuwyWAQw63IfGhLnscEKo4gagoRmJrDoxYc1bFkTnUrrKSrbXuGptx0Ga0S0B4fUdHHBtGV56ZlFV2p5L_6owqEXAN8dv7eMPxf05M6uNbil8Huji5sfv48_FbVzZNGLQ87EhFcYaDZepZ3-CJsWIEiuim04VgexQ',
                             'content-length': '682', 'content-type': 'application/json',
                             'host': 'api-staging.optiwarehouse.com', 'origin': 'https://staging.optiwarehouse.com',
                             'priority': 'u=1, i', 'referer': 'https://staging.optiwarehouse.com/',
                             'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
                             'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Windows"', 'sec-fetch-dest': 'empty',
                             'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site',
                             'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
                             'x-amzn-trace-id': 'Root=1-671a6315-3837d5ef4ac8fa34093578a0',
                             'x-forwarded-for': '*************', 'x-forwarded-port': '443',
                             'x-forwarded-proto': 'https'}, 'queryStringParameters': {
                'access_token': 'eyJraWQiOiJ4WGhMeHREQTNUM2lGenVUb0szc0NjeU9MOHoxaktJVG1PN2N1Umt1bUxFPSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dfpFMHbSdrjZ_j2jt6C9huwRJNY-5_Ex5JVdd0lAt6h1cuBsOMlflVtOGJk7vn3Ap3noGukeDEKqXMlPo1JPsPR3ghweJDTn5Z4h8JMH2y0NShpLrfhDmqXZBCdO7ugH9_uzjdkeP3w-cndwED1r3z8vRugB3DHQMjLg4YSGnwKN7Z-i1Sdny1zq5pDfYG7jQXu29rK2LXqUNEg2WNeBNhR59IHguZf9oh6MIeazzSAH6dSFaT2P2BubuiylmhZ81XiyF0NDQTBfzeIn9o-LSg7cHtN53GQe2AQJ9lbpi_0ePbkKC16vGq1cXNDkNQI_1BDVRTtheBlYXLXVo1BmPw'},
                 'requestContext': {'accountId': '************', 'apiId': '5imfncgkzk', 'authorizer': {'jwt': {
                     'claims': {'aud': '3fh2s28e3g8l23068d6j573l9a', 'auth_time': '**********',
                                'cognito:groups': '[9e61d187-426a-45ec-914d-7aea8ca7d42d-Customer 9e61d187-426a-45ec-914d-7aea8ca7d42d-Admin 9e61d187-426a-45ec-914d-7aea8ca7d42d-User]',
                                'cognito:username': 'onexapis_admin',
                                'custom:company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'custom:is_init': 'DONE',
                                'custom:plan': 'OMNICHANNEL', 'custom:role': 'Admin', 'email': '<EMAIL>',
                                'event_id': 'a799cb01-e6fc-4d3a-a904-cf497bb03052', 'exp': '**********',
                                'iat': '**********',
                                'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_Tmn6Tbm0H',
                                'jti': '08a2994d-3394-4d26-bbe3-15b4f744ac18',
                                'origin_jti': 'ad9d8b60-98f1-4bf6-836a-00cebff7c1a2',
                                'sub': '906ed4d6-26f9-4056-8f3e-ae00a37c3edb', 'token_use': 'id'}, 'scopes': None}},
                                    'domainName': 'api-staging.optiwarehouse.com', 'domainPrefix': 'api-staging',
                                    'http': {'method': 'POST',
                                             'path': '/connection/d4af2ad0-7b37-476c-862f-3bd9c32e1b6a/fetch_action_settings',
                                             'protocol': 'HTTP/1.1', 'sourceIp': '*************',
                                             'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36'},
                                    'requestId': 'AKRrcjd7SQ0EMyQ=',
                                    'routeKey': 'POST /connection/{connection_id}/fetch_action_settings',
                                    'stage': '$default', 'time': '24/Oct/2024:15:09:09 +0000',
                                    'timeEpoch': 1729782549640},
                 'pathParameters': {'connection_id': 'd4af2ad0-7b37-476c-862f-3bd9c32e1b6a'},
                 'body': '{"actions":{"get_product":{"schedule":{"type":"interval","value":"3600"},"enabled":false,"retry_settings":{"max_retries":"3","retry_delay":"5","retry_backoff":"2"},"response_mapping":{},"custom_settings":{"page":"1","icpp":"50","updatedDateTimeTo":null,"updatedDateTimeFrom":null},"rate_limit":60,"status":{"last_run":"2024-10-24T08:38:53.115157","next_run":"2024-10-24T09:38:53.115157"}},"get_order":{"enabled":true,"retry_settings":{"max_retries":"3","retry_delay":"5","retry_backoff":"2"},"rate_limit":60,"custom_settings":{"fromDate":null,"toDate":null,"page":"1"},"status":{"last_run":null,"next_run":null},"response_mapping":{},"schedule":{"type":"interval","value":"3600"}}}}',
                 'isBase64Encoded': False}

        response = set_fetch_action_settings(event, {})


if __name__ == '__main__':
    unittest.main()
