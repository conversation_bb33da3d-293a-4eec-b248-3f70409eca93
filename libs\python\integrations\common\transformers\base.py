from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

from nolicore.utils.utils import logger, json_dumps

from helpers.custom_jsonpath import get_value_from_jsonpath, set_value_using_jsonpath
from helpers.transformation.handle_transform import process_transformations
from integrations.channels.action_types import ActionGroup, ActionGroupSettings


class BaseTransformer(ABC):
    def __init__(self, connection_settings: Dict[str, Any]):
        self.connection_settings = connection_settings
        self.dynamic_settings = connection_settings.get('dynamic_settings', {})
        self.action_group_settings: Optional[ActionGroupSettings] = None
        self.custom_settings: Optional[Dict[str, Any]] = None
        self.custom_mappings: Optional[Dict[str, Any]] = None
        self._set_action_group_settings()
        self._set_custom_settings()
        self._set_custom_mappings()

    def _set_action_group_settings(self):
        action_groups = self.connection_settings.get('action_groups', {})
        for group, settings in action_groups.items():
            if group == self.action_group:
                self.action_group_settings = ActionGroupSettings(**settings)
                break

    def _set_custom_settings(self):
        if isinstance(self, SourceTransformer):
            self.custom_settings = self._get_fetch_action_settings()
        elif isinstance(self, DestinationTransformer):
            self.custom_settings = self._get_publish_action_settings()

    def _set_custom_mappings(self):
        action_type = self._map_object_type_to_action_type()
        if isinstance(self, SourceTransformer) and self.action_group_settings:
            action_settings = self.action_group_settings.fetch_actions.get(action_type, {})
        elif isinstance(self, DestinationTransformer) and self.action_group_settings:
            action_settings = self.action_group_settings.publish_actions.get(action_type, {})
        else:
            action_settings = {}

        self.custom_mappings = action_settings.get('field_mapping', {})

    def _get_fetch_action_settings(self) -> Optional[Dict[str, Any]]:
        if self.action_group_settings and self.action_group_settings.fetch_actions:
            action_type = self._map_object_type_to_action_type()
            fetch_action = self.action_group_settings.fetch_actions.get(action_type)
            if fetch_action:
                return fetch_action if isinstance(fetch_action, dict) else fetch_action.dict()
        return None

    def _get_publish_action_settings(self) -> Optional[Dict[str, Any]]:
        if self.action_group_settings and self.action_group_settings.publish_actions:
            action_type = self._map_object_type_to_action_type()
            publish_action = self.action_group_settings.publish_actions.get(action_type)
            if publish_action:
                return publish_action if isinstance(publish_action, dict) else publish_action.dict()
        return None

    @abstractmethod
    def _map_object_type_to_action_type(self) -> str:
        pass

    def transform(self, data: Dict[str, Any], object_type=None, mappings=None, is_source=True) -> Dict[str, Any]:
        if mappings is None:
            mappings = []
        transformed_data = self._transform(data)

        # Apply mappings
        custom_transformed_data = self.apply_custom_mappings(data, transformed_data, object_type,
                                                             mappings, is_source)
        if is_source:
            source = custom_transformed_data.get('source', {})
            source.update({
                'channel_name': self.channel_name,
                'id': self.connection_settings['id']
            })
            custom_transformed_data['source'] = source

        return custom_transformed_data

    @abstractmethod
    def _transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        pass

    def process_single_mapping(self, master_data, mapped_data, mapping, record_type):
        """
        Process a single mapping configuration.

        Args:
            master_data: Source data to read from
            mapped_data: Target data to update
            mapping: Mapping configuration dictionary
        """
        try:
            source_field = mapping['source_field']
            destination_field = mapping['destination_field']
            transformations = mapping.get('transformations', None)
            enabled = mapping.get('enabled', False)

            if record_type == ActionGroup.product.value:
                # check if this is a variants field that needs special mapping (coming soon)
                if source_field == 'variants':
                    # Coming soon
                    pass
                else:
                    # Get source value
                    input_value = get_value_from_jsonpath(master_data, source_field)
                    logger.info(f"Source field '{source_field}' value: {input_value}")

                    # Apply transformations
                    try:
                        output_values = process_transformations(input_value, transformations, source_field)
                        output_value = output_values[-1] if output_values and enabled else input_value
                        logger.info(f"Transformed value: {output_value}")
                    except Exception as e:
                        logger.error(f"Error processing transformations for field '{source_field}': {str(e)}")
                        output_value = input_value

                    # Set destination value
                    set_value_using_jsonpath(mapped_data, destination_field, output_value)
                    logger.info(f"Updated mapped_data: {json_dumps(mapped_data)}")

        except Exception as e:
            logger.error(f"Error processing mapping {mapping}: {str(e)}")

    def apply_custom_mappings(self, master_data, destination_formated_data, record_type, mappings=None,
                              is_source=False):
        """
        Apply custom mappings to transform data from source format to destination format.

        Args:
            master_data: Source data to read from
            destination_formated_data: Initial destination data structure
            mappings: List of mapping configurations
            other_mapping: Additional mapping configuration for variants

        Returns:
            Transformed data according to mappings
        """
        if mappings is None:
            mappings = []
        if is_source:
            return destination_formated_data
        else:
            try:
                # Start with a copy of destination data
                mapped_data = destination_formated_data.copy()
                logger.info(f"Initial mapped_data: {json_dumps(mapped_data)}")

                # Apply each mapping
                for mapping in mappings:
                    self.process_single_mapping(master_data, mapped_data, mapping, record_type)

                return mapped_data

            except Exception as e:
                logger.error(f"Critical error in apply_custom_mappings: {str(e)}")
                return destination_formated_data

    @property
    @abstractmethod
    def input_schema(self) -> Dict[str, Any]:
        pass

    @property
    @abstractmethod
    def output_schema(self) -> Dict[str, Any]:
        pass

    @property
    @abstractmethod
    def channel_name(self) -> str:
        pass

    @property
    @abstractmethod
    def object_type(self) -> str:
        pass

    @property
    @abstractmethod
    def action_group(self) -> ActionGroup:
        pass


class SourceTransformer(BaseTransformer):
    def transform(self, data: Dict[str, Any], object_type=None, mappings=None, is_source=True) -> Dict[str, Any]:
        return super().transform(data, object_type, mappings, is_source)


class DestinationTransformer(BaseTransformer):
    def transform(self, data: Dict[str, Any], object_type=None, mappings=None, is_source=True) -> Dict[str, Any]:
        return super().transform(data, object_type, mappings, is_source)
