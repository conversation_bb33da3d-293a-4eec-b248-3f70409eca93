import os
import uuid
from decimal import Decimal
from typing import List

import pendulum
from nolicore.utils.api import api_message
from nolicore.utils.exceptions import BadRequest, NotFoundRequest, InvalidSchemaExp

from helpers.errors import LocationNotFound, ReturnOrderStatusInvalid, ProductNotFound
from helpers.finance import sync_payment
from helpers.inventory import update_inventory
from helpers.lambda_client import lambda_client_invoke_async
from helpers.order import sync_order, get_variant_by_order_item
from helpers.utils import get_increment_id
from models.basic import BasicAttributes
from models.common.status import PaymentStatus, ReturnStatus, update_payment_status, ReturnType
from models.finance.transaction import TransactionSchema, Transaction
from models.finance.voucher import ReservedVoucher
from models.inventory.location import LocationModel
from models.order.order import OrderModel
from models.order.order_return import ReturnExternalIdIndex, ReturnModel

ENV = os.getenv('ENV')


def get_return_order_by_id(company_id, order_id) -> ReturnModel:
    key = {
        ReturnModel.key__id: order_id,
        'company_id': company_id
    }

    return_order = ReturnModel.by_key(key)
    if return_order is None:
        raise NotFoundRequest('Return order not found')
    return return_order


def get_return_order_obj(company_id, return_order, return_order_id=None):
    return_order_id = return_order_id or return_order.get('id')
    external_id = return_order.get('external_id')
    return_order_obj = None
    if return_order_id:
        return_order_obj = ReturnModel.by_key({
            ReturnModel.key__id: return_order_id,
            'company_id': company_id
        })

    if return_order_obj is None and external_id is not None:
        try:
            return_order_obj = ReturnModel(ReturnExternalIdIndex.list({
                ReturnExternalIdIndex.key__id: external_id,
                'company_id': company_id
            }, limit=10)['Items'][0])
        except IndexError:
            pass

    return return_order_obj


def sync_return_order(return_order, company_id, user=None):
    exchanged_order = return_order.pop('exchanged_order', None)

    # validate location
    location = return_order.get('location')
    location_obj = LocationModel.by_key({'id': location["id"], 'company_id': company_id})
    if location_obj is None:
        raise LocationNotFound(location["id"])

    # calculate price
    total_return_price = Decimal(0)
    for item in return_order.get('return_order_line_items', []):
        variant = get_variant_by_order_item(company_id, item)
        if variant is None:
            raise ProductNotFound(f'Sku not found {item["sku"]}')
        variant_brand = variant.attributes.brand
        if variant_brand:
            item['brand'] = {
                "id": variant_brand.id,
                "name": variant_brand.name
            }
        variant_category = variant.attributes.category
        if variant_category:
            item['category'] = {
                "id": variant_category.id,
                "name": variant_category.name
            }
        item['product_id'] = str(variant.attributes.product_id)
        item['variant_id'] = str(variant.attributes.id)
        item['variant_name'] = variant.attributes.name
        item['image_url'] = variant.attributes.images[0].url if variant.attributes.images else None
        if item.get('unit_price') is None:
            item['unit_price'] = variant.attributes.prices[0].price
        if 'id' not in item:
            item['id'] = str(uuid.uuid4())
        # Calculate discount
        item_discount = item.get('discount', 0)
        if 'sale_price' in item:
            item_discount = Decimal(item['unit_price']) - Decimal(item['sale_price'])
        item['discount'] = item_discount
        item['sale_price'] = Decimal(item['unit_price']) - Decimal(item_discount)
        item['price'] = Decimal(str(item['quantity'])) * Decimal(str(item['return_price']))
        total_line = Decimal(item['return_price']) * Decimal(item['quantity'])
        total_return_price += total_line
    return_order['total'] = total_return_price
    status = return_order.get('status')
    if status is ReturnStatus.RECEIVED.value:
        current_time = pendulum.now().to_iso8601_string()
        return_order["returned_at"] = current_time

    payments = return_order.pop('payments', None)
    return_order_obj = get_return_order_obj(company_id, return_order)
    if not return_order_obj:
        return_order['return_order_number'] = get_increment_id(company_id, ReturnModel)
        return_order_payload = BasicAttributes.add_basic_attributes(return_order, company_id, user)
        return_order_obj = ReturnModel(return_order_payload)
        return_order_obj.save()
    else:
        return_order_obj.update(return_order, user)

    if payments:
        old_payments = [item for item in payments if item.get('id') is not None]
        new_payments = [item for item in payments if item.get('id') is None]
        return_order['payments'] = old_payments
        if return_order_obj.attributes.payment_status not in [PaymentStatus.PAID]:
            sync_return_order_payment(user, return_order_obj, new_payments)

    # create exchanged order and update return_order
    if exchanged_order is not None:
        exchanged_order_id = str(uuid.uuid4())
        exchanged_order['id'] = exchanged_order_id
        return_order['exchanged_order_id'] = exchanged_order_id
        order_obj = sync_order(exchanged_order, company_id, user=user)
        exchanged_order_number = order_obj.attributes_dict.get('number', None)
        total_exchanged_amount = Decimal(0)
        for item in order_obj.attributes_dict.get('order_line_items', []):
            total_line = Decimal(item.get('sale_price')) * Decimal(item.get('quantity'))
            total_exchanged_amount += total_line
        return_order['total_exchanged_amount'] = total_exchanged_amount
        return_order_obj.update(
            {'total_exchanged_amount': total_exchanged_amount, "exchanged_order_id": exchanged_order_id,
             "exchanged_order_number": exchanged_order_number}, user)

    # update order returns
    order_id = return_order_obj.attributes_dict.get('order_id')
    if order_id:
        key = {
            OrderModel.key__id: order_id,
            'company_id': company_id
        }
        order = OrderModel.by_key(key)
        order_number = order.attributes_dict.get("number", None)
        return_order_obj.update({"order_number": order_number}, user)

        returns = order.attributes_dict.get("returns")
        if not isinstance(returns, list):
            returns = []
        returns.append(return_order)
        order.update({'returns': returns})

    # validate location
    status = return_order_obj.attributes.status
    if status == ReturnStatus.RECEIVED and return_order_obj.attributes.type != ReturnType.IMPORTED:
        current_time = pendulum.now().to_iso8601_string()
        update_inventory(ReturnModel, return_order_obj, company_id, user, update_type="received")
        updated_data = {"returned_at": current_time}
        return_order_obj.update(updated_data, user)
    return return_order_obj


def sync_return_order_payment(user, return_order: ReturnModel, payments: List[dict]):
    company_id = return_order.attributes.company_id
    total = return_order.attributes.total
    current_payments = return_order.attributes.payments
    reference = return_order.attributes.id
    effective_date = pendulum.now().to_iso8601_string()
    note = return_order.attributes.note
    voucher_id = ReservedVoucher.RETURN_ORDER_VOUCHER.value
    transactions = sync_payment(str(company_id), user, total, current_payments, payments, voucher_id,
                                reference=reference, note=note, effective_date=effective_date)

    payments_data = {
        'payments': [
                        TransactionSchema(Transaction).dump(t) for t in current_payments or []
                    ] + [
                        {k: v for k, v in t.items() if k not in {
                            'created_at', 'company_id', 'updated_at', 'user'
                        }} for t in transactions
                    ]
    }
    payment_status = PaymentStatus.PAID if sum(
        [payment['amount'] for payment in payments_data['payments']]
    ) == total else PaymentStatus.PARTIAL_PAID
    user_id = user.get("id")
    update_payment_status(user_id, return_order, payment_status, extras=payments_data)
    return transactions


def receive(return_order_obj: ReturnModel, user, company_id):
    if return_order_obj.attributes.status == ReturnStatus.CANCELLED:
        return api_message('This return order can not be received.')
    if return_order_obj.attributes.status == ReturnStatus.RECEIVED:
        return api_message('Return order has already been received')
    if return_order_obj.attributes.payment_status == PaymentStatus.PAID:
        return api_message('This order has been refunded. Can not cancel!')
    returned_at = pendulum.now().to_iso8601_string()
    updated_data = {'returned_at': returned_at}
    update_inventory(ReturnModel, return_order_obj, company_id, user, update_type="received",
                     status=ReturnStatus.RECEIVED, updated_data=updated_data)
    return api_message('Return order received')


def pay(company_id, user, return_order_id=None, return_order_obj: ReturnModel = None, payments: list = None):
    return_order_obj = return_order_obj if return_order_obj else get_return_order_by_id(company_id, return_order_id)
    if return_order_obj.attributes.payment_status not in [PaymentStatus.PAID]:
        return sync_return_order_payment(user, return_order_obj, payments)
    raise ReturnOrderStatusInvalid()


def cancel(return_order_obj: ReturnModel, user):
    if return_order_obj.attributes.status == ReturnStatus.CANCELLED:
        return api_message('Return order has already been cancelled')
    if return_order_obj.attributes.status == ReturnStatus.RECEIVED:
        return api_message('Received order can not cancel')
    if return_order_obj.attributes.payment_status == PaymentStatus.PAID:
        return api_message('This order has been refunded. Can not cancel!')
    cancelled_at = pendulum.now().to_iso8601_string()
    return_order_obj.update({'cancelled_at': cancelled_at, "status": ReturnStatus.CANCELLED.value}, user)
    return api_message('Return order cancelled')


def invoke_import_return_order(request_id, company_id, start=0):
    payload = {
        'request_id': str(request_id),
        'company_id': str(company_id),
        'start': start,
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-orders-service-{ENV}-importReturnOrderHandle')


def import_return_order(return_order, company_id, user):
    try:
        return_order_obj = sync_return_order(return_order, company_id, user=user)
    except InvalidSchemaExp as ex:
        invalid_scheme_exc = BadRequest(str(ex))
        invalid_scheme_exc.error_code = ex.error_code
        raise invalid_scheme_exc
    return return_order_obj.attributes_dict
