{"create_department": {"POST": [{"name": "Create a new department", "body": {"name": "IT Department", "description": "IT Department"}, "response": {"id": "department-id-123", "name": "IT Department", "description": "IT Department", "company_id": "company-id-123", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "staffs": [], "total_staffs": 0}}]}, "get_department": {"GET": [{"name": "Get department by ID", "path_params": {"department_id": "department-id-123"}, "response": {"id": "department-id-123", "name": "IT Department", "description": "IT Department", "company_id": "company-id-123", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "staffs": [{"id": "staff-id-123", "name": "<PERSON>", "role": "IT Staff", "department_id": "department-id-123", "company_id": "company-id-123", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "image": "https://example.com/image.jpg", "domain_expertise": ["expertise1", "expertise2"]}], "total_staffs": 10}}]}, "list_departments": {"GET": [{"name": "List departments with filters", "query_params": {"query": "IT", "created_at_from": "2024-01-01T00:00:00Z", "created_at_to": "2024-12-31T23:59:59Z", "sort_created_at": "desc", "page": 0, "limit": 10}, "response": {"items": [{"id": "department-id-123", "name": "IT Department", "description": "IT Department", "company_id": "company-id-123", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "staffs": [{"id": "staff-id-123", "name": "<PERSON>", "role": "IT Staff", "department_id": "department-id-123", "company_id": "company-id-123", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "image": "https://example.com/image.jpg", "domain_expertise": ["expertise1", "expertise2"]}], "total_staffs": 10}], "total": 1, "page": 1, "limit": 10}}]}}