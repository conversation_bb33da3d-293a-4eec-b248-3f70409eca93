import os
import re

from boto3.dynamodb.types import TypeDeserializer
from elasticsearch.helpers import bulk
from nolicore.adapters.db.model import Model
from nolicore.utils.aws.boto_helper import get_elastic_search_client
from nolicore.utils.aws.decorator import invoke
from nolicore.utils.aws.request import LambdaRequest
from nolicore.utils.utils import logger

from helpers.index_dynamo_table import table_mappings, create_index_not_existed

deserializer = TypeDeserializer()

SERVICE = os.getenv('SERVICE')


@invoke()
def handle(request: LambdaRequest):
    documents = []
    logger.info(request.event)
    for record in request.event['Records']:
        try:
            arn = record['eventSourceARN']
            key = {key: deserializer.deserialize(value) for key, value in record['dynamodb']['Keys'].items()}
            table_name = re.search(r'table/(.*)/stream', arn)[1]
            table_model: Model = table_mappings.get(table_name)
            index_name = table_model.get_search_index_name(SERVICE).lower()
            if table_model is None:
                continue
            indexer = create_index_not_existed(table_name)
            if record['eventName'] in ['MODIFY', 'INSERT']:
                record_data = {key: deserializer.deserialize(value) for key, value in
                               record['dynamodb']['NewImage'].items()}
                documents.append({
                    "_op_type": "update",
                    "_index": index_name,
                    "_id": indexer.get_search_key(key),
                    "doc": table_model.get_index_data(record_data),
                    "doc_as_upsert": True
                })
            elif record['eventName'] == 'REMOVE':
                documents.append({
                    "_op_type": "delete",
                    "_index": index_name,
                    "_id": indexer.get_search_key(key)
                })
        except Exception as e:
            logger.exception(f"Record: {record}")

    if len(documents) > 0:
        elastic_search = get_elastic_search_client()
        if elastic_search is None:
            raise Exception("Failed to get Elasticsearch client")
        success, failed = bulk(elastic_search, documents, raise_on_error=False, refresh="false")
        logger.info(f"Successful upserts: {success}")
        logger.info(f"Failed upserts: {failed}")

