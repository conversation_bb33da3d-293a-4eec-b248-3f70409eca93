from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class LowercaseTransformationHandler(BaseTransformationHandler):
    """Handler for lowercase transformation"""
    
    config_definition = TransformationConfig(
        label="Lowercase",
        description="Convert text to lowercase",
        direction=TransformationDirection(
            title="Lowercase",
            content="Convert text to lowercase",
            example='"BLUE DENIM" → "blue denim"',
            value_type="Single Value"
        )
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Converts the value to lowercase if it's not None"""
        return str(value).lower() if value is not None else None 