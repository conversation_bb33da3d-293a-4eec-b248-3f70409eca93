from dataclasses import dataclass
from decimal import Decimal

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema, Attributes

from models.promotion.discount_info.discount_amount import DiscountAmountAttributes, DiscountAmountAttributesSchema


@dataclass
class DiscountOrderTotal(Attributes):
    amount_from: Decimal
    amount_to: Decimal = None


class DiscountOrderTotalSchema(AttributesSchema):
    amount_from = fields.Decimal(required=True)
    amount_to = fields.Decimal(allow_none=True)


@dataclass
class DiscountOrderTotalAttributes(DiscountOrderTotal, DiscountAmountAttributes):
    pass


class DiscountOrderTotalAttributesSchema(DiscountOrderTotalSchema, DiscountAmountAttributesSchema):
    pass
