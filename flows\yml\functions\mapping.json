{"get_mapping_list": {"GET": [{"name": "Get unmapped products", "path_params": {"record_type": "product"}, "query_params": {"connection_id": "b66c48f8-a826-4440-bfa9-ca4b1304f854", "page": "0", "limit": "20", "mapping_status": "unmapped"}}]}, "get_mapping": {"POST": [{"name": "Get mapping details", "path_params": {"record_type": "product"}, "body": {"sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json"}}]}, "unmap": {"PUT": [{"name": "Unmap a record", "body": {"connection_id": "b66c48f8-a826-4440-bfa9-ca4b1304f854", "sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json"}}]}, "map_sync_records": {"PUT": [{"name": "Map a product", "body": {"sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json", "connection_id": "b66c48f8-a826-4440-bfa9-ca4b1304f854", "destination_data_id": "1", "is_extra_destination_mapping": false, "extra_mapping_data": {"variant_1": "dest_variant_1", "variant_2": "dest_variant_2"}, "destination_key": "skus", "record_type": "product"}}]}, "get_transformation_list": {"GET": [{"name": "Get all transformations", "description": "Returns list of available transformations with their configurations"}]}, "handle_transform": {"PUT": [{"name": "Transform a field", "body": {"sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314604776.json", "connection_id": "b66c48f8-a826-4440-bfa9-ca4b1304f854", "source_field": "name", "transformations": [{"transformation_type": "uppercase", "config": {}}, {"transformation_type": "split", "config": {"delimiter": " "}}, {"transformation_type": "prefix", "config": {"prefix": "Pre-"}}]}}]}}