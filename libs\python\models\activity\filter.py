from dataclasses import dataclass
from enum import Enum

from marshmallow import fields, EXCLUDE

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


class ObjectType(Enum):
    PRODUCT = 'PRODUCT'
    ORDER = 'ORDER'


@dataclass
class Activity(BasicAttributes):
    user_id: str = None
    object_type: str = None
    name: str = None
    filters: dict = None


class ActivitySchema(BasicAttributesSchema):
    class Meta:
        unknown = EXCLUDE

    user_id = fields.Str(required=True)
    type = fields.Str(required=True)
    name = fields.Str(required=True)
    filters = fields.Dict(required=True)


class ActivityModel(BasicModel):
    table_name = 'filter'
    key__id = 'id'
    key_range_id = 'company_user_type'
    key__ranges = ['company_id', 'user_id', 'type']
    attributes: Activity
    attributes_schema = ActivitySchema
    attributes_class = Activity

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'user_id': 'query',
            'type': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'filters': 'filters'
        }
    }
