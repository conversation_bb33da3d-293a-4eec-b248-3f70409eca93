{"create_knowledge_from_url": {"POST": [{"name": "Create knowledge from URL", "body": {"url": "https://example.com"}}]}, "create_knowledge_from_text": {"POST": [{"name": "Create knowledge from text", "body": {"content": "This is a test knowledge content", "knowledge_name": "test_knowledge"}}]}, "create_knowledge_from_file": {"POST": [{"name": "Create knowledge from files", "body": {"files": [{"file_name": "test.txt", "type": "text/plain", "size": 1024, "s3_key": "company-id/knowledge/123.txt"}, {"file_name": "test.pdf", "type": "application/pdf", "size": 2048, "s3_key": "company-id/knowledge/456.pdf"}]}, "response": {"success": true, "message": "Knowledge created successfully", "data": [{"id": "knowledge-id-1", "name": "test.txt", "s3_key": "company-id/knowledge/123.txt", "status": "PENDING", "source": "FILE", "size": 1024, "meta_data": {"filename": "test.txt", "type": "text/plain", "size": 1024}}, {"id": "knowledge-id-2", "name": "test.pdf", "s3_key": "company-id/knowledge/456.pdf", "status": "PENDING", "source": "FILE", "size": 2048, "meta_data": {"filename": "test.pdf", "type": "application/pdf", "size": 2048}}]}}]}, "get_knowledge": {"GET": [{"name": "Get knowledge by ID", "path_params": {"id": "knowledge-id-123"}}]}, "list_knowledge": {"GET": [{"name": "List all knowledge", "query_params": {"page": "0", "limit": "20"}}]}, "check_knowledge_status": {"GET": [{"name": "Check knowledge status", "path_params": {"id": "knowledge-id-123"}, "response": {"success": true, "message": "Knowledge status retrieved successfully", "data": {"status": "PENDING"}}}]}, "update_knowledge_status": {"PUT": [{"name": "Update knowledge status", "path_params": {"id": "knowledge-id-123"}, "body": {"status": "READY"}, "response": {"success": true, "message": "Knowledge status updated successfully", "data": {"status": "READY"}}}]}, "update_knowledge": {"PUT": [{"name": "Update knowledge details", "path_params": {"id": "knowledge-id-123"}, "body": {"name": "Updated name", "description": "Updated description", "summary_content": "Updated summary"}, "response": {"success": true, "message": "Knowledge updated successfully", "data": {"id": "knowledge-id-123", "name": "Updated name", "description": "Updated description", "summary_content": "Updated summary"}}}]}, "delete_knowledge": {"DELETE": [{"name": "Delete knowledge", "path_params": {"id": "knowledge-id-123"}, "response": {"success": true, "message": "Knowledge deleted successfully"}}]}}