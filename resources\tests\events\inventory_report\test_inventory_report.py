import unittest

from tests.base import BaseTestCase
from resources.src.events.inventory_report import handle
from resources.tests.events.inventory_report.data_inventory_report import order_event


class InventoryReportTestCase(BaseTestCase):
    def test_inventory_report(self):
        response = handle(order_event, {})
        self.assertTrue(True)


if __name__ == '__main__':
    # begin the unittest.main()
    unittest.main()
