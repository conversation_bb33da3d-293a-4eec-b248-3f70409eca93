from dataclasses import dataclass
from typing import List

from marshmallow import fields, pre_load
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.text import get_slug_by_name
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.inventory.inventory_item import InventoryItem, InventoryItemSchema
from models.media.image import Image, ImageSchema
from models.price.price import PriceAttributes, PriceAttributesSchema
from models.product.brand import Brand, BrandSchema
from models.product.category import Category, CategorySchema
from models.product.measurement import MeasurementAttributes, MeasurementAttributesSchema
from models.product.unit import Unit, UnitSchema


@dataclass
class Variant(Attributes):
    id: str = None
    product_id: str = None
    name: str = None
    sku: str = None
    original_sku: str = None
    unit: Unit = None
    prices: List[PriceAttributes] = None
    measurements: MeasurementAttributes = None
    images: List[Image] = None
    brand: Brand = None
    category: Category = None
    inventories: List[InventoryItem] = None
    barcode: str = None
    option1: str = None
    optionTitle1: str = None
    option2: str = None
    optionTitle2: str = None
    option3: str = None
    optionTitle3: str = None
    slug: str = None


class VariantSchema(AttributesSchema):
    id = fields.Str(required=True)
    product_id = fields.Str(required=True)
    name = fields.Str(required=True)
    sku = fields.Str(required=True)
    original_sku = fields.Str(allow_none=True)
    unit = fields.Nested((UnitSchema(Unit)), allow_none=True)
    barcode = fields.Str(allow_none=True)
    option1 = fields.Str(allow_none=True)
    optionTitle1 = fields.Str(allow_none=True)
    option2 = fields.Str(allow_none=True)
    optionTitle2 = fields.Str(allow_none=True)
    option3 = fields.Str(allow_none=True)
    optionTitle3 = fields.Str(allow_none=True)
    brand = fields.Nested(BrandSchema(Brand), allow_none=True)
    category = fields.Nested(CategorySchema(Category), allow_none=True)
    measurements = fields.Nested(MeasurementAttributesSchema(MeasurementAttributes), allow_none=True)
    images = fields.List(fields.Nested(ImageSchema(Image)), allow_none=True)
    prices = fields.List(fields.Nested(PriceAttributesSchema(PriceAttributes)), allow_none=True)
    inventories = fields.List(fields.Nested(InventoryItemSchema(InventoryItem)), allow_none=True)
    slug = fields.Str(allow_none=True)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        data['slug'] = data['slug'] if 'slug' in data else get_slug_by_name(data['name'], data['sku'])
        data['original_sku'] = data['original_sku'] if 'original_sku' in data else data['sku']
        return data


@dataclass
class VariantAttributes(Variant, BasicAttributes):
    pass


class VariantAttributesSchema(VariantSchema, BasicAttributesSchema):
    pass


class VariantModel(BasicModel):
    key__id = 'id'
    table_name = 'variant'
    attributes: VariantAttributes
    attributes_class = VariantAttributes
    attributes_schema = VariantAttributesSchema

    query_mapping = {
        'query': ['id', 'name', 'sku', 'barcode', 'product_id', 'slug'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'sku': 'query',
            'original_sku': 'query',
            'unit.id': 'query',
            'unit.name': 'query',
            'slug': 'query',
            'product_id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
            'brand.id': 'query',
            'brand.name': 'query',
            'category.id': 'query',
            'category.name': 'query',
        },
        'mapping': {
            'images': 'images',
            'prices': 'prices',
            'brand': 'brand',
            'category': 'category',
            'unit': 'unit',
        }
    }

    @classmethod
    def update_inventory(cls, sku, company_id):
        try:
            return cls(VariantSkuIndex.list({'sku': sku, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return


class VariantSkuIndex(VariantModel):
    key__id = 'sku'
    index_name = 'variantSkuIndex'

    @classmethod
    def get_variant_by_sku(cls, sku, company_id):
        try:
            return cls(VariantSkuIndex.list({'sku': sku, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return


class ProductIdIndex(VariantModel):
    key__id = 'product_id'
    index_name = 'productIdIndex'

    @classmethod
    def get_variants(cls, product_id, company_id):
        return ProductIdIndex.list({
            'product_id': product_id, 'company_id': company_id
        }, limit=1000).get('Items', [])


class VariantSlugIndex(VariantModel):
    key__id = 'slug'
    index_name = 'variantSlugIndex'

    @classmethod
    def get_variant_by_slug(cls, slug, company_id):
        try:
            return cls(VariantSlugIndex.list({'slug': slug, 'company_id': company_id}, limit=10).get('Items', [])[0])
        except IndexError:
            return


class VariantOriginalSkuIndex(VariantModel):
    key__id = 'original_sku'
    index_name = 'variantOriginalSkuIndex'

    @classmethod
    def list_variants_by_original_sku(cls, sku, company_id):
        try:
            variants = VariantOriginalSkuIndex.list({'original_sku': sku, 'company_id': company_id}, limit=20).get(
                'Items',
                [])
            return variants
        except IndexError:
            return
