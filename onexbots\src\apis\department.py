from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import NotFoundRequest

from helpers.utils import RESOURCE_SERVICE
from models.basic import BasicAttributes
from models.bots.department import DepartmentModel
from models.bots.virtual_staff import VirtualStaffModel


@as_api()
def _list(api_gateway_request: ApiGatewayRequest):
    params = api_gateway_request.query_string_parameters or {}
    company_id = api_gateway_request.company_id
    departments = DepartmentModel.search(params, service=RESOURCE_SERVICE, company_id=company_id)
    for x in departments['items']:
        params = {"department_id": x['id'], "limit": 1000}
        staffs = VirtualStaffModel.search(params, service=RESOURCE_SERVICE, company_id=company_id)
        x['staffs'] = staffs['items']
        x['total_staffs'] = staffs['total']
    return departments


@as_api()
def get(api_gateway_request: ApiGatewayRequest):
    department_id = api_gateway_request.path_parameters['department_id']
    company_id = api_gateway_request.company_id
    key = {
        DepartmentModel.key__id: department_id,
        'company_id': company_id
    }
    department = DepartmentModel.by_key(key)
    if department is None:
        raise NotFoundRequest('Department could not found')
    params = {"department_id": department_id, "limit": 1000}
    staffs = VirtualStaffModel.search(params, service=RESOURCE_SERVICE, company_id=company_id)
    return {**department.attributes_dict, "staffs": staffs['items'], "total_staffs": staffs['total']}


@as_api(api_request=ApiGatewayRequest)
def add(api_gateway_request: ApiGatewayRequest):
    company_id = api_gateway_request.company_id
    department = api_gateway_request.body
    user = {"id": api_gateway_request.user_id,
            "name_staff": api_gateway_request.user_name}
    department_obj = DepartmentModel(BasicAttributes.add_basic_attributes(department, company_id, user))
    department_obj.save()
    return department_obj.attributes_dict


@as_api(api_request=ApiGatewayRequest)
def update(api_gateway_request: ApiGatewayRequest):
    company_id = api_gateway_request.company_id
    department = api_gateway_request.body
    department_id = api_gateway_request.path_parameters['department_id']
    user = {"id": api_gateway_request.user_id,
            "name_staff": api_gateway_request.user_name}

    key = {
        DepartmentModel.key__id: department_id,
        'company_id': company_id
    }
    department_obj = DepartmentModel.by_key(key)
    if department_obj is None:
        raise NotFoundRequest('Department could not found')
    department.pop('total_staff')
    department_obj.update(department, user)
    params = {"department_id": department_id, "limit": 1000}
    staffs = VirtualStaffModel.search(params, service=RESOURCE_SERVICE, company_id=company_id)
    return {**department_obj.attributes_dict, "staffs": staffs['items'], "total_staffs": staffs['total']}

# @as_api()
# def delete(api_gateway_request: ApiGatewayRequest):
#     department_id = api_gateway_request.path_parameters['department_id']
#     company_id = api_gateway_request.company_id
#     key = {
#         DepartmentModel.key__id: department_id,
#         'company_id': company_id
#     }
#     DepartmentModel.delete(key)
#     return api_message('Department is deleted')
