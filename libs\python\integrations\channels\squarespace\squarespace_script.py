import requests
from requests.auth import HTTP<PERSON>asicAuth

# Replace with your Squarespace API key
API_KEY = '************************************'

# Base URL for Squarespace Orders API
BASE_URL = 'https://api.squarespace.com/1.0/commerce/orders'

# Set the API version in headers
headers = {
    'User-Agent': 'YourAppName (<EMAIL>)',  # Replace with your app name and contact email
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {API_KEY}'
}

def get_orders(page_size=50, cursor=None):
    # Construct the API URL with pagination
    url = f'{BASE_URL}?limit={page_size}'
    if cursor:
        url += f'&cursor={cursor}'

    try:
        # Make the API request
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Check for HTTP errors

        # Parse the JSON response
        orders = response.json()
        return orders

    except requests.exceptions.RequestException as e:
        print(f'Error fetching orders: {e}')
        return None

# Fetch and print orders
orders = get_orders()

if orders and 'result' in orders:
    print(f"Total orders fetched: {len(orders['result'])}")
    for order in orders['result']:
        print(f"Order ID: {order['id']}, Email: {order['billingAddress']['email']}, Total: {order['grandTotal']['value']}")
else:
    print("No orders found or error in fetching data.")
