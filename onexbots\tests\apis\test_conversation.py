import unittest

from tests.base import BaseTestCase
from models.bots.conversation import Role

from onexbots.src.apis.conversation import (
    create_conversation,
    get_conversation,
    list_conversations,
    delete_conversation
)
from nolicore.utils.exceptions import BadRequest, NotFoundRequest


class TestConversation(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

    def test_create_conversation(self):
        """Test creating a new conversation"""
        # Test successful creation
        test_data = {
            "name": "Test Conversation 233",
            "customer_name": "zuong thiu 2232",
            "customer_phone_number": "0123456789",
            "assignee_id": "c44d0698-9ba7-4476-9ba8-0ff63cf442b7",
        }

        # Create API Gateway request
        event = self.create_lambda_event(
            body=test_data,
            is_authorized=True
        )

        # Call the API function
        response = create_conversation(event, self.create_lambda_context())

        # Verify response
        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Conversation created successfully')
        self.assertIsNotNone(response['data'])
        self.assertEqual(response['data']['name'], test_data['name'])

        # Verify members include assignee_id and customer_id
        self.assertIn(test_data['assignee_id'], response['data']['members'])
        self.assertIn(response['data']['customer_id'], response['data']['members'])

        # Test missing required fields
        invalid_data = {
            "name": "Test Conversation"
            # Missing customer_name, customer_phone_number, assignee_id
        }
        event = self.create_lambda_event(
            body=invalid_data,
            is_authorized=True
        )

        with self.assertRaises(BadRequest) as context:
            create_conversation(event, self.create_lambda_context())
        self.assertIn("Missing required field", str(context.exception))

    def test_get_conversation(self):
        """Test getting a conversation by ID"""


        # Test getting the created conversation
        event = self.create_lambda_event(
            path_params={'id': "3f489d60-6867-42e7-b7b6-f20c593d68ba"},
            is_authorized=True
        )
        response = get_conversation(event, self.create_lambda_context())



    def test_list_conversations(self):
        """Test listing conversations"""

        # Test listing with filters
        event = self.create_lambda_event(
            query_params={
                "limit": "10",
                "page": "0"
            },
            is_authorized=True
        )
        response = list_conversations(event, self.create_lambda_context())

        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Conversations retrieved successfully')
        self.assertIsInstance(response['data'], dict)
        self.assertIn('items', response['data'])

    def test_delete_conversation(self):
        """Test deleting a conversation"""
        # First create a conversation
        test_data = {
            "name": "Test Conversation for Delete",
            "customer_name": "Bob Johnson",
            "customer_phone_number": "+4445556666",
            "assignee_id": "staff_4",
            "role": Role.VIRTUAL_STAFF.value
        }

        create_event = self.create_lambda_event(
            body=test_data,
            is_authorized=True
        )
        created_conv = create_conversation(create_event, self.create_lambda_context())
        conv_id = created_conv['data']['id']

        # Test successful deletion
        event = self.create_lambda_event(
            path_params={'id': conv_id},
            is_authorized=True
        )
        response = delete_conversation(event, self.create_lambda_context())

        self.assertTrue(response['success'])
        self.assertEqual(response['message'], 'Conversation deleted successfully')

        # Test deleting non-existent conversation
        event = self.create_lambda_event(
            path_params={'id': 'non-existent-id'},
            is_authorized=True
        )
        with self.assertRaises(NotFoundRequest):
            delete_conversation(event, self.create_lambda_context())


if __name__ == '__main__':
    unittest.main()
