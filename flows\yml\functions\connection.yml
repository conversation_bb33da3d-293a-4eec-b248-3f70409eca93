sync_record:
  handler: src/apis/connection.sync_record
  events:
    - httpApi:
        path: /connections/sync_record
        method: post
        authorizer:
          name: optiAuthorizer

get_sync_records:
  handler: src/apis/connection.get_sync_records
  events:
    - httpApi:
        path: /connections/get_sync_records
        method: post
        authorizer:
          name: optiAuthorizer

search_records:
  handler: src/apis/connection.search_records
  events:
    - httpApi:
        path: /connections/{connection_id}/search_records
        method: post
        authorizer:
          name: optiAuthorizer

search_records_params:
  handler: src/apis/connection.search_records_params
  events:
    - httpApi:
        path: /connections/{connection_id}/search_records_params
        method: post
        authorizer:
          name: optiAuthorizer

sync_records:
  handler: src/apis/connection.sync_records
  events:
    - httpApi:
        path: /connections/{connection_id}/sync_records
        method: post
        authorizer:
          name: optiAuthorizer

list_sync_records:
  handler: src/apis/connection.list_sync_records
  events:
    - httpApi:
        path: /connections/list_sync_records
        method: get
        authorizer:
          name: optiAuthorizer


list_fetch_events:
  handler: src/apis/connection.list_fetch_events
  events:
    - httpApi:
        path: /connections/list_fetch_events
        method: get
        authorizer:
          name: optiAuthorizer

get_fetch_events:
  handler: src/apis/connection.get_fetch_events
  events:
    - httpApi:
        path: /connections/get_fetch_events/{fetch_event_id}
        method: get
        authorizer:
          name: optiAuthorizer

import_records:
  handler: src/apis/connection.import_records
  events:
    - httpApi:
        path: /connections/{connection_id}/import_records
        method: post
        authorizer:
          name: optiAuthorizer

import_records_clone:
  handler: src/apis/connection.import_records_clone
  events:
    - httpApi:
        path: /connections/{connection_id}/import_records_clone
        method: post
        authorizer:
          name: optiAuthorizer

destination_data_handle:
  handler: src/apis/connection.import_destination_data_handle
  timeout: 900
  memorySize: 384


get_destination_data:
  handler: src/apis/connection.get_destination_data
  events:
    - httpApi:
        path: /connections/{connection_id}/destination_data/{group}
        method: get
        authorizer:
          name: optiAuthorizer

destination_data_detail:
  handler: src/apis/connection.get_details_destination_data
  events:
    - httpApi:
        path: /connections/{connection_id}/destination_data/{group}/{destination_data_id}
        method: get
        authorizer:
          name: optiAuthorizer

invoke_sync_records_handle:
  handler: src/apis/sync_mapping.invoke_sync_records_handle
  timeout: 900
  memorySize: 384