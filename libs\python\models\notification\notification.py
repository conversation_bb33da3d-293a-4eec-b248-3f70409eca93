from dataclasses import dataclass
from enum import Enum

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


class NotificationType(Enum):
    ORDER = 'ORDER'
    OTHER = 'OTHER'


@dataclass
class Notification(Attributes):
    user_id: str = None
    title: str = None
    content_title: str = None
    content_description: str = None
    type: NotificationType = NotificationType.OTHER
    content: str = None
    unread: bool = True
    data: dict = None


@dataclass
class NotificationAttributes(Notification, BasicAttributes):
    pass


class NotificationSchema(AttributesSchema):
    user_id = fields.Str(required=True)
    title = fields.Str(required=True)
    type = EnumField(NotificationType, allow_none=True, default=NotificationType.OTHER)
    content_title = fields.Str(required=True)
    content_description = fields.Str(required=True)
    content = fields.Str(allow_none=True)
    unread = fields.Bool(allow_none=True, default=True)
    data = fields.Dict(allow_none=True)


class NotificationAttributesSchema(NotificationSchema, BasicAttributesSchema):
    pass


class NotificationModel(BasicModel):
    table_name = 'notification'
    attributes: NotificationAttributes
    attributes_schema = NotificationAttributesSchema
    attributes_class = NotificationAttributes

    query_mapping = {
        'query': ['user_id', 'title', 'id', 'content_title'],
        'filter': {
            'user_id': 'query',
            'type': 'query',
            'unread': 'boolean',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'content_description': 'content_description',
            'content': 'content',
            'data': 'data',
        }
    }
