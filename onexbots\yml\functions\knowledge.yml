create_knowledge_from_url:
  handler: src.apis.knowledge.create_knowledge_from_urls_api
  events:
    - httpApi:
        path: /knowledge/url
        method: post
        authorizer:
          name: optiAuthorizer

create_knowledge_from_text:
  handler: src.apis.knowledge.create_knowledge_from_text_api
  events:
    - httpApi:
        path: /knowledge/text
        method: post
        authorizer:
          name: opti<PERSON>uthorizer

create_knowledge_from_file:
  handler: src.apis.knowledge.create_knowledge_from_files_api
  events:
    - httpApi:
        path: /knowledge/file
        method: post
        authorizer:
          name: optiAuthorizer

get_knowledge:
  handler: src.apis.knowledge.get_knowledge
  events:
    - httpApi:
        path: /knowledge/{id}
        method: get
        authorizer:
          name: optiAuthorizer

list_knowledge:
  handler: src.apis.knowledge.list_knowledge
  events:
    - httpApi:
        path: /knowledge
        method: get
        authorizer:
          name: optiAuthorizer

check_knowledge_status:
  handler: src.apis.knowledge.check_knowledge_status
  events:
    - httpApi:
        path: /knowledge/{id}/status
        method: get
        authorizer:
          name: optiAuthorizer

update_knowledge_status:
  handler: src.apis.knowledge.update_knowledge_status
  events:
    - httpApi:
        path: /knowledge/{id}/status
        method: put
        authorizer:
          name: optiAuthorizer
    - httpApi:
        path: /services/knowledge/{id}/status
        method: put
        authorizer:
          name: onexbotAuthorizer

update_knowledge:
  handler: src.apis.knowledge.update_knowledge
  events:
    - httpApi:
        path: /knowledge/{id}
        method: put
        authorizer:
          name: optiAuthorizer

delete_knowledge:
  handler: src.apis.knowledge.delete_knowledge
  events:
    - httpApi:
        path: /knowledge/{id}
        method: delete
        authorizer:
          name: optiAuthorizer

removeStaffFromKnowledge:
  handler: src.apis.knowledge.remove_staff_from_knowledge_handler
  timeout: 900
  memorySize: 256

getKnowledgeUploadUrls:
  handler: src.apis.knowledge.generate_upload_url
  events:
    - httpApi:
        path: /knowledge/get_upload_urls
        method: post
        authorizer:
          name: optiAuthorizer
