import time

from scripts.load_env_test import load_env_test

load_env_test("dev")
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.transformers import registry

from helpers.utils import RESOURCE_SERVICE

from integrations.channels.haravan.haravan_library import Haravan<PERSON><PERSON><PERSON>
from integrations.channels.nhanh.nhanh_library import Nhanh<PERSON>ibrary
from models.integration.fetch_event import FetchEventModel

company_id = "4c8e6154-fba3-4837-909b-8a50dff16a2b"
nhanh_library = NhanhLibrary(
    app_id="74307",
    business_id="58863",
    secret_key="GzqIl8r6iiEcvd35QEgodW3huwvkagwehORW2lDLIBBVLi8OO76K9MH40eLzzLa7pr9I8KTYxN7dNnfD4WqCy5yFInw5GHHbEa7xH6EuPlstlpX9YJ5F6cTH9RV92k3D",
    auth_url="https://nhanh.vn/oauth",
    base_url="https://open.nhanh.vn",
    access_token="pAFAI4RemQPMlCHLAMj75oUOMrMyRQpEEMy4y6wq6Qh2hPLGuAlGwQThViHTIFOlNbcrvuDm2WnUKrhgrAMNfrjJEAhViVEwIH0c3gX8n8IbNl2Wk2BizZlUiQiJglyx33FXmBkU0Fqu"
)
haravan_library = HaravanLibrary(
    client_id="06c110f8a1ca7206a16c87acbcf4eb47",
    org_id="200000867385",
    client_secret="09d491296e9743a5c9e256bfcc7a999b71a8ac29637f5a1c00619c09d16c8f94",
    access_token="8051C6B518A51A5CC9FC387CC5F85052D6C56E95C8D128A43C80C28B986F663F"
)

fetch_events = FetchEventModel.search({"event_time_from": "2025-05-29",
                                       "event_time_to": "2025-06-02",
                                       "limit": 10,
                                       "page": 0,
                                       "event_source": "webhook",
                                       "sort_event_time": "desc",
                                       "action_type": "get_inventory"
                                       }, service=RESOURCE_SERVICE, company_id=company_id
                                      )['items']

object_ids = set()
for index, fetch_event in enumerate(fetch_events):
    response = FetchEventModel.by_key({FetchEventModel.key__id: fetch_event['id'],
                                       "continuation_token": fetch_event['continuation_token']}).attributes_dict
    if response.get('object_ids'):
        object_ids.update(response['object_ids'])

nhanh_connection = get_connection_by_id("6b5ca363-434b-4127-8258-3bbf1afc5e50").attributes_dict
source_transformer_class = registry.get_source_transformer("nhanh", "inventory", nhanh_connection)

haravan_connection = get_connection_by_id("5abf07bc-dca7-4c43-872a-4c7f11791566").attributes_dict
destination_transformer_class = registry.get_destination_transformer("haravan", "inventory", haravan_connection)

line_items = []
for pid in object_ids:
    response = nhanh_library._make_request(nhanh_library.endpoints.PRODUCT_DETAIL, data=int(pid))
    for product_id, product in response.items():
        if not product.get('inventory', {}).get('depots', {}):
            continue
        standard_inventory = nhanh_library.extract_inventories(response, None)
        input_data = source_transformer_class.transform(standard_inventory[0].raw_data)
        output_data =  destination_transformer_class.transform(input_data)
        output_data.pop('source')
        line_items.extend(output_data['inventory']['line_items'])

# Chia line_items thành các nhóm, mỗi nhóm tối đa 100 dòng
chunk_size = 100
result = []
for i in range(0, len(line_items), chunk_size):
    chunk = line_items[i:i + chunk_size]
    haravan_inventory = {
        "inventory": {
            "type": "set",
            "reason": "productionofgoods",
            "note": "update from onexapis",
            "line_items": chunk
        }
    }
    time.sleep(1)
    result.append(haravan_library.sync_inventory(haravan_inventory))

    print(result)
