from dataclasses import dataclass
from decimal import Decimal

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema, Attributes

from models.common.default_obj import DefaultObject, DefaultObjectSchema


@dataclass
class OrderLineItem(Attributes):
    id: str
    price: Decimal
    sale_price: Decimal
    quantity: Decimal
    sku: str
    unit_price: Decimal = None
    return_price: Decimal = None
    note: str = None
    discount: Decimal = 0
    product_id: str = None
    variant_id: str = None
    location: DefaultObject = None
    category: DefaultObject = None
    brand: DefaultObject = None
    variant_name: str = None
    image_url: str = None
    name: str = None
    custom: bool = False


class OrderLineItemSchema(AttributesSchema):
    id = fields.UUID(required=True)
    unit_price = fields.Decimal(allow_none=True)
    price = fields.Decimal(required=True)
    sale_price = fields.Decimal(required=True)
    return_price = fields.Decimal(allow_none=True)
    discount = fields.Decimal(allow_none=True)
    quantity = fields.Decimal(required=True)
    sku = fields.Str(required=True)
    note = fields.Str(allow_none=True)
    product_id = fields.UUID(allow_none=True)
    location = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    category = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    brand = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    variant_id = fields.UUID(allow_none=True)
    variant_name = fields.Str(allow_none=True)
    image_url = fields.Url(allow_none=True)
    name = fields.Str(allow_none=True)
    custom = fields.Bool(allow_none=True, default=False)
