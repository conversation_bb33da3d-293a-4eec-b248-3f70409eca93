from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import List

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import AttributesSchema, Attributes
from models.promotion.discount_info.discount_amount import DiscountAmountAttributes, DiscountAmountAttributesSchema
from models.product.brand import BrandSchema, Brand
from models.product.category import CategorySchema, Category
from models.product.product import Product, ProductSchema
from models.product.variant import VariantSchema, Variant


class ItemType(Enum):
    PRODUCTS = 'PRODUCTS'
    VARIANTS = 'VARIANTS'
    CATEGORIES = 'CATEGORIES'
    BRANDS = 'BRANDS'


@dataclass
class Quantity(Attributes):
    quantity_from: Decimal
    quantity_to: Decimal = None


class QuantitySchema(AttributesSchema):
    quantity_from = fields.Decimal(required=True)
    quantity_to = fields.Decimal(allow_none=True)


@dataclass
class QuantityAttributes(Quantity, DiscountAmountAttributes):
    pass


class QuantityAttributesSchema(QuantitySchema, DiscountAmountAttributesSchema):
    pass


@dataclass
class DiscountQuantityAttributes(Attributes):
    item_type: ItemType
    conditions: List[QuantityAttributes]
    for_all_items: bool = False
    products: List[Product] = None
    variants: List[Variant] = None
    categories: List[Category] = None
    brands: List[Brand] = None


class DiscountQuantityAttributesSchema(AttributesSchema):
    item_type = EnumField(ItemType, required=True)
    conditions = fields.List(fields.Nested(QuantityAttributesSchema(QuantityAttributes)), required=True)
    for_all_items = fields.Bool(allow_none=True)
    products = fields.List(fields.Nested(ProductSchema(Product)), allow_none=True)
    variants = fields.List(fields.Nested(VariantSchema(Variant)), allow_none=True)
    categories = fields.List(fields.Nested(CategorySchema(Category)), allow_none=True)
    brands = fields.List(fields.Nested(BrandSchema(Brand)), allow_none=True)
