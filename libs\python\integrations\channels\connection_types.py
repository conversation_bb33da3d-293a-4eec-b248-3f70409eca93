from enum import Enum
from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from datetime import datetime

from marshmallow import fields, Schema, INCLUDE
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import AttributesSchema

from integrations.channels.action_types import ActionGroup, ActionGroupSettings, ActionGroupSettingsSchema
from models.basic import BasicAttributesSchema, BasicAttributes


class ConnectionType(str, Enum):
    API = "API"
    DATABASE = "DATABASE"
    FILE = "FILE"


class ChannelType(str, Enum):
    ECOMMERCE = "ECOMMERCE"
    OMS = "OMS"
    WMS = "WMS"
    PIM = "PIM"
    ERP = "ERP"
    MARKETPLACE = "MARKETPLACE"
    PAYMENT = "PAYMENT"
    INVOICE = "INVOICE"
    SHIPPING_PROVIDER = "SHIPPING_PROVIDER"
    MARKETING = "MARKETING"  # Add this line


class AuthType(str, Enum):
    BASIC = "BASIC"
    OAUTH = "OAUTH"
    API_KEY = "API_KEY"
    NONE = "NONE"


class ConnectionStatus(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    PENDING = "PENDING"


@dataclass
class ConnectionSettings:
    url: str = None


class ConnectionSettingsSchema(AttributesSchema):
    url = fields.Url(required=True)

    class Meta:
        unknown = INCLUDE


class ConnectionSchema(BasicAttributesSchema):
    id = fields.UUID(required=True)
    status = EnumField(ConnectionStatus, allow_none=True, default=ConnectionStatus.INACTIVE)
    name = fields.Str(required=True)
    external_id = fields.Str(allow_none=True)
    logo = fields.Str(required=True)
    description = fields.Str(required=True)
    channel_name = fields.Str(required=True)
    channel_type = EnumField(ChannelType, required=True)
    settings = fields.Dict(allow_none=True)
    type = EnumField(ConnectionType, allow_none=True, default=ConnectionType.API)
    auth_type = EnumField(AuthType, allow_none=True, default=AuthType.NONE)
    action_groups = fields.Dict(keys=fields.Str(), values=fields.Nested(ActionGroupSettingsSchema))
    webhook_settings = fields.Dict(keys=fields.Str(), values=fields.Raw(), required=False)
    dynamic_settings = fields.Dict(keys=fields.Str(), values=fields.Raw(), required=False)

    class Meta:
        unknown = INCLUDE


@dataclass
class ConnectionAttributes(BasicAttributes):
    id: str = None
    name: str = None
    logo: str = None
    description: str = None
    external_id: str = field(default=None)
    channel_name: str = field(default=None)
    channel_type: ChannelType = field(default=None)
    settings: dict = None
    status: ConnectionStatus = field(default=ConnectionStatus.INACTIVE)
    type: ConnectionType = field(default=ConnectionType.API)
    auth_type: AuthType = field(default=AuthType.BASIC)
    action_groups: Dict[str, ActionGroupSettings] = field(default_factory=lambda: {
        group.value: ActionGroupSettings(group_name=group.value) for group in ActionGroup
    })
    webhook_settings: Dict[str, Any] = field(default_factory=dict)
    dynamic_settings: Dict[str, Any] = field(default_factory=dict)
