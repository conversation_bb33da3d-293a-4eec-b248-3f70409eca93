from decimal import Decimal

from nolicore.utils.exceptions import BadRequest

from models.actor.customer_group import CustomerGroupModel
from models.basic import BasicAttributes


def validate_customer_group(company_id, customer_group):
    next_group = customer_group.get("next_group")
    min_purchased_amount = customer_group.get("min_purchased_amount")
    discount_percent = customer_group.get("discount_percent")
    if discount_percent is None:
        raise BadRequest("Invalid discount: 'discount_percent' must be provided and be a valid decimal number.")
    if next_group is not None and isinstance(next_group, dict) and "id" in next_group:
        customer_group_obj = CustomerGroupModel.by_key(
            {CustomerGroupModel.key__id: next_group.get("id"), "company_id": company_id})
        if customer_group_obj.attributes.min_purchased_amount <= Decimal(min_purchased_amount):
            raise BadRequest(
                "The minimum purchase amount for the next group must be greater than the current group's amount.")
        if customer_group_obj.attributes.discount_percent <= Decimal(discount_percent):
            raise BadRequest(
                "The discount percentage for the next group must be greater than the current group's discount percentage.")


def get_default_customer_group(company_id):
    customer_group = CustomerGroupModel.by_key({CustomerGroupModel.key__id: company_id, "company_id": company_id})
    if customer_group is None:
        default_cg = {
            "id": company_id,
            "name": "Member",
            "min_purchased_amount": 0,
            "discount_percent": 0
        }
        customer_group = CustomerGroupModel(BasicAttributes.add_basic_attributes(default_cg, company_id))
        customer_group.save()
    return customer_group.attributes_dict
