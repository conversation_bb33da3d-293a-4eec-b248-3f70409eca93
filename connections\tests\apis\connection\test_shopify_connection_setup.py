import json
import unittest
import uuid
import os
from dotenv import load_dotenv

from tests.base import BaseTestCase
from connections.src.apis.connection import setup_connection
from integrations.channels.shopify.shopify_connection import ShopifyConnection


class ShopifyConnectionSetupIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.api_key = 'SHOPIFY_API_KEY'
        # cls.shop_url = 'https://91af33-b6.myshopify.com'
        # cls.secret_key = '201532b57851eda1c860c04bae0769a5'
        # cls.access_token = '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'

        cls.shop_url = 'https://www.pinkpoppy.com.au'
        cls.secret_key = '9336ddcdee3c276b47687ec44091b821'
        cls.access_token = '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'

        if not all([cls.shop_url, cls.api_key, cls.secret_key, cls.access_token]):
            raise ValueError("Shopify credentials not found in environment variables")

    def setUp(self):
        self.maxDiff = None
        self.company_id = str(uuid.uuid4())
        self.user_id = str(uuid.uuid4())
        self.username = f"user_{self.user_id}"

    def create_lambda_event(self, body):
        return {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_setup_shopify_connection_success(self):
        # Arrange
        event_body = {
            "connection_type": "shopify",
            "setup_data": {
                "shop_url": self.shop_url,
                "api_key": self.api_key,
                "secret_key": self.secret_key,
                "access_token": self.access_token
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'shopify connection setup successful')
        self.assertIn('connection_id', response_body)

        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = ShopifyConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.shop_url, self.shop_url)
        self.assertEqual(created_connection.attributes.api_key, self.api_key)
        self.assertEqual(created_connection.attributes.secret_key, self.secret_key)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status, 'ACTIVE')

        # Clean up
        created_connection.delete()

    def test_setup_shopify_connection_invalid_credentials(self):
        # Arrange
        event_body = {
            "connection_type": "shopify",
            "setup_data": {
                "shop_url": self.shop_url,
                "api_key": "invalid_api_key",
                "secret_key": "invalid_secret_key",
                "access_token": self.access_token
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['error'], 'Bad request')
        self.assertIn('Invalid Shopify credentials', response_body['message'])

    def test_setup_shopify_connection_invalid_shop_url(self):
        # Arrange
        event_body = {
            "connection_type": "shopify",
            "setup_data": {
                "shop_url": "https://nonexistent-shop.myshopify.com",
                "api_key": self.api_key,
                "secret_key": self.secret_key,
                "access_token": self.access_token
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 400)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['error'], 'Bad request')
        self.assertIn('Invalid Shopify shop URL', response_body['message'])


if __name__ == '__main__':
    unittest.main()
