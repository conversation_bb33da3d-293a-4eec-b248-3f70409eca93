import hashlib
import hmac
import json
import time
from datetime import datetime
from typing import Dict, Any, Union

import aiohttp
from nolicore.utils.exceptions import BadRequest

from integrations.channels.action_types import ActionGroup
from integrations.common.base_api_library import (
    APIEndpoints, EndpointType, BaseAPILibrary, FetchAPIResponse, SyncAPIResponse,
    MetaData, PublishMetaData, StandardOrder, StandardProduct, StandardReturnOrder, StandardPurchaseOrder
)


class ShopeeEndpoints(APIEndpoints):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.ORDERS = EndpointType(type="rest", method="GET", path=f"{self.base_url}/api/v2/order/get_order_list")
        self.PRODUCTS = EndpointType(type="rest", method="GET", path=f"{self.base_url}/api/v2/product/get_item_list")
        self.SYNC_ORDER = EndpointType(type="rest", method="POST",
                                       path=f"{self.base_url}/api/v2/order/handle_buyer_cancellation")
        self.SYNC_PRODUCT = EndpointType(type="rest", method="POST", path=f"{self.base_url}/api/v2/product/update_item")
        self.ORDER_DETAIL = EndpointType(type="rest", method="GET",
                                         path=f"{self.base_url}/api/v2/order/get_order_detail")
        self.PRODUCT_DETAIL = EndpointType(type="rest", method="GET",
                                           path=f"{self.base_url}/api/v2/product/get_item_base_info")
        self.CREATE_WEBHOOK = EndpointType(type="rest", method="POST", path=f"{self.base_url}/api/v2/push/subscribe")


class ShopeeLibrary(BaseAPILibrary):
    AVAILABLE_WEBHOOKS = {
        "order_status_update": "/shopee/order/updated",
        "product_create": "/shopee/product/created",
        "product_update": "/shopee/product/updated",
        "inventory_update": "/shopee/inventory/updated",
    }

    def __init__(self, partner_id: str, partner_key: str, shop_id: str, access_token: str,
                 base_url: str = "https://partner.shopeemobile.com"):
        super().__init__(base_url)
        self.partner_id = partner_id
        self.partner_key = partner_key
        self.shop_id = shop_id
        self.access_token = access_token
        self.endpoints = ShopeeEndpoints(self.endpoints.base_url)

    def _generate_signature(self, api_path: str, params: Dict[str, Any]) -> str:
        sorted_params = "&".join(f"{k}={v}" for k, v in sorted(params.items()))
        base_string = f"{api_path}|{sorted_params}"
        return hmac.new(self.partner_key.encode(), base_string.encode(), hashlib.sha256).hexdigest()

    async def _make_request(self, endpoint: EndpointType, params: Dict[str, Any] = None, data: Dict[str, Any] = None) -> \
            Dict[str, Any]:
        timestamp = int(time.time())
        common_params = {
            "partner_id": int(self.partner_id),
            "timestamp": timestamp,
            "access_token": self.access_token,
            "shop_id": int(self.shop_id),
        }
        if params:
            common_params.update(params)

        signature = self._generate_signature(endpoint.path, common_params)
        common_params["sign"] = signature

        headers = {
            "Content-Type": "application/json"
        }

        async with aiohttp.ClientSession() as session:
            if endpoint.method == "GET":
                async with session.get(endpoint.path, params=common_params, headers=headers) as response:
                    response.raise_for_status()
                    return await response.json()
            elif endpoint.method in ["POST", "PUT"]:
                async with session.request(endpoint.method, endpoint.path, params=common_params, json=data,
                                           headers=headers) as response:
                    response.raise_for_status()
                    return await response.json()

    async def test_connection(self) -> bool:
        try:
            # Use a simple API call to test the connection
            await self._make_request(self.endpoints.PRODUCTS, params={"page_size": 1})
            return True
        except Exception:
            return False

    async def get_orders(self, params: Dict[str, Any] = None) -> FetchAPIResponse[StandardOrder]:
        default_params = {
            "time_range_field": "create_time",
            "time_from": int(time.time() - 86400),  # Default to last 24 hours
            "time_to": int(time.time()),
            "page_size": 50,
            "cursor": "",
            "order_status": "READY_TO_SHIP"
        }
        params = {**default_params, **(params or {})}

        response = await self._make_request(self.endpoints.ORDERS, params=params)

        orders = [
            self.standardize_order(order)
            for order in response.get("response", {}).get("order_list", [])
        ]

        return FetchAPIResponse(
            success=True,
            data=orders,
            meta=MetaData(
                total_count=response.get("response", {}).get("total", len(orders)),
                page=1,
                limit=params["page_size"],
                next_cursor=response.get("response", {}).get("next_cursor", "")
            )
        )

    async def get_products(self, params: Dict[str, Any] = None) -> FetchAPIResponse[StandardProduct]:
        default_params = {
            "offset": 0,
            "page_size": 50,
            "item_status": "NORMAL"
        }
        params = {**default_params, **(params or {})}

        response = await self._make_request(self.endpoints.PRODUCTS, params=params)

        products = [
            self.standardize_product(product)
            for product in response.get("response", {}).get("item", [])
        ]

        return FetchAPIResponse(
            success=True,
            data=products,
            meta=MetaData(
                total_count=response.get("response", {}).get("total", len(products)),
                page=1,
                limit=params["page_size"]
            )
        )

    async def sync_order(self, order_data: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        params = {
            "order_sn": order_data["order_number"],
            "status": order_data.get("status", "READY_TO_SHIP")
        }
        response = await self._make_request(self.endpoints.SYNC_ORDER, params=params)

        if response.get("error") == "":
            synced_order = self.standardize_order(order_data)
            return SyncAPIResponse(
                success=True,
                data=synced_order,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Order {synced_order.order_number} synced successfully"
            )
        else:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=response.get("message", "Unknown error occurred")
            )

    async def sync_product(self, product_data: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        data = {
            "item_id": int(product_data["id"]),
            "price_info": [{
                "current_price": product_data["price"],
            }],
            "stock_info": [{
                "stock": product_data["inventory_quantity"]
            }]
        }
        response = await self._make_request(self.endpoints.SYNC_PRODUCT, data=data)

        if response.get("error") == "":
            synced_product = self.standardize_product(product_data)
            return SyncAPIResponse(
                success=True,
                data=synced_product,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Product {synced_product.title} synced successfully"
            )
        else:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=response.get("message", "Unknown error occurred")
            )

    def get_purchase_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                            fetch_event_id: str = None) -> FetchAPIResponse[StandardPurchaseOrder]:
        pass

    def standardize_purchase_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def standardize_return_order(self, raw_product: Dict[str, Any], fetch_event_id: str) -> StandardReturnOrder:
        pass

    def sync_return_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardReturnOrder]:
        pass

    def sync_purchase_order(self, product_data: Dict[str, Any], settings: Dict[str, Any] = None) -> SyncAPIResponse[
        StandardPurchaseOrder]:
        pass

    def get_return_orders(self, params: Dict[str, Any] = None, settings: Dict[str, Any] = None, next_page=None,
                          fetch_event_id: str = None) -> FetchAPIResponse[StandardReturnOrder]:
        pass

    def standardize_order(self, raw_order: Dict[str, Any], fetch_event_id: str = None) -> StandardOrder:
        return StandardOrder(
            id=str(raw_order["order_sn"]),
            order_number=raw_order["order_sn"],
            total_price=float(raw_order["total_amount"]),
            currency=raw_order.get("currency", ""),
            status=raw_order.get("order_status", ""),
            created_at=datetime.fromtimestamp(raw_order["create_time"]),
            raw_data=raw_order
        )

    def standardize_product(self, raw_product: Dict[str, Any], fetch_event_id: str = None) -> StandardProduct:
        return StandardProduct(
            id=str(raw_product["item_id"]),
            title=raw_product["item_name"],
            price=float(raw_product["price_info"][0]["original_price"]),
            currency=raw_product.get("currency", ""),
            inventory_quantity=int(raw_product["stock_info"][0]["current_stock"]),
            status=raw_product.get("item_status", ""),
            created_at=datetime.fromtimestamp(raw_product.get("create_time", int(time.time()))),
            raw_data=raw_product
        )

    async def process_webhook(self, event_type: str, payload: Dict[str, Any]) -> SyncAPIResponse[
        Union[StandardOrder, StandardProduct, Dict[str, Any]]]:
        event_processors = {
            'ORDER_STATUS_UPDATE': self._process_order_status_update,
            'PRODUCT_UPDATE': self._process_product_update,
        }

        processor = event_processors.get(event_type)
        if not processor:
            raise ValueError(f"Unsupported event type: {event_type}")

        return await processor(payload)

    async def _process_order_status_update(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardOrder]:
        order_sn = payload.get('data', {}).get('ordersn')
        if not order_sn:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid payload: missing order_sn"
            )

        params = {"order_sn_list": json.dumps([order_sn])}
        response = await self._make_request(self.endpoints.ORDER_DETAIL, params=params)

        if response.get("error") == "":
            order_detail = response.get("response", {}).get("order_list", [])[0]
            order = self.standardize_order(order_detail)
            return SyncAPIResponse(
                success=True,
                data=order,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Processed order status update for order: {order.order_number}"
            )
        else:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to process order status update: {response.get('message', 'Unknown error')}"
            )

    async def _process_product_update(self, payload: Dict[str, Any]) -> SyncAPIResponse[StandardProduct]:
        item_id = payload.get('data', {}).get('item_id')
        if not item_id:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid payload: missing item_id"
            )

        params = {"item_id_list": json.dumps([item_id])}
        response = await self._make_request(self.endpoints.PRODUCT_DETAIL, params=params)

        if response.get("error") == "":
            product_detail = response.get("response", {}).get("item_list", [])[0]
            product = self.standardize_product(product_detail)
            return SyncAPIResponse(
                success=True,
                data=product,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Processed product update for product: {product.title}"
            )
        else:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=f"Failed to process product update: {response.get('message', 'Unknown error')}"
            )

    @staticmethod
    def verify_webhook_signature(payload: str, signature: str, partner_key: str) -> bool:
        calculated_signature = hmac.new(partner_key.encode(), payload.encode(), hashlib.sha256).hexdigest()
        return hmac.compare_digest(calculated_signature, signature)

    async def handle_webhook(self, event_type: str, payload: str, signature: str) -> SyncAPIResponse[
        Union[StandardOrder, StandardProduct, Dict[str, Any]]]:
        if not self.verify_webhook_signature(payload, signature, self.partner_key):
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid webhook signature"
            )

        try:
            payload_dict = json.loads(payload)
            return await self.process_webhook(event_type, payload_dict)
        except json.JSONDecodeError:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message="Invalid JSON payload"
            )
        except ValueError as e:
            return SyncAPIResponse(
                success=False,
                data=None,
                meta=PublishMetaData(updated_at=datetime.now()),
                message=str(e)
            )

    async def setup_webhooks(self, connection):
        webhooks_to_create = [
            {"topic": topic, "address": connection.get_webhook_url(self.AVAILABLE_WEBHOOKS[topic])}
            for topic in self.AVAILABLE_WEBHOOKS
            if connection.attributes.webhook_settings.get(topic, False)
        ]

        results = []
        for webhook in webhooks_to_create:
            result = await self._create_webhook(webhook)
            results.append((webhook['topic'], result['success']))

        for topic, success in results:
            if success:
                print(f"Successfully created webhook: {topic}")
            else:
                print(f"Failed to create webhook: {topic}")

    async def _create_webhook(self, webhook_data: Dict[str, str]) -> Dict[str, Any]:
        try:
            payload = {
                "shop_id": int(self.shop_id),
                "topic": webhook_data['topic'],
                "callback_url": webhook_data['address']
            }
            response = await self._make_request(
                self.endpoints.CREATE_WEBHOOK,
                data=payload
            )
            if response.get("error") == "":
                return {
                    "success": True,
                    "data": response.get("response", {}),
                    "message": f"Webhook {webhook_data['topic']} created successfully"
                }
            else:
                return {
                    "success": False,
                    "data": None,
                    "message": f"Error creating webhook {webhook_data['topic']}: {response.get('message', 'Unknown error')}"
                }
        except Exception as e:
            return {
                "success": False,
                "data": None,
                "message": f"Error creating webhook {webhook_data['topic']}: {str(e)}"
            }

    @staticmethod
    def extract_merchant_id(payload):
        return str(payload.get('shop_id'))

    @staticmethod
    def extract_event_type(payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return headers.get('X-Shopee-Topic') or payload.get('event_type')
