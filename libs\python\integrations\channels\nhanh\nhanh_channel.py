from typing import Dict, Any, Tu<PERSON>, List, Optional

from nolicore.utils.exceptions import BadRequest
from nolicore.utils.utils import logger

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import AbstractChannel, OAuth2Channel
from integrations.channels.connection_types import ChannelType
from integrations.channels.nhanh.nhanh_connection import NhanhSettings
from integrations.channels.nhanh.nhanh_library import NhanhLibrary
from integrations.common.base_api_library import FetchAPIResponse, StandardInventory, MetaData
from integrations.common.event import FetchEvent


class NhanhChannel(OAuth2Channel, AbstractChannel):
    channel_name = "nhanh"
    library_class = NhanhLibrary

    def _create_library(self, connection):
        settings: NhanhSettings = connection.attributes.settings
        return NhanhLibrary(
            app_id=settings.app_id,
            business_id=connection.attributes.external_id,
            secret_key=settings.secret,
            auth_url=settings.auth_url,
            base_url=settings.base_url,
            access_token=connection.attributes.access_token
        )

    def initiate_oauth_flow(self, redirect_url: str = None, base_url: str = None) -> str:
        return self.library.get_authorization_url(str(self.connection.attributes.id), redirect_url, base_url)

    def get_token(self, query_params: Dict[str, Any]) -> Tuple[str, str, str, str, Dict[str, Any]]:
        access_code = query_params.get('accessCode')
        if not access_code:
            raise ValueError("Missing accessCode in query parameters")

        token_data = self.library.fetch_token(access_code)

        return (
            token_data['external_id'],
            token_data['access_token'],
            token_data['expires_at'],
            token_data['refresh_token'],
            token_data['token_data']
        )

    def refresh_oauth_token(self) -> Dict[str, Any]:
        token = self.library.refresh_token()
        self.connection.update_token(token)
        return token

    def test_connection(self) -> bool:
        try:
            return self.library.test_connection()
        except Exception:
            return False

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            "name": "Nhanh",
            "channel_type": ChannelType.OMS,
            "description": "Connect your Nhanh instance to sync products, orders, and customers.",
            "logo": cls._load_image_as_base64(__file__, "nhanh.png")
        }

    @classmethod
    def get_integration_info(cls) -> Dict[str, str]:
        """
        Returns a dictionary containing channel information.
        Must include 'name', 'description', and 'image' keys.
        """
        return {
            "title": "Effortless Integration with Nhanh.vn",
            "description": "Boost your sales with Nhanh.vn’s powerful retail management system.",
            "integration": {
                "Product API": {
                    "description":
                        "Easily manage your product listings and updates across Nhanh.vn.",
                    "apis": [
                        "POST /api/v1/nhanh/product/add",
                        "PUT /api/v1/nhanh/product/update",
                        "DELETE /api/v1/nhanh/product/delete",
                    ],
                },
                "Order API": {
                    "description": "Process and track orders from Nhanh.vn seamlessly.",
                    "apis": [
                        "GET /api/v1/nhanh/orders",
                        "POST /api/v1/nhanh/orders/update",
                        "GET /api/v1/nhanh/orders/details",
                    ],
                },
                "Inventory API": {
                    "description":
                        "Maintain accurate inventory levels with Nhanh.vn’s real-time synchronization.",
                    "apis": [
                        "GET /api/v1/nhanh/inventory",
                        "POST /api/v1/nhanh/inventory/update",
                    ],
                },
            }
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_order:
            return {
                "page": 1,
                "fromDate": None,
                "toDate": None,
            }
        elif action == ActionType.get_return_order:
            return {
                "page": 1,
                "fromDate": None,
                "toDate": None,
            }
        elif action == ActionType.get_product:
            return {
                "page": 1,
                "icpp": 50,
                "updatedDateTimeFrom": None,
                "updatedDateTimeTo": None,

            }
        elif action == ActionType.get_inventory:
            return {
                "page": 1,

                "icpp": 50,
                "updatedDateTimeFrom": None,
                "updatedDateTimeTo": None
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_order:
            return {}
        elif action == ActionType.sync_product:
            return {}
        elif action == ActionType.sync_return_order:
            return {}
        else:
            return {}

    def get_order__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}

        if fetch_event.object_id:
            # Case 1: Single online order
            return self.library.get_order(order_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        elif params.get('transform_type') == 'order':
            # Case 2: Filter-based query
            return self.library.get_orders(params=params, settings=settings, next_page=fetch_event.next_page,
                                           fetch_event_id=fetch_event.id)
        else:
            return self.library.get_offline_orders(params=params, settings=settings, next_page=fetch_event.next_page,
                                                   fetch_event_id=fetch_event.id)

    def get_purchase_order__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}
        return self.library.get_purchase_orders(params=params, settings=settings, next_page=fetch_event.next_page,
                                                fetch_event_id=fetch_event.id)

    def get_return_order__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}
        return self.library.get_return_orders(params=params, settings=settings, next_page=fetch_event.next_page,
                                              fetch_event_id=fetch_event.id)

    def get_product__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}
        if fetch_event.object_id:
            # Case 1: Single product
            return self.library.get_product(product_id=fetch_event.object_id, fetch_event_id=fetch_event.id)
        else:
            # Case 2: Filter-based query
            return self.library.get_products(params=params, settings=settings, next_page=fetch_event.next_page,
                                             fetch_event_id=fetch_event.id)

    def sync_order__publish(self, settings: Dict[str, Any], order_data: Dict[str, Any]):
        return self.library.sync_order(order_data)

    def sync_product__publish(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        # Implement product sync if Nhanh API supports it
        raise NotImplementedError("Product sync is not implemented for Nhanh")

    def get_inventory__fetch(self, settings: Dict[str, Any], fetch_event: FetchEvent) -> FetchAPIResponse[
        StandardInventory]:
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}

        if fetch_event.object_id:
            # Case 1: Single product_id
            return self.library.get_inventory(params=params, settings=settings, product_id=fetch_event.object_id,
                                              fetch_event_id=fetch_event.id)
        elif fetch_event.object_ids:
            # Case 2: List of object_ids (batch)
            # Use the data from fetch_event instead of querying the API
            inventories = []
            for product_id in fetch_event.object_ids:
                raw_inventory = fetch_event.object_data.get(product_id, {})
                inventory = self.library.standardize_inventory(product_id, raw_inventory, fetch_event_id=fetch_event.id)
                inventories.append(inventory)

            return FetchAPIResponse(
                success=True,
                data=inventories,
                meta=MetaData(
                    total_count=len(inventories),
                    page=1,
                    limit=len(inventories)
                )
            )
        else:
            # Case 3: Filter-based query
            return self.library.get_inventory(params=params, settings=settings, fetch_event_id=fetch_event.id)

    def get_order__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        meta = fetch_event.to_dict()['meta']
        params = meta['current_params'] if meta is not None else {}
        if params.get('transform_type') == 'order':
            return self.library.get_order_params()
        else:
            return self.library.get_bill_params()

    def get_return_order__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_bill_params()

    def get_purchase_order__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_bill_params()

    def get_product__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_product_params()

    def get_inventory__params(self, settings: Dict[str, Any], fetch_event: FetchEvent):
        return self.library.get_inventory_params()

    @classmethod
    def verify_webhook_signature(cls, payload: str, signature: str, secret: str) -> bool:
        return NhanhLibrary.verify_webhook_signature(payload, signature, secret)

    @classmethod
    def extract_merchant_id(cls, payload):
        return NhanhLibrary.extract_merchant_id(payload)

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return NhanhLibrary.extract_event_type(payload, headers)

    @classmethod
    def handle_test_event(cls, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                          query_string_params: Dict[str, Any] = None) -> Any:
        # event_type = payload.get('event')
        # if event_type == 'webhooksEnabled':
        #     # Extract the necessary information from the payload
        #     verify_token = payload.get('webhooksVerifyToken')
        #     registered_events = payload.get('data', {}).get('registeredEvents', {})
        #
        #     # Return the extracted information
        #     return {
        #         "status": "success",
        #         "message": "Test event processed successfully",
        #         "verify_token": verify_token,
        #         "registered_events": registered_events
        #     }
        # else:
        #     raise ValueError(f"Unsupported test event type: {event_type}")
        pass

    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> \
            Tuple[
                bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        return self.library.extract_object_details(payload, headers, event_type)

    def get_dynamic_settings(self) -> Dict[str, Any]:
        # First, check if we already have dynamic settings stored
        stored_settings = self.connection.get_dynamic_settings()
        try:
            # If not, fetch and create the initial dynamic settings
            nhanh_locations = self.library.get_locations()
            master_locations = self.get_master_locations()

            stored_settings['location_mapping'] = {
                "external": nhanh_locations,
                "master": master_locations,
                "mapping": stored_settings.get('location_mapping', {}).get('mapping', {})
            }
        except BadRequest as e:
            logger.exception(e.to_json())

        return stored_settings
