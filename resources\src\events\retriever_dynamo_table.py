import os
import re

from boto3.dynamodb.types import TypeDeserializer
from nolicore.utils.aws.boto_helper import get_client
from nolicore.utils.aws.decorator import invoke
from nolicore.utils.aws.request import LambdaRequest
from nolicore.utils.utils import logger, json_dumps
from nolicore.adapters.db.model import Model

from helpers.index_dynamo_table import table_mappings

deserializer = TypeDeserializer()

SERVICE = os.getenv('SERVICE')
sqs = get_client('sqs')
RETRIEVER_QUEUE_URL = os.environ['RETRIEVER_QUEUE_URL']

@invoke()
def handle(request: LambdaRequest):
    logger.info(request.event)
    for record in request.event['Records']:
        try:
            event_name = record['eventName']
            arn = record['eventSourceARN']
            table_name = re.search(r'table/(.*)/stream', arn)[1]
            table_model: Model = table_mappings.get(table_name)
            if table_model is None:
                continue
            record_data = None
            if event_name in ['MODIFY', 'INSERT']:
                record_data =  {key: deserializer.deserialize(value) for key, value in
                               record['dynamodb']['NewImage'].items()}
            elif event_name == 'REMOVE':
                record_data = {key: deserializer.deserialize(value) for key, value in
                               record['dynamodb']['OldImage'].items()}
            if record_data:
                sqs.send_message(
                    QueueUrl=RETRIEVER_QUEUE_URL,
                    MessageBody=json_dumps({
                        "event_name": event_name,
                        "record_data": record_data,
                        "table_name": table_name
                    })
                )
            else:
                logger.warning(f"No image found for event: {event_name}, record: {record}")
        except Exception as e:
            logger.exception(f"Record: {record}, Error: {e}")

