from dataclasses import dataclass
from enum import Enum

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes
from models.media.image import ImageSchema, Image


class Type(Enum):
    MARKETPLACE = 'MARKETPLACE'
    ECOMMERCE_PLATFORM = 'ECOMMERCE_PLATFORM'
    PHYSICAL_STORE = 'PHYSICAL_STORE'
    SOCIAL = 'SOCIAL'


@dataclass
class SaleChannel(Attributes):
    id: str = None
    name: str = None
    type: Type = None
    image: Image = None


class SaleChannelSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    type = EnumField(Type, required=True)


@dataclass
class SaleChannelAttributes(SaleChannel, BasicAttributes):
    pass


class SaleChannelAttributesSchema(BasicAttributesSchema, SaleChannelSchema):
    pass


class SaleChannelModel(BasicModel):
    table_name = 'saleChannel'
    attributes: SaleChannelAttributes
    attributes_schema = SaleChannelAttributesSchema
    attributes_class = SaleChannelAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'company_id': 'query',
            'type': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }
