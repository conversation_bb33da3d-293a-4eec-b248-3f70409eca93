import uuid
from dataclasses import dataclass
from decimal import Decimal

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema
from models.common.default_obj import DefaultObjectSchema, DefaultObject
from models.media.image import Image, ImageSchema


@dataclass
class CustomerGroup(Attributes):
    id: str = None
    name: str = None
    min_purchased_amount: Decimal = None
    discount_percent: Decimal = None
    image: Image = None
    default_price_group: DefaultObject = None
    default_payment_method: DefaultObject = None
    description: str = None
    max_discount_amount_per_order: Decimal = None
    min_order_amount: Decimal = None
    next_group: DefaultObject = None


class CustomerGroupSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(required=True)
    min_purchased_amount = fields.Decimal(required=True)
    discount_percent = fields.Decimal(required=True)
    image = fields.Nested(ImageSchema(Image), allow_none=True)
    default_price_group = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    default_payment_method = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)
    description = fields.Str(allow_none=True)
    max_discount_amount_per_order = fields.Decimal(allow_none=True)
    min_order_amount = fields.Decimal(allow_none=True)
    next_group = fields.Nested(DefaultObjectSchema(DefaultObject), allow_none=True)


@dataclass
class CustomerGroupAttributes(CustomerGroup, BasicAttributes):
    pass


class CustomerGroupAttributesSchema(CustomerGroupSchema, BasicAttributesSchema):
    pass


class CustomerGroupModel(BasicModel):
    table_name = 'customerGroup'
    attributes: CustomerGroupAttributes
    attributes_schema = CustomerGroupAttributesSchema
    attributes_class = CustomerGroupAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'min_purchased_amount': 'number',
            'max_discount_amount_per_order': 'number',
            'min_order_amount': 'number',
            'discount_percent': 'number',
            'default_price_group.id': 'query',
            'default_payment_method.id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'default_price_group': 'default_price_group',
            'default_payment_method': 'default_payment_method',
            'next_group': 'next_group',
            'image': 'image',
        }
    }

    @classmethod
    def initialize_data(cls, user_attributes_obj, initialized_data):
        company_id = user_attributes_obj.get('custom:company_id')
        member_id = company_id
        loyal_id = str(uuid.uuid4())
        vip_id = str(uuid.uuid4())
        diamond_id = str(uuid.uuid4())
        data = [
            {
                "id": member_id,
                "name": "Member",
                "min_purchased_amount": 0,
                "discount_percent": 0,
                "next_group": {
                    "id": loyal_id,
                    "name": "Vip"
                }
            },
            {
                "id": loyal_id,
                "name": "Loyal",
                "min_purchased_amount": 1000000,
                "discount_percent": 3,
                "next_group": {
                    "id": vip_id,
                    "name": "Vip"
                }
            },
            {
                "id": vip_id,
                "name": "Vip",
                "min_purchased_amount": 3000000,
                "discount_percent": 5,
                "next_group": {
                    "id": diamond_id,
                    "name": "Diamond"
                }
            },
            {
                "id": diamond_id,
                "name": "Diamond",
                "min_purchased_amount": 5000000,
                "discount_percent": 8
            }
        ]
        init_data = [BasicAttributes.add_basic_attributes(item, company_id) for item in data]
        initialized_data[cls.table_name] = init_data
        cls.batch_add(init_data)
