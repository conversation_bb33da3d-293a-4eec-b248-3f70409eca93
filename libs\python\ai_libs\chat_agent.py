import openai
import time
import json
from typing import Dict, List, Optional
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func

# Create SQLAlchemy Base
Base = declarative_base()


class UserConversation(Base):
    """Model for storing user conversations"""
    __tablename__ = 'user_conversations'

    id = Column(Integer, primary_key=True)
    user_id = Column(String(50), nullable=False, unique=True)  # Unique constraint for one conversation per user
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationship with messages
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")


class Message(Base):
    """Model for storing chat messages"""
    __tablename__ = 'messages'

    id = Column(Integer, primary_key=True)
    conversation_id = Column(Integer, ForeignKey('user_conversations.id'))
    role = Column(String(20), nullable=False)  # 'user' or 'assistant'
    content = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship with conversation
    conversation = relationship("UserConversation", back_populates="messages")


class OMSAssistant:
    def __init__(self, api_key: str, database_url: str):
        """
        Initialize the OMS Assistant

        Args:
            api_key (str): OpenAI API key
            database_url (str): SQLAlchemy database URL
        """
        self.api_key = api_key
        openai.api_key = api_key

        # Initialize database
        self.engine = create_engine(database_url)
        Base.metadata.create_all(self.engine)
        self.Session = sessionmaker(bind=self.engine)

        # Create or load assistant
        self.assistant = self._create_or_get_assistant()

    def _create_or_get_assistant(self):
        """Create or get existing OpenAI Assistant"""
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_order_details",
                    "description": "Get detailed information about a specific order",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "order_id": {
                                "type": "string",
                                "description": "The unique identifier for the order"
                            }
                        },
                        "required": ["order_id"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_product_details",
                    "description": "Get detailed information about a specific product",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "product_id": {
                                "type": "string",
                                "description": "The unique identifier for the product"
                            }
                        },
                        "required": ["product_id"]
                    }
                }
            }
        ]

        return openai.beta.assistants.create(
            name="OMS Customer Service Assistant",
            instructions="""You are a helpful customer service assistant for our e-commerce platform.
            Help customers with their order and product inquiries using the available functions.
            Always be polite, professional, and empathetic.""",
            model="gpt-4-turbo-preview",
            tools=tools
        )

    def get_or_create_conversation(self, user_id: str) -> UserConversation:
        """
        Get existing conversation or create new one for user

        Args:
            user_id (str): User identifier

        Returns:
            UserConversation: User's conversation
        """
        session = self.Session()
        try:
            conversation = session.query(UserConversation) \
                .filter(UserConversation.user_id == user_id) \
                .first()

            if not conversation:
                conversation = UserConversation(user_id=user_id)
                session.add(conversation)
                session.commit()

            return conversation
        finally:
            session.close()

    def get_chat_history(self, user_id: str, limit: int = 10) -> List[Dict]:
        """
        Get recent chat history for user

        Args:
            user_id (str): User identifier
            limit (int): Number of recent messages to return

        Returns:
            List[Dict]: Recent messages
        """
        session = self.Session()
        try:
            conversation = session.query(UserConversation) \
                .filter(UserConversation.user_id == user_id) \
                .first()

            if not conversation:
                return []

            messages = session.query(Message) \
                .filter(Message.conversation_id == conversation.id) \
                .order_by(Message.created_at.desc()) \
                .limit(limit) \
                .all()

            return [
                {
                    "role": msg.role,
                    "content": msg.content,
                    "created_at": msg.created_at.isoformat()
                }
                for msg in reversed(messages)  # Reverse to get chronological order
            ]
        finally:
            session.close()

    def store_message(self, user_id: str, role: str, content: str):
        """
        Store a message in the database

        Args:
            user_id (str): User identifier
            role (str): Message role ('user' or 'assistant')
            content (str): Message content
        """
        session = self.Session()
        try:
            conversation = self.get_or_create_conversation(user_id)

            message = Message(
                conversation_id=conversation.id,
                role=role,
                content=content
            )
            session.add(message)

            # Update last activity
            conversation.last_activity = func.now()

            session.commit()
        finally:
            session.close()

    def process_message(self, user_id: str, user_message: str) -> str:
        """
        Process a user message and return assistant's response

        Args:
            user_id (str): User identifier
            user_message (str): User's message

        Returns:
            str: Assistant's response
        """
        try:
            # Store user message
            self.store_message(user_id, 'user', user_message)

            # Create a thread
            thread = openai.beta.threads.create()

            # Add recent chat history to thread
            history = self.get_chat_history(user_id, limit=5)  # Last 5 messages for context
            for msg in history:
                openai.beta.threads.messages.create(
                    thread_id=thread.id,
                    role=msg["role"],
                    content=msg["content"]
                )

            # Add the current user message
            openai.beta.threads.messages.create(
                thread_id=thread.id,
                role="user",
                content=user_message
            )

            # Run the assistant
            run = openai.beta.threads.runs.create(
                thread_id=thread.id,
                assistant_id=self.assistant.id
            )

            # Wait for the run to complete
            while True:
                run = openai.beta.threads.runs.retrieve(
                    thread_id=thread.id,
                    run_id=run.id
                )

                if run.status == "completed":
                    messages = openai.beta.threads.messages.list(
                        thread_id=thread.id
                    )

                    # Get the latest assistant message
                    for message in messages.data:
                        if message.role == "assistant":
                            response = message.content[0].text.value
                            self.store_message(user_id, 'assistant', response)
                            return response

                elif run.status == "failed":
                    error_message = "I apologize, but I encountered an error processing your request."
                    self.store_message(user_id, 'assistant', error_message)
                    return error_message

                # Wait before checking again
                time.sleep(1)

        except Exception as e:
            error_message = f"I apologize, but an error occurred: {str(e)}"
            self.store_message(user_id, 'assistant', error_message)
            return error_message

    def clear_chat_history(self, user_id: str):
        """
        Clear chat history for a user

        Args:
            user_id (str): User identifier
        """
        session = self.Session()
        try:
            conversation = session.query(UserConversation) \
                .filter(UserConversation.user_id == user_id) \
                .first()

            if conversation:
                session.delete(conversation)
                session.commit()
        finally:
            session.close()


# Example usage
if __name__ == "__main__":
    from dotenv import load_dotenv
    import os

    # Load environment variables
    load_dotenv()

    # Initialize the assistant
    assistant = OMSAssistant(
        api_key=os.getenv("OPENAI_API_KEY"),
        database_url="sqlite:///oms_chat.db"  # Use your preferred database
    )

    # Example interaction
    USER_ID = "user123"

    # Process a message
    response = assistant.process_message(
        user_id=USER_ID,
        user_message="Can you check the status of my order #12345?"
    )
    print(f"Assistant: {response}")

    # Get chat history
    history = assistant.get_chat_history(USER_ID)
    print("\nChat History:")
    for message in history:
        print(f"{message['role']}: {message['content']}")

    # Clear chat history if needed
    # assistant.clear_chat_history(USER_ID)