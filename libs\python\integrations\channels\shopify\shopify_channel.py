from typing import Dict, Any, Tu<PERSON>, Optional, List

from integrations.channels.action_types import ActionType, ActionGroup
from integrations.channels.channel_library import AbstractChannel
from integrations.channels.connection_types import ChannelType
from integrations.channels.shopify.shopify_connection import ShopifySettings
from integrations.channels.shopify.shopify_library import ShopifyLibrary


class ShopifyChannel(AbstractChannel):
    def extract_object_details(self, payload: Dict[str, Any], headers: Dict[str, Any] = None,
                               event_type: Optional[str] = None) -> Tuple[
        bool, Optional[str], ActionGroup, Optional[Dict[str, Any]], Optional[List[str]]]:
        pass

    channel_name = "shopify"
    library_class = ShopifyLibrary

    def _create_library(self, connection):
        settings: ShopifySettings = connection.attributes.settings
        return ShopifyLibrary(
            shop_url=settings.shop_url,
            access_token=settings.access_token,
            api_version=settings.api_version
        )

    @classmethod
    def get_info(cls) -> Dict[str, str]:
        return {
            "name": "Shopify",
            "channel_type": ChannelType.ECOMMERCE,
            "description": "Connect your Shopify store to sync products, orders, and inventory.",
            "logo": cls._load_image_as_base64(__file__, "shopify.png")
        }

    @classmethod
    def get_integration_info(cls) -> Dict[str, str]:
        """
        Returns a dictionary containing channel information.
        Must include 'name', 'description', and 'image' keys.
        """
        return {
            "title": "Seamless Integration with Shopify",
            "description":
                "Connect your e-commerce operations with Shopify for streamlined business management.",
            "integration": {
                "Product API": {
                    "description": "Manage your Shopify product catalog effortlessly.",
                    "apis": [
                        "POST /api/v1/shopify/product/add",
                        "PUT /api/v1/shopify/product/update",
                        "DELETE /api/v1/shopify/product/delete",
                    ],
                },
                "Order API": {
                    "description":
                        "Handle Shopify orders with ease, including retrieval and status updates.",
                    "apis": [
                        "GET /api/v1/shopify/orders",
                        "POST /api/v1/shopify/orders/update",
                        "GET /api/v1/shopify/orders/details",
                    ],
                },
                "Inventory API": {
                    "description":
                        "Ensure your Shopify inventory is always accurate with real-time updates.",
                    "apis": [
                        "GET /api/v1/shopify/inventory",
                        "POST /api/v1/shopify/inventory/update",
                    ],
                },
                "Customer API": {
                    "description":
                        "Manage customer data and interactions within Shopify’s CRM.",
                    "apis": [
                        "GET /api/v1/shopify/customers",
                        "POST /api/v1/shopify/customers/update",
                    ],
                },
            },
        }

    @classmethod
    def get_default_fetch_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.get_order:
            return {
                "limit": 50,
                "status": "any",
                "financial_status": "any",
                "fulfillment_status": "any",
            }
        elif action == ActionType.get_product:
            return {
                "limit": 50,
                "status": "active",
                "collection_id": None,
            }
        else:
            return {}

    @classmethod
    def get_default_publish_settings(cls, action: ActionType) -> Dict[str, Any]:
        if action == ActionType.sync_order:
            return {
                "update_inventory": True,
                "send_fulfillment_email": False,
            }
        elif action == ActionType.sync_product:
            return {
                "update_variants": True,
                "create_new_products": True,
            }
        else:
            return {}

    def get_order__fetch(self, settings: Dict[str, Any]):
        return self.library.get_orders(settings)

    def get_product__fetch(self, settings: Dict[str, Any]):
        return self.library.get_products(settings)

    def sync_order__publish(self, settings: Dict[str, Any], order_data: Dict[str, Any]):
        return self.library.sync_order(settings, order_data)

    def sync_product__publish(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        return self.library.sync_product(settings, product_data)

    def webhook__webhook(self, settings: Dict[str, Any], product_data: Dict[str, Any]):
        return self.library.process_webhook(settings, product_data)

    def setup_webhooks(self):
        self.library.setup_webhooks(self.connection)

    @staticmethod
    def verify_webhook_signature(payload: str, headers: dict, settings: ShopifySettings) -> bool:
        return ShopifyLibrary.verify_webhook_signature(payload, headers.get('X-Shopify-Hmac-SHA256'),
                                                       settings.webhook_secret)

    @classmethod
    def extract_merchant_id(cls, payload):
        return cls.library_class.extract_merchant_id(payload)

    @classmethod
    def extract_event_type(cls, payload: Dict[str, Any], headers: Dict[str, str]) -> str:
        return cls.library_class.extract_event_type(payload, headers)
