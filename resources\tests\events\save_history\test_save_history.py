import unittest

from tests.base import BaseTestCase
from resources.src.events.save_history import handle
from resources.tests.events.save_history.save_history_data import history_event


class InventoryReportTestCase(BaseTestCase):
    def test_inventory_report(self):
        response = handle(history_event, {})
        self.assertTrue(True)


if __name__ == '__main__':
    # begin the unittest.main()
    unittest.main()
