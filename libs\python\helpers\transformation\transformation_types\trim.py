from typing import Any
from ..transformation import BaseTransformationHandler, TransformationConfig, TransformationDirection


class TrimTransformationHandler(BaseTransformationHandler):
    """Handler for trim transformation"""
    
    config_definition = TransformationConfig(
        label="Trim",
        description="Remove whitespace from the beginning and end of text",
        direction=TransformationDirection(
            title="Trim Whitespace",
            content="Remove leading and trailing whitespace from text",
            example='"  Hello World  " → "Hello World"',
            value_type="Single Value"
        )
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Removes whitespace from the beginning and end of the value if it's not None"""
        return str(value).strip() if value is not None else None 