from functools import lru_cache
import time

class TransformerCache:
    def __init__(self, cache_timeout=3600):  # 1 hour cache timeout
        self.cache_timeout = cache_timeout
        self.cache = {}

    @lru_cache(maxsize=100)
    def get_transformer(self, key, transformer_getter, *args, **kwargs):
        current_time = time.time()
        if key in self.cache:
            transformer, timestamp = self.cache[key]
            if current_time - timestamp < self.cache_timeout:
                return transformer

        transformer = transformer_getter(*args, **kwargs)
        self.cache[key] = (transformer, current_time)
        return transformer

    def clear_cache(self):
        self.cache.clear()
        self.get_transformer.cache_clear()
