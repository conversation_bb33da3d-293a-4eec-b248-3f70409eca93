def deep_update(target: dict, source: dict) -> dict:
    """
    Deep update a target dictionary with values from a source dictionary.
    Only updates fields that exist in the source dictionary.
    
    Args:
        target (dict): The dictionary to update
        source (dict): The dictionary containing updates
        
    Returns:
        dict: The updated dictionary
    """
    for key, value in source.items():
        if isinstance(value, dict) and key in target and isinstance(target[key], dict):
            deep_update(target[key], value)
        else:
            target[key] = value
    return target
