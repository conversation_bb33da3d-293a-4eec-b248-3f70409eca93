from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema


@dataclass
class Address(Attributes):
    name: str = None
    first_name: str = None
    last_name: str = None
    address1: str = None
    address2: str = None
    city: str = None
    country: str = None
    country_code: str = None
    phone: str = None
    province: str = None
    district: str = None
    ward: str = None
    province_code: str = None
    zip: str = None
    default_billing: bool = False
    default_shipping: bool = False


class AddressSchema(AttributesSchema):
    name = fields.Str(allow_none=True)
    first_name = fields.Str(allow_none=True)
    last_name = fields.Str(allow_none=True)
    address1 = fields.Str(allow_none=True)
    address2 = fields.Str(allow_none=True)
    city = fields.Str(allow_none=True)
    country = fields.Str(allow_none=True)
    country_code = fields.Str(allow_none=True)
    phone = fields.Str(allow_none=True)
    province = fields.Str(allow_none=True)
    district = fields.Str(allow_none=True)
    ward = fields.Str(allow_none=True)
    province_code = fields.Str(allow_none=True)
    zip = fields.Str(allow_none=True)
    default_billing = fields.Bool(allow_none=True)
    default_shipping = fields.Bool(allow_none=True)
