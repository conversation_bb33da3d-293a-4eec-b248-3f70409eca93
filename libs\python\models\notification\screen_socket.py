from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema, Model


@dataclass
class ScreenSocket(Attributes):
    user_id: str
    screen_socket_id: str


class ScreenSocketSchema(AttributesSchema):
    user_id = fields.Str(str=True)
    screen_socket_id = fields.Str(str=True)


class ScreenSocketModel(Model):
    key__id = 'user_id'
    key__ranges = ['screen_socket_id']
    table_name = 'screenSocket'
    attributes: ScreenSocket
    attributes_schema = ScreenSocketSchema
    attributes_class = ScreenSocket

    query_mapping = {
        'query': ['user_id', 'screen_socket_id'],
        'filter': {
            'screen_socket_id': 'query'
        },
    }
