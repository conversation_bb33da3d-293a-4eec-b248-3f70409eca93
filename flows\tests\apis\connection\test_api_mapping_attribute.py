import json

from tests.base import BaseTestCase
from helpers.transformation.transformation import TransformationType
from flows.src.apis.mapping_attribute import (get_attributes, get_mapping_attribute, save_mapping_attribute,
                                              remove_mapping_attribute)


class TestMappingAttribute(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.connection_id = '764b8784-c48f-4378-80ec-f0085c20f13b'
        cls.type = 'product'

    def test_get_attributes(self):
        event_body = {
            "sync_record_id": "8b0972ca-762b-4cfa-87bd-9920bcd134c4/get_product/product/product_9114314735848.json"
        }
        event = self.create_lambda_event(body=event_body, query_params={},
                                         path_params={'connection_id': self.connection_id, 'type': 'product'},
                                         is_authorized=True)
        context = self.create_lambda_context()

        result = get_attributes(event, context)
        print('result', result)

    def test_get_mapping_attribute(self):
        event_body = {}
        event = self.create_lambda_event(event_body, query_params={},
                                         path_params={'connection_id': self.connection_id, 'type': 'product'})
        context = self.create_lambda_context()

        result = get_mapping_attribute(event, context)
        print('result', result)

    def test_save_mapping_attribute(self):
        event_body = {'mappings': [{"source_field": "name", "destination_field": "title",
                                    "error_message": "", "enabled": True,
                                    "transformations": [
                                        {
                                            "type": TransformationType.LOWERCASE.value,
                                            "config": {}
                                        },
                                    ]}]}
        event = self.create_lambda_event(body=event_body, query_params={},
                                         path_params={'connection_id': self.connection_id, 'type': 'product'})
        context = self.create_lambda_context()

        result = save_mapping_attribute(event, context)
        print('result', result)

    def test_remove_mapping_attribute(self):
        event_body = {}
        event = self.create_lambda_event(event_body, query_params={},
                                         path_params={'connection_id': self.connection_id, 'type': 'product'})
        context = self.create_lambda_context()

        result = remove_mapping_attribute(event, context)
        print('result', result)
        pass
