from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import List

from marshmallow import fields
from marshmallow_enum import Enum<PERSON>ield
from nolicore.adapters.db.model import AttributesSchema, Attributes


class ObjectTimeRange(Enum):
    DAY = 'DAY'
    WEEK = 'WEEK'
    MONTH = 'MONTH'


@dataclass
class EffectiveCustomerAttributes(Attributes):
    customer_staffs: str = None
    customer_groups: str = None
    customer_gender: str = None
    customer_birthday_date: List[Decimal] = None
    customer_birthday_week: List[Decimal] = None
    customer_birthday_month: List[Decimal] = None
    customer_birthday_range: str = None
    customer_tags: str = None
    customer_effective_birthday_range: ObjectTimeRange = None


class EffectiveCustomerAttributesSchema(AttributesSchema):
    customer_staffs = fields.Str(allow_none=True)
    customer_groups = fields.Str(allow_none=True)
    customer_gender = fields.Str(allow_none=True)
    customer_birthday_date = fields.Decimal(allow_none=True)
    customer_birthday_week = fields.Decimal(allow_none=True)
    customer_birthday_month = fields.Decimal(allow_none=True)
    customer_birthday_range = fields.Str(allow_none=True)
    customer_tags = fields.Str(allow_none=True)
    customer_effective_birthday_range = EnumField(ObjectTimeRange, allow_none=True)
