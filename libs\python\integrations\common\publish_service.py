import json
import boto3
import os
import requests
from botocore.exceptions import ClientError
from datetime import datetime

s3 = boto3.client('s3')
dynamodb = boto3.resource('dynamodb')

CONNECTIONS_TABLE = os.environ['CONNECTIONS_TABLE']
table = dynamodb.Table(CONNECTIONS_TABLE)


class PublishError(Exception):
    pass


def publish_to_s3(data, destination_config):
    bucket = destination_config['bucket']
    prefix = destination_config.get('prefix', '')
    key = f"{prefix}{data['id']}.json"

    try:
        s3.put_object(
            Bucket=bucket,
            Key=key,
            Body=json.dumps(data),
            ContentType='application/json'
        )
        return f"s3://{bucket}/{key}"
    except ClientError as e:
        raise PublishError(f"Failed to publish to S3: {str(e)}")


def publish_to_api(data, destination_config):
    url = destination_config['url']
    headers = destination_config.get('headers', {})

    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        raise PublishError(f"Failed to publish to API: {str(e)}")


def publish_data(connection_id, destination_id, data):
    try:
        connection = table.get_item(Key={'id': connection_id})['Item']
        destination = next((d for d in connection['destinations'] if d['id'] == destination_id), None)

        if not destination:
            raise PublishError(f"Destination {destination_id} not found for connection {connection_id}")

        if destination['type'] == 's3':
            result = publish_to_s3(data, destination)
        elif destination['type'] == 'api':
            result = publish_to_api(data, destination)
        else:
            raise PublishError(f"Unsupported destination type: {destination['type']}")

        update_publish_status(connection_id, destination_id, 'SUCCESS', result)
        return result
    except Exception as e:
        update_publish_status(connection_id, destination_id, 'FAILURE', str(e))
        raise


def update_publish_status(connection_id, destination_id, status, message):
    table.update_item(
        Key={'id': connection_id},
        UpdateExpression=f"SET destinations[{destination_id}].last_publish = :now, "
                         f"destinations[{destination_id}].last_publish_status = :status, "
                         f"destinations[{destination_id}].last_publish_message = :message",
        ExpressionAttributeValues={
            ':now': datetime.utcnow().isoformat(),
            ':status': status,
            ':message': message
        }
    )