from dataclasses import dataclass

from marshmallow import fields

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


@dataclass
class UsedVoucher(BasicAttributes):
    customer_id: str = None
    voucher_code: str = None
    voucher_id: str = None
    reference: str = None
    used_at: str = None


class UsedVoucherSchema(BasicAttributesSchema):
    customer_id = fields.UUID(required=True)
    voucher_code = fields.Str(required=True)
    voucher_id = fields.UUID(required=True)
    reference = fields.UUID(required=True)
    used_at = fields.DateTime(required=True)


class UsedVoucherModel(BasicModel):
    table_name = 'usedVoucher'
    key__id = 'customer_id'
    key__ranges = ['voucher_code']
    attributes: UsedVoucher
    attributes_schema = UsedVoucherSchema
    attributes_class = UsedVoucher

    query_mapping = {
        'query': ['customer_id'],
        'filter': {
            'id': 'query',
            'voucher_id': 'query',
            'voucher_code': 'query',
            'reference': 'query',
            'used_at': 'query',
        }
    }
