from dataclasses import dataclass
from typing import Optional
from marshmallow import fields

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


@dataclass
class SyncResponseAttributes(BasicAttributes):
    connection_id: str = None
    action_type: str = None
    object_key: str = None
    status: str = None
    error_message: Optional[str] = None
    version_id: Optional[str] = None
    transformed_data_version_id: Optional[str] = None


class SyncResponseAttributesSchema(BasicAttributesSchema):
    connection_id = fields.Str(required=True)
    action_type = fields.Str(required=True)
    object_key = fields.Str(required=True)
    status = fields.Str(required=True)
    error_message = fields.Str(allow_none=True)
    version_id = fields.Str(allow_none=True)
    transformed_data_version_id = fields.Str(allow_none=True)


class SyncResponseModel(BasicModel):
    key__id = 'id'
    key__ranges = ['connection_id']
    table_name = 'sync_response'
    attributes: SyncResponseAttributes = None
    attributes_schema = SyncResponseAttributesSchema
    attributes_class = SyncResponseAttributes

    query_mapping = {
        'query': ['id', 'connection_id'],
        'filter': {
            'status': 'query',
            'action_type': 'query',
            'created_at': 'range',
            'updated_at': 'range'
        }
    }

    @property
    def id(self):
        return str(self.attributes.id)

    @classmethod
    def get(cls, connection_id, _id, _except=False):
        return cls.by_key({
            'id': str(_id),
            'connection_id': str(connection_id)
        })

    @classmethod
    def create_sync_response(cls, connection_id: str, action_type: str, object_key: str,
                             status: str, error_message: Optional[str] = None,
                             raw_data_version_id: Optional[str] = None,
                             transformed_data_version_id: Optional[str] = None) -> 'SyncResponseModel':
        sync_response_attributes = SyncResponseAttributes(
            connection_id=connection_id,
            action_type=action_type,
            object_key=object_key,
            status=status,
            error_message=error_message,
            version_id=raw_data_version_id,
            transformed_data_version_id=transformed_data_version_id
        )
        sync_response = cls(sync_response_attributes.__dict__)
        sync_response.save()
        return sync_response

    def update_status(self, new_status: str):
        """
        Update the status of the sync response.
        """
        self.attributes.status = new_status
        self.save()

    def add_error_message(self, error_message: str):
        """
        Add an error message to the sync response.
        """
        if self.attributes.error_message:
            self.attributes.error_message += f"\n{error_message}"
        else:
            self.attributes.error_message = error_message
        self.save()

    @classmethod
    def get_by_connection_and_action(cls, connection_id: str, action_type: str):
        """
        Get all sync responses for a specific connection and action type.
        """
        return cls.query({
            'connection_id': connection_id,
            'action_type': action_type
        })

    @classmethod
    def get_latest_by_connection(cls, connection_id: str):
        """
        Get the latest sync response for a specific connection.
        """
        responses = cls.query({
            'connection_id': connection_id
        }, limit=1, sort_key='created_at', sort_order='DESC')
        return responses[0] if responses else None

    def to_dict(self):
        """
        Convert the sync response to a dictionary.
        """
        return {
            'id': self.id,
            'connection_id': self.attributes.connection_id,
            'action_type': self.attributes.action_type,
            'object_key': self.attributes.object_key,
            'status': self.attributes.status,
            'error_message': self.attributes.error_message,
            'version_id': self.attributes.version_id,
            'transformed_data_version_id': self.attributes.transformed_data_version_id,
            'created_at': self.attributes.created_at,
            'updated_at': self.attributes.updated_at
        }
