import json
import unittest
import uuid
import os
from dotenv import load_dotenv
from nolicore.utils.exceptions import NotFoundRequest
from nolicore.utils.exceptions import NotFoundRequest

from scripts.delete_data import company_id
from scripts.delete_data import company_id
from tests.base import BaseTestCase
from connections.src.apis.connection import setup_connection, test_connection, get_connection
from connections.src.apis.connection import setup_connection, test_connection, get_connection
from integrations.channels.shopify_oauth.shopify_oauth_connection import ShopifyOAuthConnection
from integrations.common.connection_helper import get_connection_by_id


class ShopifyConnectionSetupIntegrationTestCase(BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        load_dotenv()  # Load environment variables from .env file
        cls.api_key = '5a989de7ad713e6bfedc10c4563e6148'
        cls.shop_url = 'https://trannhon-partner.myshopify.com'
        cls.secret_key = '539c6b34bca5b08b47d5aa2071be63f2'
        cls.access_token = 'shpua_c8ffa94b4cf9999e1b0272fbe3ac75e1'

        if not all([cls.shop_url, cls.api_key, cls.secret_key, cls.access_token]):
            raise ValueError("Shopify credentials not found in environment variables")

    def setUp(self):
        # self.maxDiff = None
        # self.company_id = str(uuid.uuid4())
        # self.user_id = str(uuid.uuid4())
        # self.username = f"user_{self.user_id}"

        self.maxDiff = None
        self.company_id = "9e61d187-426a-45ec-914d-7aea8ca7d42d"
        self.user_id = "906ed4d6-26f9-4056-8f3e-ae00a37c3edb"
        self.username = "onexapis_admin"

    def create_lambda_event(self, body, path_parameters=None):
        event = {
            'httpMethod': 'POST',
            'body': json.dumps(body),
            'requestContext': {
                'requestId': 'test_request_id',
                'authorizer': {
                    'jwt': {
                        'claims': {
                            'sub': self.user_id,
                            'cognito:username': self.username,
                            'custom:company_id': self.company_id
                        }
                    }
                }
            }
        }

        if path_parameters:
            event['pathParameters'] = path_parameters
        return event

    def create_lambda_context(self):
        class LambdaContext:
            def __init__(self):
                self.function_name = 'test_function'
                self.function_version = '$LATEST'
                self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test_function'

        return LambdaContext()

    def test_setup_shopify_connection_success(self):
        # Arrange
        event_body = {
            "connection_type": "shopify_oauth",
            "settings": {
                "shop_url": self.shop_url,
                "api_key": self.api_key,
                "secret_key": self.secret_key,
                "access_token": self.access_token
            }
        }
        event = self.create_lambda_event(event_body)
        context = self.create_lambda_context()

        # Act
        response = setup_connection(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'shopify_oauth OAuth2 flow initiated')
        self.assertIn('connection_id', response_body)

        # Verify the created connection
        connection_id = response_body['connection_id']
        created_connection = ShopifyOAuthConnection.get(self.company_id, connection_id)

        self.assertIsNotNone(created_connection)
        self.assertEqual(created_connection.attributes.shop_url, self.shop_url)
        self.assertEqual(created_connection.attributes.api_key, self.api_key)
        self.assertEqual(created_connection.attributes.secret_key, self.secret_key)
        self.assertEqual(created_connection.attributes.company_id, self.company_id)
        self.assertEqual(created_connection.attributes.status, 'ACTIVE')

        # Clean up
        created_connection.delete()

    def test_test_connection(self):
        # Create a mock connection record to test
        connection_id = '8dc562cf-c352-45c9-8caf-eec31189ec66'
        connection_data = {
            "connection_id": connection_id,
            "connection_type": "shopify_oauth",
            "settings": {
                "shop_url": self.shop_url,
                "api_key": self.api_key,
                "secret_key": self.secret_key,
                "access_token": self.access_token
            }
        }

        test_event = self.create_lambda_event({}, {'connection_id': connection_id})
        test_context = self.create_lambda_context()
        result = test_connection(test_event, test_context)

        # Assert
        self.assertEqual(result['statusCode'], 200)
        result_body = json.loads(result['body'])
        self.assertEqual(result_body, {"message": "Connection test successful", "status": "success"})

    def test_get_product__fetch(self):
        connection_id = '52d86b42-c2b2-4812-9b7b-4acd00f4bd7d'
        test_event = self.create_lambda_event({}, {'connection_id': connection_id})
        test_context = self.create_lambda_context()

        connection = get_connection_by_id(connection_id, self.company_id, False)
        channel = connection.get_channel()
        result = channel.get_product__fetch({'first': 1})
        print('result',result)
        
        # Assert the response structure
        self.assertIsNotNone(result)
        self.assertTrue(result.success)  # Access success as property
        self.assertIsNotNone(result.data)  # Access data as property
        self.assertIsInstance(result.data, list)

    def test_get_product__params(self):
        connection_id = '8b0972ca-762b-4cfa-87bd-9920bcd134c4'
        connection = get_connection_by_id(connection_id, self.company_id, False)
        channel = connection.get_channel()
        result = channel.get_product__params({}, {})
        self.assertIsNotNone(result)
        self.assertTrue(result.success)

if __name__ == '__main__':
    unittest.main()
