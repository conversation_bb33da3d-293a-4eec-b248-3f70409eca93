sequenceDiagram
    participant Client
    participant setup_connection
    participant _setup_connection
    participant ConnectionRegistry
    participant ConnectionClass
    participant OAuth2Callback

    Client->>setup_connection: Request with connection_type and setup_data
    setup_connection->>_setup_connection: Call with parameters
    _setup_connection->>ConnectionRegistry: Get connection class
    ConnectionRegistry-->>_setup_connection: Return connection class
    _setup_connection->>ConnectionClass: create_connection(setup_data)
    ConnectionClass-->>_setup_connection: Return connection instance
    alt is OAuth2Connection
        _setup_connection->>ConnectionClass: get_authorization_url()
        ConnectionClass-->>_setup_connection: Return auth URL
        _setup_connection->>_setup_connection: Save connection as 'pending'
        _setup_connection-->>setup_connection: Return auth URL and connection ID
        setup_connection-->>Client: Return auth URL and connection ID
        Client->>OAuth2Callback: Call with auth code
        OAuth2Callback->>ConnectionClass: handle_oauth_callback()
        ConnectionClass->>ConnectionClass: Update and activate connection
        OAuth2Callback-->>Client: Return success message
    else
        _setup_connection->>ConnectionClass: save()
        _setup_connection-->>setup_connection: Return connection ID
        setup_connection-->>Client: Return success message and connection ID
    end