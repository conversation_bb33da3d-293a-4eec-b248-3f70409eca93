import unittest

from tests.base import BaseTestCase
from onexbots.src.apis.department import _list, add, get, update
from nolicore.utils.exceptions import BadRequest


class TestDepartment(BaseTestCase):
    def setUp(self):
        super().setUp('onexapis_admin')

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

    def test_create_department(self):
        """Test creating a new department"""
        # Test successful creation
        test_data = {
            "name": "Test Department 233",
            "description": "Test Department 233",
        }

        # Create API Gateway request
        event = self.create_lambda_event(
            body=test_data,
            is_authorized=True
        )

        # Call the API function
        response = add(event, self.create_lambda_context())

        # Verify response
        self.assertEqual(response['name'], test_data['name'])

        # Test missing required fields
        invalid_data = {
            "name": "Test Department"
        }
        event = self.create_lambda_event(
            body=invalid_data,
            is_authorized=True
        )

        with self.assertRaises(BadRequest) as context:
            add(event, self.create_lambda_context())
        self.assertIn("Missing required field", str(context.exception))

    def test_get_department(self):
        """Test getting a department by ID"""


        # Test getting the created conversation
        event = self.create_lambda_event(
            path_params={'id': "3f489d60-6867-42e7-b7b6-f20c593d68ba"},
            is_authorized=True
        )
        response = get(event, self.create_lambda_context())
        self.assertEqual(response['name'], "Test Department 233")


    def test_update_department(self):
        """Test updating a department"""
        test_data = {
            "name": "Test Department 233",
            "description": "Test Department 233",
        }

        # Create API Gateway request    
        event = self.create_lambda_event(
            path_params={'id': "3f489d60-6867-42e7-b7b6-f20c593d68ba"},
            body=test_data,
            is_authorized=True
        )
        response = update(event, self.create_lambda_context())
        self.assertEqual(response['name'], "Test Department 233")

    def test_list_departments(self):
        """Test listing departments"""

        # Test listing with filters
        event = self.create_lambda_event(
            query_params={
                "limit": "10",
                "page": "0"
            },
            is_authorized=True
        )
        response = _list(event, self.create_lambda_context())
        self.assertIn('items', response)

if __name__ == '__main__':
    unittest.main()
