from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.finance.account import Account, AccountSchema


@dataclass
class PaymentMethod(Attributes):
    id: str = None
    name: str = None
    account: Account = None
    note: str = None
    settings: dict = None


class PaymentMethodSchema(AttributesSchema):
    id = fields.UUID(required=True)
    name = fields.Str(required=True)
    account = fields.Nested(AccountSchema(Account), required=True)
    note = fields.Str(allow_none=True)
    settings = fields.Dict(allow_none=True)


@dataclass
class PaymentMethodAttributes(PaymentMethod, BasicAttributes):
    pass


class PaymentMethodAttributesSchema(BasicAttributesSchema, PaymentMethodSchema):

    @staticmethod
    def sample():
        return {
            "note": None,
            "settings": None,
            "account": {
                "account_number": "asd",
                "note": "asd",
                "user_id": "e9da356c-b0c1-70f6-4290-8ca64e679fc5",
                "account_name": "asd",
                "bank_name": "Vietcombank",
                "name": "Viettinbank",
                "id": "********-4954-452d-b3c7-3d46361d56c7",
                "type": "BANK"
            },
            "company_id": "e9da356c-b0c1-70f6-4290-8ca64e679fc5",
            "updated_at": "2023-09-06T08:12:03.312199+00:00",
            "name": "Chuyển khoản",
            "created_at": "2023-09-06T08:12:03.312199+00:00",
            "id": "9c72e215-0f4b-4983-8d9e-a5da6cf6d05d"
        }


class PaymentMethodModel(BasicModel):
    table_name = 'paymentMethod'
    attributes: PaymentMethodAttributes
    attributes_schema = PaymentMethodAttributesSchema
    attributes_class = PaymentMethodAttributes

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'account.type': 'query',
            'account.id': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'account': 'account'
        }
    }

    @classmethod
    def initialize_data(cls, user_attributes_obj, initialized_data):
        company_id = user_attributes_obj.get('custom:company_id')
        data = [
            {"name": "Cash"}
        ]
        accounts = initialized_data.get("account", [])
        formatted_accounts = [
            {k: v for k, v in t.items() if k not in {
                'created_at', 'company_id', 'updated_at', 'user'
            }} for t in accounts
        ]
        account = formatted_accounts[0]
        init_data = [BasicAttributes.add_basic_attributes({**item, "account": account}, company_id) for item in data]
        initialized_data[cls.table_name] = init_data
        cls.batch_add(init_data)
