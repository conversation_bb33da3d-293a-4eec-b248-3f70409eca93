from typing import Any
from ..transformation import <PERSON>Trans<PERSON>Hand<PERSON>, TransformationConfig, TransformationDirection


class DirectTransformationHandler(BaseTransformationHandler):
    """Handler for direct value transformation (no transformation)"""
    
    config_definition = TransformationConfig(
        label="Direct Mapping",
        description="Maps the source field directly to the target field without any transformation",
        direction=TransformationDirection(
            title="Direct Copy",
            content="Copy the value directly without any changes",
            example='"Hello World" → "Hello World"',
            value_type="Single Value"
        )
    )
    
    def handle_transform(self, value: Any) -> Any:
        """Returns the value without any transformation"""
        return value 