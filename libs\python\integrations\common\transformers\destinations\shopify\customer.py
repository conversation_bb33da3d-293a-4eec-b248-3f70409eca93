from ...base import SourceTransformer


class ShopifyCustomerTransformer(SourceTransformer):
    channel_name = 'shopify'
    object_type = 'customer'

    def transform(self, data):
        return {
            'id': data.get('id'),
            'first_name': data.get('first_name'),
            'last_name': data.get('last_name'),
            'email': data.get('email'),
            'phone': data.get('phone'),
            'address': {
                'street': data.get('default_address', {}).get('address1'),
                'city': data.get('default_address', {}).get('city'),
                'state': data.get('default_address', {}).get('province'),
                'postal_code': data.get('default_address', {}).get('zip'),
                'country': data.get('default_address', {}).get('country')
            }
        }

    @property
    def input_schema(self):
        return {
            'type': 'object',
            'properties': {
                'id': {'type': 'string'},
                'first_name': {'type': 'string'},
                'last_name': {'type': 'string'},
                'email': {'type': 'string'},
                'phone': {'type': 'string'},
                'default_address': {
                    'type': 'object',
                    'properties': {
                        'address1': {'type': 'string'},
                        'city': {'type': 'string'},
                        'province': {'type': 'string'},
                        'zip': {'type': 'string'},
                        'country': {'type': 'string'}
                    }
                }
            }
        }

    @property
    def output_schema(self):
        return {
            'type': 'object',
            'properties': {
                'id': {'type': 'string'},
                'first_name': {'type': 'string'},
                'last_name': {'type': 'string'},
                'email': {'type': 'string'},
                'phone': {'type': 'string'},
                'address': {
                    'type': 'object',
                    'properties': {
                        'street': {'type': 'string'},
                        'city': {'type': 'string'},
                        'state': {'type': 'string'},
                        'postal_code': {'type': 'string'},
                        'country': {'type': 'string'}
                    }
                }
            }
        }
