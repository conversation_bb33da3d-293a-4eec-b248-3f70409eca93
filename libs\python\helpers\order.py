import math
import os
import uuid
from decimal import Decimal
from typing import List

import pendulum
from nolicore.utils.api import api_message
from nolicore.utils.exceptions import NotFoundRequest, BadRequest, InvalidSchemaExp

from helpers.common import get_default_source
from helpers.customer import update_customer, check_point
from helpers.errors import ProductNotFound, LocationNotFound, OutOfStock, OrderStatusInvalid
from helpers.finance import sync_payment
from helpers.inventory import update_inventory
from helpers.lambda_client import lambda_client_invoke_async
from helpers.package import ship, delivered, complete, return_, cancel_package
from helpers.pos import create_order_update_shift
from helpers.utils import get_increment_id
from helpers.voucher import handle_check_valid_voucher
from models.actor.customer import CustomerModel, CustomerSchema, Customer
from models.basic import BasicAttributes
from models.common.address import AddressSchema, Address
from models.common.default_obj import DefaultObjectSchema, DefaultObject
from models.common.order_line import OrderLineItem
from models.common.status import Status, PaymentStatus, update_payment_status, update_status, OrderType
from models.finance.transaction import TransactionSchema, Transaction
from models.finance.voucher import ReservedVoucher
from models.inventory.location import LocationModel
from models.loyalty.loyalty_program import LoyaltyProgramModel
from models.loyalty.reward_program import RewardProgramModel
from models.order.order import OrderModel, OrderExternalIdIndex
from models.order.package import PackageModel
from models.product.variant import VariantModel, VariantSkuIndex
from models.promotion.discount import DiscountStatus, DiscountModel, DiscountType
from models.promotion.discount_info.discount_amount import DiscountAmountType
from models.promotion.discount_info.discount_product import ItemType

ENV = os.getenv('ENV')


def get_order_by_id(company_id, order_id) -> OrderModel:
    key = {
        OrderModel.key__id: order_id,
        'company_id': company_id
    }

    order = OrderModel.by_key(key)
    if order is None:
        raise NotFoundRequest('Order not found')
    return order


def get_order_obj(company_id, order, order_id=None):
    order_id = order_id or order.get('id')
    external_id = order.get('external_id')
    order_obj = None
    if order_id:
        order_obj = OrderModel.by_key({
            OrderModel.key__id: order_id,
            'company_id': company_id
        })

    if order_obj is None and external_id is not None:
        try:
            order_obj = OrderModel(OrderExternalIdIndex.list({
                OrderExternalIdIndex.key__id: external_id,
                'company_id': company_id
            }, limit=10)['Items'][0])
        except IndexError:
            pass

    return order_obj


def get_variant_by_order_item(company_id, order_line) -> VariantModel:
    variant = None
    variant_id = order_line.get("variant_id")
    if variant_id:
        variant = VariantModel.by_key({
            VariantModel.key__id: variant_id,
            'company_id': company_id
        })
    if variant is None and order_line.get('sku') is not None:
        variant = VariantSkuIndex.get_variant_by_sku(order_line['sku'], company_id)

    return variant


def sync_order(order, company_id, order_id=None, user=None) -> OrderModel:
    location_ids = {}
    for order_line in order['order_line_items']:
        # validate variant
        if not order_line.get('custom', None):
            variant = get_variant_by_order_item(company_id, order_line)
            if variant is None:
                raise ProductNotFound(f'Sku not found {order_line["sku"]}')
            variant_brand = variant.attributes.brand
            if variant_brand:
                order_line['brand'] = {
                    "id": variant_brand.id,
                    "name": variant_brand.name
                }
            variant_category = variant.attributes.category
            if variant_category:
                order_line['category'] = {
                    "id": variant_category.id,
                    "name": variant_category.name
                }
            order_line['product_id'] = str(variant.attributes.product_id)
            order_line['variant_id'] = str(variant.attributes.id)
            order_line['variant_name'] = variant.attributes.name
            order_line['image_url'] = variant.attributes.images[0].url if variant.attributes.images else None
            if order_line.get('unit_price') is None:
                order_line['unit_price'] = variant.attributes.prices[0].price

        # Calculate discount
        item_discount = order_line.get('discount', 0)
        if 'sale_price' in order_line:
            item_discount = Decimal(order_line['unit_price']) - Decimal(order_line['sale_price'])
        order_line['discount'] = item_discount
        order_line['sale_price'] = Decimal(order_line['unit_price']) - Decimal(item_discount)
        order_line['price'] = Decimal(str(order_line['quantity'])) * Decimal(str(order_line['sale_price']))

        if 'id' not in order_line:
            order_line['id'] = str(uuid.uuid4())

        # validate location
        location = order_line.get('location')
        if location and location['id'] and location["id"] not in location_ids:
            location_obj = LocationModel.by_key({'id': location["id"], 'company_id': company_id})
            if location_obj is None:
                raise LocationNotFound(order_line)
            location_ids[location["id"]] = {
                'id': location["id"],
                'name': location_obj.attributes.name
            }
        if location and location['id']:
            order_line['location'] = location_ids[location["id"]]
        else:
            order_line['location'] = None

    sub_total = sum(
        [Decimal(str(item['quantity'])) * Decimal(str(item['sale_price'])) for item in order['order_line_items']])
    other_fees = order.get('other_fees')
    if isinstance(other_fees, dict):
        other_fees = [{
            'name': fee_name,
            'amount': fee_value
        } for fee_name, fee_value in other_fees.items()]
    other_fee_amount = sum(Decimal(str(fee['amount'])) for fee in other_fees) if other_fees else 0
    shipping_fee = Decimal(str(order.get('shipping_fee', 0)))
    discount = Decimal(str(order.get('discount', 0)))
    tax = Decimal(str(order.get('tax', 0)))
    tax_rate = Decimal(str(order.get('tax_rate', 0)))
    total = sub_total + shipping_fee + tax - discount - other_fee_amount

    customer = CustomerModel.sync(company_id, order['customer'])
    customer_group = customer.get("customer_group")

    if customer_group is not None:
        discount_percent = customer.get("discount_percent", 0)
        max_discount_amount_per_order = customer.get("max_discount_amount_per_order")
        min_order_amount = customer.get("min_order_amount")
        discount_amount_by_customer_group = Decimal(discount_percent) * sub_total / 100
        if min_order_amount is None:
            if max_discount_amount_per_order is not None:
                discount_by_customer_group = min(discount_amount_by_customer_group,
                                                 Decimal(max_discount_amount_per_order))
            else:
                discount_by_customer_group = discount_amount_by_customer_group
        else:
            if Decimal(min_order_amount) <= sub_total:
                if max_discount_amount_per_order is not None:
                    discount_by_customer_group = min(discount_amount_by_customer_group,
                                                     Decimal(max_discount_amount_per_order))
                else:
                    discount_by_customer_group = discount_amount_by_customer_group
            else:
                discount_by_customer_group = 0
    else:
        discount_by_customer_group = 0

    total -= discount_by_customer_group

    sub_voucher_total = 0
    if order.get('voucher_id') and order.get('voucher_code'):
        try:
            value_voucher = handle_check_valid_voucher(order['voucher_id'], company_id, user['id'],
                                                       order['voucher_code'],
                                                       sub_total, order['order_line_items'])
            if value_voucher['apply_with_other_promotion']:
                sub_voucher_total = value_voucher['total_apply_voucher']
                total = total - Decimal(str(sub_voucher_total))
            else:
                has_discount = any(Decimal(str(product["discount"])) > 0 for product in order['order_line_items'])
                if has_discount or discount > 0:
                    sub_voucher_total = 0
                else:
                    sub_voucher_total = value_voucher['total_apply_voucher']
                    total = total - Decimal(str(sub_voucher_total))
        except ValueError as ve:
            raise BadRequest(ve)

    order_payload = {
        **order,
        'sub_total': sub_total,
        'shipping_fee': shipping_fee,
        'other_fees': other_fees,
        'discount': discount,
        'discount_by_customer_group': discount_by_customer_group,
        'tax': tax,
        'tax_rate': tax_rate,
        'total': total,
        'customer': customer,
        'sub_voucher_total': sub_voucher_total,
    }
    status = order.get('status')
    order_type = order.get('order_type', OrderType.ECOMMERCE.value)
    if order_type == OrderType.IMPORTED.value:
        order_payload['status'] = status
    if 'external_id' in order:
        order_payload['external_id'] = order['external_id']
    order_obj = get_order_obj(company_id, order, order_id=order_id)
    if order_obj is None:
        order_payload = BasicAttributes.add_basic_attributes(order_payload, company_id, user)
        if 'external_id' not in order_payload:
            order_payload['external_id'] = order_payload['id']
        order_payload['number'] = get_increment_id(company_id, OrderModel)
        if order_payload.get('source') is None:
            order_payload['source'] = get_default_source()
        order_obj = OrderModel(order_payload)
        order_obj.save()
        if order_type != OrderType.IMPORTED.value:
            update_status(user, order_obj, Status.DRAFT)
        else:
            update_status(user, order_obj, Status(status))
    else:
        order_obj.update(order_payload, user)
    # if order_obj.attributes.order_type != OrderType.IMPORTED:
    #     redeem_point = order_obj.attributes.redeem_point
    #     decrease_point(company_id, order_obj, CustomerModel(customer), redeem_point)
    return order_obj


def sync_status(company_id, user, order_obj: OrderModel, status):
    current_packages = OrderModel.get_packages(company_id, order_obj.attributes.id)
    if status == Status.PENDING.value:
        user_id = user.get('id') if isinstance(user, dict) else None
        # confirm(company_id, user_id, order_obj.attributes.id, order_obj)
        update_payment_status(user_id, order_obj, PaymentStatus.PENDING_ESCROW)
    if status == Status.CANCELLED.value:
        for package in current_packages:
            package_obj = PackageModel(package)
            cancel_package(company_id, user, package_obj)
        cancel(company_id, user, order_obj.attributes.id, order_obj)

    if status == Status.IN_CANCEL.value:
        request_cancel(company_id, user, order_obj.attributes.id, order_obj)
    if status == Status.SHIPPING.value:
        for package in current_packages:
            package_obj = PackageModel(package)
            ship(company_id, user, package_obj)
    if status == Status.DELIVERED.value:
        for package in current_packages:
            package_obj = PackageModel(package)
            delivered(company_id, user, package_obj)
    if status == Status.COMPLETED.value:
        for package in current_packages:
            package_obj = PackageModel(package)
            complete(company_id, user, package_obj)
    if status == Status.RETURNING.value:
        for package in current_packages:
            package_obj = PackageModel(package)
            return_(company_id, user, package_obj)


def sync_order_payment(user, order: OrderModel, payments: List[dict]):
    company_id = str(order.attributes.company_id)
    total = order.attributes.total

    # validate reward program
    redeem_point = order.attributes.redeem_point
    reward_program = order.attributes.reward_program
    if reward_program is not None and redeem_point is not None:
        reward_program_id = reward_program.id
        reward_program_obj = RewardProgramModel.get_effective_program(company_id, reward_program_id)
        if reward_program_obj is None:
            raise BadRequest("No effective reward program found.")
        customer = order.attributes.customer
        check_point(customer, redeem_point)
        discount_amount_by_point = Decimal(redeem_point) * reward_program_obj.attributes.money
        total = total - discount_amount_by_point

    current_payments = order.attributes.payments
    reference = order.attributes.id
    effective_date = pendulum.now().to_iso8601_string()
    note = order.attributes.note
    voucher_id = ReservedVoucher.ORDER_PAYMENT_VOUCHER.value
    transactions = sync_payment(company_id, user, total, current_payments, payments, voucher_id,
                                reference=reference, note=note, effective_date=effective_date,
                                is_destination_account=True)

    payments_data = {
        'payments': [
                        TransactionSchema(Transaction).dump(t) for t in current_payments or []
                    ] + [
                        {k: v for k, v in t.items() if k not in {
                            'created_at', 'company_id', 'updated_at', 'user'
                        }} for t in transactions
                    ]
    }
    payment_status = PaymentStatus.PAID if math.isclose(sum(
        [payment['amount'] for payment in payments_data['payments']]
    ), total) else PaymentStatus.PARTIAL_PAID
    user_id = user.get("id")
    update_payment_status(user_id, order, payment_status, extras=payments_data)
    customer = order.attributes_dict.get("customer")
    customer_obj = CustomerModel.get_customer(company_id, customer)

    total = sum([transaction['amount'] for transaction in transactions])
    increase_point(company_id, order, customer_obj, total)
    return transactions


def confirm(company_id, user, order_id, order_obj: OrderModel = None):
    order_obj = order_obj if order_obj else get_order_by_id(company_id, order_id)
    if order_obj.attributes.status == Status.DRAFT:
        update_inventory(OrderModel, order_obj, company_id, user, update_type="confirm", status=Status.PENDING)
        return api_message('Order confirmed')

    raise OrderStatusInvalid()


def pay(company_id, user, order_id=None, order_obj: OrderModel = None, payments: list = None):
    order_obj = order_obj if order_obj else get_order_by_id(company_id, order_id)
    if order_obj.attributes.payment_status not in [PaymentStatus.PAID]:
        return sync_order_payment(user, order_obj, payments)
    raise OrderStatusInvalid()


def increase_point(company_id, order_obj: OrderModel, customer: CustomerModel, total):
    loyalty_program_obj = LoyaltyProgramModel.get_effective_program(company_id)
    if loyalty_program_obj is not None:
        point = total // loyalty_program_obj.attributes.money
        update_customer(company_id, customer, order_obj, "pay", loyal_point=point)
        LoyaltyProgramModel.increase_used_number(company_id, loyalty_program_obj.attributes.id)
        loyal_program = {k: v for k, v in loyalty_program_obj.attributes_dict.items() if k not in {
            'created_at', 'company_id', 'updated_at', 'user'
        }}
        order_obj.update({"loyal_point": point, "loyalty_program": loyal_program})


def decrease_point(company_id, order_obj: OrderModel, customer: CustomerModel, redeem_point):
    if redeem_point is not None:
        reward_program_obj = RewardProgramModel.get_effective_program(company_id)
        if reward_program_obj is not None:
            update_customer(company_id, customer, order_obj, "create", redeem_point=redeem_point)
            RewardProgramModel.increase_used_number(company_id, reward_program_obj.attributes.id)


def cancel_point(company_id, order_obj: OrderModel, customer: CustomerModel):
    update_customer(company_id, customer, order_obj, "cancel")


def pay_and_complete(company_id, user, order_id=None, order_obj: OrderModel = None, payments: list = None):
    response = pay(company_id, user, order_id=order_id, order_obj=order_obj, payments=payments)
    order_obj = order_obj if order_obj else get_order_by_id(company_id, order_id)
    if order_obj.attributes.payment_status in [PaymentStatus.PAID]:
        order_obj = update_inventory(OrderModel, order_obj, company_id, user, update_type="pay_pos",
                                     status=Status.COMPLETED)
        shift_id = order_obj.attributes.shift_id
        create_order_update_shift(order_obj, shift_id, company_id, user)
    return api_message('Order paid')


def cancel(company_id, user, order_id, order_obj: OrderModel = None):
    order_obj = order_obj if order_obj else get_order_by_id(company_id, order_id)
    if order_obj.attributes.status == Status.CANCELLED:
        return api_message('Order canceled')

    if order_obj.attributes.status in [Status.DRAFT, Status.PENDING, Status.IN_CANCEL, Status.AWAIT_PACKING,
                                       Status.PACKING,
                                       Status.PARTIAL_PACKING, Status.READY, Status.PARTIAL_READY]:
        if order_obj.attributes.status != Status.DRAFT:
            if order_obj.attributes.packages is None:
                update_inventory(OrderModel, order_obj, company_id, user, update_type="cancelled")
            else:
                for item in order_obj.attributes.packages:
                    package_obj = PackageModel(BasicAttributes.add_basic_attributes(item, company_id))
                    if package_obj.attributes.status in [Status.PENDING, Status.PACKING, Status.READY]:
                        update_inventory(PackageModel, package_obj, company_id, user, update_type="cancelled",
                                         status=Status.CANCELLED)
        update_status(user, order_obj, Status.CANCELLED)
        customer = order_obj.attributes_dict.get("customer")
        customer_obj = CustomerModel.get_customer(company_id, customer)
        cancel_point(company_id, order_obj, customer_obj)
        return api_message('Order canceled')

    raise OrderStatusInvalid()


def request_cancel(company_id, user, order_id, order_obj: OrderModel = None):
    order_obj = order_obj if order_obj else get_order_by_id(company_id, order_id)
    if order_obj.attributes.status in [Status.DRAFT, Status.PENDING, Status.AWAIT_PACKING, Status.PACKING,
                                       Status.PARTIAL_PACKING, Status.READY, Status.PARTIAL_READY]:
        update_status(user, order_obj, Status.IN_CANCEL)
        return api_message('Cancel requested')

    raise OrderStatusInvalid()


def pack(company_id, user, order_id, order_obj: OrderModel = None, packages: list = None):
    if order_obj.attributes.status not in [Status.DRAFT, Status.PENDING]:
        return api_message('Order is packed')
    current_packages = OrderModel.get_packages(company_id, order_id)
    order_line_items_without_service = [item for item in order_obj.attributes.order_line_items if
                                        not getattr(item, 'custom', None)]
    sku_dict = {item.sku: item for item in order_line_items_without_service}
    packed_sku_dict = {}
    if current_packages:
        for package in current_packages:
            for item in package['order_line_items']:
                item_sku = item['sku']
                packed_sku_dict.setdefault(item_sku, 0)
                packed_sku_dict[item_sku] += item['quantity']

    # validate quantity
    for package in packages:
        location_id = package['location_id']
        location_obj = LocationModel.get(company_id, location_id)
        if location_obj is None:
            raise LocationNotFound(location_id)
        for item in package['order_line_items']:
            sku = item['sku']
            order_line_item: OrderLineItem = sku_dict[sku]
            if Decimal(str(item['quantity'])) > order_line_item.quantity - packed_sku_dict.get(sku, 0):
                raise OutOfStock(f'Invalid quantity - sku {sku}')
            item['id'] = order_line_item.id
            item['price'] = order_line_item.price
            item['location'] = DefaultObjectSchema(DefaultObject).dump(order_line_item.location)
            item['sale_price'] = order_line_item.sale_price
            item['unit_price'] = order_line_item.unit_price
            item['product_id'] = order_line_item.product_id
            item['variant_id'] = order_line_item.variant_id
            item['variant_name'] = order_line_item.variant_name
            item['image_url'] = order_line_item.image_url

    responses = []
    for package in packages:
        package_id = str(uuid.uuid4())
        package['id'] = package_id
        package['order_id'] = str(order_id)
        package['source'] = order_obj.attributes.source
        package['billing_address'] = AddressSchema(Address).dump(order_obj.attributes.billing_address)
        package['shipping_address'] = AddressSchema(Address).dump(order_obj.attributes.shipping_address)
        package['customer'] = CustomerSchema(Customer).dump(order_obj.attributes.customer)
        package['staff_id'] = order_obj.attributes.staff.id
        package['sub_total'] = order_obj.attributes.sub_total
        package['total'] = order_obj.attributes.total
        package['priority'] = order_obj.attributes.priority
        package['tax'] = order_obj.attributes.tax
        package['tax_rate'] = order_obj.attributes.tax_rate
        package['shipping_fee'] = order_obj.attributes.shipping_fee
        package['package_number'] = get_increment_id(company_id, PackageModel)
        package['order_number'] = order_obj.attributes.number

        package_payload = BasicAttributes.add_basic_attributes(package, company_id)
        package_obj = PackageModel(package_payload)
        package_obj.save()
        responses.append(package_obj.attributes_dict)

    OrderModel.update_order_packing_status(user, company_id, order_obj)

    return {
        'packages': responses
    }


def quick_pack(company_id, user, order_id, order_obj: OrderModel, package: dict):
    order_line_items_without_service = [item for item in order_obj.attributes.order_line_items if
                                        not getattr(item, 'custom', None)]
    packages = [
        {
            "package_number": package['package_number'],
            "shipping_provider": package.get('shipping_provider'),
            "tracking_number": package.get('tracking_number'),
            "location_id": str(order_obj.attributes.order_line_items[0].location.id),
            "order_line_items": [
                {
                    "quantity": item.quantity,
                    "sku": item.sku,
                    "note": item.note
                } for item in order_line_items_without_service
            ]
        }
    ]
    return pack(company_id, user, order_id, order_obj=order_obj, packages=packages)


def check_discount_activate(discount_id, company_id, order):
    discount_key = {
        DiscountModel.key__id: discount_id,
        'company_id': company_id
    }
    discount_obj = DiscountModel.by_key(discount_key)
    if discount_obj is None:
        raise BadRequest('Discount not found')
    discount = discount_obj.attributes_dict
    discount_status = discount.get('status')
    discount_publish = discount.get('publish')
    discount_start_at = discount.get('start_at')
    discount_expire_at = discount.get('expire_at')
    discount_effective_time_range = discount.get('effective_time_range')
    discount_effective_days_of_week = discount.get('effective_days_of_week')
    discount_effective_days_of_month = discount.get('effective_days_of_month')
    discount_effective_months = discount.get('effective_months')
    discount_locations = discount.get('locations')
    discount_sources = discount.get('sources')
    discount_used_times = discount.get('used_times')
    discount_max_using_times = discount.get('max_using_times')

    # validate later
    discount_customers = discount.get('customers ')

    current_time = pendulum.now()
    if discount_publish is False:
        raise BadRequest('Discount is not published')
    if discount_status is not DiscountStatus.RUNNING.value:
        raise BadRequest('Discount is not running')
    if pendulum.parse(discount_start_at) > current_time:
        raise BadRequest(f'Discount starts in {discount_start_at}')
    if discount_expire_at is not None:
        if pendulum.parse(discount_expire_at) < current_time:
            raise BadRequest(f'Discount has expired at {discount_expire_at}')
    if discount_effective_time_range is not None:
        if "-" in discount_effective_time_range:
            start_time_hour = Decimal(str(discount_effective_time_range.split("-")[0].split(":")[0]))
            start_time_minute = Decimal(str(discount_effective_time_range.split("-")[0].split(":")[1]))
            end_time_hour = Decimal(str(discount_effective_time_range.split("-")[1].split(":")[0]))
            end_time_minute = Decimal(str(discount_effective_time_range.split("-")[1].split(":")[1]))
            current_hour = current_time.hour
            current_minute = current_time.minute
            if start_time_hour > current_hour > end_time_hour:
                raise BadRequest(f'Not in active hour')
            if start_time_hour == current_hour and start_time_minute > current_minute:
                raise BadRequest(f'Not in active hour')
            if end_time_hour == current_hour and end_time_minute < current_minute:
                raise BadRequest(f'Not in active hour')
    if discount_effective_days_of_week is not None:
        current_day_of_week = str(current_time.day_of_week)
        if current_day_of_week not in discount_effective_days_of_week:
            raise BadRequest(f'Not in activate days of week')
    if discount_effective_days_of_month is not None:
        current_day_of_month = str(current_time.day)
        if current_day_of_month not in discount_effective_days_of_month:
            raise BadRequest(f'Not in activate days of month')
    if discount_effective_months is not None:
        current_month = str(current_time.month)
        if current_month not in discount_effective_months:
            raise BadRequest(f'Not in activate months')
    if discount_locations is not None:
        for order_line_item in order['order_line_items']:
            check = next((item for item in discount_locations if item['id'] == order_line_item.get('location_id')),
                         None)
            if check is None:
                raise BadRequest(f'Not in active locations')
    if discount_sources is not None:
        order_source = order.get('source')
        if order_source is None:
            raise BadRequest(f'Source required')
        check = next((item for item in discount_sources if item['id'] == order_source.get('id')), None)
        if check is None:
            raise BadRequest(f'Not in active sources')
    if discount_customers is not None:
        raise BadRequest(f'Not validate customer yet. Try later!')
    if discount_max_using_times is not None:
        if discount_used_times == discount_max_using_times:
            raise BadRequest(f'This discount has reached its maximum usage')

    # order_total case
    if discount['type'] == DiscountType.BY_ORDER_TOTAL.value:
        order_total = order['total']
        condition = next((item for item in discount.get('discount_by_order_total', []) if
                          (item.get('amount_to') is not None and item['amount_to'] >= order_total >= item[
                              'amount_from']) or
                          (item.get('amount_to') is None and order_total >= item['amount_from'])
                          ), None)
        if condition is None:
            raise BadRequest(f'Discount can not apply for this order')
        if condition.get('type') == DiscountAmountType.VALUE.value:
            order['discount'] = condition.get('amount')
        else:
            order['discount'] = (condition.get('rate') * order_total) / 100
        modify_order(order)

    order_line_items = order['order_line_items']
    order_line_items_without_service = [item for item in order_line_items if not getattr(item, 'custom', None)]
    discount_details = order['discount_details']

    if discount['type'] == DiscountType.BY_PRODUCT.value:
        new_filter_order_line_items = []
        discount_conditions = discount['discount_by_product']
        for_all_items = discount_conditions[0].get("for_all_items", None)
        if isinstance(for_all_items, bool):
            for condition in discount_conditions:
                if for_all_items:
                    total_quantity = sum(Decimal(str(item["quantity"])) for item in order_line_items_without_service)
                    if condition["min_quantity"] <= total_quantity <= condition["discount_limit"]:
                        new_filter_order_line_items = [
                            {**item,
                             "sale_price": item["price"] - (
                                 condition["amount"] if condition["type"] == DiscountAmountType.VALUE.value
                                 else (condition["rate"] * item["price"]) / 100),
                             } for item in order_line_items_without_service
                        ]
                else:
                    filtered_order_items = filter_order_line_items(condition, order_line_items)
                    total_quantity = sum(Decimal(str(item["quantity"])) for item in filtered_order_items)
                    if condition["min_quantity"] <= total_quantity <= condition["discount_limit"]:
                        new_filter_order_line_items += [
                            {**item,
                             "sale_price": item["price"] - (
                                 condition["amount"] if condition["type"] == DiscountAmountType.VALUE.value
                                 else (condition["rate"] * item["price"]) / 100),
                             } for item in filtered_order_items
                        ]
        discount_details_match = all(
            any(
                new_item["sku"] == discount_detail["sku"] and new_item["sale_price"] == discount_detail["sale_price"]
                for new_item in new_filter_order_line_items
            )
            for discount_detail in discount_details
        )
        if discount_details_match is False:
            raise BadRequest(f'Discount can not apply for this order')
        order["order_line_items"] = [
            next((discount_detail for discount_detail in discount_details if
                  discount_detail["sku"] == order_line_item["sku"]), order_line_item)
            for order_line_item in order_line_items
        ]
        modify_order(order)

    if discount['type'] == DiscountType.BY_QUANTITY.value:
        new_filter_order_line_items = []
        conditions = discount['discount_by_quantity']
        for_all_items = conditions[0].get("for_all_items", None)
        if isinstance(for_all_items, bool):
            for condition in conditions:
                if for_all_items:
                    total_quantity = sum(Decimal(str(item["quantity"])) for item in order_line_items_without_service)
                    quantity_condition = next(
                        (cc for cc in condition["conditions"] if (cc.get("quantity_to")
                                                                  and cc["quantity_from"] <= total_quantity <= cc[
                                                                      "quantity_to"])
                         or (cc.get("quantity_to") is None and cc["quantity_from"] <= total_quantity)),
                        None
                    )
                    if quantity_condition:
                        new_filter_order_line_items = [
                            {
                                **item,
                                "sale_price": item["price"] - (quantity_condition["amount"]
                                                               if quantity_condition[
                                                                      "type"] == DiscountAmountType.VALUE.value
                                                               else (quantity_condition["rate"] * item["price"]) / 100),
                            } for item in order_line_items_without_service
                        ]
                else:
                    filtered_order_items = filter_order_line_items(condition, order_line_items)
                    total_quantity = sum(Decimal(str(item["quantity"])) for item in filtered_order_items)
                    quantity_condition = next(
                        (cc for cc in condition["conditions"] if (cc.get("quantity_to")
                                                                  and cc["quantity_from"] <= total_quantity <= cc[
                                                                      "quantity_to"])
                         or (cc.get("quantity_to") is None and cc["quantity_from"] <= total_quantity)),
                        None
                    )
                    if quantity_condition:
                        new_filter_order_line_items += [
                            {
                                **item,
                                "sale_price": item["price"] - (quantity_condition["amount"]
                                                               if quantity_condition[
                                                                      "type"] == DiscountAmountType.VALUE.value
                                                               else (quantity_condition["rate"] * item["price"]) / 100),
                            } for item in filtered_order_items
                        ]
        discount_details_match = all(
            any(
                new_item["sku"] == discount_detail["sku"] and new_item["sale_price"] == discount_detail["sale_price"]
                for new_item in new_filter_order_line_items
            )
            for discount_detail in discount_details
        )
        if discount_details_match is False:
            raise BadRequest(f'Discount can not apply for this order')
        order["order_line_items"] = [
            next((discount_detail for discount_detail in discount_details if
                  discount_detail["sku"] == order_line_item["sku"]), order_line_item)
            for order_line_item in order_line_items
        ]
        modify_order(order)

    if (discount['type'] == DiscountType.PRODUCT_BY_ORDER_TOTAL.value
            or discount['type'] == DiscountType.GIFT_BY_ORDER_TOTAL.value):
        conditions = discount['discount_product_by_order_total'] \
            if discount['type'] == DiscountType.PRODUCT_BY_ORDER_TOTAL.value else discount['gift_by_order_total']
        order_sub_total = order.get('sub_total')
        order_other_fees = 0 if type(order.get('other_fees')) != list else sum(
            item.get('amount', 0) for item in order.get('other_fees'))
        order_shipping_fee = order.get('shipping_fee', 0)
        order_total = order_sub_total + order_shipping_fee - order_other_fees
        effective_condition = max(
            (c for c in conditions if c["amount_from"] <= order_total),
            key=lambda c: c["amount_from"]
        ) if conditions else None
        if effective_condition is None:
            raise BadRequest(f'Discount can not apply for this order')
        filtered_order_items = filter_order_line_items(effective_condition, discount_details)
        if sum(Decimal(str(item["quantity"])) for item in filtered_order_items) != sum(
                Decimal(str(item["quantity"])) for item in discount_details):
            raise BadRequest(f'Discount can not apply for this order')
        if discount['type'] == DiscountType.PRODUCT_BY_ORDER_TOTAL.value:
            discount_details_match = all(
                discount_detail["sale_price"] ==
                discount_detail["price"] -
                (effective_condition["amount"] if effective_condition["type"] == DiscountAmountType.VALUE.value
                 else (effective_condition["rate"] * discount_detail["price"]) / 100)
                for discount_detail in discount_details
            )
            if discount_details_match is False:
                raise BadRequest(f'Discount can not apply for this order')
        order['order_line_items'] += discount_details
        modify_order(order)

    if (discount['type'] == DiscountType.BY_PURCHASE_PRODUCT.value
            or discount['type'] == DiscountType.GIFT_BY_PURCHASE_PRODUCT.value):
        conditions = discount['discount_by_purchase_product'] \
            if discount['type'] == DiscountType.BY_PURCHASE_PRODUCT.value else discount['gift_by_purchase_product']
        for_all_items = conditions[0].get("for_all_purchase_product", None)
        effective_conditions = []
        if isinstance(for_all_items, bool):
            for index, c in enumerate(conditions):
                if for_all_items:
                    total_quantity = sum(
                        Decimal(str(order_line_item["quantity"])) for order_line_item in order_line_items)
                    if c["purchase_product_quantity"] <= total_quantity:
                        effective_conditions += conditions
                else:
                    new_filter_order_line_items = filter_order_line_items(c, order_line_items, type_key="purchase_")
                    total_quantity = sum(
                        Decimal(str(order_line_item["quantity"])) for order_line_item in new_filter_order_line_items)
                    if c["purchase_product_quantity"] <= total_quantity:
                        c["condition_index"] = index
                        effective_conditions.append(c)
        if len(effective_conditions) == 0:
            raise BadRequest(f'Discount can not apply for this order')

        discount_details_match = True
        for effective_condition in effective_conditions:
            new_discount_details = [discount_detail for discount_detail in discount_details if
                                    discount_detail["condition_index"] == effective_condition["condition_index"]]
            max_quantity = effective_condition["sale_offs_product_max_quantity"]
            if discount["is_stackable"]:
                new_filter_order_line_items = filter_order_line_items(effective_condition, order_line_items,
                                                                      type_key="purchase_")
                total_quantity = sum(
                    Decimal(str(order_line_item["quantity"])) for order_line_item in new_filter_order_line_items)
                max_quantity = total_quantity // effective_condition.get("purchase_product_quantity",
                                                                         1) * effective_condition.get(
                    "sale_offs_product_max_quantity", 0)
            if len(new_discount_details) > max_quantity:
                discount_details_match = False
            for new_discount_detail in new_discount_details:
                if discount["type"] == DiscountType.BY_PURCHASE_PRODUCT.value:
                    new_sale_price = new_discount_detail['price'] - (
                        effective_condition["amount"] if effective_condition[
                                                             "type"] == DiscountAmountType.VALUE.value
                        else (effective_condition["rate"] * new_discount_detail["price"] / 100))
                    if new_sale_price != Decimal(str(new_discount_detail['sale_price'])):
                        discount_details_match = False
                else:
                    if Decimal(str(new_discount_detail['sale_price'])) != 0:
                        discount_details_match = False

        if discount_details_match is False:
            raise BadRequest(f'Discount can not apply for this order')

        new_order_line_items = [{k: v for k, v in item.items() if k != "condition_index"} for item in discount_details]
        order['order_line_items'] += new_order_line_items
        modify_order(order)

    del order['discount_details']
    return order


def filter_order_line_items(c, items, type_key=""):
    filtered_order_items = []
    item_type = c.get(f"{type_key}product_type", c.get("item_type", ""))
    condition_line_key = f"{type_key}{item_type.lower()}"
    for order_line_item in items:
        if item_type == ItemType.PRODUCTS.value:
            if any(item["id"] == order_line_item["product_id"] for item in c[condition_line_key]):
                filtered_order_items.append(order_line_item)
        elif item_type == ItemType.VARIANTS.value:
            if any(item["id"] == order_line_item["variant_id"] for item in c[condition_line_key]):
                filtered_order_items.append(order_line_item)
        elif item_type == ItemType.CATEGORIES.value:
            if any(item["id"] == order_line_item["category"]["id"] for item in c[condition_line_key]):
                filtered_order_items.append(order_line_item)
        elif item_type == ItemType.BRANDS.value:
            if any(item["id"] == order_line_item["brand"]["id"] for item in c[condition_line_key]):
                filtered_order_items.append(order_line_item)
    return filtered_order_items


def modify_order(order):
    order['sub_total'] = sum(item["sale_price"] * item["quantity"] for item in order["order_line_items"])
    order_other_fees = 0 if type(order.get('other_fees')) != list else sum(
        item.get('amount', 0) for item in order.get('other_fees'))
    order_shipping_fee = order.get('shipping_fee', 0)
    order['total'] = (order['sub_total'] + order_shipping_fee - order_other_fees - order['discount'])


def invoke_import_order(request_id, company_id, start=0):
    payload = {
        'request_id': str(request_id),
        'company_id': str(company_id),
        'start': start,
    }
    return lambda_client_invoke_async(payload, function_name=f'optiwarehouse-orders-service-{ENV}-importOrderHandle')


def import_order(order, company_id, user):
    status = order.get('status')
    try:
        order_obj = sync_order(order, company_id, user=user)
    except InvalidSchemaExp as ex:
        invalid_scheme_exc = BadRequest(str(ex))
        invalid_scheme_exc.error_code = ex.error_code
        raise invalid_scheme_exc
    if order_obj.attributes.order_type != OrderType.IMPORTED:
        sync_status(company_id, user, order_obj, status)
        if status == Status.AWAIT_PACKING.value:
            package = order.get('package')
            if isinstance(package, dict):
                quick_pack(company_id, user, order_obj.attributes.id, order_obj, package)
            else:
                pack(company_id, user, order_obj.attributes.id, order_obj, package)
    return order_obj.attributes_dict
