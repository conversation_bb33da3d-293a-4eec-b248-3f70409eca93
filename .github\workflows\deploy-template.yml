name: Deploy Template

on:
  workflow_call:
    inputs:
      service:
        required: true
        type: string
      jira_link:
        required: true
        type: string
      slack_dev_user_ids:
        required: true
        type: string
    secrets:
      SSH_PRIVATE_KEY:
        required: true
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      DEFAULT_REGION:
        required: true
      SERVERLESS_ACCESS_KEY:
        required: true
      SLACK_BOT_TOKEN:
        required: true

jobs:
  deploy:
    runs-on: ubuntu-22.04
    steps:
      - name: 🔄 Checkout Repo
        uses: actions/checkout@v4

      - name: 🛠 Setup Deployment for ${{ inputs.service }}
        run: echo "Set up for ${{ inputs.service }}"

      - name: Set up Python 3.9
        uses: actions/setup-python@v4
        with:
          python-version: "3.9"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.18.1"
          cache: "npm"

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.5.4
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Deploy ${{ inputs.service }}
        working-directory: ${{ inputs.service }}
        run: |
          export $(sed 's/ *= */=/g' .env.dev | grep -v '^#' | xargs)

          echo "Deploying ${{ matrix.service }} ..."
          pip install -r requirements.txt || echo "No Python requirements"
          npm install

          mkdir -p ~/.aws
          echo "[$PROFILE]" > ~/.aws/credentials
          echo "aws_access_key_id = $AWS_ACCESS_KEY_ID" >> ~/.aws/credentials
          echo "aws_secret_access_key = $AWS_SECRET_ACCESS_KEY" >> ~/.aws/credentials

          echo "[$PROFILE]" > ~/.aws/config
          echo "region = $AWS_DEFAULT_REGION" >> ~/.aws/config

          if [ -n "$PROFILE" ]; then
            npx serverless deploy --stage dev --aws-profile "$PROFILE"
          else
            npx serverless deploy --stage dev
          fi
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ secrets.DEFAULT_REGION }}
          SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}

      - name: 📢 Notify Success for ${{ inputs.service }}
        if: success()
        working-directory: ${{ inputs.service }}
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          GITHUB_REF_NAME: ${{ github.ref_name }}
          DEPLOY_ENV: "staging"
          JIRA_LINK: ${{ inputs.jira_link }}
          SLACK_DEV_USER_IDS: ${{ inputs.slack_dev_user_ids }}
          GITHUB_ACTOR: ${{ github.actor }}
          PR_TITLE: ${{ github.event.pull_request.title }}
          PR_URL: ${{ github.event.pull_request.html_url }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          PR_AUTHOR: ${{ github.event.pull_request.user.login }}
          SERVICE_NAME: ${{ inputs.service }}
        run: |
          AUTHOR=${PR_AUTHOR:-$GITHUB_ACTOR}

          # Lấy commit message và commit URL
          COMMIT_MESSAGE=$(git log -1 --pretty=%s)
          COMMIT_URL="https://github.com/$GITHUB_REPOSITORY/commit/$GITHUB_SHA"
          
          # Format PR title nếu có
          FORMATTED_TITLE=$(echo "$PR_TITLE" | sed -E "s#(OWS-[0-9]+)#<${JIRA_LINK}\1|\1>#g")
          
          # Tạo nội dung thông báo dựa trên việc là PR hay push
          if [ -n "$PR_NUMBER" ] && [ -n "$PR_URL" ]; then
            PR_TEXT="*Pull Request:*\n<$PR_URL|#${PR_NUMBER}> - $FORMATTED_TITLE"
          else
            PR_TEXT="*Commit:* <$COMMIT_URL|$COMMIT_MESSAGE>\n*Push directly to branch:* \`$GITHUB_REF_NAME\`"
          fi
          
          # Escape chuỗi để đưa vào JSON
          PR_BLOCK=$(printf '%s' "$PR_TEXT" | sed -E ':a;N;$!ba;s/\\/\\\\/g; s/"/\\"/g; s/\n/\\n/g')

          MENTIONS=$(echo "$SLACK_DEV_USER_IDS" | tr ',' '\n' | sed 's/.*/<@&>/' | paste -sd' ' -)

          curl -X POST https://slack.com/api/chat.postMessage \
            -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
            -H "Content-type: application/json" \
            --data "$(cat <<EOF
          {
            "channel": "#prd-optiwarehouse-integration-deploymenting",
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "✅ PR Preview $SERVICE_NAME Deployment Successful!",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "$PR_BLOCK"
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Author:*\n$AUTHOR"
                  }
                ]
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "👀 *Please review the code changes and provide feedback!*\ncc: $MENTIONS"
                  }
                ]
              },
              {
                "type": "divider"
              }
            ]
          }
          EOF
          )"
      

      - name: 📢 Notify Failure ${{ inputs.service }}
        if: failure()
        working-directory: ${{ inputs.service }}
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          GITHUB_REF_NAME: ${{ github.ref_name }}
          GITHUB_REPOSITORY: ${{ github.repository }}
          GITHUB_RUN_ID: ${{ github.run_id }}
          GITHUB_ACTOR: ${{ github.actor }}
          DEPLOY_ENV: "staging"
          SLACK_DEV_USER_IDS: ${{ inputs.slack_dev_user_ids }}
          JIRA_LINK: ${{ inputs.jira_link }}
          PR_TITLE: ${{ github.event.pull_request.title }}
          PR_URL: ${{ github.event.pull_request.html_url }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          PR_AUTHOR: ${{ github.event.pull_request.user.login }}
          SERVICE_NAME: ${{ inputs.service }}
        run: |
          AUTHOR=${PR_AUTHOR:-$GITHUB_ACTOR}
          FORMATTED_TITLE=$(echo "$PR_TITLE" | sed -E "s#(OWS-[0-9]+)#<${JIRA_LINK}\1|\1>#g")

          if [ -n "$PR_NUMBER" ] && [ -n "$PR_URL" ]; then
            PR_TEXT="*Pull Request:*\n<$PR_URL|#${PR_NUMBER}> - $FORMATTED_TITLE"
          else
            PR_TEXT="*Pull Request:*\n<none> - Direct push to branch \`$GITHUB_REF_NAME\`"
          fi

          # Escape double quotes and backslashes in PR_TEXT for JSON
          PR_BLOCK=$(printf '%s' "$PR_TEXT" | sed 's/\\/\\\\/g; s/"/\\"/g; s/$/\\n/' | tr -d '\n')

          MENTIONS=$(echo "$SLACK_DEV_USER_IDS" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | paste -sd' ' -)
          RUN_URL="https://github.com/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"

          curl -X POST https://slack.com/api/chat.postMessage \
            -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
            -H "Content-type: application/json" \
            --data "$(cat <<EOF
          {
            "channel": "#prd-optiwarehouse-integration-deploymenting",
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "❌ Deployment Failed: $SERVICE_NAME",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "$PR_BLOCK"
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Author:*\n$AUTHOR"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "🔍 *View logs:* <$RUN_URL|Click here to view the error logs>"
                }
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "cc: $MENTIONS"
                  }
                ]
              }
            ]
          }
          EOF
          )"
      
