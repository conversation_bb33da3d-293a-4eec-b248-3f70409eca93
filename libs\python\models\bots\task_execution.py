from dataclasses import dataclass
from enum import Enum

from marshmallow import fields
from marshmallow_enum import EnumField

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


class TriggerType(str, Enum):
    CONVERSATION = "CONVERSATION"
    WEBHOOK = "WEBHOOK"
    SCHEDULER = "SCHEDULER"


@dataclass
class TaskExecutionAttributes(BasicAttributes):
    virtual_staff_id: str = None
    task_id: str = None
    trigger_id: str = None
    trigger_type: TriggerType = None
    execute_time: str = None
    result: str = None
    feedback: str = None


class TaskExecutionAttributesSchema(BasicAttributesSchema):
    virtual_staff_id = fields.Str(required=True)
    task_id = fields.Str(required=True)
    trigger_id = fields.Str(required=True)
    trigger_type = EnumField(TriggerType, required=True)
    execute_time = fields.Str(required=True)
    result = fields.Str(allow_none=True)
    feedback = fields.Str(allow_none=True)


class TaskExecutionModel(BasicModel):
    key__ranges = ['task_id']
    table_name = 'taskExecution'
    attributes: TaskExecutionAttributes
    attributes_schema = TaskExecutionAttributesSchema
    attributes_class = TaskExecutionAttributes

    query_mapping = {
        'query': ['id', 'virtual_staff', 'task_id', 'trigger_type'],
        'filter': {
            'id': 'query',
            'virtual_staff': 'query',
            'task_id': 'query',
            'trigger_id': 'query',
            'trigger_type': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
    }


class TaskExecutionVirtualStaffIdIndex(TaskExecutionModel):
    key__ranges = ['virtual_staff_id']
    index_name = 'taskExecutionByVirtualStaffIndex'
