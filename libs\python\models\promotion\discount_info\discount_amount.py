from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

from marshmallow import fields
from marshmallow_enum import EnumField
from nolicore.adapters.db.model import AttributesSchema, Attributes


class DiscountAmountType(Enum):
    PERCENT = 'PERCENT'
    VALUE = 'VALUE'


@dataclass
class DiscountAmountAttributes(Attributes):
    type: DiscountAmountType
    rate: Decimal
    amount: Decimal


class DiscountAmountAttributesSchema(AttributesSchema):
    type = EnumField(DiscountAmountType, required=True, default=DiscountAmountType.VALUE)
    amount = fields.Decimal(allow_none=True)
    rate = fields.Decimal(allow_none=True)
