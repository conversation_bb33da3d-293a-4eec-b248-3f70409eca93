import os

from nolicore.utils.api import api_message
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.exceptions import NotFoundRequest, BadRequest
from nolicore.utils.utils import logger

from helpers.channel import get_channel
from helpers.master_data_attribute import MasterDataAttribute
from integrations.common.bucket_helper import get_transformed_data
from models.integration.mapping_attribute import MappingAttributeTypeIndex
from models.integration.sync_record import SyncRecordCompanyIdIndex

ENV = os.getenv('ENV')
TRANSFORMED_DATA_BUCKET = os.environ['TRANSFORMED_DATA_BUCKET']


@as_api()
def get_attributes(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    object_type = api_gateway_request.path_parameters['type']
    company_id = api_gateway_request.company_id
    sync_record_id = api_gateway_request.body.get('sync_record_id', None) if api_gateway_request.body else None

    for field_name, value in {"connection_id": connection_id, "type": object_type}.items():
        if not value:
            raise BadRequest(f"Missing required field: {field_name}")

    source_attributes = MasterDataAttribute.get_object_attributes(object_type)
    channel = get_channel(connection_id, company_id)
    destination_channel_info = channel['channel_info']
    desination_channel = channel['channel']
    destination_attributes = desination_channel._get_attributes(object_type)
    master_data = None

    if sync_record_id:
        source_sync_records = SyncRecordCompanyIdIndex.list({'id': sync_record_id, 'is_source': True},
                                                            limit=1000)
        source_sync_record = source_sync_records['Items'][0] if source_sync_records.get('Items') else None

        source_key = source_sync_record['transformed_record_id']
        source_version_id = source_sync_record['transformed_record_version']

        try:
            master_data = get_transformed_data(TRANSFORMED_DATA_BUCKET, source_key, source_version_id)
        except Exception as e:
            master_data = None

    return {
        "connection_id": connection_id,
        "type": object_type,
        "channel": destination_channel_info.get('channel_name', ''),
        "source_attributes": source_attributes,
        "destination_attributes": destination_attributes,
        "master_data": master_data
    }


@as_api()
def get_mapping_attribute(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    object_type = api_gateway_request.path_parameters['type']
    company_id = api_gateway_request.company_id

    for field_name, value in {"connection_id": connection_id, "type": object_type}.items():
        if not value:
            raise BadRequest(f"Missing required field: {field_name}")

    channel = get_channel(connection_id, company_id)
    destination_channel_info = channel['channel_info']

    mapping_attribute = MappingAttributeTypeIndex.get_mapping_attribute(connection_id, object_type)
    if not mapping_attribute:
        mapping_attribute = MappingAttributeTypeIndex.put(connection_id, object_type, [], company_id,
                                                          destination_channel_info)
    return mapping_attribute.attributes_dict


@as_api()
def save_mapping_attribute(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    object_type = api_gateway_request.path_parameters['type']
    mappings = api_gateway_request.body.get('mappings')

    if not isinstance(mappings, list):
        raise BadRequest("Mappings must be an array")
    
    if mappings:  # Only validate if mappings has items
        for field_name, value in {"mappings": mappings}.items():
            if not value:
                raise BadRequest(f"Missing required field: {field_name}")

    mapping_attribute = MappingAttributeTypeIndex.get_mapping_attribute(connection_id, object_type)
    if not mapping_attribute:
        raise NotFoundRequest("Mapping_attribute not found")
    mapping_attribute = MappingAttributeTypeIndex.put(connection_id, object_type, mappings)
    return mapping_attribute.attributes_dict


@as_api()
def remove_mapping_attribute(api_gateway_request: ApiGatewayRequest):
    connection_id = api_gateway_request.path_parameters['connection_id']
    object_type = api_gateway_request.path_parameters['type']

    for field_name, value in {"connection_id": connection_id, "type": object_type}.items():
        if not value:
            raise BadRequest(f"Missing required field: {field_name}")

    mapping_attribute = MappingAttributeTypeIndex.get_mapping_attribute(connection_id, object_type)
    if not mapping_attribute:
        raise NotFoundRequest("Mapping_attribute not found")
    try:
        mapping_attribute = MappingAttributeTypeIndex.put(connection_id, object_type, mappings=[])
        return api_message('back to default mapping successful')
    except Exception as e:
        logger.error(f"Unexpected error in delete mapping_attribute: {str(e)}")
        return api_message('back to default mapping unsuccessful')
