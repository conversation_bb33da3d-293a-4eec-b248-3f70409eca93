from dataclasses import dataclass
from decimal import Decimal
from typing import List

from elasticsearch import NotFoundError
from marshmallow import fields, EXCLUDE
from marshmallow_enum import <PERSON><PERSON><PERSON><PERSON>
from nolicore.adapters.db.model import Attributes, AttributesSchema

from helpers.errors import NotFound
from helpers.utils import RESOURCE_SERVICE
from models.actor.customer import Customer, CustomerSchema
from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.common.address import Address, AddressSchema
from models.common.order_line import OrderLineItem, OrderLineItemSchema
from models.common.status import Status, History, HistorySchema, Priority


@dataclass
class TrackingInfo(Attributes):
    description = str
    logistics_status = str
    updated_at = str


class TrackingInfoSchema(AttributesSchema):
    description = fields.UUID(required=True)
    logistics_status = fields.Str(required=True)
    updated_at = fields.DateTime(required=True)


@dataclass
class Package(Attributes):
    id: str = None
    order_id: str = None
    location_id: str = None
    package_number: str = None
    order_number: str = None
    billing_address: Address = None
    shipping_address: Address = None
    customer: Customer = None
    order_line_items: List[OrderLineItem] = None
    sub_total: Decimal = None
    total: Decimal = None
    priority: Priority = None
    tax: Decimal = 0
    tax_rate: Decimal = 0
    history: List[TrackingInfo] = None
    histories: List[History] = None
    shipping_fee: Decimal = 0
    note: str = None
    source: str = None
    tracking_number: str = None
    tracking_link: str = None
    shipping_provider: str = None
    shipping_label: str = None
    staff_id: str = None
    packing_staff: str = None
    status: Status = Status.PENDING


class PackageSchema(AttributesSchema):
    class Meta:
        unknown = EXCLUDE

    id = fields.UUID(required=True)
    order_id = fields.Str(required=True)
    location_id = fields.Str(required=True)
    package_number = fields.Str(required=True)
    order_number = fields.Str(required=True)
    staff_id = fields.UUID(allow_none=True)
    customer = fields.Nested(CustomerSchema(Customer), required=True)
    billing_address = fields.Nested(AddressSchema(Address), required=True)
    shipping_address = fields.Nested(AddressSchema(Address), required=True)
    history = fields.List(fields.Nested(TrackingInfoSchema(TrackingInfo)), allow_none=True)
    histories = fields.List(fields.Nested(HistorySchema(History)), allow_none=True)
    order_line_items = fields.List(fields.Nested(OrderLineItemSchema(OrderLineItem)), required=True)
    sub_total = fields.Decimal(required=True)
    total = fields.Decimal(required=True)
    priority = EnumField(Priority, allow_none=True)
    tax = fields.Decimal(allow_none=True)
    tax_rate = fields.Decimal(allow_none=True)
    status = EnumField(Status, allow_none=True, default=Status.PENDING)
    note = fields.Str(allow_none=True)
    source = fields.Str(allow_none=True)
    tracking_number = fields.Str(allow_none=True)
    tracking_link = fields.Url(allow_none=True)
    shipping_provider = fields.Str(allow_none=True)
    shipping_label = fields.Url(allow_none=True)
    shipping_fee = fields.Decimal(allow_none=True)
    packing_staff = fields.Str(allow_none=True)


@dataclass
class PackageAttributes(Package, BasicAttributes):
    pass


class PackageAttributesSchema(BasicAttributesSchema, PackageSchema):
    pass


class PackageModel(BasicModel):
    table_name = 'package'
    attributes: PackageAttributes
    attributes_schema = PackageAttributesSchema
    attributes_class = PackageAttributes

    query_mapping = {
        'query': ['id', 'order_id', 'package_number', 'tracking_number', 'order_number'],
        'filter': {
            'id': 'query',
            'status': 'query',
            'tags': 'tags',
            'staff_id': 'query',
            'source': 'query',
            'shipping_provider': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
            'priority': 'number',
            'total': 'range',
            'packing_staff': 'query'
        },
        'mapping': {
            'customer.phone': 'customer.phone',
            'customer.email': 'customer.email',
            'customer.first_name': 'customer.first_name',
            'customer.last_name': 'customer.last_name',
            'note': 'note',
            'priority': 'priority',
            'histories': 'histories'
        }
    }

    @classmethod
    def status_count(cls):
        try:
            search_params = {
                "aggs": {
                    "status_count": {
                        "terms": {
                            "field": "status.keyword"
                        },
                        "aggs": {
                            "provider_count": {
                                "terms": {
                                    "field": "shipping_provider.keyword"
                                }
                            },
                            "channel_count": {
                                "terms": {
                                    "field": "source.keyword"
                                }
                            }
                        }
                    }
                }
            }
            response = cls.report(search_params, service=RESOURCE_SERVICE)
            return response.body['aggregations']['status_count']['buckets']
        except NotFoundError as ex:
            return []


class PackageOrderIdIndex(PackageModel):
    key__id = 'order_id'
    key__ranges = ['id']
    index_name = 'packageOrderIdIndex'


class PackageNumberIndex(PackageModel):
    key__id = 'package_number'
    key__ranges = ['id']
    index_name = 'packageNumberIndex'

    @classmethod
    def get(cls, company_id, package_number, _except=False):
        try:
            package = PackageNumberIndex.list({
                'package_number': package_number,
                'company_id': company_id
            }, limit=100)['Items'][0]
            return cls(package)
        except ImportError:
            if _except:
                raise NotFound()
