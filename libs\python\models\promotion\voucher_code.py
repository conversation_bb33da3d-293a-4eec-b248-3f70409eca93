from dataclasses import dataclass
from enum import Enum

from marshmallow import fields
from marshmallow_enum import EnumField

from models.basic import BasicModel, BasicAttributesSchema, BasicAttributes


class VoucherCodeStatus(Enum):
    APPLY = 'APPLY'
    STOP_APPLY = 'STOP_APPLY'
    NOT_ACTIVE = 'NOT_ACTIVE'


@dataclass
class VoucherCode(BasicAttributes):
    code: str = None
    voucher_id: str = None
    max_quantity: str = None
    status: VoucherCodeStatus = VoucherCodeStatus.NOT_ACTIVE
    used: int = 0


class VoucherCodeSchema(BasicAttributesSchema):
    code = fields.Str(required=True)
    used = fields.Int(allow_none=True)
    max_quantity = fields.Int(allow_none=True)
    status = EnumField(VoucherCodeStatus, allow_none=True, default=VoucherCodeStatus.NOT_ACTIVE)
    voucher_id = fields.UUID(required=True)


class VoucherCodeModel(BasicModel):
    key__id = 'code'
    table_name = 'voucherCode'
    key__ranges = ['voucher_id']
    attributes: VoucherCode
    attributes_schema = VoucherCodeSchema
    attributes_class = VoucherCode

    query_mapping = {
        'query': ['code'],
        'filter': {
            'id': 'query',
            'voucher_id': 'query',
            'status': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'used': 'used',
            'max_quantity': 'max_quantity',
        }
    }
