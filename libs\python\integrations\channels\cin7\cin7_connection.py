from dataclasses import dataclass, field

from marshmallow import fields

from models.integration.connection import ConnectionModel
from ..connection_base import ConnectionAttributes, ConnectionSchema, OAuth2SettingsSchema, OAuth2Settings


class Cin7SettingsSchema(OAuth2SettingsSchema):
    account_id = fields.Str(required=True)
    api_key = fields.Str(required=True)
    base_url = fields.Url(default="https://inventory.dearsystems.com/ExternalApi/v2")


@dataclass
class Cin7Settings(OAuth2Settings):
    account_id: str = None
    api_key: str = None
    base_url: str = "https://inventory.dearsystems.com/ExternalApi/v2"


@dataclass
class Cin7ConnectionAttributes(ConnectionAttributes):
    channel_name: str = field(default='cin7')
    settings: Cin7Settings = field(default_factory=Cin7Settings)


class Cin7ConnectionSchema(ConnectionSchema):
    settings = fields.Nested(Cin7SettingsSchema(Cin7Settings))


class Cin7Connection(ConnectionModel):
    attributes: Cin7ConnectionAttributes = None
    attributes_schema = Cin7ConnectionSchema
    attributes_class = Cin7ConnectionAttributes
