from typing import Any
from ..transformation import BaseTrans<PERSON><PERSON>and<PERSON>, TransformationConfig, TransformationDirection


class ReplaceTransformationHandler(BaseTransformationHandler):
    """Handler for replace transformation"""
    
    config_definition = TransformationConfig(
        label="Replace",
        description="Replace text with another value",
        direction=TransformationDirection(
            title="Replace Text",
            content="Replace specific text with another value",
            example='"Hello World" + (World → Universe) → "Hello Universe"',
            value_type="Single Value"
        ),
        config_fields=[
            {
                "key": "searchValue",
                "label": "Search Value",
                "type": "text",
                "placeholder": "Text to search for",
                "description": "The text to find and replace"
            },
            {
                "key": "replaceValue",
                "label": "Replace With",
                "type": "text",
                "placeholder": "Text to replace with",
                "description": "The text to replace with"
            }
        ]
    )
    
    def handle_transform(self, value: Any) -> Any:
        """
        Replaces occurrences of search value with replace value in the text
        """
        if value is None:
            return None
            
        if not self.transformation.config or 'searchValue' not in self.transformation.config or 'replaceValue' not in self.transformation.config:
            return value
            
        return str(value).replace(
            self.transformation.config['searchValue'],
            self.transformation.config['replaceValue']
        ) 