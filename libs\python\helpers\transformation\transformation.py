from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod

from nolicore.adapters.db.model import Attributes


class TransformationType(str, Enum):
    # Text Transformations
    DIRECT = "DIRECT"
    UPPERCASE = "UPPERCASE"
    LOWERCASE = "LOWERCASE"
    TITLECASE = "TITLECASE"
    TRIM = "TRIM"
    REPLACE = "REPLACE"
    CONCAT = "CONCAT"
    SPLIT = "SPLIT"
    PREFIX = "PREFIX"
    POSTFIX = "POSTFIX"
    SUBSTRING = "SUBSTRING"
    PAD_START = "PADSTART"
    PAD_END = "PADEND"
    PAD = "PAD"

    # Number Transformations
    ROUND = "ROUND"
    CEIL = "CEIL"
    FLOOR = "FLOOR"
    FORMAT = "FORMAT"
    CURRENCY = "CURRENCY"
    PERCENTAGE = "PERCENTAGE"
    MATH = "MATH"
    RANGE = "RANGE"
    NUMBER_FORMAT = "NUMBERFORMAT"

    # Date Transformations
    DATE_FORMAT = "DATEFORMAT"
    DATE_ADD = "DATEADD"
    DATE_SUBTRACT = "DATESUBTRACT"
    TO_TIMEZONE = "TOTIMEZONE"
    AGE = "AGE"
    DURATION = "DURATION"

    # Array Transformations
    FILTER = "FILTER"
    SORT = "SORT"
    UNIQUE = "UNIQUE"
    LIMIT = "LIMIT"
    JOIN = "JOIN"
    MAP = "MAP"
    ARRAY = "ARRAY"

    # Logic Transformations
    CONDITIONAL = "CONDITIONAL"
    SWITCH = "SWITCH"
    DEFAULT = "DEFAULT"
    COALESCE = "COALESCE"
    VALIDATE = "VALIDATE"
    CASE = "CASE"
    BOOLEAN = "BOOLEAN"

    # Conversion Transformations
    TRANSLATE = "TRANSLATE"
    UNIT_CONVERT = "UNITCONVERT"
    PARSE = "PARSE"
    STRINGIFY = "STRINGIFY"

    # Advanced Transformations
    REGEX = "REGEX"
    TEMPLATE = "TEMPLATE"
    LOOKUP = "LOOKUP"
    SUGGEST = "SUGGEST"
    AGGREGATE = "AGGREGATE"
    CUSTOM = "CUSTOM"
    SEVERITY = "SEVERITY"


@dataclass
class TransformationDirection:
    """Direction information for a transformation"""
    title: str
    content: str
    example: str
    value_type: str = "Single Value"  # or "Multiple Values"


@dataclass
class TransformationConfig:
    """Configuration for a transformation type"""
    label: str
    description: str
    direction: TransformationDirection
    config_fields: Optional[List[Dict[str, Any]]] = None
    multiple_fields: bool = False


@dataclass
class Transformation(Attributes):
    """Transformation instance with configuration"""
    id: str
    source_field: List[str] = field(default_factory=list)
    type: Optional[TransformationType] = None
    config: Dict[str, Any] = field(default_factory=dict)


class BaseTransformationHandler(ABC):
    """Abstract base class for handling transformations"""

    # Class level configuration that will be set by each implementation
    config_definition: TransformationConfig

    def __init__(self, transformation: Transformation):
        self.transformation = transformation

    @abstractmethod
    def handle_transform(self, value: Any) -> Any:
        """Handle the transformation of a value"""
        pass

    @classmethod
    def get_config(cls) -> TransformationConfig:
        """Get the configuration definition for this transformation type"""
        return cls.config_definition
