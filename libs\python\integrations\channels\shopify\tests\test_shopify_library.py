import unittest
import os

from integrations.channels.shopify.shopify_library import Shopify<PERSON>ibrary
from integrations.common.base_api_library import FetchAPIResponse, StandardOrder, StandardProduct, SyncAPIResponse


class TestShopifyLibrary(unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        self.library = ShopifyLibrary(
            # shop_url="91af33-b6.myshopify.com",
            # access_token="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",
            shop_url="www.pinkpoppy.com.au",
            access_token="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",
            api_version="2023-07"  # Make sure this matches the version in your ShopifyLibrary
        )
        # self.webhook_secret = "201532b57851eda1c860c04bae0769a5"
        self.webhook_secret = "9336ddcdee3c276b47687ec44091b821"

    async def test_get_orders(self):
        result = await self.library.get_orders()

        self.assertIsInstance(result, FetchAPIResponse)
        self.assertTrue(result.success)
        if result.data:
            self.assertIsInstance(result.data[0], StandardOrder)
            print(f"Retrieved {len(result.data)} orders. First order number: {result.data[0].order_number}")
        else:
            print("No orders retrieved.")

    async def test_get_products(self):
        result = await self.library.get_products()

        self.assertIsInstance(result, FetchAPIResponse)
        self.assertTrue(result.success)
        if result.data:
            self.assertIsInstance(result.data[0], StandardProduct)
            print(f"Retrieved {len(result.data)} products. First product title: {result.data[0].title}")
        else:
            print("No products retrieved.")

    async def test_get_product_by_id(self):
        # First, get a list of products
        products_result = await self.library.get_products()
        if not products_result.data:
            self.skipTest("No products available to test get_product_by_id")

        # Use the ID of the first product
        product_id = products_result.data[0].id

        result = await self.library.get_product_by_id(product_id)

        self.assertIsInstance(result, SyncAPIResponse)
        self.assertTrue(result.success)
        self.assertIsInstance(result.data, StandardProduct)
        print(f"Retrieved product by ID. Title: {result.data.title}")

    async def test_adjust_inventory(self):
        # First, get a list of products
        products_result = await self.library.get_products()
        if not products_result.data:
            self.skipTest("No products available to test adjust_inventory")

        # Use the first product's variant
        product = products_result.data[0]
        variant = product.raw_data['variants'][0]
        inventory_item_id = variant['inventory_item_id']
        location_id = variant['inventory_quantity']  # Assuming this is the location ID, adjust if necessary

        # Adjust inventory by 1
        result = await self.library.adjust_inventory(inventory_item_id, location_id, 1)

        self.assertIsInstance(result, SyncAPIResponse)
        self.assertTrue(result.success)
        print(f"Adjusted inventory. New available quantity: {result.data['available']}")

    def test_verify_webhook_signature(self):
        payload = '{"test": "data"}'
        # You need to generate a valid HMAC signature for this test
        # This is just an example and won't pass the test
        hmac_header = "invalid_signature"

        result = self.library.verify_webhook_signature(payload, hmac_header, self.webhook_secret)

        self.assertFalse(result)  # This will be False because we used an invalid signature

    async def test_process_webhook_order_created(self):
        # You need to provide a valid order payload for this test
        payload = {
            "id": 123456789,
            "name": "#1001",
            "total_price": "100.00",
            "currency": "USD",
            "status": "fulfilled",
            "created_at": "2023-08-25T12:00:00Z"
        }

        result = await self.library.process_webhook("orders/create", payload)

        self.assertIsInstance(result, SyncAPIResponse)
        self.assertTrue(result.success)
        self.assertIsInstance(result.data, StandardOrder)
        self.assertEqual(result.data.order_number, "#1001")
        print(f"Processed webhook for order creation. Order number: {result.data.order_number}")


if __name__ == '__main__':
    unittest.main()
