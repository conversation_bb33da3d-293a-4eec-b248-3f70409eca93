from dataclasses import dataclass
from typing import List

from marshmallow import fields

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel
from models.media.image import Image, ImageSchema
from models.price.price import PriceAttributes, PriceAttributesSchema
from models.product.brand import Brand, BrandSchema
from models.product.category import Category, CategorySchema
from models.product.variant import VariantAttributes, VariantAttributesSchema


@dataclass
class WishlistAttributes(BasicAttributes):
    user_id: str = None
    name: str = None
    sku: str = None
    slug: str = None
    shortDescription: str = None
    tags: str = None
    brand: Brand = None
    images: List[Image] = None
    category: Category = None
    prices: List[PriceAttributes] = None
    variants: List[VariantAttributes] = None


class WishlistAttributesSchema(BasicAttributesSchema):
    user_id = fields.Str(required=True)
    name = fields.Str(required=True)
    sku = fields.Str(required=True)
    slug = fields.Str(allow_none=True)
    shortDescription = fields.Str(allow_none=True)
    tags = fields.Str(allow_none=True)
    brand = fields.Nested(BrandSchema(Brand), allow_none=True)
    images = fields.List(fields.Nested(ImageSchema(Image), allow_none=True))
    category = fields.Nested(CategorySchema(Category), allow_none=True)
    prices = fields.List(fields.Nested(PriceAttributesSchema(PriceAttributes)), allow_none=True)
    variants = fields.List(fields.Nested(VariantAttributesSchema(VariantAttributes)), allow_none=True)


class WishlistModel(BasicModel):
    key__id = 'user_id'
    key__ranges = ['id']
    table_name = 'wishlist'
    attributes: WishlistAttributes
    attributes_class = WishlistAttributes
    attributes_schema = WishlistAttributesSchema
    query_mapping = {
        'query': ['user_id', 'sku', 'id', 'name', 'sku', 'variants.sku', 'slug'],
        'filter': {
            'id': 'query',
            'slug': 'query',
            'brand.name': 'query',
            'brand.id': 'query',
            'company_id': 'query',
            'category.name': 'query',
            'category.id': 'query',
            'name': 'query',
            'sku': 'query',
            'tags': 'tags',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'brand.id': 'brand.id',
            'brand.name': 'brand.name',
            'category.id': 'category.id',
            'category.name': 'category.name',
            'images': 'images',
            'prices': 'prices',
            'variants': 'variants'
        }
    }

