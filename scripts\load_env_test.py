import os


def load_env_test(environment = "dev"):
    os.environ['LOG_LEVEL'] = '20'
    os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
    os.environ['USER_POOL_ID'] = 'ap-southeast-1_Tmn6Tbm0H'
    os.environ['APP_CLIENT_ID'] = '3fh2s28e3g8l23068d6j573l9a'
    os.environ['ELASTIC_SEARCH_HOST'] = 'https://**************:9200'
    os.environ['ELASTIC_SEARCH_USERNAME'] = 'elastic'
    os.environ['ELASTIC_SEARCH_PASSWORD'] = 'OneXAPIES999'
    os.environ['RESOURCE_SERVICE'] = 'optiwarehouse-resources-services'
    os.environ['SERVICE'] = 'optiwarehouse-resources-services'
    os.environ['ONEXAPIS'] = 'https://api-staging.onexapis.com'
    os.environ['HARAVAN_API'] = 'https://haravan.onexapis.com'
    os.environ['FETCH_QUEUE_URL'] = 'https://haravan.onexapis.com'
    os.environ['TRANSFORM_QUEUE_URL'] = 'https://haravan.onexapis.com'
    os.environ['PUBLISH_QUEUE_URL'] = 'https://haravan.onexapis.com'
    os.environ['RAW_DATA_BUCKET'] = f'optiwarehouse-flow-service-{environment}-raw-data'
    os.environ['TRANSFORMED_DATA_BUCKET'] = f'optiwarehouse-flow-service-{environment}-transformed-data'

    os.environ[
        'LLM_API_KEY'] = "***********************************************************************************************************************************************************************"
    os.environ['LLM_MODEL'] = "gpt-4o-mini"
    os.environ['DEFAULT_LLM_TEMPERATURE'] = "0.2"
    os.environ['LLM_MAX_TOKENS'] = "2000"
    os.environ['MAX_LLM_CALL_RETRIES'] = "3"
    os.environ['TOP_P'] = "0.8"

    if environment == 'prod':
        # prod
        os.environ['BUCKET_NAME'] = 'optiwarehouse-prod'
        os.environ['AWS_PROFILE'] = 'optiwarehouse-prod'
        os.environ['ENV'] = 'prod'
        os.environ['BASE_URL'] = 'https://api.optiwarehouse.com'
        os.environ['WEBHOOK_BASE_URL'] = 'https://api.optiwarehouse.com'
        os.environ['SHOPIFY_CLIENT_ID'] = 'fe5b9b2d0ac99b3dc23ab2362e54133b'
        os.environ['SHOPIFY_CLIENT_SECRET'] = '281bff9d1eb0f930d70b0b87e7b23067'
        os.environ['TIKTOK_APP_KEY'] = '6emv3rous6f2u'
        os.environ['TIKTOK_APP_SECRET'] = 'b2285d947e5f4e1795124406ab3e415393c5d855'
        os.environ['TIKTOK_SERVICE_ID'] = '7448925715311937285'
        os.environ['APP_URL'] = 'https://app.optiwarehouse.com'
        os.environ['WEBHOOK_BASE_URL'] = 'https://api.optiwarehouse.com'
        os.environ[
            'FETCH_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/533267084422/optiwarehouse-flow-service-prod-fetch-queue'
        os.environ[
            'TRANSFORM_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/533267084422/optiwarehouse-flow-service-prod-transform-queue'
        os.environ[
            'SYNC_MAPPING_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/533267084422/optiwarehouse-flow-service-prod-sync-mapping-queue'
    if environment == 'dev':
        # staging
        os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
        os.environ['AWS_PROFILE'] = 'noliwms_staging'
        os.environ['ENV'] = 'dev'
        os.environ['BASE_URL'] = 'https://api-staging.optiwarehouse.com'
        os.environ['WEBHOOK_BASE_URL'] = 'https://api-staging.optiwarehouse.com'
        os.environ['SHOPIFY_CLIENT_ID'] = '08530b114bafc5f05eca180a53598b48'
        os.environ['SHOPIFY_CLIENT_SECRET'] = '0568bfe6f5c5b643016055ed7d6d36ee'
        os.environ['APP_URL'] = 'https://dev.optiwarehouse.com'
        os.environ['TIKTOK_APP_KEY'] = '6emv3rous6f2u'
        os.environ['TIKTOK_APP_SECRET'] = 'b2285d947e5f4e1795124406ab3e415393c5d855'
        os.environ['TIKTOK_SERVICE_ID'] = '7448925715311937285'
        os.environ['WEBHOOK_BASE_URL'] = 'https://api-staging.optiwarehouse.com'
        os.environ[
            'FETCH_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-flow-service-dev-fetch-queue'
        os.environ[
            'TRANSFORM_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-flow-service-dev-transform-queue'
        os.environ[
            'PUBLISH_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-flow-service-dev-publish-queue.fifo'
        os.environ[
            'SYNC_MAPPING_QUEUE_URL'] = 'https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-flow-service-dev-sync-mapping-queue'
    if environment == 'local':
        # local
        os.environ['BUCKET_NAME'] = 'optiwarehouse-staging'
        os.environ['AWS_PROFILE'] = 'noliwms_staging'
        os.environ['ENV'] = 'local'
        os.environ['SHOPIFY_CLIENT_ID'] = '08530b114bafc5f05eca180a53598b48'
        os.environ['SHOPIFY_CLIENT_SECRET'] = '0568bfe6f5c5b643016055ed7d6d36ee'
        os.environ['APP_URL'] = 'http://localhost:3000'
        os.environ['TIKTOK_APP_KEY'] = '6emv3rous6f2u'
        os.environ['TIKTOK_APP_SECRET'] = 'b2285d947e5f4e1795124406ab3e415393c5d855'
        os.environ['TIKTOK_SERVICE_ID'] = '7448925715311937285'
