from dataclasses import dataclass

from marshmallow import fields, EXCLUDE

from models.basic import BasicModel, BasicAttributes, BasicAttributesSchema


@dataclass
class Filter(BasicAttributes):
    user_id: str = None
    type: str = None
    name: str = None
    icon: str = None
    filters: dict = None


class FilterSchema(BasicAttributesSchema):
    class Meta:
        unknown = EXCLUDE

    user_id = fields.Str(required=True)
    type = fields.Str(required=True)
    name = fields.Str(required=True)
    icon = fields.Str(allow_none=True)
    filters = fields.Dict(required=True)


class FilterModel(BasicModel):
    table_name = 'filter'
    key__id = 'id'
    key_range_id = 'company_user_type'
    key__ranges = ['company_id', 'user_id', 'type']
    attributes: Filter
    attributes_schema = FilterSchema
    attributes_class = Filter

    query_mapping = {
        'query': ['id', 'name'],
        'filter': {
            'id': 'query',
            'user_id': 'query',
            'type': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        },
        'mapping': {
            'filters': 'filters',
            'icon': 'icon'
        }
    }
