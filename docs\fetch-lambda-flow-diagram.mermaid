graph TD
    A[Start: SQS Trigger] -->|Fetch Queue Message| B[Parse Message]
    B --> C{Retrieve Connection Settings}
    C -->|Success| D[Initialize Channel]
    C -->|Failure| Z[Log Error and Exit]
    D --> E[Get Fetch Settings]
    E --> F{Execute Fetch and Process}
    F -->|Success| G{More Data?}
    F -->|Failure| H{Retry Count < Max?}
    G -->|Yes| I[Enqueue Continuation Message]
    G -->|No| J[Update Connection Status]
    H -->|Yes| K[Enqueue Retry Message]
    H -->|No| L[Log Max Retries Exceeded]
    I --> M[End]
    J --> M
    K --> M
    L --> M

    subgraph "Fetch and Process"
        N[Start Fetch] --> O[Execute Channel Action]
        O --> P[Store Raw Data in S3]
        P --> Q[Update Fetch Progress]
        Q --> R{Batch Size Reached?}
        R -->|Yes| S[Return Continuation Token]
        R -->|No| T[Continue Processing]
        T --> O
        S --> U[End Fetch]
    end

    F -.->|Expand| N
    U -.->|Return to| F
