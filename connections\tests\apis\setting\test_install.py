import json
import unittest
from tests.base import BaseTestCase
from dotenv import load_dotenv

from connections.src.apis.setting import synchronization


class SettingTestCase(BaseTestCase):
    def setUp(self):
        super().setUp(account_type="onexapis_admin")
        self.company_id = "6dd01d64-51db-41f0-bb88-3482615a0104"
        self.user_id = "da5da4eb-f2e0-4904-8e05-146163b162af"
        self.username = f"user_{self.user_id}"
        load_dotenv()
        
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.source = '5d14cc4b-dde8-4755-82a5-6d1a6309af6f'
        cls.destination = 'ba08bf41-2219-4efb-a0e9-48dabb2e4b04'
        cls.actions = {
            "order":  True,
            "product": True,
        }

        if not all([cls.source, cls.destination, cls.actions]):
            raise ValueError("Credentials not found in environment variables")

    def test_synchronization_success(self):
        # Arrange
        event = self.create_lambda_event(
            body={"source": self.source, "destination": self.destination, "actions": self.actions})
        context = self.create_lambda_context()
        # Act
        response = synchronization(event, context)

        # Assert
        self.assertEqual(response['statusCode'], 200)
        response_body = json.loads(response['body'])
        self.assertEqual(response_body['message'], 'Synchronization completed successfully')
        
if __name__ == '__main__':
    unittest.main()
