import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
from botocore.exceptions import ClientError
import json

from integrations.flows.src.service.schedule_service import get_connections_to_fetch, SchedulerError, should_fetch, \
    enqueue_fetch_job, update_connection_status, run_scheduler


class TestScheduleService(unittest.TestCase):

    @patch('integrations.flows.src.service.schedule_service.dynamodb')
    def test_connections_to_fetch_returns_items(self, mock_dynamodb):
        mock_table = MagicMock()
        mock_dynamodb.Table.return_value = mock_table
        mock_table.scan.return_value = {'Items': [{'id': '1'}, {'id': '2'}]}

        result = get_connections_to_fetch('test_table')
        self.assertEqual(result, [{'id': '1'}, {'id': '2'}])

    @patch('integrations.flows.src.service.schedule_service.dynamodb')
    def test_connections_to_fetch_raises_scheduler_error_on_client_error(self, mock_dynamodb):
        mock_table = MagicMock()
        mock_dynamodb.Table.return_value = mock_table
        mock_table.scan.side_effect = ClientError({'Error': {}}, 'scan')

        with self.assertRaises(SchedulerError):
            get_connections_to_fetch('test_table')

    def test_should_fetch_returns_true_for_interval(self):
        connection = {
            'schedule': {'type': 'interval', 'value': 3600},
            'status': {'last_run': (datetime.utcnow() - timedelta(hours=2)).isoformat()}
        }
        current_time = datetime.utcnow()
        self.assertTrue(should_fetch(connection, current_time))

    def test_should_fetch_returns_false_for_interval(self):
        connection = {
            'schedule': {'type': 'interval', 'value': 3600},
            'status': {'last_run': (datetime.utcnow() - timedelta(minutes=30)).isoformat()}
        }
        current_time = datetime.utcnow()
        self.assertFalse(should_fetch(connection, current_time))

    def test_should_fetch_raises_scheduler_error_for_unsupported_schedule_type(self):
        connection = {
            'schedule': {'type': 'unsupported', 'value': 3600},
            'status': {'last_run': (datetime.utcnow() - timedelta(hours=2)).isoformat()}
        }
        current_time = datetime.utcnow()
        with self.assertRaises(SchedulerError):
            should_fetch(connection, current_time)

    @patch('integrations.flows.src.service.schedule_service.sqs')
    def test_enqueue_fetch_job_sends_message(self, mock_sqs):
        mock_sqs.send_message.return_value = {}
        enqueue_fetch_job('test_id', 'test_queue_url')
        mock_sqs.send_message.assert_called_once_with(
            QueueUrl='test_queue_url',
            MessageBody=json.dumps({'connection_id': 'test_id'})
        )

    @patch('integrations.flows.src.service.schedule_service.sqs')
    def test_enqueue_fetch_job_raises_scheduler_error_on_client_error(self, mock_sqs):
        mock_sqs.send_message.side_effect = ClientError({'Error': {}}, 'send_message')
        with self.assertRaises(SchedulerError):
            enqueue_fetch_job('test_id', 'test_queue_url')

    @patch('integrations.flows.src.service.schedule_service.dynamodb')
    def test_update_connection_status_updates_item(self, mock_dynamodb):
        mock_table = MagicMock()
        mock_dynamodb.Table.return_value = mock_table
        update_connection_status('test_table', 'test_id', 'SCHEDULED')
        mock_table.update_item.assert_called_once_with(
            Key={'id': 'test_id'},
            UpdateExpression="SET status.last_run = :now, status.last_status = :status",
            ExpressionAttributeValues={
                ':now': unittest.mock.ANY,
                ':status': 'SCHEDULED'
            }
        )

    @patch('integrations.flows.src.service.schedule_service.dynamodb')
    def test_update_connection_status_raises_scheduler_error_on_client_error(self, mock_dynamodb):
        mock_table = MagicMock()
        mock_dynamodb.Table.return_value = mock_table
        mock_table.update_item.side_effect = ClientError({'Error': {}}, 'update_item')
        with self.assertRaises(SchedulerError):
            update_connection_status('test_table', 'test_id', 'SCHEDULED')

    @patch('integrations.flows.src.service.schedule_service.get_connections_to_fetch')
    @patch('integrations.flows.src.service.schedule_service.should_fetch')
    @patch('integrations.flows.src.service.schedule_service.enqueue_fetch_job')
    @patch('integrations.flows.src.service.schedule_service.update_connection_status')
    def test_run_scheduler_processes_connections(self, mock_update, mock_enqueue, mock_should_fetch, mock_get_connections):
        mock_get_connections.return_value = [{'id': '1', 'schedule': {'type': 'interval', 'value': 3600}, 'status': {
            'last_run': (datetime.utcnow() - timedelta(hours=2)).isoformat()}}]
        mock_should_fetch.return_value = True
        run_scheduler('test_table', 'test_queue_url')
        mock_enqueue.assert_called_once_with('1', 'test_queue_url')
        mock_update.assert_called_once_with('test_table', '1', 'SCHEDULED')

    @patch('integrations.flows.src.service.schedule_service.get_connections_to_fetch')
    @patch('integrations.flows.src.service.schedule_service.should_fetch')
    @patch('integrations.flows.src.service.schedule_service.enqueue_fetch_job')
    @patch('integrations.flows.src.service.schedule_service.update_connection_status')
    def test_run_scheduler_handles_exceptions(self, mock_update, mock_enqueue, mock_should_fetch, mock_get_connections):
        mock_get_connections.return_value = [{'id': '1', 'schedule': {'type': 'interval', 'value': 3600}, 'status': {
            'last_run': (datetime.utcnow() - timedelta(hours=2)).isoformat()}}]
        mock_should_fetch.side_effect = Exception('Test Exception')
        run_scheduler('test_table', 'test_queue_url')
        mock_update.assert_called_once_with('test_table', '1', 'SCHEDULE_FAILED')


if __name__ == '__main__':
    unittest.main()
