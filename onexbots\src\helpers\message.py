import os

from helpers.lambda_client import lambda_client_invoke_async

ENV = os.getenv('ENV')


def invoke_delete_messages_by_conversation(conversation_id: str) -> None:
    """Delete all messages associated with a conversation.

    Args:
        conversation_id: Conversation ID
    """

    payload = {
        'conversation_id': conversation_id,
    }
    return lambda_client_invoke_async(payload,
                                      function_name=f'optiwarehouse-onexbots-service-{ENV}-deleteMessagesHandler')
