from dataclasses import dataclass
from enum import Enum

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel


class ReservedPriceGroup(Enum):
    RETAILS = 'RETAILS'
    WHOLESALE = 'WHOLESALE'
    COST = 'COST'


@dataclass
class PriceGroup(Attributes):
    id: str
    name: str = None


class PriceGroupSchema(AttributesSchema):
    id = fields.Str(required=True)
    name = fields.Str(allow_none=True)


@dataclass
class PriceGroupAttributes(BasicAttributes, PriceGroup):
    pass


class PriceGroupAttributesSchema(BasicAttributesSchema, PriceGroupSchema):
    pass


class PriceGroupModel(BasicModel):
    table_name = 'priceGroup'
    attributes: PriceGroupAttributes
    attributes_schema = PriceGroupAttributesSchema
    attributes_class = PriceGroupAttributes

    query_mapping = {
        'query': ['name', 'id'],
        'filter': {
            'id': 'query',
            'name': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
        }
    }

    @classmethod
    def initialize_data(cls, user_attributes_obj, initialized_data):
        company_id = user_attributes_obj.get('custom:company_id')
        data = [
            {"id": ReservedPriceGroup.RETAILS.value, "name": "Retail price"},
            {"id": ReservedPriceGroup.WHOLESALE.value, "name": "Wholesale price"},
            {"id": ReservedPriceGroup.COST.value, "name": "Cost price"},
        ]
        init_data = [BasicAttributes.add_basic_attributes(item, company_id) for item in data]
        initialized_data[cls.table_name] = init_data
        cls.batch_add(init_data)
