from dataclasses import dataclass, field

from marshmallow import fields
from ..connection_base import OAuth2ConnectionAttributes, OAuth2ConnectionSchema, OAuth2Connection, \
    OAuth2SettingsSchema, OAuth2Settings


@dataclass
class FacebookOAuthSettings(OAuth2Settings):
    app_id: str = None
    app_secret: str = None
    redirect_uri: str = None


class FacebookOAuthSettingsSchema(OAuth2SettingsSchema):
    app_id = fields.Str(required=True)
    app_secret = fields.Str(required=True)
    redirect_uri = fields.Str(required=True)


@dataclass
class FacebookOAuthConnectionAttributes(OAuth2ConnectionAttributes):
    channel_name: str = field(default='facebook_oauth')
    settings: FacebookOAuthSettings = field(default_factory=FacebookOAuthSettings)


class FacebookOAuthConnectionSchema(OAuth2ConnectionSchema):
    channel_name = fields.Str(required=True)
    settings = fields.Nested(FacebookOAuthSettingsSchema(FacebookOAuthSettings))


class FacebookOAuthConnection(OAuth2Connection):
    attributes: FacebookOAuthConnectionAttributes = None
    attributes_schema = FacebookOAuthConnectionSchema
    attributes_class = FacebookOAuthConnectionAttributes
