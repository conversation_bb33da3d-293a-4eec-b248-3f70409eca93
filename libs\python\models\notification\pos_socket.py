from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import Attributes, AttributesSchema, Model


@dataclass
class PosSocket(Attributes):
    user_id: str
    message: dict


class PosSocketSchema(AttributesSchema):
    user_id = fields.Str(str=True)
    message = fields.Dict(required=True)


class PosSocketModel(Model):
    key__id = 'user_id'
    table_name = 'posSocket'
    attributes: PosSocket
    attributes_schema = PosSocketSchema
    attributes_class = PosSocket

    query_mapping = {
        'query': ['user_id'],
        'filter': {
        },
    }
