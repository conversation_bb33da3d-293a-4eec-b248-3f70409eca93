graph TD
    A[API Gateway] --> B[Lambda Function]
    B --> C{Determine Webhook Type}
    C -->|Type 1: Direct URL| D[Get Connection by ID]
    C -->|Type 2: Merchant ID in Payload| E[Get Connection by Merchant ID]
    D --> F[Get Channel Class from Factory]
    E --> F
    F --> G[Instantiate Channel]
    G --> H[Verify Webhook Signature]
    H -->|Invalid| I[Return 401 Unauthorized]
    H -->|Valid| J[Process Webhook]
    J --> K[Execute Channel Actions]
    K --> L[Store Raw Data in S3]
    L --> M[Update Fetch Progress]
    M --> N[Return 200 OK]
    J -->|Error| O[Return 500 Internal Server Error]

    subgraph Execute Channel Actions
    K --> P[Determine Action Type]
    P --> Q[Execute Channel Action]
    Q --> R{More Actions?}
    R -->|Yes| Q
    R -->|No| L
    end
