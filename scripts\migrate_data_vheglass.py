import csv
from scripts.load_env_test import load_env_test
load_env_test("dev")

from integrations.channels.cin7.cin7_library import Cin7<PERSON>ibrary
from integrations.channels.squarespace.squarespace_library import SquarespaceLibrary
from integrations.common.connection_helper import get_connection_by_id
from integrations.common.transformers import registry

cin7_library = Cin7Library(
    account_id="cdd0ccef-78e3-4d91-9d76-ed0e33ad148c",
    api_key="09296dff-7116-ff3a-4870-4e60c92509e0",
)
squarespace_library = SquarespaceLibrary(
    api_key="************************************"
)

cin7_connection= get_connection_by_id("67651bd0-fd91-42e7-8f82-ddd2d83f5827").attributes_dict
square_space_connection= get_connection_by_id("266abf14-f2fe-4dca-8db6-8137642e361e").attributes_dict
source_transformer_class = registry.get_source_transformer("cin7", "product", cin7_connection)
destination_transformer_class = registry.get_destination_transformer("squarespace", "product", square_space_connection)
squarespace_products = squarespace_library._get_all_products({})
items = []
raw_data = []
with open('vglass_data.csv', newline='', encoding='utf-8') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        data = cin7_library.get_products({'Sku': row['ProductCode']}, settings={})
        raw_data.append(data.data[0].raw_data)
        if not data:
            print(row['ProductCode'])
        input_data = source_transformer_class.transform(data.data[0].raw_data)
        output_data = destination_transformer_class.transform(input_data)
        output_data.pop('source')
        items.append(output_data)

# 1. Build a set of SKUs from items (new data)
item_skus = set()
for item in items:
    for variant in item.get('variants', []):
        item_skus.add(variant.get('sku'))

# 2. Build a mapping of Squarespace product id and their SKUs
squarespace_product_map = {}  # product_id -> set of skus
sku_to_product_id = {}  # sku -> product_id
for product in squarespace_products:
    product_id = product['id']
    product_skus = set()
    for variant in product.get('variants', []):
        sku = variant.get('sku')
        product_skus.add(sku)
        sku_to_product_id[sku] = product_id
    squarespace_product_map[product_id] = product_skus

# 3. Delete products on Squarespace that have no SKU in the new items
for product_id, product_skus in squarespace_product_map.items():
    if not (product_skus & item_skus):  # no overlap
        print(f"Deleting product {product_id} with SKUs {product_skus}")
        # Call Squarespace API to delete product
        squarespace_library._make_request(squarespace_library.endpoints.DELETE_PRODUCT,
                                          path_params={"product_id": product_id})

# --- Xử lý xóa sản phẩm trùng SKU và isVisible=False trên Squarespace ---
from collections import defaultdict

sku_to_products = defaultdict(list)  # sku -> list of (product, variant)
for product in squarespace_products:
    for variant in product.get('variants', []):
        sku = variant.get('sku')
        sku_to_products[sku].append((product, variant))

for sku, prod_list in sku_to_products.items():
    if len(prod_list) > 1:
        # Giữ lại product đầu tiên, xóa các product còn lại
        for product, variant in prod_list[1:]:
            print(f"Deleting extra duplicate product {product['id']} with SKU {sku} (keep only one)")
            squarespace_library._make_request(
                squarespace_library.endpoints.DELETE_PRODUCT,
                path_params={"product_id": product['id']}
            )

sync_response = []
# 4. Add or update products in Squarespace
for item in items:
    # Try to find an existing product by SKU
    print(f"Syncing product with SKUs {[v.get('sku') for v in item.get('variants', [])]}")
    sync_response.append(squarespace_library.sync_product(item))

print(sync_response)

