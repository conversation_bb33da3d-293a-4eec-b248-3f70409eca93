from dataclasses import dataclass
from decimal import Decimal

from marshmallow import fields

from models.basic import BasicAttributes, BasicAttributesSchema, BasicModel, User, UserSchema


@dataclass
class ShiftAttributes(BasicAttributes):
    location_id: str = None
    user: User = None
    terminal_id: str = None
    is_closed: bool = None
    close_time: str = None
    starting_fund: Decimal = 0
    expected_fund: Decimal = 0
    actual_fund: Decimal = 0
    paid_in: Decimal = 0
    paid_out: Decimal = 0
    starting_cash: Decimal = 0
    cash_payments: Decimal = 0
    cash_refund: Decimal = 0
    expected_cash: Decimal = 0
    actual_cash: Decimal = 0
    difference: Decimal = 0
    gross_sale: Decimal = 0
    refunds: Decimal = 0
    discounts: Decimal = 0
    net_sales: Decimal = 0
    cash: Decimal = 0
    banking: Decimal = 0
    note: str = None
    fund_note: str = None


class ShiftAttributesSchema(BasicAttributesSchema):
    location_id = fields.Str(required=True)
    user = fields.Nested(UserSchema(User), required=True)
    terminal_id = fields.Str(required=True)
    is_closed = fields.Bool(allow_none=True, default=False)
    close_time = fields.Str(allow_none=True)
    starting_fund = fields.Decimal(allow_none=True, default=0)
    expected_fund = fields.Decimal(allow_none=True, default=0)
    actual_fund = fields.Decimal(allow_none=True, default=0)
    paid_in = fields.Decimal(allow_none=True, default=0)
    paid_out = fields.Decimal(allow_none=True, default=0)
    starting_cash = fields.Decimal(allow_none=True, default=0)
    cash_payments = fields.Decimal(allow_none=True, default=0)
    cash_refund = fields.Decimal(allow_none=True, default=0)
    expected_cash = fields.Decimal(allow_none=True, default=0)
    actual_cash = fields.Decimal(allow_none=True, default=0)
    difference = fields.Decimal(allow_none=True, default=0)
    gross_sale = fields.Decimal(allow_none=True, default=0)
    refunds = fields.Decimal(allow_none=True, default=0)
    discounts = fields.Decimal(allow_none=True, default=0)
    net_sales = fields.Decimal(allow_none=True, default=0)
    cash = fields.Decimal(allow_none=True, default=0)
    banking = fields.Decimal(allow_none=True, default=0)
    note = fields.Str(allow_none=True)
    fund_note = fields.Str(allow_none=True)


class ShiftModel(BasicModel):
    table_name = 'shift'
    attributes: ShiftAttributes
    attributes_schema = ShiftAttributesSchema
    attributes_class = ShiftAttributes

    query_mapping = {
        'query': ['id', 'note', 'fund_note'],
        'filter': {
            'id': 'query',
            'user.id': 'query',
            'terminal_id': 'query',
            'location_id': 'query',
            'is_closed': 'boolean',
            'starting_cash': 'query',
            'cash_refund': 'query',
            'starting_fund': 'query',
            'expected_fund': 'query',
            'actual_fund': 'query',
            'paid_in': 'query',
            'paid_out': 'query',
            'expected_cash': 'query',
            'actual_cash': 'query',
            'difference': 'query',
            'gross_sale': 'query',
            'refunds': 'query',
            'discounts': 'query',
            'net_sales': 'query',
            'cash': 'query',
            'banking': 'query',
            'company_id': 'query',
            'updated_at': 'range',
            'created_at': 'range',
            'close_time': 'range',
        },
        'mapping': {
            'user': 'user',
        }
    }
